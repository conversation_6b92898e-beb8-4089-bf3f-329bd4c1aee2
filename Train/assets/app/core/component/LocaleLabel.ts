import LocaleLang from "./LocalLang";

const { ccclass, property, menu, requireComponent } = cc._decorator;

@ccclass
@menu('多语言组件/LocaleLabel')
@requireComponent(cc.Label)
export default class LocaleLabel extends LocaleLang {
    protected _label: cc.Label = null

    protected get label() {
        if (!this._label) {
            this._label = this.getComponent(cc.Label)
        }
        return this._label
    }

    private fontName: string = ''

    @property()
    private useSystemFont: boolean = false

    onEnable() {
        if (this.label.enableBold) {
            console.warn("不要用bold", this.node.getPath())
            this.label.enableBold = false
        }
        super.onEnable()
    }

    // 刷新字体
    protected updateFont() {
        if (this.useSystemFont) return

        let jsonFont = this._json?.font
        if (jsonFont) {
            if (jsonFont !== this._font)
                this.setFont(jsonFont)
            return
        }

        this.fontName = "jcyt_600W"
        if (this.fontName !== this._font || this.label.font == null) {
            this.setFont(this.fontName)
        }
    }

    private setFont(fontUrl: string) {
        this._font = fontUrl
        const font = assetsMgr.getFont(fontUrl)
        if (font) {
            this.label.font = font
        }
    }

    public setUpdate(func: Function) {
        this.updateFunc = func
        this.updateString()
    }
}

cc.LocaleLabel = LocaleLabel