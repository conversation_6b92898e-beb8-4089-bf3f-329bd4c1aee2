import ActionEasy from "./ActionEasy";

const { ccclass, property, menu, executeInEditMode } = cc._decorator

@ccclass
@menu('简易动画/ActionMoveBy')
export default class ActionMoveBy extends ActionEasy {
    @property()
    public timeOnce: number = 3
    @property()
    public moveByX: number = 0
    @property()
    public moveByY: number = -8
    play() {
        cc.Tween.stopAllByTarget(this.node)
        cc.tween(this.node).repeatForever(
            cc.tween()
                .by(this.timeOnce, { x: this.moveByX, y: this.moveByY })
                .by(this.timeOnce, { x: -this.moveByX, y: -this.moveByY })
        ).start();
    }
}
