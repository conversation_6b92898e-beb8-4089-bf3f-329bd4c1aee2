import BaseLocale from "../base/BaseLocale";
import CoreEventType from "../event/CoreEventType";
import { languageMgr } from "../manage/LanguageMgr";

const { ccclass, property, menu, requireComponent } = cc._decorator;

@ccclass
@menu('多语言组件/LocaleSprite')
@requireComponent(cc.Sprite)
export default class LocaleSprite extends BaseLocale {

    @property()
    private key: string = ''
    @property([cc.SpriteFrame])
    private frames: cc.SpriteFrame[] = []

    private _sprite: cc.Sprite = null

    private _json: any = null
    private _lang: string = ''
    private _change: boolean = false

    private get sprite() {
        if (!this._sprite) {
            this._sprite = this.getComponent(cc.Sprite)
        }
        return this._sprite
    }

    onEnable() {
        if (!languageMgr.lang) {
            return
        }
        this._change = languageMgr.change
        if (this._change) {
            eventCenter.on(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
        this._lang = languageMgr.lang
        if (!this._json) {
            this.updateJson()
        }
        this.updateSprite()
    }

    onDisable() {
        if (languageMgr.lang && this._change) {
            eventCenter.off(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    onDestroy() {
        assetsMgr.releaseTempResByTag(this.uuid)
    }

    // 语言切换
    private onLanguageChanged(lang: string) {
        this._lang = lang
        this.updateSprite()
    }

    // 刷新string
    private async updateSprite() {
        const val = this._json ? this._json[this._lang] : undefined
        if (val !== undefined) {
            let spr: cc.SpriteFrame = this.frames.find(m => m.name === val)
            if (!spr) {
                spr = await assetsMgr.loadTempRes(`lang/${val}`, cc.SpriteFrame, this.uuid)
            }
            if (!spr) {
                twlog.info('没有找到多语言图片, key=' + this.key + ', url=' + val)
            }
            if (this.isValid) {
                this.sprite.spriteFrame = spr
            }
        } else {
            this.sprite.spriteFrame = null
        }
    }

    // 刷新json
    private updateJson() {
        if (this.key) {
            let key = assetsMgr.getLangKey(this.key)
            let [name, id] = key.split('.')
            this._json = assetsMgr.getJsonData(name, id)
        } else {
            this._json = null
        }
    }

    // 设置key
    public setKey(key: string) {
        if (this.key !== key || !this._json) {
            this.key = key
            this.updateJson()
        }
        this.updateSprite()
    }

    public addSpriteFrame(val: cc.SpriteFrame) {
        this.frames.push(val)
    }
}

cc.LocaleSprite = LocaleSprite