import BaseLocale from "../base/BaseLocale";
import CoreEventType from "../event/CoreEventType";
import { languageMgr } from "../manage/LanguageMgr";

const { ccclass, property, menu, requireComponent } = cc._decorator;

@ccclass
@menu('多语言组件/LocaleRichText')
@requireComponent(cc.RichText)
export default class LocaleRichText extends BaseLocale {

    @property()
    private key: string = ''
    @property()
    private fontName: string = ''
    @property([cc.String])
    private params: string[] = []

    private _label: cc.RichText = null

    private _string: string = ''
    private _lang: string = ''
    private _font: string = ''
    private _json: any = null

    private _params: any[] = []
    private _temp_params: any[] = [] //转换好过后的参数
    private _change: boolean = false

    private _is_empty_string: boolean = false //是否主动设置空字符串

    private typeKP: KeyParams = null

    private updateFunc: Function = null

    private get label() {
        if (!this._label) {
            this._label = this.getComponent(cc.RichText)
        }
        return this._label
    }

    onLoad(): void {
        this._params = this.params
        this.updateFont()
    }

    onEnable() {
        if (!languageMgr.lang) {
            return
        }
        if (this._lang !== languageMgr.lang) {
            this._lang = languageMgr.lang
            this._json = null
            this.updateTempParams()
        }
        if (!this._json) {
            this.updateJson()
        }
        this.updateString()
        this._change = languageMgr.change
        if (this._change) {
            eventCenter.on(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    onDisable() {
        if (this._change) {
            this._change = false
            eventCenter.off(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    // 语言切换
    private onLanguageChanged(lang: string) {
        this._lang = lang
        this.updateTempParams()
        this.updateJson()
        this.updateString()
    }

    public set string(val: string) {
        this.label.string = val
        this._is_empty_string = val === ''
    }
    public get string() { return this.label.string }

    public updateLang() {
        this._lang = languageMgr.lang
    }

    // 刷新string
    public updateString() {
        if (this.updateFunc) {
            this.label.string = this.updateFunc()
            return
        }
        const val = this._json ? this._json[this._lang] : undefined
        if (val !== undefined) {
            this._string = this.richVal(ut.stringFormat(val, this._temp_params), this.typeKP)
        } else if (this.key) {
            if (!this._json) {
                this._string = this.key
            }
            else {
                this.label.string = ''
            }
        } else if (this._is_empty_string) {
            this._string = ''
        } else {
            this._string = '404'
        }
        if (this._string !== this.label.string) {
            this.label.string = this._string
        }
    }

    // 设置参数
    public setParams(params: any[]) {
        this._params.length = 0
        params.forEach(m => Array.isArray(m) ? this._params.pushArr(m) : this._params.push(m))
        this.updateTempParams()
    }

    private richVal(val: string, kp: KeyParams) {
        if (kp) {
            let r = kp.rich
            if (r) {
                let rp = [val]
                rp.pushArr(kp.richParams)
                return ut.stringFormat(r, rp)
            }
        }
        return val
    }

    // 刷新参数
    private updateTempParams() {
        this._temp_params = this.mapParams(this._params)
    }

    private mapParams(params: any[]) {
        return params.map(m => this.getValByParam(m))
    }

    private getValByParam(m: any) {
        if (typeof m == "object") {
            let m2 = m as KeyParams
            let val = this.getValByKey(m2.key)
            let p = m2.params
            if (p) {
                val = ut.stringFormat(val, this.mapParams(p))
            }
            return this.richVal(val, m2)
        } else if (typeof (m) === 'string') {
            return m
            // return this.getValByStr(m)
        }
        return m
    }

    private getValByKey(m: string) {
        if (this.startDian(m)) {
            let [_, id] = m.split('.')
            return id
        }
        return this.getValByStr(assetsMgr.getLangKey(m))
    }

    private getValByStr(m: string) {
        let json = this.getJsonByKey(m)
        if (json) {
            let val = json[this._lang]
            if (val !== undefined) {
                return val
            }
        }
        return m
    }

    private getJsonByKey(m: string) {
        let [name, id] = m.split('.')
        if (name && id) {
            return assetsMgr.getJsonData(name, id)
        }
    }

    private startDian(str: string) {
        return str[0] == '.'
    }

    // 刷新json
    private updateJson() {
        if (this.key) {
            if (!this.startDian(this.key)) {
                this._json = this.getJsonByKey(assetsMgr.getLangKey(this.key))
                this.updateFont()
                return
            }
        }
        this._json = null
    }

    // 刷新字体
    private updateFont() {
        this.fontName = "jcyt_600W"

        if (this.fontName !== '') {
            if (this.fontName !== this._font) {
                this.setFont(this.fontName)
            }
        } else if (this._json && this._json.font && this._json.font !== this._font) {
            this.setFont(this._json.font)
        }
    }

    private setFont(fontUrl: string) {
        this._font = fontUrl
        let preLang = this._lang
        //先用系统字体
        this.label.font = null
        let font = assetsMgr.getFont(fontUrl)
        if (font) {
            if (this._font === fontUrl && preLang === this._lang) {
                if (this.isValid) {
                    this._font = fontUrl
                    this.label.font = font
                    // @ts-ignore
                    this.label._updateRichText()
                }
            }
        }
        else if (this.isValid) {//找不到字体
            this.label.font = null
            this._font = ''
        }
    }

    private initKey(key: string) {
        if (!this._lang) {
            this._lang = languageMgr.lang
        }
        if (this.key !== key || !this._json) {
            this.key = key
            this.string = ''
            this.updateJson()
        }
        this._is_empty_string = key === ''
    }

    // 设置key
    public setKey(key: string, ...params: any[]) {
        this.updateFunc = null
        this.typeKP = null
        this.initKey(key)
        this.setParams(params)
        this.updateString()
    }

    public setUpdate(func: Function) {
        this.updateFunc = func
        this.updateString()
    }

    public setRichKey(kp: KeyParams) {
        this.updateFunc = null
        this.typeKP = kp
        this.initKey(kp.key)
        this.setParams(kp.params)
        this.updateString()
    }
}

cc.LocaleRichText = LocaleRichText