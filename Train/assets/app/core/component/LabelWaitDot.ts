const { ccclass, property, requireComponent, menu } = cc._decorator;

@ccclass
@menu('自定义组件/LabelWaitDot')
@requireComponent(cc.Label)
class LabelWaitDot extends cc.Component {

    @property()
    private interval: number = 0.5

    private label: cc.Label = null
    private originalString: string = ''
    private dot: string = ''

    private elapsed: number = 0
    private running: boolean = false
    private pause: boolean = false

    onLoad() {
        this.label = this.getComponent(cc.Label)
    }

    onEnable() {
        this.pause = false
    }

    onDisable() {
        this.pause = true
    }

    public play(val: string = "") {
        this.originalString = this.label.string = val !== undefined ? val : this.label.string
        this.dot = ''
        this.elapsed = 0
        this.running = true
    }

    public stop(val?: string) {
        this.running = false
        if (val !== undefined) {
            this.label.string = val
        } else {
            this.label.string = this.originalString === undefined ? this.label.string : this.originalString
        }
        this.originalString = ''
        this.dot = ''
    }

    private tick() {
        if (this.dot.length < 3) {
            this.dot += '.'
        } else {
            this.dot = ''
        }
        this.label.string = this.originalString + this.dot
    }

    update(dt: number) {
        if (!this.running || this.pause) {
            return
        }
        this.elapsed += dt
        if (this.elapsed >= this.interval) {
            this.elapsed -= this.interval
            this.tick()
        }
    }
}

cc.LabelWaitDot = LabelWaitDot
