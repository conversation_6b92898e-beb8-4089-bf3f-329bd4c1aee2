const { ccclass, property, menu, requireComponent, executeInEditMode, executionOrder } = cc._decorator;

@ccclass
@requireComponent(cc.Widget)
@executeInEditMode
@menu('自定义组件/WidgetEx')
export default class WidgetEx extends cc.Component {

    @property({ visible() { return this.widget?.isAlignTop } })
    public top: number = 0

    @property({ visible() { return this.widget?.isAlignLeft } })
    public left: number = 0

    @property({ visible() { return this.widget?.isAlignRight } })
    public right: number = 0

    @property({ visible() { return this.widget?.isAlignBottom } })
    public bottom: number = 0

    private widget: cc.Widget = null

    onLoad() {
        this.widget = this.Component(cc.Widget)

        if (CC_EDITOR) return
        cc._widgetManager.updateAlignment(this.node.parent)
        let align = this.widget.getAlignByPos()

        let widget = this.widget
        let fixAlign = (type: string) => {
            let isAlignKey = `isAlign${type}`
            type = type.toLowerCase()
            if (widget[isAlignKey]) {
                let min = widget[type]
                let max = this[type]
                if (min > max) {
                    max = min
                    min = this[type]
                }
                let val = align[type]
                if (val <= min) {
                    widget[type] = min
                }
                else if (val >= max) {
                    widget[type] = max
                }
                // let val = cc.misc.clampf(align[type], min, max)
                // console.log(type, val, align[type], min, max)
                // widget[type] = val
            }
        }

        fixAlign("Top")
        fixAlign("Left")
        fixAlign("Right")
        fixAlign("Bottom")
    }
}

cc.WidgetEx = WidgetEx
