const { ccclass, property, menu, requireComponent } = cc._decorator;

@ccclass
@menu('自定义组件/LabelPlayer')
export default class LabelPlayer extends cc.Component {
    //每秒会出现的字数
    public speed: number = 20
    private label: cc.Label | cc.RichText = null

    public isPlaying: boolean = false
    private index: number = 0
    private totIndex: number = 0

    private onEnd: Function = null

    private textArray: { text: string, style?: { color?: string, isImage?: boolean, src?: string } }[] = []
    private passTime: number = 0

    onLoad() {
        this.label = this.getComponent(cc.Label) || this.getComponent(cc.RichText)
        if (!this.label) {
            twlog.error("LabelPlayer需要cc.Label or cc.RichText")
            return
        }
    }

    public play(onEnd?: Function) {
        if (!this.label) {
            return
        }
        this.passTime = 0
        this.index = 0
        if (this.label instanceof cc.RichText) {
            this.textArray = this.label["_textArray"].slice()
        }
        else {
            this.textArray = [{ text: this.label.string }]
        }
        this.totIndex = 0
        for (let { text } of this.textArray) {
            this.totIndex += text.length
        }
        this.label.string = ""
        this.isPlaying = true
        this.onEnd = onEnd
    }

    public playAsync() {
        return new Promise(r => {
            this.play(r)
        })
    }

    public end() {
        if (!this.label) {
            return
        }
        this.isPlaying = false
        this.index = this.totIndex
        this.updateStr()

        let call = this.onEnd
        if (call) {
            this.onEnd = null
            call()
        }
    }

    public isEnd() {
        return this.index >= this.totIndex
    }

    update(dt) {
        if (!this.label) {
            return
        }
        if (this.isPlaying) {
            this.passTime += dt * this.speed
            if (this.passTime > 1) {
                let inc = Math.floor(this.passTime)
                this.passTime -= inc
                this.index += inc
                this.updateStr()
                if (this.isEnd()) {
                    this.end()
                }
            }
        }
    }

    private updateStr() {
        let index = this.index
        let texts = []
        for (let { text, style } of this.textArray) {
            let t = text
            if (index < text.length) {
                t = text.substring(0, index)
            }
            if (style?.color) {
                texts.push(`<color=${style.color}>${t}</c>`)
            }
            else if (style?.isImage) {
                texts.push(`<img src='${style.src}'/></c>`)
            }
            else {
                texts.push(t)
            }
            index -= t.length
            if (index <= 0) break
        }
        this.label.string = texts.join("")
    }
}

cc.LabelPlayer = LabelPlayer
