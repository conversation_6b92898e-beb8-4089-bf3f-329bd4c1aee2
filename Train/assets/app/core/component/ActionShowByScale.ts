import ActionEasy from "./ActionEasy"

const { ccclass, property, menu, executeInEditMode } = cc._decorator

@ccclass
@menu('简易动画/ActionShowByScale')
export default class ActionShowByScale extends ActionEasy {
    @property()
    public time: number = 0.25
    @property()
    public scale: number = 0.4
    play(cb?: Function) {
        cb = cb || function () { }
        let root = this.node
        let orgScale = root.scale
        root.scale = this.scale
        cc.tween(root).to(this.time, { scale: orgScale }, { easing: cc.easing.backOut }).call(cb).start()
    }
}
