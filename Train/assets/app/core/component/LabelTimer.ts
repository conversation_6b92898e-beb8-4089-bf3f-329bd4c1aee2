import { game<PERSON>elper } from "../../script/common/helper/GameHelper";

const { ccclass, property, menu, requireComponent } = cc._decorator;

@ccclass
@menu('自定义组件/LabelTimer')
@requireComponent(cc.Label)
export default class LabelTimer extends cc.Component {

    @property()
    private format: string = 'hh:mm:ss'
    @property()
    private endTime: number = 0 //结束时间
    @property()
    private prefix: string = ''

    private _label: cc.Label = null
    private pause: boolean = false
    private formatFunc: Function = null //格式化方法

    private time: number = 0
    private tempTime: number = 0
    private callback: Function = null

    private startTime: number = 0

    private speed: number = 1

    private get label() {
        if (!this._label) {
            this._label = this.getComponent(cc.Label)
        }
        return this._label
    }

    public setPause(val: boolean) {
        this.pause = val
    }

    public setPrefix(val: string) {
        this.prefix = val
        return this
    }

    public setFormat(val: string | Function) {
        if (typeof val === 'string') {
            this.format = val
            this.formatFunc = null
        } else if (typeof val === 'function') {
            this.format = ''
            this.formatFunc = val
        }
        return this
    }

    public setEndTime(val: number) {
        this.endTime = val
        return this
    }

    public run(time: number, callback?: Function) {
        this.time = time
        this.speed = ut.normalizeNumber(this.endTime - time)
        this.callback = callback
        this.tempTime = Math.floor(this.time)
        this.startTime = gameHelper.now()
        this.updateLabelString()
    }

    public setTime(time: number) {
        this.time = time
        this.speed = ut.normalizeNumber(this.endTime - time)
        this.tempTime = Math.floor(this.time)
        this.startTime = gameHelper.now()
    }

    public set string(val: string) {
        this.time = -1
        this.tempTime = -1
        this.startTime = 0
        this.callback = null
        this.label.string = val
    }
    public get string() { return this.label.string }
    public getTime() { return this.time }

    update() {
        if (this.startTime === 0 || this.pause) {
            return
        }
        const now = gameHelper.now()
        const dt = (now - this.startTime)
        this.startTime = now

        this.time += dt * this.speed
        const t = Math.floor(this.time)
        if (this.tempTime === t) {
            return
        }
        this.tempTime = t
        if ((this.speed < 0 && this.tempTime <= this.endTime) || (this.speed > 0 && this.tempTime >= this.endTime)) {
            this.startTime = 0
            this.tempTime = this.endTime
            this.updateLabelString()
            this.callback && this.callback()
            this.callback = null
        } else {
            this.updateLabelString()
        }
    }

    private updateLabelString() {
        if (this.format) {
            this.label.string = this.prefix + ut.millisecondFormat(this.tempTime, this.format)
        } else if (this.formatFunc) {
            this.label.string = this.prefix + (this.formatFunc(this.tempTime) ?? this.tempTime)
        } else {
            this.label.string = this.prefix + this.tempTime
        }
    }
}

cc.LabelTimer = LabelTimer
