//这个组件是用来配合自动释放资源的，通常需要与resHelper.loadResTmp配合使用

const {ccclass} = cc._decorator;

@ccclass
export default class AutoReleaseCmt extends cc.Component {

    url: string = null;
    tag: string = null

    public init (url: string, tag: string) {
        this.url = url
        this.tag = tag
    }

    protected onDestroy(): void {
        if (this.url) {
            assetsMgr.releaseTempRes(this.url, this.tag)
            this.url = null
        }
    }
}
