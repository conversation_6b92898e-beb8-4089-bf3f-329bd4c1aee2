const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu('自定义组件/MultiMaterial')
export default class MultiMaterial extends cc.Component {

    @property([cc.Material])
    private materials: cc.Material[] = []

    public setMaterial(idx: number | boolean, properties?: {[key: string]: any}) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.materials.length) {
            return
        }
        let material = this.node.Component(cc.RenderComponent).setMaterial(0, this.materials[i])
        if (properties) {
            for (let key in properties) {
                material.setProperty(key, properties[key])
            }
        }
        return material
    }

    public getMaterial(idx) {
        return this.materials[idx]
    }

}
