const { ccclass, property, menu, requireComponent, executeInEditMode, executionOrder } = cc._decorator

@ccclass
@requireComponent(cc.Label)
@menu('自定义组件/LabelSizeAdapter')
export default class LabelSizeAdapter extends cc.Component {

    @property
    private maxHeight: number = 0

    private label: cc.Label = null

    private preString: string = null

    private orgFontSize: number = 0
    private orgLineHeight: number = 0

    onLoad() {
        this.label = this.getComponent(cc.Label)
        this.orgFontSize = this.label.fontSize
        this.orgLineHeight = this.label.lineHeight
    }

    update() {
        this.checkUpdate()
    }

    public checkUpdate() {
        if (!this.label) return
        if (this.label.string != this.preString) {
            this.preString = this.label.string
            this.adapterSize()
        }
    }

    private adapterSize() {
        let height = this.maxHeight
        if (!height) {
            return
        }

        let label = this.label
        let orgFontSize = this.orgFontSize
        let orgLineHeight = this.orgLineHeight

        let ratio = orgLineHeight / orgFontSize

        if (label.node.height > height) {
            let maxSize = orgFontSize
            let minSize = 16
            while (minSize < maxSize - 1) {
                let midSize = Math.floor((minSize + maxSize) * 0.5)
                label.enabled = false //避免修改属性触发多次更新
                label.fontSize = midSize
                label.lineHeight = midSize * ratio
                label.enabled = true
                if (label.node.height <= height) {
                    minSize = midSize
                }
                else {
                    maxSize = midSize
                }
            }
        }
    }

}

cc.LabelSizeAdapter = LabelSizeAdapter
