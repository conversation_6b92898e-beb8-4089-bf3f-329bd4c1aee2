const { ccclass, property, menu, executeInEditMode } = cc._decorator

enum TypeChange {
    Position,//改变位置
    Size,//改变尺寸
}
enum TypePosition {
    None,
    Center,//对齐sizeNode的x/y轴中心点
}

@ccclass
@executeInEditMode
@menu('自定义组件/SizeChange')
export default class SizeChange extends cc.Component {
    @property(cc.Node)
    private sizeNode: cc.Node = null
    @property({ type: cc.Enum(TypeChange) })
    private changeType: TypeChange = TypeChange.Position
    @property({ type: cc.Enum(TypePosition), visible: function () { return this.changeType === TypeChange.Position } })
    private setXType: TypePosition = TypePosition.None
    @property({ type: cc.Enum(TypePosition), visible: function () { return this.changeType === TypeChange.Position } })
    private setYType: TypePosition = TypePosition.None
    onLoad() {
        this.sizeNode.on(cc.Node.EventType.SIZE_CHANGED, this.onChange.bind(this))
        this.onChange()
    }
    private onChange() {
        if (this.changeType == TypeChange.Position) {
            this.setXByType()
            this.setYByType()
        }
    }
    private setXByType() {
        let type = this.setXType
        if (type == TypePosition.None) return
        let num = 0
        if (type == TypePosition.Center) {
            num = this.sizeNode.x + this.sizeNode.width * (0.5 - this.sizeNode.anchorX)
        }
        this.node.x = num
    }
    private setYByType() {
        let type = this.setYType
        if (type == TypePosition.None) return
        let num = 0
        if (type == TypePosition.Center) {
            num = this.sizeNode.y + this.sizeNode.height * (0.5 - this.sizeNode.anchorY)
        }
        this.node.y = num
    }
}
