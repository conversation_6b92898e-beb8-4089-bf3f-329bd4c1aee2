const { ccclass, property, menu, requireComponent } = cc._decorator;

@ccclass
@menu('自定义组件/MultiFrame')
@requireComponent(cc.Sprite)
export default class MultiFrame extends cc.Component {

    @property([cc.SpriteFrame])
    private frames: cc.SpriteFrame[] = []

    public setFrame(idx: number | boolean) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.frames.length || i < 0) {
            return
        }
        this.node.Component(cc.Sprite).spriteFrame = this.frames[i]
    }

    public getFrame(idx: number | boolean): cc.SpriteFrame {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        return this.frames[i]
    }

    public getIndex(): number {
        let frame = this.node.Component(cc.Sprite).spriteFrame
        return this.frames.findIndex(m => frame === m)
    }

    public frameCount(): number {
        return this.frames.length
    }

    public getSpriteFrame() {
        return this.node.Component(cc.Sprite).spriteFrame
    }

    public random() {
        const len = this.frames.length
        if (len === 0) {
            return -1
        }
        const i = ut.random(0, len - 1)
        this.node.Component(cc.Sprite).spriteFrame = this.frames[i]
        return i
    }
}

cc.MultiFrame = MultiFrame