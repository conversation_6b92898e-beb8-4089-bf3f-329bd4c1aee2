const { ccclass, property, requireComponent, menu } = cc._decorator;

enum TextType {
    NONE,
    COMMA,
}

// 滚动数字
@ccclass
@menu('自定义组件/LabelRollNumber')
@requireComponent(cc.Label)
export default class LabelRollNumber extends cc.Component {

    @property()
    private duration: number = 0.5
    @property({ type: cc.Enum(TextType) })
    private type: number = TextType.NONE
    @property()
    private prefix: string = ''

    private _label: cc.Label = null

    private _start: number = 0
    private _cur: number = 0
    private _end: number = 0
    private elapsed: number = 0
    private time: number = 0

    private get label() {
        if (!this._label) {
            this._label = this.getComponent(cc.Label)
        }
        return this._label
    }

    private getCurrNumber() {
        return this._cur
        // let val = this.label.string
        // if (this.type === TextType.COMMA) {
        //     val = val.replace(/,/g, '')
        // }
        // if (this.prefix) {
        //     val = val.replace(this.prefix, '')
        // }
        // return Number(val)
    }

    private format(val: number) {
        let str = val + ''
        if (this.type === TextType.COMMA) {
            str = ut.formatNumberByComma(val)
        } else if (this.type == TextType.NONE) {
            str = ut.simplifyMoney(val)
        }
        this._cur = val
        return this.prefix + str
    }

    update(dt: number) {
        if (this.time === 0) {
            return
        }
        this.elapsed += dt
        if (this.elapsed >= this.time) {
            this.time = 0
            this.label.string = this.format(this._end)
        } else {
            const current = Math.floor(this._start + (this._end - this._start) * this.sineOut())
            this.label.string = this.format(current)
        }
    }

    private sineOut() {
        return Math.sin((this.elapsed / this.time) * Math.PI * 0.5)
    }

    public setPrefix(val: string) {
        this.prefix = val
        return this
    }

    public set(val: number) {
        this.label.string = this.format(val)
        return this
    }

    public to(end: number, duration?: number) {
        this._start = this.getCurrNumber()
        if (this._start === end) {
            return
        }
        this._end = end
        this.elapsed = 0
        this.time = duration || this.duration
    }

    public by(val: number, duration?: number) {
        if (!val) {
            return
        }
        this._start = this.getCurrNumber()
        this._end = Math.max(this._start + val, 0)
        this.elapsed = 0
        this.time = duration || this.duration
    }

    public run(init: number, val: number, duration?: number) {
        this.set(init)
        this.to(val)
    }
}

cc.LabelRollNumber = LabelRollNumber