const { ccclass, property, requireComponent, menu, executeInEditMode } = cc._decorator;

@ccclass
@requireComponent(cc.Layout)
@menu('自定义组件/LayoutMinSize')
@executeInEditMode
class LayoutMinSize extends cc.Component {

    @property(cc.Size)
    private _minSize: cc.Size = cc.size(0, 0)

    @property(cc.Size)
    public get minSize() {
        return this._minSize
    }
    public set minSize(val) {
        this._minSize = val
        this.doLayout()
    }

    private get layout() {
        return this.node.getComponent(cc.Layout)
    }

    protected onLoad(): void {
        let orgFunc = this.layout["_doLayout"]
        this.layout["_doLayout"] = ()=>{
            orgFunc.call(this.layout)
            this.node.width = Math.max(this.node.width, this.minSize.width)
            this.node.height = Math.max(this.node.height, this.minSize.height)
        }
    }

    protected onEnable(): void {
        this.doLayout()
    }

    private doLayout() {
        this.layout["_doLayout"]()
    }
}