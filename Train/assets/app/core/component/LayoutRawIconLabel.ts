/**
 * 把sprite(sizeMode为raw)与label自动排列
 * 如果有cc.Layout 则只会修改ccLayout的spacingX
*/
const { ccclass, property, menu, executeInEditMode } = cc._decorator

// 谁向谁靠拢
enum TypeChange {
    LabelToIcon,
    IconToLabel,
}
@ccclass
@executeInEditMode
@menu('自定义组件/LayoutRawIconLabel')
export default class LayoutRawIconLabel extends cc.Component {
    @property(cc.Integer)
    private spaceX: number = 0
    @property({ type: cc.Enum(TypeChange) })
    private changeType: TypeChange = TypeChange.LabelToIcon
    private icon: cc.Sprite = null
    private label: cc.Label = null

    onLoad() {
        if (CC_EDITOR) {
            this.initAndCheck()
            this.updateSpace()
        }
    }
    updateSpace() {
        this.initAndCheck()
        let spf = this.icon.spriteFrame
        if (!spf) return
        let off = spf.getOriginalSize().width - spf.getRect().width
        if (off > 0) {
            off = this.icon.node.scale * off * 0.5
        }
        let lyt = this.Component(cc.Layout)
        if (lyt) {
            lyt.spacingX = this.spaceX - off
        } else {
            if (this.changeType == TypeChange.LabelToIcon) {
                this.label.node.x = this.icon.node.x - off + this.spaceX
            } else {
                this.icon.node.x = this.label.node.x + off - this.spaceX
            }
        }
    }
    private initAndCheck() {
        if (this.icon != null) return
        this.icon = this.node.Child('icon', cc.Sprite)
        this.label = this.node.Child('count', cc.Label)
        if (this.icon.node.anchorX != 1) {
            cc.warn('icon的anchorX应该为1')
        }
        if (this.icon.sizeMode != cc.Sprite.SizeMode.RAW) {
            cc.warn('icon的sizeMode应该为Raw')
        }
        if (this.label.node.anchorX != 0) {
            cc.warn('label的anchorX应该为0')
        }
    }
}
