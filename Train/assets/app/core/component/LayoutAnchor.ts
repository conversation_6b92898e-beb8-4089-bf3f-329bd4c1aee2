const { ccclass, property, requireComponent, menu, executeInEditMode } = cc._decorator;

@ccclass
@menu('自定义组件/LayoutAnchor')
@executeInEditMode
class LayoutAnchor extends cc.Component {

    protected preAnchor: cc.Vec2 = null

    protected onEnable(): void {
        this.preAnchor = this.node.getAnchorPoint()
        this.node.on(cc.Node.EventType.ANCHOR_CHANGED, this.updateChildren, this)
    }

    protected onDisable(): void {
        this.node.off(cc.Node.EventType.ANCHOR_CHANGED, this.updateChildren, this)
    }

    updateChildren() {
        let size = this.node.getContentSize()
        let cur = this.node.getAnchorPoint()
        let offset = cur.sub(this.preAnchor)
        offset.x *= size.width
        offset.y *= size.height
        for (let child of this.node.children) {
            child.x -= offset.x
            child.y -= offset.y
        }
        this.preAnchor = cur
    }
}