const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu('自定义组件/MultiColor')
export default class MultiColor extends cc.Component {

    @property([cc.Color])
    private colors: cc.Color[] = []

    public setColor(idx: number | boolean) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.colors.length) {
            return
        }
        this.node.color = this.colors[i]
    }

    public setOutlineColor(idx: number | boolean) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.colors.length) {
            return
        }
        let cmpt = this.node.Component(cc.LabelOutline)
        if (!cmpt) {
            console.warn("not found cc.LabelOutline")
            return
        }
        cmpt.SetColor(this.colors[i])
    }

    public getColor(idx: number | boolean) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.colors.length) {
            return
        }
        return this.colors[i]
    }
}

cc.MultiColor = MultiColor