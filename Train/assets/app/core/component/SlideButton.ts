import { util } from '../utils/Utils';
import ButtonEx from './ButtonEx';

const { ccclass, property, menu, requireComponent, executeInEditMode } = cc._decorator;

@ccclass
@executeInEditMode
@requireComponent(cc.Button)
@menu('自定义组件/SlideButton')
export default class SlideButton extends cc.Component {
    @property({ tooltip: '滑动最小值' })
    public slideMin: cc.Vec2 = cc.v2();
    @property({ tooltip: '滑动最大值' })
    public slideMax: cc.Vec2 = cc.v2();
    @property()
    private eventName: string = '' //事件名字
    @property()
    private eventParams: string = '' //事件参数
    private button: cc.Button = null
    private target: cc.Node = null
    private deltaX: number = 0;
    private slideTarget = -1;
    private inMove: boolean = false;// 滑动中

    protected onLoad(): void {
        this.button = this.getComponent(cc.Button)
        this.target = this.button.target ? this.button.target : this.node
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
    }
    public set interactable(val: boolean) {
        this.button.interactable = val
    }
    public get interactable() {
        return this.button.interactable
    }

    private onTouchMove(event: cc.Event.EventTouch) {
        if (!this.interactable) {
            return
        }
        const hit = this.node._hitTest(event.getLocation())
        if (hit || this.inMove) {
            this.move(event)
        }
    }

    @util.addLock
    private async onTouchEnd(event: cc.Event.EventTouch) {
        if (!this.interactable) {
            return
        }
        await this.restore(event)
    }

    @util.addLock
    private async onTouchCancel(event: cc.Event.EventTouch) {
        if (!this.interactable) {
            return
        }
        this.restoreState();
    }

    //todo 暂时只支持左右滑
    private move(event: cc.Event.EventTouch) {
        let offx = event.getDeltaX();
        if (this.target.x + offx >= this.slideMax.x) return;
        if (this.target.x + offx <= this.slideMin.x) return;
        if (Math.abs(offx) < 1) return;
        this.inMove = true;
        this.deltaX += offx;
        if ((offx >= 0 && this.target.x + this.deltaX > this.slideMin.x * 0.3) || (offx <= 0 && this.target.x + this.deltaX < this.slideMax.x * 0.3)) {
            this.slideTarget = offx >= 0 ? this.slideMax.x : this.slideMin.x;
            this.onTouchEnd(event)
        } else {
            this.slideTarget = -1;
        }
    }
    // 还原
    @util.addLock
    public async restore(event: cc.Event.EventTouch) {
        if (this.inMove) {
            if (this.slideTarget == -1) {
                this.restoreState();
                return;
            }
            await this.moveTo(0.1, this.slideTarget)
        }
        // 发送事件
        if (this.eventName) {
            eventCenter.emit(this.eventName, this.target, this.eventParams || '')
        }
        this.restoreState();
        audioMgr.playSFX(ButtonEx.DefaultClickPath, { volume: 1 })
    }

    public restoreState() {
        this.deltaX = 0;
        this.inMove = false;
    }

    private async moveTo(moveTime: number, targetX: number) {
        await cc.tween(this.target).to(moveTime, { x: targetX }).promise()
        this.target.x = targetX;
    }
}