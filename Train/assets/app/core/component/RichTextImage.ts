const { ccclass, property, menu, requireComponent, executeInEditMode, executionOrder } = cc._decorator;

@ccclass
@menu('自定义组件/RichTextImage')
@requireComponent(cc.RichText)
@executeInEditMode
export default class RichTextImage extends cc.Component {

    public richText: cc.RichText = null

    @property([cc.SpriteAtlas])
    public _imageAtlases: cc.SpriteAtlas[] = []
    @property([cc.SpriteAtlas])
    public get imageAtlases() {
        return this._imageAtlases
    }
    public set imageAtlases (val) {
        this._imageAtlases = val

        this.updateRichText()
    }

    @property([cc.SpriteFrame])
    public _images: cc.SpriteFrame[] = []
    @property([cc.SpriteFrame])
    public get images() {
        return this._images
    }
    public set images (val) {
        this._images = val
        this.updateRichText()
    }

    protected onLoad(): void {
        this.richText = this.getComponent(cc.RichText)

       //@ts-ignore
        this.richText._getSpriteFrame = (spriteFrameName)=>{
            let spf = this.richText.imageAtlas?.getSpriteFrame(spriteFrameName) 
            || this.imageAtlases.find(altas => altas.getSpriteFrame(spriteFrameName))?.getSpriteFrame(spriteFrameName) 
            || this.images.find(spf => spf.name == spriteFrameName)
            if (!spf) {
                cc.warn("richText image not found: " + spriteFrameName)
            }
            return spf
        }
    }

    private updateRichText() {
       //@ts-ignore
       this.richText._layoutDirty = true;
       //@ts-ignore
       this.richText._updateRichTextStatus();
    }
}
