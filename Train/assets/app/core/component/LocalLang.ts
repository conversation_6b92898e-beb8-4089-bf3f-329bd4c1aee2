import BaseLocale from "../base/BaseLocale";
import CoreEventType from "../event/CoreEventType";
import { languageMgr } from "../manage/LanguageMgr";

const { ccclass, property, menu, requireComponent } = cc._decorator;

@ccclass
export default class LocaleLang extends BaseLocale {

    @property()
    protected key: string = ''

    protected _label: cc.Label | cc.RichText = null
    protected _font: string = ''

    protected _json: any = null
    protected _lang: string = ''
    protected _params: any[] = []
    protected _change: boolean = false

    protected updateFunc: Function = null

    protected _is_empty_string: boolean = false //是否主动设置空字符串

    protected get label() {
        if (!this._label) {
            this._label = this.getComponent(cc.Label) || this.getComponent(cc.RichText)
        }
        return this._label
    }

    protected onLoad(): void {
        this.updateFont()
    }

    onEnable() {
        if (!languageMgr.lang) {
            return
        }
        this._change = languageMgr.change
        if (this._change) {
            eventCenter.on(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
        this._lang = languageMgr.lang
        if (!this._json) {
            this.updateJson()
        }
        this.updateString()
    }

    onDisable() {
        if (languageMgr.lang && this._change) {
            eventCenter.off(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
        }
    }

    // 语言切换
    private onLanguageChanged(lang: string) {
        this._lang = lang
        this.updateString()
    }

    public set string(val: string) {
        this.label.string = val
        this._is_empty_string = val === ''
    }
    public get string() { return this.label.string }

    public updateLang() {
        this._lang = languageMgr.lang
    }

    // 刷新string
    public updateString() {
        if (this.updateFunc) {
            this.label.string = this.updateFunc()
            return
        }

        const val = this._json ? this._json[this._lang] : undefined
        if (val !== undefined) {
            this.label.string = ut.stringFormat(val, this.updateParams())
        } else if (this.key) {
            if (!this._json) {
                this.label.string = this.key
            }
            else {
                this.label.string = ''
            }
        } else if (this._is_empty_string) {
            this.label.string = ''
        } else {
            this.label.string = 'miss-key'
        }
    }

    // 刷新json
    private updateJson() {
        if (this.key) {
            let key = assetsMgr.getLangKey(this.key)
            let [name, id] = key.split('.')
            this._json = assetsMgr.getJsonData(name, id)
            this.updateFont()
        } else {
            this._json = null
        }
    }

    // 刷新字体
    protected updateFont() {
    }

    // 刷新参数
    private updateParams() {
        return this._params.map(m => {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                const [name, id] = m.split('.')
                const json = assetsMgr.getJsonData(name, id) || {}
                const val = json[this._lang]
                return val !== undefined ? val : m
            }
            return m
        })
    }

    public getKey() {
        return this.key
    }

    // 设置key
    public setKey(key: string, ...params: any[]) {
        this.updateFunc = null
        if (this.key !== key || !this._json) {
            this.key = key
            this.updateJson()
        }
        this._is_empty_string = key === ''
        this._params.length = 0
        params.forEach(m => Array.isArray(m) ? this._params.pushArr(m) : this._params.push(m))
        this.updateString()
    }

    public setUpdate(func: Function) {
        this.updateFunc = func
        this.updateString()
    }
}