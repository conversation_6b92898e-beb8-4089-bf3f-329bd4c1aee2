import ActionEasy from "./ActionEasy";

const { ccclass, property, menu, executeInEditMode } = cc._decorator

@ccclass
@menu('简易动画/ActionMoveXLoop')
export default class ActionMoveXLoop extends ActionEasy {
    @property()
    public moveLeft: boolean = false
    @property()
    private moveSpeed: number = 10

    private isMove: boolean = false

    play() {
        this.isMove = true
    }
    update(dt: number) {
        if (!this.isMove) return
        let dir = this.moveLeft ? -1 : 1
        let delta = this.moveSpeed * dt
        let width = this.node.width
        let x = this.node.x + delta * dir
        if (this.moveLeft) {
            if (x <= -width) {
                x += width
            }
        } else {
            if (x >= width) {
                x -= width
            }
        }
        this.node.x = x
    }
}

