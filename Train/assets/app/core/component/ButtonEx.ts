import BasePnlCtrl from "../base/BasePnlCtrl";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property, menu, requireComponent, executeInEditMode } = cc._decorator;

enum SoundType {
    NONE,
    DEFAULT,
    CUSTOM,
}

enum EventType {
    NONE,
    OPEN_PNL,
    HIDE_PNL,
    GOTO_WIND,
    CUSTOM,
    CLOSE_PNL,
}

@ccclass
@executeInEditMode
@requireComponent(cc.Button)
@menu('自定义组件/ButtonEx')
export default class ButtonEx extends cc.Component {

    @property()
    private autoZoomScale: boolean = true

    // 点击音效
    @property({ type: cc.Enum(SoundType) })
    private sound: SoundType = SoundType.DEFAULT //音效
    @property({ visible: function () { return this.sound === SoundType.CUSTOM } })
    private soundPath: string = '' //音效路径
    @property({ visible: function () { return this.sound === SoundType.CUSTOM } })
    private soundVolume: number = 1.0 //音量

    // 点击事件
    @property({ type: cc.Enum(EventType) })
    private event: EventType = EventType.NONE //事件类型
    @property({ visible: function () { return this.event === EventType.CUSTOM } })
    private eventName: string = '' //事件名字
    @property({ visible: function () { return this.event !== EventType.NONE } })
    private eventParams: string = '' //事件参数

    private button: cc.Button = null
    private target: cc.Node = null
    private __originalPosition: cc.Vec2 = null
    private __originalAngle: number = null

    private isDown: boolean = false// 是否按下
    private touchId: number = -1

    private tempPnl: BasePnlCtrl = null

    public static DefaultClickPath: string = ''// 默认点击音效

    private __pressedFlag: boolean = false

    private zoomScale: number = 0

    onLoad() {
        this.button = this.getComponent(cc.Button)
        this.target = this.button.target ? this.button.target : this.node
        // 监听事件
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
    }

    public get originalPosition() {
        if (!this.__originalPosition) {
            this.__originalPosition = this.target.getPosition()
        }
        return this.__originalPosition
    }

    public get originalAngle() {
        if (this.__originalAngle === null) {
            this.__originalAngle = this.target.angle
        }
        return this.__originalAngle
    }

    onEnable() {
        if (!CC_EDITOR) {
            this.button["_resetState"]()
        }
    }

    // 是否可点击
    public set interactable(val: boolean) {
        this.button.interactable = val
    }
    public get interactable() {
        return this.button.interactable
    }

    // 触摸开始
    private onTouchStart(event: cc.Event.EventTouch) {
        if (!this.interactable || (event.getID() !== 10000 && this.touchId !== -1)) {
            return
        }
        this.touchId = event.getID()
        this.down()
    }

    // 触摸移动
    private onTouchMove(event: cc.Event.EventTouch) {
        if (!this.interactable || this.touchId !== event.getID()) {
            return
        }
        const hit = this.node._hitTest(event.getLocation())
        if (hit) {
            if (!this.isDown) {
                this.down()
            }
        } else {
            if (this.isDown) {
                this.restore()
            }
        }
    }

    // 触摸结束
    private onTouchEnd(event: cc.Event.EventTouch) {
        if (this.__pressedFlag) {
            return
        }

        if (!this.interactable || (event.getID() !== 10000 && this.touchId !== event.getID())) {
            return
        }

        this.touchId = -1
        this.restore()
        // 播放音效
        this.playSound()
        // 发送事件
        this.emit()

        this.__pressedFlag = true;

        (async () => {
            await ut.waitNextFrame();
            if (this.isValid) {
                this.__pressedFlag = null
            }
        })()
    }

    // 触摸取消
    private onTouchCancel(event: cc.Event.EventTouch) {
        if (!this.interactable || (event && this.touchId !== event.getID())) {
            return
        }
        this.touchId = -1
        this.restore()
    }

    // 按下
    private down() {
        this.isDown = true
        if (this.autoZoomScale && !this.zoomScale) {
            this.zoomScale = this.calcZoomScale()
            this.button.zoomScale = this.zoomScale
            // console.log("btnEx autoScale: ", this.zoomScale)
            this.button["_zoomUp"]()
        }
    }

    // 还原
    private restore() {
        this.isDown = false
        // this.button["_resetState"]()
    }

    // 播放声音
    private playSound() {
        if (this.sound === SoundType.NONE) {
            return
        }
        let url = this.sound === SoundType.DEFAULT ? ButtonEx.DefaultClickPath : this.soundPath
        if (url) {
            audioMgr.playSFX(url, { volume: this.soundVolume })
        }
    }

    // 发送事件
    private emit() {
        if (this.event === EventType.NONE) {
            return
        }
        if (this.event === EventType.HIDE_PNL) {
            if (this.eventParams) {
                return eventCenter.emit(CoreEventType.HIDE_PNL, this.eventParams)
            }
            if (this.tempPnl) {
                return eventCenter.emit(CoreEventType.HIDE_PNL, this.tempPnl)
            }
            let node = this.node
            this.tempPnl = node.getComponent(BasePnlCtrl)
            while (!this.tempPnl) {
                node = node.parent
                this.tempPnl = node && node.getComponent(BasePnlCtrl)
            }
            if (this.tempPnl) {
                eventCenter.emit(CoreEventType.HIDE_PNL, this.tempPnl)
            } else {
                twlog.error('button event [HIDE_PNL] not pnl?')
            }
        } 
        else if (this.event == EventType.CLOSE_PNL) {
            if (this.eventParams) {
                return eventCenter.emit(CoreEventType.CLOSE_PNL, this.eventParams)
            }
            if (this.tempPnl) {
                return eventCenter.emit(CoreEventType.CLOSE_PNL, this.tempPnl)
            }
            let node = this.node
            this.tempPnl = node.getComponent(BasePnlCtrl)
            while (!this.tempPnl) {
                node = node.parent
                this.tempPnl = node && node.getComponent(BasePnlCtrl)
            }
            if (this.tempPnl) {
                eventCenter.emit(CoreEventType.CLOSE_PNL, this.tempPnl)
            } else {
                twlog.error('button event [HIDE_PNL] not pnl?')
            }
        }
        else if (this.event === EventType.CUSTOM) {
            eventCenter.emit(this.eventName, this.eventParams || this.target)
        } else if (this.event === EventType.OPEN_PNL) {
            eventCenter.emit(CoreEventType.OPEN_PNL, this.eventParams)
        } else if (this.event === EventType.GOTO_WIND) {
            eventCenter.emit(CoreEventType.GOTO_WIND, this.eventParams)
        } else {
            twlog.error(`button event not ${this.event}`)
        }
    }

    private calcZoomScale() {
        let {width, height} = this.target.getBoundingBoxToWorld()
        let stockSize = 144
        let stockGainScale = 0.2
        let max = Math.max(width, height)
        let scale = 1
        if (max <= stockSize) {
            scale = 1 + stockGainScale
        }
        else {
            scale = 1 + (stockSize * stockGainScale) / max
        }
        return scale
    }
}

cc.ButtonEx = ButtonEx