const { ccclass, property, menu, requireComponent, executeInEditMode } = cc._decorator

@ccclass
@executeInEditMode
@requireComponent(cc.RichText)
@menu('自定义组件/RichTextMaxWidth')
export default class RichTextMaxWidth extends cc.Component {
    @property
    private maxWidth: number = 0

    public changeWidth() {
        let max = this.maxWidth
        if (max <= 0) return
        let rich = this.node.Component(cc.RichText)
        rich.maxWidth = 0
        if (this.node.width <= max) return
        rich.maxWidth = max
    }
}
