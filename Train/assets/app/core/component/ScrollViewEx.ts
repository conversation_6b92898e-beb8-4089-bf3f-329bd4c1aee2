const { ccclass, property, menu, requireComponent, disallowMultiple, executionOrder } = cc._decorator;

@ccclass
@disallowMultiple()
@requireComponent(cc.ScrollView)
@menu('自定义组件/ScrollViewEx')
@executionOrder(-100)
export default class ScrollViewEx extends cc.Component {

    @property()
    private virtual: boolean = true //动态列表
    @property({ tooltip: CC_DEV && '子的ScrollView,如果有' })
    private childScrollViewName: string = '' //子列表
    @property({ type: cc.Integer, range: [1, 10, 1], tooltip: CC_DEV && '多少帧渲染一次', slide: true })
    private updateRate: number = 1 //渲染频率 多少帧渲染一个
    @property({ type: cc.Integer, range: [0, 12, 1], tooltip: CC_DEV && '一次渲染多少个,0渲染所有', slide: true })
    private updateRenderCount: number = 1 //分帧渲染个数

    private scrollView: cc.ScrollView = null
    private viewSize: cc.Size = null
    private content: cc.Node = null
    private layout: cc.Layout = null
    private scrollType: number = 0 //滚动类型 0.纵向 1.横向
    private rowCount: number = 1 //一列或一行的个数
    private preContentPosition: cc.Vec2 = cc.v2()
    private tempContentPosition: cc.Vec2 = cc.v2()

    private frameCount: number = 0 //多少帧
    private renderStartIndex: number = 0 //渲染起点下标
    private needRenderCount: number = 0 //需要渲染的item个数
    private currRenderCount: number = 0 //当前渲染的item个数

    private item: cc.Node = null
    private pool: cc.Node[] = []
    private itemSize: cc.Size = null
    private childContentDefaultPosition: cc.Vec2 = cc.v2() //子ScrollView的默认位置
    private itemDefaultPosition: cc.Vec2 = cc.v2() //

    private datas: { index: number, node: cc.Node, x: number, y: number, childContentPosition?: cc.Vec2 }[] = []
    private dataCount: number = 0
    private setItemCallback: Function = null
    private callbackTarget: any = null

    private tempVec: cc.Vec2 = cc.v2()

    onLoad() {
        this.init()
    }

    private init() {
        if (this.scrollView) {
            return
        }
        this.scrollView = this.getComponent(cc.ScrollView)
        this.scrollView.Component(cc.Widget)?.updateAlignment()
        this.content = this.scrollView.content
        let view = this.content.parent
        view.Component(cc.Widget)?.updateAlignment()
        this.viewSize = view.getContentSize()
        this.layout = this.content.getComponent(cc.Layout)
        if (!this.layout) {
            return twlog.error('需要Layout组件')
        }
        this.layout.enabled = false

        let item = this.item || this.content.children[0]
        if (this.item != item) {
            item = this.item = cc.instantiate(item)
        }
        this.item = item
        if (!this.item) {
            return twlog.error('必须满足content中有一个可拷贝的节点')
        }
        if (this.childScrollViewName) {
            const sv = this.item.Child(this.childScrollViewName, cc.ScrollView)
            sv && sv.content.getPosition(this.childContentDefaultPosition)
        }
        this.item.getPosition(this.itemDefaultPosition)
        this.itemSize = this.item.getContentSize()
        if (this.layout.affectedByScale) {
            this.itemSize.width *= this.item.scaleX
            this.itemSize.height *= this.item.scaleY
        }
        this.item.active = false
    }

    private reset() {
        if (!this.scrollView) {
            this.init()
        }
        this.scrollView.stopAutoScroll()
        const sum = this.dataCount
        const layout = this.layout, LayoutType = cc.Layout.Type
        let setItemPos: Function = null, width = this.content.width, height = this.content.height
        if (layout.type === LayoutType.VERTICAL) {
            height = sum * this.itemSize.height + (sum - 1) * layout.spacingY + layout.paddingTop + layout.paddingBottom
            setItemPos = this.setItemPosByVertical
            this.scrollType = 0
            this.rowCount = 1
        } else if (layout.type === LayoutType.HORIZONTAL) {
            width = sum * this.itemSize.width + (sum - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight
            setItemPos = this.setItemPosByHorizontal
            this.scrollType = 1
            this.rowCount = 1
        } else if (layout.type === LayoutType.GRID) {
            if (layout.startAxis === cc.Layout.AxisDirection.HORIZONTAL) {
                const w = width - layout.paddingLeft - layout.paddingRight + layout.spacingX
                const row = this.rowCount = Math.floor(w / (this.itemSize.width + layout.spacingX))
                const count = Math.ceil(sum / row)
                height = count * this.itemSize.height + (count - 1) * layout.spacingY + layout.paddingTop + layout.paddingBottom
                setItemPos = this.setItemPosByGridHorizontal
                this.scrollType = 0
            } else {
                const h = height - layout.paddingTop - layout.paddingBottom + layout.spacingY
                const row = this.rowCount = Math.floor(h / (this.itemSize.height + layout.spacingY))
                const count = Math.ceil(sum / row)
                width = count * this.itemSize.width + (count - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight
                setItemPos = this.setItemPosByGridVertical
                this.scrollType = 1
            }
        }
        this.content.setContentSize(width, height)
        this.preContentPosition.set(this.content.getPosition(this.tempContentPosition))
        // 设置需要显示的个数
        if (!this.virtual) {
            this.renderStartIndex = 0
            this.needRenderCount = sum
        } else if (this.scrollType === 0) {
            const vh = this.viewSize.height - layout.paddingTop - layout.paddingBottom + layout.spacingY
            this.needRenderCount = (Math.ceil(vh / (this.itemSize.height + layout.spacingY)) + 1) * this.rowCount
            // 下面计算开始位置 先默认 anchorY = 1
            const cy = Math.abs(this.preContentPosition.y - layout.paddingTop + layout.spacingY)
            this.renderStartIndex = cc.misc.clampf(Math.floor(cy / (this.itemSize.height + layout.spacingY)) * this.rowCount, 0, this.dataCount - 1)
        } else if (this.scrollType === 1) {
            const vw = this.viewSize.width - layout.paddingLeft - layout.paddingRight + layout.spacingX
            this.needRenderCount = (Math.ceil(vw / (this.itemSize.width + layout.spacingX)) + 1) * this.rowCount
            // 下面计算开始位置 先默认 anchorY = 0
            const cx = Math.abs(this.preContentPosition.x + layout.paddingLeft - layout.spacingX)
            this.renderStartIndex = cc.misc.clampf(Math.floor(cx / (this.itemSize.width + layout.spacingX)) * this.rowCount, 0, this.dataCount - 1)
        }
        // 清空content
        this.pool.length = 0
        // this.items.length = 0
        for (let i = this.content.childrenCount - 1; i >= 0; i--) {
            const it = this.content.children[i]
            if (this.pool.length < this.needRenderCount && cc.isValid(it)) {
                this.putItem(it)
            } else {
                it.destroy()
            }
        }
        // 调整ScrollBar位置
        // @ts-ignore
        this.scrollView.horizontalScrollBar && this.scrollView.horizontalScrollBar._onScroll(cc.Vec2.ZERO)
        // @ts-ignore
        this.scrollView.verticalScrollBar && this.scrollView.verticalScrollBar._onScroll(cc.Vec2.ZERO)
        // 预设item坐标
        this.datas.length = 0
        for (let i = 0; i < sum; i++) {
            this.datas.push(setItemPos.call(this, i, { index: i, x: 0, y: 0 }))
        }
    }

    // VERTICAL
    private setItemPosByVertical(i: number, out: any) {
        const layout = this.layout
        // x
        out.x = this.itemDefaultPosition.x
        // y
        const startY = this.content.anchorY === 1 ? layout.paddingTop : layout.paddingBottom
        out.y = startY + this.itemSize.height * (1 - this.item.anchorY) + i * this.itemSize.height + i * layout.spacingY
        if (this.content.anchorY === 1) {
            out.y = -out.y
        }
        return out
    }

    // HORIZONTAL
    private setItemPosByHorizontal(i: number, out: any) {
        const layout = this.layout
        // x
        const startX = this.content.anchorX === 1 ? layout.paddingRight : layout.paddingLeft
        out.x = startX + this.itemSize.width * this.item.anchorX + i * this.itemSize.width + i * layout.spacingX
        if (this.content.anchorX === 1) {
            out.x = -out.x
        }
        // y
        out.y = this.itemDefaultPosition.y
        return out
    }

    // GRID.HORIZONTAL 需要content.anchorY != 0.5
    private setItemPosByGridHorizontal(index: number, out: any) {
        const layout = this.layout
        // x
        const startX = -this.content.anchorX * this.content.width + layout.paddingLeft
        let i = index % this.rowCount
        out.x = startX + this.itemSize.width * this.item.anchorX + i * this.itemSize.width + i * layout.spacingX
        // y
        const startY = this.content.anchorY === 1 ? layout.paddingTop : layout.paddingBottom
        i = Math.floor(index / this.rowCount)
        out.y = startY + this.itemSize.height * this.item.anchorY + i * this.itemSize.height + i * layout.spacingY
        if (this.content.anchorY === 1) {
            out.y = -out.y
        }
        return out
    }

    // GRID.VERTICAL
    private setItemPosByGridVertical(index: number, out: any) {
        const layout = this.layout
        // y
        const startY = -this.content.anchorY * this.content.height + layout.paddingBottom
        let i = index % this.rowCount
        out.y = startY + this.itemSize.height * this.item.anchorY + i * this.itemSize.height + i * layout.spacingY
        // x
        const startX = this.content.anchorX === 1 ? layout.paddingRight : layout.paddingLeft
        i = Math.floor(index / this.rowCount)
        out.x = startX + this.itemSize.width * this.item.anchorX + i * this.itemSize.width + i * layout.spacingX
        if (this.content.anchorX === 1) {
            out.x = -out.x

        }
        return out
    }

    private getItem() {
        const it = this.pool.pop() || cc.instantiate2(this.item, this.content)
        it.active = true
        return it
    }

    private putItem(it: cc.Node) {
        it.active = false
        it.Data = null
        if (!this.pool.has('uuid', it.uuid)) {
            this.pool.push(it)
        } else {
            twlog.error('ScrollViewEx.putItem has uuid?')
        }
    }

    // 删除节点
    private delItems(start: number, count: number) {
        count = start + count
        for (let i = start; i < count && i < this.dataCount; i++) {
            const d = this.datas[i]
            if (!d.node) {
                continue
            }
            // 如果有子列表 这里记录他的位置
            if (this.childScrollViewName) {
                const sv = d.node.Child(this.childScrollViewName, cc.ScrollView)
                if (!sv) { } else if (d.childContentPosition) {
                    d.childContentPosition.set(sv.content.getPosition(this.tempVec))
                } else {
                    d.childContentPosition = sv.content.getPosition()
                }
            }
            // 回收
            this.putItem(d.node)
            d.node = null
        }
    }

    // 开始创建item
    private updateItems() {
        let cnt = this.updateRenderCount > 0 ? this.updateRenderCount : this.needRenderCount, cur = 0
        if (this.currRenderCount + cnt > this.needRenderCount) {
            cnt = this.needRenderCount - this.currRenderCount
        }
        let i = this.renderStartIndex, l = Math.min(this.renderStartIndex + this.needRenderCount, this.dataCount)
        while (i < l && cur < cnt) {
            const d = this.datas[i++]
            if (d.node) {
                continue
            }
            const it = d.node = this.getItem()
            it.setPosition(d.x, d.y)
            // 如果有子列表 这里还原他的位置
            if (this.childScrollViewName) {
                const sv = d.node.Child(this.childScrollViewName, cc.ScrollView)
                if (!sv) { } else if (d.childContentPosition) {
                    sv.content.setPosition(d.childContentPosition)
                } else {
                    sv.content.setPosition(this.childContentDefaultPosition)
                }
            }
            // 回调
            if (this.setItemCallback) {
                this.callbackTarget ? this.setItemCallback.call(this.callbackTarget, it, d.index) : this.setItemCallback(it, d.index)
            }
            cur += 1
        }
        this.currRenderCount += cnt
        // if (this.currRenderCount === this.needRenderCount) {
        //     cc.log(this.content.childrenCount)
        // }
    }

    // 检测是否有item被滚动出去或进来
    private checkScroll() {
        if (this.datas.length === 0) {
            return
        }
        const d = this.datas[this.renderStartIndex]
        if (!d.node) {
            this.currRenderCount = 0
            return
        }
        let index = this.renderStartIndex, dir = 0

        if (this.scrollType === 0) {
            // 这里先默认 content.anchorY = 1 和 view.anchorY = 1
            if (this.content.anchorY == 0) {
                const sy = this.preContentPosition.y + (d.y + d.node.height * d.node.anchorY)
                if (sy < 0) { //出去了
                    dir = 1
                } else if (sy > (d.node.height - this.layout.spacingY)) {
                    dir = -1
                }
            }
            else {
                const sy = this.preContentPosition.y + (d.y - d.node.height * d.node.anchorY)
                if (sy > 0) { //出去了
                    dir = 1
                } else if (sy < -(d.node.height + this.layout.spacingY)) {
                    dir = -1
                }
            }
        } else if (this.scrollType === 1) {
            // 这里先默认 content.anchorX = 0 和 view.anchorX = 0
            const sx = this.preContentPosition.x + (d.x + d.node.width * (1 - d.node.anchorX))
            if (sx < 0) { //出去了
                dir = 1
            } else if (sx > d.node.width + this.layout.spacingX) {
                dir = -1
            }
        }
        if (dir === 0) {
            return
        } else if (dir === 1) {
            this.delItems(index, this.rowCount)
            index = Math.min(index + this.rowCount, this.dataCount - 1)
            // cc.log('out ........', index)
        } else if (dir === -1) {
            const idx = Math.max(index - this.rowCount, 0)
            if (index !== idx) {
                this.delItems(index + this.needRenderCount - this.rowCount, this.rowCount)
                index = idx
                // cc.log('in ........', index)
            }
        }
        if (this.renderStartIndex !== index) {
            this.renderStartIndex = index
            this.currRenderCount = 0
        }
    }

    update(dt: number) {
        // 虚拟列表检测 滑动
        if (this.virtual && !this.preContentPosition.equals(this.content.getPosition(this.tempContentPosition))) {
            this.preContentPosition.set(this.tempContentPosition)
            this.checkScroll()
        }
        // 渲染
        if (this.currRenderCount < this.needRenderCount) {
            this.frameCount += 1
            if (this.frameCount >= this.updateRate) {
                this.frameCount = 0
                this.updateItems()
            }
        }
    }

    // 填充列表
    public list(len: number, cb?: Function, target?: any) {
        this.dataCount = len
        this.setItemCallback = cb
        this.callbackTarget = target
        this.reset()
        this.currRenderCount = 0
    }

    public jumpToItem(anchor: cc.Vec2) {
        this.scrollView.scrollTo(anchor)
        this.reset()
        this.currRenderCount = 0
    }

    // 添加
    public addByList() {

    }
}

cc.ScrollViewEx = ScrollViewEx