const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu('自定义组件/MultiFont')
export default class MultiFont extends cc.Component {

    @property([cc.Font])
    private fonts: cc.Font[] = []

    public setFont(idx: number | boolean) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.fonts.length) {
            return
        }
        this.node.Component(cc.Label).font = this.fonts[i]
    }

}

cc.MultiFont = MultiFont
