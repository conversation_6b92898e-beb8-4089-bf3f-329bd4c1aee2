const { ccclass, property, menu, requireComponent, executeInEditMode, executionOrder } = cc._decorator

@ccclass
@requireComponent(cc.RichText)
@menu('自定义组件/RichTextSizeAdapter')
export default class RichTextSizeAdapter extends cc.Component {

    @property
    private maxHeight: number = 0

    private richText: cc.RichText = null

    private preString: string = null

    private orgFontSize: number = 0
    private orgLineHeight: number = 0

    onLoad() {
        this.richText = this.getComponent(cc.RichText)
        this.orgFontSize = this.richText.fontSize
        this.orgLineHeight = this.richText.lineHeight
    }

    update() {
        this.checkUpdate()
    }

    public checkUpdate() {
        if (!this.richText) return
        if (this.richText.string != this.preString) {
            this.preString = this.richText.string
            this.adapterSize()
        }
    }

    private adapterSize() {
        let height = this.maxHeight
        if (!height) {
            return
        }
        let richText = this.richText
        let orgFontSize = this.orgFontSize
        let orgLineHeight = this.orgLineHeight

        let ratio = orgLineHeight / orgFontSize

        if (richText.node.height > height) {
            let maxSize = orgFontSize
            let minSize = 16
            while (minSize < maxSize - 1) {
                let midSize = Math.floor((minSize + maxSize) * 0.5)
                richText.enabled = false //避免修改属性触发多次更新
                richText.fontSize = midSize
                richText.lineHeight = midSize * ratio
                richText.enabled = true
                if (richText.node.height <= height) {
                    minSize = midSize
                }
                else {
                    maxSize = midSize
                }
            }
        }
    }

}

cc.RichTextSizeAdapter = RichTextSizeAdapter
