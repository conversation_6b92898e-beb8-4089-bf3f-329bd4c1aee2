const { ccclass, property, menu, requireComponent } = cc._decorator;

/**
 * 当content的大小没有超出列表的范围时,直接让列表不生效
*/
@ccclass
@menu('自定义组件/ScrollViewContent')
@requireComponent(cc.ScrollView)
export default class ScrollViewContent extends cc.Component {
    private myScroll: cc.ScrollView
    onLoad() {
        this.myScroll = this.Component(cc.ScrollView)
        this.myScroll.content.on(cc.Node.EventType.SIZE_CHANGED, this.updateSvSize, this);
        this.updateSvSize()
    }
    private updateSvSize() {
        let bol = this.checkOut()
        this.myScroll.enabled = bol
        let mask = this.node.Child('view', cc.Mask)
        if (mask) mask.enabled = bol
    }
    private checkOut() {
        let node = this.myScroll.node
        let content = this.myScroll.content
        return content.width > node.width || content.height > node.height
    }

}
