
/**
 * Vec2扩展方法
 */

cc.Vec2.prototype.set2 = function (x: number, y: number): cc.Vec2 {
    this.x = x;
    this.y = y;
    return this;
};

// 比较
cc.Vec2.prototype.equals2 = function (x: number, y: number): boolean {
    return this.x === x && this.y === y;
}

// 拼接字符串
cc.Vec2.prototype.Join = function (separator: string = ','): string {
    return this.x + separator + this.y
};

cc.Vec2.prototype.toVec3 = function (): cc.Vec3 {
    this['z'] = 0
    return this
}

cc.Vec2.prototype.newVec3 = function (): cc.Vec3 {
    return cc.v3(this.x, this.y, 0)
}

cc.Vec2.prototype.toJson = function (): any {
    return { x: this.x, y: this.y }
}

// 交换xy的值
cc.Vec2.prototype.swapSelf = function (): cc.Vec2 {
    let x = this.x
    this.x = this.y
    this.y = x
    return this
}

//欧几里得距离
cc.Vec2.prototype.edistance = function (point: cc.Vec2): number {
    return Math.abs(this.x - point.x) + Math.abs(this.y - point.y)
}

// 获取向量相对于x轴正方向的夹角，带符号
cc.Vec2.prototype.xAngle = function (): number {
    return cc.misc.radiansToDegrees(Math.atan2(this.y, this.x));
}

/**
 * Vec3扩展方法
 */

cc.Vec3.prototype.set2 = function (x: number, y: number, z: number): cc.Vec3 {
    this.x = x;
    this.y = y;
    this.z = z;
    return this;
};

// 比较
cc.Vec3.prototype.equals2 = function (x: number, y: number, z: number): boolean {
    return this.x === x && this.y === y && (z === undefined || this.z === z);
}

// 拼接字符串
cc.Vec3.prototype.Join = function (separator: string = ','): string {
    return this.x + separator + this.y + separator + this.z
};

cc.Vec3.prototype.toVec2 = function (): cc.Vec2 {
    return this
}

cc.Vec3.prototype.newVec2 = function (): cc.Vec2 {
    return cc.v2(this.x, this.y)
}

cc.Vec3.prototype.xAngle = function (): number {
    return cc.misc.radiansToDegrees(Math.atan2(this.y, this.x));
}
