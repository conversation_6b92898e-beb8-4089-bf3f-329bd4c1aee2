import { MAX_VALUE } from "../../script/common/constant/Constant";
import { util } from "../utils/Utils";

if (!Array.prototype.remove) {
    Object.defineProperty(Array.prototype, 'remove', {
        value(key: any, value?: any) {
            let i = value !== undefined ? this.findIndex(m => m && m[key] === value) : this.findIndex(m => m == key);
            return i === -1 ? null : this.splice(i, 1)[0];
        },
        enumerable: false
    });
}

if (!Array.prototype.delete) {
    Object.defineProperty(Array.prototype, 'delete', {
        value(cb: (value: any, index: number) => boolean) {
            const arr = [];
            for (let i = this.length - 1; i >= 0; i--) {
                if (cb(this[i], i)) {
                    arr.push(this.splice(i, 1)[0]);
                }
            }
            return arr;
        },
        enumerable: false
    });
}

if (!Array.prototype.random) {
    Object.defineProperty(Array.prototype, 'random', {
        value() {
            if (this.length === 0) {
                return undefined;
            }
            const i = util.randomIndex(this.length)
            return this[i];
        },
        enumerable: false
    });
}

if (!Array.prototype.has) {
    Object.defineProperty(Array.prototype, 'has', {
        value(key: any, value?: any) {
            return value !== undefined ? this.some(m => m && m[key] === value) : this.some(m => m == key);
        },
        enumerable: false
    });
}

if (!Array.prototype.append) {
    Object.defineProperty(Array.prototype, 'append', {
        value(val: any) {
            this.push(val);
            return this;
        },
        enumerable: false
    });
}

if (!Array.prototype.push2) {
    Object.defineProperty(Array.prototype, 'push2', {
        value(val: any): boolean {
            let include = this.includes(val);
            if (!include) this.push(val);
            return !include;
        },
        enumerable: false
    });
}

if (!Array.prototype.add) {
    Object.defineProperty(Array.prototype, 'add', {
        value(val: any) {
            this.push(val);
            return val;
        },
        enumerable: false
    });
}

if (!Array.prototype.last) {
    Object.defineProperty(Array.prototype, 'last', {
        value() {
            return this[this.length - 1];
        },
        enumerable: false
    });
}

// 拼接数组 对象
if (!Array.prototype.join2) {
    Object.defineProperty(Array.prototype, 'join2', {
        value(cb: (value: any, index: number) => string, separator: string = ',') {
            return this.map((value: any, index: number) => cb(value, index) || '').join(separator)
        },
        enumerable: false
    });
}

if (!Array.prototype.pushArr) {
    Object.defineProperty(Array.prototype, 'pushArr', {
        value(arr: any[]) {
            if (!arr || arr.length === 0) {
                return this.length
            }
            return Array.prototype.push.apply(this, arr)
        },
        enumerable: false
    });
}

if (!Array.prototype.set) {
    Object.defineProperty(Array.prototype, 'set', {
        value(arr: any[]) {
            this.length = 0
            return Array.prototype.push.apply(this, arr)
        },
        enumerable: false
    });
}

if (!Array.prototype.findLastIndex) {
    Object.defineProperty(Array.prototype, 'findLastIndex', {
        value(cb: (value: any, index: number) => boolean) {
            for (let i = this.length - 1; i >= 0; i--) {
                if (cb(this[i], i)) {
                    return i
                }
            }
            return -1
        },
        enumerable: false
    });
}

if (!Array.prototype.min) {
    let defaultCalFunc = function (a) {
        return a
    }
    Object.defineProperty(Array.prototype, 'min', {
        value<T>(calfunc?: (value: T, index: number) => number): T {
            let minVal = MAX_VALUE
            let index = 0
            calfunc = calfunc || defaultCalFunc
            for (let i = 0; i < this.length; i++) {
                let val = calfunc(this[i], i)
                if (minVal > val) {
                    minVal = val
                    index = i
                }
            }
            return this[index]
        },
        enumerable: false
    });
}

if (!Array.prototype.minList) {
    let defaultCalFunc = function (a) {
        return a
    }
    Object.defineProperty(Array.prototype, 'minList', {
        value<T>(calfunc?: (value: T, index: number) => number): T[] {
            calfunc = calfunc || defaultCalFunc
            let obj = this.min(calfunc)
            let res = []
            if (!obj) return res
            let min = calfunc(obj, 0)
            for (let i = 0; i < this.length; i++) {
                let val = calfunc(this[i], i)
                if (min == val) {
                    res.push(this[i])
                }
            }
            return res
        },
        enumerable: false
    });
}

if (!Array.prototype.max) {
    let defaultCalFunc = function (a) {
        return a
    }
    Object.defineProperty(Array.prototype, 'max', {
        value<T>(calfunc?: (value: T, index: number) => number): T {
            let maxVal = -MAX_VALUE
            let index = 0
            calfunc = calfunc || defaultCalFunc
            for (let i = 0; i < this.length; i++) {
                let val = calfunc(this[i], i)
                if (maxVal < val) {
                    maxVal = val
                    index = i
                }
            }
            return this[index]
        },
        enumerable: false
    });
}

if (!Array.prototype.maxList) {
    let defaultCalFunc = function (a) {
        return a
    }
    Object.defineProperty(Array.prototype, 'maxList', {
        value<T>(calfunc?: (value: T, index: number) => number): T[] {
            calfunc = calfunc || defaultCalFunc
            let obj = this.max(calfunc)
            let res = []
            if (!obj) return res
            let min = calfunc(obj, 0)
            for (let i = 0; i < this.length; i++) {
                let val = calfunc(this[i], i)
                if (min == val) {
                    res.push(this[i])
                }
            }
            return res
        },
        enumerable: false
    });
}


if (!Array.prototype.for) {
    Object.defineProperty(Array.prototype, 'for', {
        value<T>(callback: (value: T, index: number) => null) {
            for (let i = this.length - 1; i >= 0; i--) {
                callback(this[i], i)
            }
        },
        enumerable: false
    });
}

if (!Array.prototype.includes) {
    Object.defineProperty(Array.prototype, 'includes', {
        value<T>(val) {
            return !!this.find(el => el == val)
        },
        enumerable: false
    });
}

if (!Array.range) {
    Object.defineProperty(Array, 'range', {
        value<T>(start: number, end: number) {
            let arr = []
            for (let i = start; i <= end; i++) arr.push(i)
            return arr
        },
        enumerable: false
    });
}