import ButtonEx from '../component/ButtonEx';

cc.Button.prototype._onTouchEnded = function (event) {
    if (!this.interactable || !this.enabledInHierarchy) return;
    let bex = this.node.Component(ButtonEx);
    if (mc && mc.currWind && !bex) {
        audioMgr.playSFX('click', { volume: 1 })
    }
    if (this._pressed) {
        cc.Component.EventHandler.emitEvents(this.clickEvents, event);
        this.node.emit('click', this);
    }
    this._pressed = false;
    this._updateState();
    event.stopPropagation();
};