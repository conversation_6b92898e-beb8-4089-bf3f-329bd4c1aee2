/*
在tween中插入异步函数
这里的做法是返回一个新的tween，来承接后面的链式调用
*/
cc.Tween.prototype.wait = function (func: Function) {
    let newTween = cc.tween(this._target)
    let start = newTween.start
    let orgTween = this
    orgTween.call(async () => { //当前tween把异步函数加入队尾，执行完后再执行新的tween
        await func()
        //@ts-ignore
        if (newTween._actions.length > 0) {
            start.call(newTween)
        }
    })
    newTween.start = function () { //返回新的tween的start重写成调用当前的start，保证顺序是先调用当前的tween
        orgTween.start()
        return newTween
    }
    return newTween
}

cc.Tween.prototype.promise = function () {
    return new Promise(r => {
        this.call(r).start()
    })
}

cc.Tween.prototype.progress = function (duration: number, func: Function) {
    let action = new ProgressAction(duration, func)
    return this.then(action)
}

cc.Tween.prototype.update = function (dt) {
    for (let action of this._actions) {
        action.update(dt)
    }
}
class ProgressAction extends cc.ActionInterval {

    protected _duration: number = 0
    protected _func: Function = null

    constructor(duration?: number, func?: Function) {
        super()
        this._duration = duration
        this._func = func
        if (func) {
            this.initWithDuration(duration, func)
        }
    }

    initWithDuration(duration, func) {
        this._duration = duration
        this._func = func
        //@ts-ignore
        if (super.initWithDuration(duration)) {
            return true;
        }
        return false;
    }

    clone() {
        var action = new ProgressAction();
        //@ts-ignore
        this._cloneDecoration(action);
        action.initWithDuration(this._duration, this._func);
        return action;
    }

    update(dt) {
        //@ts-ignore
        dt = this._computeEaseTime(dt);
        this._func(dt)
    }
}