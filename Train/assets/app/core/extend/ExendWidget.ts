let TOP     = 1 << 0;
let MID     = 1 << 1;   // vertical center
let BOT     = 1 << 2;
let LEFT    = 1 << 3;
let CENTER  = 1 << 4;   // horizontal center
let RIGHT   = 1 << 5;
let HORIZONTAL = LEFT | CENTER | RIGHT;
let VERTICAL = TOP | MID | BOT;

let tInverseTranslate = cc.Vec2.ZERO;
let tInverseScale = cc.Vec2.ONE;

function computeInverseTransForTarget (widgetNode, target, out_inverseTranslate, out_inverseScale) {
    let scaleX = widgetNode._parent.scaleX;
    let scaleY = widgetNode._parent.scaleY;
    let translateX = 0;
    let translateY = 0;
    for (let node = widgetNode._parent;;) {
        translateX += node.x;
        translateY += node.y;
        node = node._parent;    // loop increment
        if (!node) {
            // ERROR: widgetNode should be child of target
            out_inverseTranslate.x = out_inverseTranslate.y = 0;
            out_inverseScale.x = out_inverseScale.y = 1;
            return;
        }
        if (node !== target) {
            let sx = node.scaleX;
            let sy = node.scaleY;
            translateX *= sx;
            translateY *= sy;
            scaleX *= sx;
            scaleY *= sy;
        }
        else {
            break;
        }
    }
    out_inverseScale.x = scaleX !== 0 ? (1 / scaleX) : 1;
    out_inverseScale.y = scaleY !== 0 ? (1 / scaleY) : 1;
    out_inverseTranslate.x = -translateX;
    out_inverseTranslate.y = -translateY;
}

function getReadonlyNodeSize (parent) {
    if (parent instanceof cc.Scene) {
        return cc.view.getDesignResolutionSize()
    }
    else {
        return parent._contentSize
    }
}

// cc.Widget.prototype.getAlignByPos = function (pos?: cc.Vec2) {
//     let newPos = pos || this.node.position;
//     let delta = newPos;

//     let target = this.node._parent;
//     let inverseScale = cc.Vec2.ONE;

//     if (this._target) {
//         target = this._target;
//         computeInverseTransForTarget(this.node, target, new cc.Vec2(), inverseScale);
//     }

//     let targetSize = getReadonlyNodeSize(target);
//     let deltaInPercent;
//     if (targetSize.width !== 0 && targetSize.height !== 0) {
//         deltaInPercent = new cc.Vec2(delta.x / targetSize.width, delta.y / targetSize.height);
//     }
//     else {
//         deltaInPercent = cc.Vec2.ZERO;
//     }

//     let align:any = {}
//     if (this.isAlignTop) {
//         align.top = (this.isAbsoluteTop ? delta.y : deltaInPercent.y) * inverseScale.y;
//     }
//     if (this.isAlignBottom) {
//         align.bottom = (this.isAbsoluteBottom ? delta.y : deltaInPercent.y) * inverseScale.y;
//     }
//     if (this.isAlignLeft) {
//         align.left = (this.isAbsoluteLeft ? delta.x : deltaInPercent.x) * inverseScale.x;
//     }
//     if (this.isAlignRight) {
//         align.right = (this.isAbsoluteRight ? delta.x : deltaInPercent.x) * inverseScale.x;
//     }
//     if (this.isAlignHorizontalCenter) {
//         align.horizontalCenter = (this.isAbsoluteHorizontalCenter ? delta.x : deltaInPercent.x) * inverseScale.x;
//     }
//     if (this.isAlignVerticalCenter) {
//         align.verticalCenter = (this.isAbsoluteVerticalCenter ? delta.y : deltaInPercent.y) * inverseScale.y;
//     }
//     return align
// };

cc.Widget.prototype.getAlignByPos = function() {
    let node = this.node
    let widget = this
    let hasTarget = this._target;
    let target;
    let inverseTranslate, inverseScale;
    if (hasTarget) {
        target = hasTarget;
        inverseTranslate = tInverseTranslate;
        inverseScale = tInverseScale;
        computeInverseTransForTarget(node, target, inverseTranslate, inverseScale);
    }
    else {
        target = node._parent;
    }
    let targetSize = getReadonlyNodeSize(target);
    let targetAnchor = target._anchorPoint;

    let isRoot = !CC_EDITOR && target instanceof cc.Scene;
    let x = node.x, y = node.y;
    let anchor = node._anchorPoint;

    let align: any = {}

    if (this._alignFlags & HORIZONTAL) {
        let localLeft = 0, localRight = 0, targetWidth = targetSize.width;
        let width, anchorX = anchor.x, scaleX = node.scaleX;

        if (widget.isStretchWidth) {
            width = node.width //这里可能有问题
            if (scaleX !== 0) {
                width = node.width / scaleX;
            }
            localLeft = x - anchorX * width;
            localRight = width + localLeft
        }
        else {
            width = node.width * scaleX;
            if (widget.isAlignHorizontalCenter) {
                let targetCenter = (0.5 - targetAnchor.x) * targetSize.width;
                if (hasTarget) {
                    targetCenter += inverseTranslate.x;
                    targetCenter *= inverseScale.x;
                }

                let localHorizontalCenter = x - targetCenter + (anchorX - 0.5) * width
                if (hasTarget && inverseScale.x) {
                    localHorizontalCenter /= inverseScale.x;
                }

                align.horizontalCenter = widget._isAbsHorizontalCenter ? localHorizontalCenter : (localHorizontalCenter / (targetWidth || 1))
            }
            localLeft = x - anchorX * width;
            localRight = x - (anchorX - 1) * width;
        }

        if (hasTarget) {
            inverseTranslate.x && (localLeft -= inverseTranslate.x)
            inverseScale.x && (localLeft /= inverseScale.x)
            inverseTranslate.x && (localRight -= inverseTranslate.x)
            inverseScale.x && (localRight /= inverseScale.x)
        }

        if (isRoot) {
            localLeft -= cc.visibleRect.left.x;
            localRight -= cc.visibleRect.right.x;
        }
        else {
            if (!widget.isStretchHeight) {
                localRight = (targetWidth + -targetAnchor.x * targetWidth) - localRight
            }
            else {
                localRight = (targetWidth + localLeft) - localRight;
            }
            localLeft -= (-targetAnchor.x * targetWidth);
        }

        if (!widget._isAbsLeft && targetWidth) {
            localLeft /= targetWidth
        }
        if (!widget._isAbsRight && targetWidth) {
            localRight /= targetWidth
        }
        align.left = localLeft
        align.right = localRight
    }

    if (widget._alignFlags & VERTICAL) {

        let localTop = 0, localBottom = 0, targetHeight = targetSize.height;
        let height, anchorY = anchor.y, scaleY = node.scaleY;

        if (widget.isStretchHeight) {
            height = node.height //这里可能有问题
            if (scaleY !== 0) {
                height = node.height / scaleY;
            }
            localBottom = y - anchorY * height;
            localTop = height + localBottom
        }
        else {
            height = node.height * scaleY;
            if (widget.isAlignVerticalCenter) {
                let targetCenter = (0.5 - targetAnchor.y) * targetSize.height;
                if (hasTarget) {
                    targetCenter += inverseTranslate.y;
                    targetCenter *= inverseScale.y;
                }

                let localVerticalCenter = y - targetCenter + (anchorY - 0.5) * height
                if (hasTarget && inverseScale.y) {
                    localVerticalCenter /= inverseScale.y;
                }

                align.localVerticalCenter = widget._isAbsVerticalCenter ? localVerticalCenter : (localVerticalCenter / (targetHeight || 1))
            }
            localBottom = y - anchorY * height;
            localTop = y - (anchorY - 1) * height;
        }

        if (hasTarget) {
            inverseTranslate.y && (localBottom -= inverseTranslate.y)
            inverseScale.y && (localBottom /= inverseScale.y)
            inverseTranslate.y && (localTop -= inverseTranslate.y)
            inverseScale.y && (localTop /= inverseScale.y)
        }

        if (isRoot) {
            localBottom -= cc.visibleRect.left.y;
            localTop = cc.visibleRect.right.y - localTop;
        }
        else {
            if (!widget.isStretchHeight) {
                localTop = (targetHeight + -targetAnchor.y * targetHeight) - localTop
            }
            else {
                localTop = (targetHeight + localBottom) - localTop;
            }
            localBottom -= (-targetAnchor.y * targetHeight);
        }

        if (!widget._isAbsBottom && targetHeight) {
            localBottom /= targetHeight
        }
        if (!widget._isAbsTop && targetHeight) {
            localTop /= targetHeight
        }
        align.bottom = localBottom
        align.top = localTop
    }
    return align
}

