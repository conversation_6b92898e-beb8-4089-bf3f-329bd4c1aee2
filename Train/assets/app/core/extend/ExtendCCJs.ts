
//是否是数组
cc.js.isArray = function (val: any) { return val != null && val instanceof Array }
//是否是table
cc.js.isTable = function (val: any) { return val != null && typeof val === 'object' }
//是否是函数
cc.js.isFunction = function (val: any) { return val != null && typeof val === 'function' }
//倾倒对象
cc.js.dump = function (objIn: any, descript?: string) { cc.log(this.getDumpStr(objIn, descript)) }
//对象转换成字符串
cc.js.getDumpStr = function (objIn: any, descript?: string) {
    let strAll = descript != null ? descript + "=" : ""
    if (!cc.js.isTable((objIn))) {
        return strAll + objIn
    }
    let _dump = function (obj, kongGe) {
        strAll += "{"
        let haveValue = false
        let newKong = kongGe + "    "
        for (let key in obj) {
            let value = obj[key]
            if (cc.js.isFunction(value)) {
                break
            }
            haveValue = true
            strAll += "\n" + newKong + key + ": "
            if (cc.js.isTable((value))) {
                _dump(value, newKong)
            } else {
                strAll += value + ','
            }
        }
        if (haveValue)
            strAll += "\n" + kongGe + "},"
        else
            strAll += "},"
    }
    _dump(objIn, "")
    return strAll
}
