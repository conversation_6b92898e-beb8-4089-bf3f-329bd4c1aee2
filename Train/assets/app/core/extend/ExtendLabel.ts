
/**
 * Label扩展方法
 */

cc.Label.prototype.setLocaleKey = function (key: string, ...params: any[]) {
    const localeLabel = this.Component(cc.LocaleLabel)
    if (localeLabel) {
        localeLabel.setKey(key, ...params);
    } else {
        this.string = key;
    }
}

cc.Label.prototype.getLineCount = function () {
    this._forceUpdateRenderData()
    return Math.floor(this.node.height / this.lineHeight)
} 

cc.Label.prototype.setLocaleUpdate = function(func: Function) {
    const localeLabel = this.Component(cc.LocaleLabel)
    if (localeLabel) {
        localeLabel.setUpdate(func);
    } else {
        this.string = func()
    }
}