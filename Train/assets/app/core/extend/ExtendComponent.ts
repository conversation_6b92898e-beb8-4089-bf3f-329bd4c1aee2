
/**
 * Component扩展方法
 */

cc.Component.prototype.getActive = function () {
    return this.node.active
}

cc.Component.prototype.setActive = function (val: boolean) {
    this.node.active = val
}

cc.Component.prototype.getPosition = function (out?: cc.Vec2) {
    return this.node.getPosition(out)
}

cc.Component.prototype.FindChild = function (name: string | number, className?: any): any {
    return this.node.FindChild(name, className)
}

cc.Component.prototype.Child = function (name: string | number, className?: any): any {
    return this.node.Child(name, className)
}

cc.Component.prototype.Component = function (className: any): any {
    return this.node.Component(className)
}

cc.Component.prototype.SetColor = function (val: string | cc.Color) {
    if (this instanceof cc.LabelOutline) {
        if (val instanceof cc.Color) {
            this.color = val
        }
        else {
            this.color = new cc.Color().fromHEX(val)
        }
    }
    else {
        this.node.SetColor(val);
    }
    return this;
}

cc.Component.prototype.GetColor = function () {
    return this.node.color;
}

cc.Component.prototype.scheduleUpdate = function(callback){
    return this.schedule(callback, 0, cc.macro.REPEAT_FOREVER, 0)
}