cc.RichText.prototype.getLineCount = function () {
    return this._lineCount
}

cc.RichText.prototype.setLocaleKey = function (key: string, ...params: any[]) {
    const localeLabel = this.Component(cc.LocaleRichText);
    if (localeLabel) {
        localeLabel.setKey(key, ...params);
    } else {
        this.string = key;
    }
}

cc.RichText.prototype.setKeyParams = function (kp: KeyParams | string) {
    if (typeof kp == 'string') {//显示错误
        this.string = kp
        return
    }
    let localeLabel = this.Component(cc.LocaleRichText)
    if (localeLabel) {
        localeLabel.setRichKey(kp)
    } else {
        this.string = kp.key
    }
}

cc.RichText.prototype.setLocaleUpdate = function(func: Function) {
    const localeLabel = this.Component(cc.LocaleRichText)
    if (localeLabel) {
        localeLabel.setUpdate(func);
    } else {
        this.string = func()
    }
}
