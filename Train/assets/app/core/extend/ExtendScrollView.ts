
/**
 * cc.ScrollView 扩展方法
 */

// 填充列表
cc.ScrollView.prototype.Items = function <T>(list: T[] | number, cb?: (it: cc.Node, data: T, i: number) => void, target?: any) {
    let i = 0
    let prefabKey = "__itemsPrefab"
    let item = this[prefabKey] || this.content.children[0]
    let count = 0
    if (typeof (list) === 'number') {
        count = list
        list = null
    } else {
        count = list.length
    }
    if (!item) {
        return twlog.error('必须满足content中有一个可拷贝的节点')
    }
    if (this[prefabKey] != item) {
        item = this[prefabKey] = cc.instantiate(item)
    }
    let childs = this.content.children
    for (let l = this.content.childrenCount; i < l; i++) {
        const it = childs[i]
        if (i < count) {
            it.active = true
            setItemData(it, list && list[i], i, cb, target)
        } else {
            it.Data = null
            it.active = false
        }
    }
    for (; i < count; i++) {
        const it = cc.instantiate2(item, this.content)
        it.active = true
        setItemData(it, list && list[i], i, cb, target)
    }
}

// 添加一个
cc.ScrollView.prototype.AddItem = function (cb: (it: cc.Node, i: number) => void, target?: any) {
    let i = this.content.children.findIndex(m => !m.active)
    let it = null
    if (i !== -1) {
        it = this.content.children[i]
    } else {
        i = this.content.childrenCount
        it = cc.instantiate2(this.content.children[0], this.content)
    }
    it.active = true
    setItemData(it, i, undefined, cb, target)
}

function setItemData(it: cc.Node, data: any, i: number, cb: Function, target: any) {
    it.Data = data;
    if (!cb) {
        return
    }
    if (target) {
        cb.call(target, it, data, i)
    } else {
        cb(it, data, i)
    }
}

// 查找content的子节点
cc.ScrollView.prototype.Find = function (predicate: (value: cc.Node, index: number, obj: cc.Node[]) => unknown, thisArg?: any): cc.Node {
    return this.content.children.find(predicate, thisArg)
}

//
cc.ScrollView.prototype.IsEmpty = function () {
    return !this.content.children.some(m => m.active)
}

// list填充
cc.ScrollView.prototype.List = function (len: number, cb?: (it: cc.Node, i: number) => void, target?: any) {
    const ex = this.Component(cc.ScrollViewEx)
    if (ex) {
        return ex.list(len, cb, target)
    } else {
        twlog.error('List error, not ScrollViewEx!')
    }
}

// 跳到指定百分比位置上
cc.ScrollView.prototype.JumpToItem = function (anchor: cc.Vec2) {
    const ex = this.Component(cc.ScrollViewEx)
    if (ex) {
        return ex.jumpToItem(anchor)
    } else {
        twlog.error('JumpTo error, not ScrollViewEx!')
    }
}


cc.PageView.prototype.Items = function <T>(list: T[] | number, cb?: (it: cc.Node, data: T, i: number) => void, target?: any) {
    let i = 0
    let prefabKey = "__itemsPrefab"
    let item = this[prefabKey] || this.content.children[0]
    let count = 0
    if (typeof (list) === 'number') {
        count = list
        list = null
    } else {
        count = list.length
    }
    if (!item) {
        return twlog.error('必须满足content中有一个可拷贝的节点')
    }
    if (this[prefabKey] != item) {
        item = this[prefabKey] = cc.instantiate(item)
    }
    let childs = this.content.children
    for (let l = this.content.childrenCount; i < l; i++) {
        const it = childs[i]
        if (i < count) {
            setItemData(it, list && list[i], i, cb, target)
        } else {
            this.removePageAtIndex(i)
        }
    }
    for (; i < count; i++) {
        const it = cc.instantiate(item)
        this.addPage(it)
        setItemData(it, list && list[i], i, cb, target)
    }
}
