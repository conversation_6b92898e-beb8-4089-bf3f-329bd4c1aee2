/**
 * 
 * @param iterators 
 * @description 返回第一个成功的
 */
if (!Promise.any) {
    Promise.any = function <T>(iterators: readonly (T | PromiseLike<T>)[]): Promise<T> {
        const promises = Array.from(iterators);
        const num = promises.length;
        const rejectedList = new Array(num);
        let rejectedNum = 0;
    
        return new Promise((resolve, reject) => {
            promises.forEach((promise, index) => {
                Promise.resolve(promise)
                    .then(value => resolve(value))
                    .catch(error => {
                        rejectedList[index] = error;
                        if (++rejectedNum === num) {
                            reject(rejectedList);
                        }
                    });
            });
        });
    };
}
