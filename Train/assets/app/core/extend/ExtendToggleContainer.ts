
/**
 * cc.ToggleContainer 扩展方法
 */

// 切换
cc.ToggleContainer.prototype.Swih = function (val: string | number | SwihToggleCallback): cc.Toggle[] {
    let name: string = '', cb: SwihToggleCallback = null
    if (typeof (val) === 'function') {
        cb = val
    } else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val)
    } else {
        return []
    }
    let arr: cc.Toggle[] = []
    this.toggleItems.forEach((m: cc.Toggle) => {
        let checked = cb ? !!cb(m) : (m.node.name === name)
        if (checked) {
            if (!m.isChecked) {
                m.isChecked = true
            }
            arr.push(m)
        } else if (m.isChecked) {
            m.isChecked = false
        }
    })
    return arr
}

// 切换tabs
cc.ToggleContainer.prototype.Tabs = function (val: string | number): cc.Toggle {
    let name = String(val), ret: cc.Toggle = null
    this.toggleItems.forEach((m: cc.Toggle) => {
        if (m.node.name === name) {
            m.isChecked = true
            this.checkEvents[0].emit([m, 'true'])
            ret = m
        } else if (m.isChecked) {
            m.isChecked = false
        }
    })
    return ret
}