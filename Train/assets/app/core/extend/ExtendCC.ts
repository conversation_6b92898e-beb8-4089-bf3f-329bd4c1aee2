
cc.instantiate2 = function (item: cc.Node | cc.Prefab, parent: cc.Node | cc.Component) {
    const it = cc.instantiate(item) as cc.Node;
    it.parent = parent instanceof cc.Component ? parent.node : parent;
    return it;
}

cc.setEnumAttr = function (obj, propName, enumDef) {
    cc.setClassAttr(obj, propName, 'type', 'Enum');
    // @ts-ignore
    cc.setClassAttr(obj, propName, 'enumList', cc.Enum.getList(enumDef));
}

cc.setClassAttr = function (obj, propName, defKey, defValue) {
    // @ts-ignore
    cc.Class.Attr.setClassAttr(obj, propName, defKey, defValue);
}
