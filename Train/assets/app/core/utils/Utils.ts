import Random from "./Random";

// 时间
const Time = {
    Year: 24 * 60 * 60 * 1000 * 7 * 30 * 12,
    Month: 24 * 60 * 60 * 1000 * 7 * 30,
    Week: 24 * 60 * 60 * 1000 * 7,
    Day: 24 * 60 * 60 * 1000,// 天
    Hour: 60 * 60 * 1000,// 时
    Minute: 60 * 1000,// 分
    Second: 1000, //秒
};

function now() {
    return Date.now();
}

function getTimeAgo(time: number | string): string {
    var date = new Date(time);
    var dateTimeStamp = date.getTime()
    var now = new Date().getTime();
    var diffValue = now - dateTimeStamp;
    var result = "";
    if (diffValue < 0) result = assetsMgr.lang('gacha_guiText_12');
    var _min = Math.floor(diffValue / ut.Time.Minute);
    var _hour = Math.floor(diffValue / ut.Time.Hour);
    var _day = Math.floor(diffValue / ut.Time.Day);
    var _week = Math.floor(diffValue / ut.Time.Week);
    var _month = Math.floor(diffValue / ut.Time.Month);
    var _year = Math.floor(diffValue / ut.Time.Year);
    if (_year >= 1) {
        result = assetsMgr.lang('gacha_guiText_11', _year)
    }
    else if (_month >= 1 && _month < 12) {
        result = assetsMgr.lang('gacha_guiText_10', _month)
    }
    else if (_week >= 1 && _week < 5 && _week > 6 && _month < 1) {
        result = assetsMgr.lang('gacha_guiText_9', _week)
    }
    else if (_day >= 1 && _day <= 6) {
        result = assetsMgr.lang('gacha_guiText_8', _day)
    }
    else if (_hour >= 1 && _hour <= 23) {
        result = assetsMgr.lang('gacha_guiText_7', _hour)
    }
    else if (_min >= 1 && _min <= 59) {
        result = assetsMgr.lang('gacha_guiText_6', _min)
    }
    else if (diffValue >= 0 && diffValue <= ut.Time.Minute) {
        result = assetsMgr.lang('gacha_guiText_5')
    }
    return result;
}

function millisecondToString(msd: number) {
    const second = msd / 1000
    const hour = Math.floor(second / 3600)
    const minute = Math.floor(second / 60)
    if (hour > 0) {
        return hour + '小时'
    }
    else if (minute > 0) {
        return minute + '分钟'
    }
    else {
        return Math.floor(second) + '秒'
    }
}

// 将毫秒数格式化
function millisecondFormat(msd: number, format: string = 'mm:ss') {
    let second = msd / 1000
    if (/(d+)/i.test(format)) {
        const day = Math.floor(second / 86400)
        second -= day * 86400
        format = format.replace(/(d+)/g, RegExp.$1.length === 1 ? day + '' : pad(day))
    }
    if (/(h+)/i.test(format)) {
        const hour = Math.floor(second / 3600)
        second -= hour * 3600
        format = format.replace(/(h+)/g, RegExp.$1.length === 1 ? hour + '' : pad(hour))
    }
    if (/(m+)/i.test(format)) {
        const minute = Math.floor(second / 60)
        second -= minute * 60
        format = format.replace(/(m+)/g, RegExp.$1.length === 1 ? minute + '' : pad(minute))
    }
    if (/(s+)/i.test(format)) {
        format = format.replace(/(s+)/g, RegExp.$1.length === 1 ? Math.floor(second) + '' : pad(Math.floor(second)))
    }
    return format
}
function secondFormat(val: number, format: string = 'mm:ss') {
    return millisecondFormat(val * 1000, format)
}

// 将日期毫秒数格式化 format('yyyy-MM-dd hh:mm:ss')
function dateFormat(format: string, msd?: number) {
    const date = msd ? new Date(msd) : new Date()
    const obj = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        'S+': date.getMilliseconds(),
    }
    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (let k in obj) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? String(obj[k]) : pad(obj[k]),
            )
        }
    }
    return format
}

// 首字母变成大写
function initialUpperCase(str: string): string {
    return str.length > 0 ? (str[0].toUpperCase() + str.substring(1, str.length)) : str;
}

// 将数字转换为String
function simplifyMoneyCh(money: number, num: number = 1000): string {
    const value = Math.abs(money);
    if (value >= 100000000 && value >= num) {
        return parseFloat((money / 100000000).toFixed(2)) + '亿';
    }
    else if (value >= 10000 && value >= num) {
        return parseFloat((money / 10000).toFixed(2)) + '万';
    }
    else if (value >= 1000 && value >= num) {
        return parseFloat((money / 1000).toFixed(2)) + '千';
    }
    return money + '';
}

// 将数字转换为String
function simplifyMoneyEn(money: number, num: number = 1000): string {
    const value = Math.abs(money);
    if (value < num) {
        return parseFloat(money.toFixed(2)) + '';
        // } else if (value >= 1000000000000000000) {
        //     return parseFloat((money / 1000000000000000000).toFixed(2)) + 'e';
        // } else if (value >= 1000000000000000) {
        //     return parseFloat((money / 1000000000000000).toFixed(2)) + 'p';
    }
    else if (value >= 1000000000000) {
        return parseFloat((money / 1000000000000).toFixed(2)) + 't';
    }
    else if (value >= 1000000000) {
        return parseFloat((money / 1000000000).toFixed(2)) + 'g';
    }
    else if (value >= 1000000) {
        return parseFloat((money / 1000000).toFixed(2)) + 'm';
    }
    else {
        return parseFloat((money / 1000).toFixed(2)) + 'k';
    }
}

let _moneyUnit = ['K', 'M', 'G', 'T', 'P', 'E', 'Z']
/**
 * 将数字转换为String
 * 货币数量显示规则 最大4位数(不包括小数点和千位符)
 * */
function simplifyMoney(money: number): string {
    let value = Math.abs(money)
    if (value < 10000) {
        return money + ''
    }
    let unit = 1000
    let max = unit
    let idx = 0
    while (true) {
        if (idx == _moneyUnit.length - 1) break
        if (value < max * unit) break
        max *= unit
        idx++
    }
    return toFixed(money / max, 1) + _moneyUnit[idx]
}

// 名字省略
function nameFormator(name: string, max: number, extra: string = '...'): string {
    if (name.length <= max) {
        return name;
    }
    let cnt = 0, len = 0;
    max = max * 2;
    for (let i = 0; i < name.length; i++) {
        const val = name.charCodeAt(i) > 255 ? 2 : 1;
        if (len + val <= max - 2) {
            cnt += 1;
        }
        len += val;
        if (len > max) {
            break;
        }
    }
    return len <= max ? name : name.substr(0, cnt) + extra;
}

// 将数字以逗号隔开
function formatNumberByComma(num: number): string {
    return num.toString().replace(/\d(?=(?:\d{3})+\b)/g, '$&,');
}

// 获取对象中的基本数据类型
function getBaseAttribute(data: Object, attribute: Array<string>) {
    let result = {};
    for (let i = 0; i < attribute.length; ++i) {
        let key = attribute[i];
        if (data[key] == undefined) continue;
        if (!isBasicType(data[key])) continue;
        result[key] = data[key];
    }
    return result;
}

// 是否是基本数据类型
function isBasicType(value: any) {
    const type = Object.prototype.toString.call(value);
    return (type == '[object String]' ||
        type == '[object Boolean]' ||
        type == '[object Number]' ||
        type == '[object Null]' ||
        type == '[object Symbol]')
}

let randomObj = new Random()

// 随机一个整数 包括min和max
function random(min: number, max?: number): number {
    return randomObj.int(min, max)
}

// 概率
function chance(odds: number, mul: number = 100): boolean {
    return randomObj.chance(odds, mul)
}

// 随机小数
function randomRange(min: number, max: number): number {
    return randomObj.get(min, max)
}

function randomInAry(ary: number[]) {
    return ut.randomRange.call(null, ...ary)
}

// 取得任意一个随机数
function getRandomNum(min: number, max: number, isFloat: boolean = false, decimal: number = 2): number {
    decimal = decimal < 0 ? 0 : decimal;
    let multiple = Math.pow(10, Math.floor(decimal));
    return isFloat ? (Math.floor((min + (Math.random() * (max - min))) * multiple) / multiple) : (min + Math.round(Math.random() * (max - min)));
}

//绘制虚线
function drawDottedLine(graphics: cc.Graphics, from: cc.Vec2, to: cc.Vec2, color: cc.Color, width: number = 10, length: number = 10, interval: number = 5) {
    let off = to.sub(from);
    let dir = off.normalize();
    let dis = off.mag();
    let delta = dir.mul(length + interval);
    let delta1 = dir.mul(length);
    let sum = Math.floor(dis / (length + interval))
    for (let i = 0; i < sum; ++i) {
        let start = from.add(delta.mul(i));
        graphics.moveTo(start.x, start.y);
        let end = start.add(delta1);
        graphics.lineTo(end.x, end.y);
    }
    let start1 = from.add(delta.mul(sum));
    graphics.moveTo(start1.x, start1.y);
    if (length < dis - (length + interval) * sum) {
        let end = start1.add(delta1);
        graphics.lineTo(end.x, end.y);
    }
    else {
        graphics.lineTo(to.x, to.y);
    }
    graphics.lineWidth = width;
    graphics.strokeColor = color;
    graphics.stroke();
}

// 绘制实线
function drawLine(graphics: cc.Graphics, from: cc.Vec2, to: cc.Vec2, color: cc.Color, width: number = 10) {
    graphics.moveTo(from.x, from.y);
    graphics.lineTo(to.x, to.y);
    graphics.strokeColor = color;
    graphics.lineWidth = width;
    graphics.stroke();
}

// 随机一个下标出来
function randomIndex(len: number) {
    return randomObj.int(0, len - 1)
}

function randomArray<T>(array: T[]) {
    return randomObj.randomArray(array)
}

// 获取两点之间的角度
function getAngle(a: cc.Vec2, b: cc.Vec2): number {
    return cc.misc.radiansToDegrees(Math.atan2(b.y - a.y, b.x - a.x));
}

// 规范角度
function normAngle(angle: number) {
    return angle > 0 ? angle - 360 : angle;
}
function sin(angle: number) {
    return Math.sin(cc.misc.degreesToRadians(angle))
}
function cos(angle: number) {
    return Math.cos(cc.misc.degreesToRadians(angle))
}

function tan(angle: number) {
    return Math.tan(cc.misc.degreesToRadians(angle))
}

// 同父级下两点之间的距离
function calculationDis(startPos: cc.Vec2, tarPos: cc.Vec2) {
    let dx = startPos.x - tarPos.x;
    let dy = startPos.y - tarPos.y;
    let dis = Math.sqrt(dx * dx + dy * dy);
    return dis;
}

// 根据角度和距离 获取坐标
const _temp_point = cc.v2()
function angleToPoint(angle: number, dis: number, out?: cc.Vec2) {
    out = out || _temp_point
    out.x = cos(angle) * dis
    out.y = sin(angle) * dis
    return out
}

const _temp_vec3 = cc.v3();
const _out_vec2 = cc.v2();
const _mat4 = cc.mat4();
// 获取某个节点的某个坐标在某个节点里面的坐标
function convertToNodeAR(node: cc.Node, targetNode: cc.Node, nodePoint?: cc.Vec2, out?: cc.Vec2, withCamera: boolean = false): cc.Vec2 {
    out = out || cc.v2()
    // 先将节点转到世界坐标
    let worldMatrix = node.getWorldMatrix(_mat4);
    if (nodePoint) {
        cc.Vec2.transformMat4(_temp_vec3, cc.v3(nodePoint), worldMatrix);
    }
    else {
        worldMatrix.getTranslation(_temp_vec3);
    }

    if (withCamera) {
        let camera = cc.Camera.findCamera(node);
        let targetCamera = cc.Camera.findCamera(targetNode);
        if (!!camera && !!targetCamera && camera != targetCamera) { //不同摄像机进行视角变换
            camera.getWorldToScreenPoint(_temp_vec3, _temp_vec3); //camera视角下的世界坐标到屏幕坐标
            targetCamera.getScreenToWorldPoint(_temp_vec3, _temp_vec3); //屏幕坐标到targetCamera下的世界坐标
        }
        ;
    }

    // 再将节点转到目标节点局部坐标
    return targetNode.convertToNodeSpaceAR(_temp_vec3.toVec2(), out);
}

// 改变父节点但保持相对位置不变
function convertParent(node: cc.Node, parent: cc.Node, useCamera: boolean = false) {
    node.setPosition(convertToNodeAR(node.parent, parent, node.getPosition(), null, useCamera))
    node.parent = parent
}

// 数字 字符串补0,根据长度补出前面差的0
const _pad_tbl = {};
function pad(num: number, length: number = 2): string {
    const len = length - num.toString().length;
    if (len <= 0) {
        return num + '';
    }
    if (!_pad_tbl[len]) {
        _pad_tbl[len] = (new Array(len + 1)).join('0');
    }
    return _pad_tbl[len] + num;
}

// 将一个数字 分解成多个类型的数字
function decomposeNumberToTypes(num: number, types: number[] = [100000, 10000, 1000, 100, 10, 1], out?: any): any {
    let ret = out || {}, type: number, count: number;
    types.sort((a, b) => b - a);// 先从大到小排个序
    for (let i = 0; i < types.length; i++) {
        type = types[i];
        count = Math.floor(num / types[i]);
        if (count >= 1) {
            ret[type] = count;
            num -= type * count;
        }
    }
    // 如果还有 就默认去最后一个 算一个
    if (num > 0) {
        type = types[types.length - 1];
        count = ret[type] || 0;
        ret[type] = count ? count + 1 : 1;
    }
    return ret;
}

// 将一个字符串转换成向量
function stringToVec2(str: string, separator: string = ','): cc.Vec2 {
    if (!str) {
        return cc.v2();
    }
    const [x, y] = str.split(separator);
    return cc.v2(parseFloat(x), parseFloat(y));
}

// 讲一个字符串拆分为数组
function stringToNumbers(str: string, separator: string = '|') {
    if (!str) {
        return []
    }
    else if (typeof str === 'number') {
        str = String(str)
    }
    return str.split(separator).filter(m => m.trim() !== '').map(m => Number(m))
}

//将时间格式转成毫秒
function stringToMs(format: string) {
    let date = new Date(`1970/1/1 ${format}`)
    return date.getTime() - date.getTimezoneOffset() * ut.Time.Minute
}

// 将一个常数变成1
function normalizeNumber(val: number): number {
    return val === 0 ? 0 : val / Math.abs(val);
}

// 将一个数字转换为带正负符号的字符串
function numberToString(val: number): string {
    return (val >= 0 ? '+' : '') + val;
}

// 字符串填充参数
function stringFormat(text: string, params: any[]): string {
    if (!text || !params || params.length === 0) {
        return text;
    }
    params.forEach((p, i) => (text = text.replace(new RegExp('\\{' + i + '\\}', 'g'), p)));
    return text;
}

// 富文本标签参数填充
function stringRichTextFormat(text: string, params: any[]): string {
    if (!text || !params || params.length === 0) {
        return text;
    }
    params.forEach((p, i) => (text = text.replace(new RegExp('\\<' + i + '\\>', 'g'), p)));
    return text;
}

//尽量平均地拆分数字
function numAvgSplit(number: number, count: number, random: boolean = true): Array<number> {
    let arr = [];
    let base = Math.floor(number / count)
    let remain = number - base * count
    for (let i = 0; i < count; i++) {
        let add = 0
        if (remain > 0) {
            remain--
            add++
        }
        arr.push(base + add)
    }
    if (random) {
        arr = ut.randomArray(arr)
    }
    return arr
}

function numRandomSplit(total: number, n: number) {
    if (n <= 0) {
        throw new Error('n 必须大于 0');
    }

    // 生成 n-1 个随机数
    let randoms = [];
    for (let i = 0; i < n - 1; i++) {
        randoms.push(Math.random());
    }

    // 添加 0 和 1 作为边界
    randoms.push(0, 1);
    randoms.sort((a, b) => a - b);

    // 计算每个区间的值
    let result = [];
    for (let i = 0; i < n; i++) {
        result.push(Math.floor(total * (randoms[i + 1] - randoms[i])));
    }

    // 调整最后一个值以确保总和等于 total
    result[n - 1] = total - result.slice(0, n - 1).reduce((sum, value) => sum + value, 0);

    return result;
}

// 等待 单位秒
async function wait(delay: number, target?: cc.Component): Promise<void> {
    if (delay <= 0) {
        return Promise.resolve()
    }
    const timer = target || cc.Canvas.instance
    return new Promise(resolve => timer.scheduleOnce(resolve, delay))
}

// 等待
async function waitTimeout(delay: number): Promise<void> {
    if (delay <= 0) {
        return Promise.resolve()
    }
    return new Promise(resolve => setTimeout(resolve, delay))
}

// 等待下一帧
async function waitNextFrame(frams?: number, target?: cc.Component): Promise<void> {
    frams = Math.max(1, frams || 1)
    return new Promise(resolve => {
        const timer = target || cc.Canvas.instance
        function callback() {
            frams -= 1
            if (frams <= 0) {
                timer.unschedule(callback)
                resolve()
            }
        }
        timer.schedule(callback, 0)
    })
}

// 根据字符串切换颜色
function colorFromHEX(hex: string): cc.Color {
    return cc.Color.WHITE.fromHEX(hex)
}

/**
 * 角度转化弧度
 * @param value
 * @returns 弧度
 */
function angle2radian(value: number): number {
    return value / 180 * Math.PI;
}

/**
 * 弧度转化角度
 * @param value
 * @returns 角度
 */
function radian2angle(value: number): number {
    return value / Math.PI * 180;
}


// 生成唯一ID
let _accumulation = 1;
let _last_now = 0;
function uid(): string {
    let now = Date.now()
    let id = now * 1000 + 1
    if (now !== _last_now) {
        _last_now = now
        _accumulation = 0
    }
    else if (_accumulation >= 999) {
        _last_now = now + 1
        _accumulation = 0
        id = _last_now * 1000 + 1
    }
    else {
        _accumulation += 1
        id += _accumulation
    }
    return id + ''
}

// 是否对象
function isObject(o: any) {
    return Object.prototype.toString.call(o) === '[object Object]'
}

// 判断对象是否空对象{}
function isEmptyObject(o: any) {
    if (!o) {
        return true
    }
    for (let k in o) {
        return false
    }
    return true
}

// 拷贝对象
function cloneObject<T>(obj: T): T {
    let ret: any = {}
    for (let k in obj) {
        ret[k] = obj[k]
    }
    return ret
}

// 深度拷贝对象
let cloneCache = []
function deepClone(obj, inDeep: boolean = false): any {
    if (!obj) {
        return null;
    }
    if (!inDeep) {
        cloneCache = []
    }
    let objClone = Array.isArray(obj) ? [] : {};
    if (obj && typeof obj === "object") {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                //判断ojb子元素是否为对象，如果是，递归复制
                let value = obj[key];
                if (value && typeof value === "object") {
                    if (cloneCache.indexOf(value) === -1) {
                        cloneCache.push(value);
                        objClone[key] = deepClone(value, true);
                    }

                }
                else {
                    //如果不是，简单复制
                    objClone[key] = value;
                }
            }
        }
    }
    if (!inDeep) {
        cloneCache = null;
    }
    return objClone;
}

// 深底比较两个对象是否相等
function compareObject(x: any, y: any, leftChain = [], rightChain = []) {

    // 如果都是NaN 直接返回
    if (isNaN(x) && isNaN(y) && typeof x === 'number' && typeof y === 'number') {
        return true
    }

    // 一样直接返回
    if (x === y) {
        return true
    }

    // 如果是方法
    // if ((typeof x === 'function' && typeof y === 'function') ||
    //     (x instanceof Date && y instanceof Date) ||
    //     (x instanceof RegExp && y instanceof RegExp) ||
    //     (x instanceof String && y instanceof String) ||
    //     (x instanceof Number && y instanceof Number)) {
    //     return x.toString() === y.toString()
    // }

    if (!(x instanceof Object && y instanceof Object)) {
        return false
    }

    if (x.isPrototypeOf(y) || y.isPrototypeOf(x)) {
        return false
    }

    if (x.constructor !== y.constructor) {
        return false
    }

    if (x.prototype !== y.prototype) {
        return false
    }

    // Check for infinitive linking loops
    if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1) {
        return false
    }

    let p: any
    // Quick checking of one object being a subset of another.
    // todo: cache the structure of arguments[0] for performance
    for (p in y) {
        if (y[p] === undefined) {
            continue
        }
        if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
            return false
        }
        else if (typeof y[p] !== typeof x[p]) {
            return false
        }
    }
    for (p in x) {
        if (x[p] === undefined) {
            continue
        }
        if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
            return false
        }
        else if (typeof y[p] !== typeof x[p]) {
            return false
        }
        const tf = typeof (x[p])
        if (tf === 'object' || tf === 'function') {
            leftChain.push(x)
            rightChain.push(y)
            if (!compareObject(x[p], y[p], leftChain, rightChain)) {
                return false
            }
            leftChain.pop()
            rightChain.pop()
        }
        else if (x[p] !== y[p]) {
            return false
        }
    }
    return true
}

// 循环值
function loopValue(val: number, len: number) {
    if (val < 0) {
        return len - 1
    }
    else if (val >= len) {
        return 0
    }
    return val
}

// 组装列表
function items(arr: cc.Node[], datas: any[], item: cc.Node | cc.Prefab, parent: cc.Node, cb: Function) {
    let i = 0, len = datas.length
    for (let l = arr.length; i < l; i++) {
        if (i < len) {
            cb(arr[i], datas[i], i)
        }
        else {
            arr[i].active = false
        }
    }
    for (; i < len; i++) {
        const it = cc.instantiate2(item, parent)
        cb(it, datas[i], i)
        arr.push(it)
    }
}

// 设置屏幕常亮
function setKeepScreenOn(val: boolean) {
    // @ts-ignore
    CC_JSB && jsb.Device.setKeepScreenOn(val)
}

function boolToNumber(val: boolean) {
    return val ? 1 : 0
}

// 对象给对象赋值
function setValue(fields: string, data: any, target?: any) {
    target = target || {}
    fields.split('|').forEach(m => target[m] = data[m])
    return target
}

// http请求
async function httpRequest(method: string, url: string, data?: any) {
    return new Promise<any>(resolve => {
        method = method.toUpperCase()
        if (method !== 'POST' && method !== 'GET') {
            twlog.info('method error')
            return resolve(null)
        }
        const xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                resolve(xhr.responseText)
            }
        }
        xhr.timeout = 5000;
        xhr.ontimeout = function (e) {
            twlog.info('http timeout')
            resolve(null)
        }
        xhr.onerror = function (e) {
            twlog.info('http disconnect' + e['result'])
            resolve(null)
        }
        xhr.open(method, url, true)
        if (method === 'POST' && data) {
            xhr.setRequestHeader("Content-Type", "application/json")
            xhr.send(JSON.stringify(data))
        }
        else {
            xhr.send(null)
        }
    })
}

// 是否手机平台
function isMobile() {
    return cc.sys.isNative && (cc.sys.os == cc.sys.OS_ANDROID || cc.sys.os == cc.sys.OS_IOS)
}

// 是否微信游戏
function isWechatGame() {
    return cc.sys.platform === cc.sys.WECHAT_GAME
}

// 是否qq游戏
function isQQGame() {
    return typeof qq !== 'undefined'
}

// 判断是否是小程序
function isMiniGame() {
    return isWechatGame() || isQQGame()
}

//判断是否是ios
function isIos() {
    return cc.sys.os == cc.sys.OS_IOS;
}

//判断是否是安卓
function isAndroid() {
    return cc.sys.os == cc.sys.OS_ANDROID;
}

// 随机字符串
function getRandomString(len: number = 8): string {
    const $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    const maxPos = $chars.length
    let str = ''
    for (let i = 0; i < len; i++) {
        str += $chars.charAt(Math.floor(Math.random() * maxPos))
    }
    return str
}

// 创建一个数组
function newArray(count: number, val?: any) {
    if (count <= 0) return []
    return new Array(count).fill(val)
}

// Array.map的异步版本
async function promiseMap<T>(arr: T[], callback?: (T, index) => any): Promise<any[]> {
    let promsies = arr;
    if (callback) {
        promsies = arr.map(callback);
    }
    let results = await Promise.all(promsies)
    return results;
}

/**
 * 对方法进行加锁的修饰器
 */
function addLock(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor {
    let lockKey = `__lock_${propertyName}`;
    if (target && !propertyName) {
        lockKey = target as string;
    }

    const method = propertyDescriptor.value;
    propertyDescriptor.value = async function (...args: any[]) {
        if (this[lockKey]) return;
        this[lockKey] = true;
        try {
            const result = await method.apply(this, args);
            this[lockKey] = false;
            return result
        } finally {
            this[lockKey] = false;
        }
    }
    return propertyDescriptor;
};

/**
 * 处理一个异步方法被多次调用的情况，会按照调用顺序依次进行调用 (处理完第一个调用的结果再处理第二次调用)
 * 使用场景如 日志上报，需要上报每一条日志
 */
function queue(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor {
    let queueKey = `__queue_${propertyName}`;
    if (target && !propertyName) {
        queueKey = target as string;
    }

    const method = propertyDescriptor.value;
    propertyDescriptor.value = async function (...args: any[]) {
        if (!this[queueKey]) this[queueKey] = []
        let queue = this[queueKey] as any[]
        let promise = new Promise((resolve) => {
            queue.push({
                args, resolve
            })
        })
        if (queue.length > 1) {
            return promise
        }

        let handleQueue = async () => {
            while (queue.length > 0) {
                let { args, resolve } = queue[0]
                let result
                try {
                    result = await method.apply(this, args);
                } catch (error) {
                    twlog.error(error)
                } finally {
                    queue.shift()
                    resolve(result)
                }
            }
        }

        handleQueue()
        return promise
    }
    return propertyDescriptor;
};

/** 节流
 * 处理一个异步方法被多次调用的情况，只处理最后一次调用
 * 使用场景如 上报蜡烛，只需要上报最新的就好了
 */
function throttle(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor {
    let queueKey = `__throttle_${propertyName}`;
    if (target && !propertyName) {
        queueKey = target as string;
    }

    const method = propertyDescriptor.value;
    propertyDescriptor.value = async function (...args: any[]) {
        if (!this[queueKey]) this[queueKey] = []
        let queue = this[queueKey] as any[]
        if (!queue) this[queueKey] = []
        let promise = new Promise((resolve) => {
            queue.push({
                args, resolve
            })
        })
        if (queue.length > 1) {
            return promise
        }

        let handleQueue = async () => {
            let len = queue.length
            let last = queue[len - 1]
            if (!last) return
            const result = await method.apply(this, last.args);
            for (let i = 0; i < len; i++) {
                queue[i].resolve(result)
            }
            queue.splice(0, len)
            handleQueue()
        }

        handleQueue()
        return promise
    }
    return propertyDescriptor;
};

/**
 * 保证函数全局只会被调用一次，直到退出游戏
 * 用于多个地方await一个初始化结果
 * *****注意：目前没办法递归
 */
function callOnce(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor {
    let promiseKey = `__callOnce_${propertyName}`;
    if (target && !propertyName) {
        promiseKey = target as string;
    }

    const method = propertyDescriptor.value;
    propertyDescriptor.value = function (...args: any[]) {
        if (this[promiseKey] !== undefined) return this[promiseKey];
        this[promiseKey] = method.apply(this, args)
        return this[promiseKey]
    }
    return propertyDescriptor;
};

function scaleNode(width: number, height: number, target: cc.Node) {
    let scaleX = width / target.width, scaleY = height / target.height
    target.scale = Math.min(scaleX, scaleY)
}

let _pow10 = [1, 10, 100, 1000, 10000, 100000]
function toFixed(val: number, precision: number = 2) {
    let pow10 = _pow10[precision] || Math.pow(10, precision)
    return Math.floor(val * pow10) / pow10
}

function toRound(val: number, precision: number = 2) {
    let pow10 = _pow10[precision] || Math.pow(10, precision)
    return Math.round(val * pow10) / pow10
}

function forEach<T>(array: T[], callback: (el: T, index: number) => {}) {
    for (let i = array.length - 1; i >= 0; i--) {
        callback(array[i], i)
    }
}

function lineLine(a1: cc.Vec2, a2: cc.Vec2, b1: cc.Vec2, b2: cc.Vec2, retP?: cc.Vec2) {
    let ua_t = (b2.x - b1.x) * (a1.y - b1.y) - (b2.y - b1.y) * (a1.x - b1.x)
    let ub_t = (a2.x - a1.x) * (a1.y - b1.y) - (a2.y - a1.y) * (a1.x - b1.x)
    let u_b = (b2.y - b1.y) * (a2.x - a1.x) - (b2.x - b1.x) * (a2.y - a1.y)

    if (u_b !== 0) {
        let ua = ua_t / u_b
        let ub = ub_t / u_b

        if (0 <= ua && ua <= 1 && 0 <= ub && ub <= 1) {
            if (retP) {
                retP.x = a1.x + ua * (a2.x - a1.x)
                retP.y = a1.y + ua * (a2.y - a1.y)
            }
            return true
        }
    }

    return false
}

const _lineRectTmpVec1 = cc.v2(), _lineRectTmpVec2 = cc.v2()
function lineRect(a1: cc.Vec2, a2: cc.Vec2, b: cc.Rect, retP?: cc.Vec2) {
    // let r0 = new cc.Vec2( b.x, b.y );
    // let r1 = new cc.Vec2( b.x, b.yMax );
    // var r2 = new cc.Vec2( b.xMax, b.yMax );
    // var r3 = new cc.Vec2( b.xMax, b.y );

    _lineRectTmpVec1.x = b.x, _lineRectTmpVec1.y = b.y      //r0
    _lineRectTmpVec2.x = b.x, _lineRectTmpVec2.y = b.yMax   //r1

    if (lineLine(a1, a2, _lineRectTmpVec1, _lineRectTmpVec2, retP)) //r0, r1
        return true;

    _lineRectTmpVec2.x = b.xMax, _lineRectTmpVec2.y = b.y   //r3

    if (lineLine(a1, a2, _lineRectTmpVec1, _lineRectTmpVec2, retP)) //r0, r3
        return true;

    _lineRectTmpVec1.x = b.xMax, _lineRectTmpVec1.y = b.yMax  //r2

    if (lineLine(a1, a2, _lineRectTmpVec1, _lineRectTmpVec2, retP)) //r2, r3
        return true;

    _lineRectTmpVec2.x = b.x, _lineRectTmpVec2.y = b.yMax   //r1

    if (lineLine(a1, a2, _lineRectTmpVec1, _lineRectTmpVec2, retP)) //r2, r1
        return true;

    return false;
}

function setTimeout(cb: Function, delay: number, target?: cc.Component) {
    const timer = target || cc.Canvas.instance
    timer.scheduleOnce(cb, delay * 0.001)
    return cb
}

function clearTimeout(cb: Function, target?: cc.Component) {
    const timer = target || cc.Canvas.instance
    timer.unschedule(cb)
}

function resetLightItem(item: cc.Node) {
    item.color = cc.Color.WHITE
    item.children.forEach(node => {
        resetLightItem(node)
    })
}

function colorCodeToNormAry(val: string): number[] {
    let color = new cc.Color().fromHEX(val)
    return [color.r / 255, color.g / 255, color.b / 255, color.a / 255]
}

function combination(datas: any[], count: number, startIndex = 0, path = [], result: any[] = []) {
    if (path.length === count) {
        result.push([...path]);
        return result;
    }

    for (let i = startIndex; i < datas.length; i++) {
        path.push(datas[i]);
        combination(datas, count, i + 1, path, result);
        path.pop();
    }

    return result;
}

const permutationVis = {}
function permutation(datas: any[], count?: number, step = 0, path: any[] = [], result: any[] = []) {
    count = count || datas.length
    if (step >= count) {
        result.push([...path])
        return result
    }
    for (let i = 0; i < datas.length; i++) {
        if (permutationVis[i]) continue
        path[step] = datas[i]
        permutationVis[i] = true
        permutation(datas, count, step + 1, path, result)
        permutationVis[i] = false
    }
    return result
}

// 获取两个矩形的交集面积
function getIntersectionArea(rect1: cc.Rect, rect2: cc.Rect): number {
    if (!rect1.intersects(rect2)) {
        return 0
    }
    const left = Math.max(rect1.x, rect2.x);
    const right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
    const top = Math.max(rect1.y, rect2.y);
    const bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);
    const width = right - left;
    const height = bottom - top;
    return width * height;
}

window['ut'] = {
    Time,
    now,
    getTimeAgo,
    millisecondToString,
    drawLine,
    millisecondFormat,
    secondFormat,
    dateFormat,
    initialUpperCase,
    simplifyMoneyCh,
    simplifyMoneyEn,
    nameFormator,
    formatNumberByComma,
    random,
    randomArray,
    chance,
    randomRange,
    getRandomNum,
    randomIndex,
    getAngle,
    normAngle,
    sin,
    cos,
    tan,
    drawDottedLine,
    angleToPoint,
    convertToNodeAR,
    convertParent,
    pad,
    decomposeNumberToTypes,
    stringToVec2,
    stringToNumbers,
    normalizeNumber,
    numberToString,
    stringFormat,
    stringRichTextFormat,
    wait,
    randomInAry,
    waitTimeout,
    waitNextFrame,
    colorFromHEX,
    calculationDis,
    uid,
    isObject,
    isEmptyObject,
    cloneObject,
    deepClone,
    compareObject,
    items,
    loopValue,
    setKeepScreenOn,
    boolToNumber,
    setValue,
    httpRequest,
    isMobile,
    isMiniGame,
    isIos,
    isAndroid,
    getRandomString,
    newArray,
    promiseMap,
    addLock,
    angle2radian,
    radian2angle,
    scaleNode,
    toFixed,
    toRound,
    forEach,
    getBaseAttribute,
    isBasicType,
    simplifyMoney,
    numAvgSplit,
    lineLine,
    lineRect,
    queue,
    setTimeout,
    clearTimeout,
    stringToMs,
    resetLightItem,
    colorCodeToNormAry,
    callOnce,
    throttle,
    isWechatGame,
    isQQGame,
    combination,
    permutation,
    numRandomSplit,
    getIntersectionArea
}

export const util = window.ut
