import { localConfig } from "../../script/common/LocalConfig"

const methodsToPatch = ['push', 'pop', 'shift', 'unshift', 'sort', 'reverse', 'splice'];
const FLAG = "__ob__"

export default class Observer {
    private _callback: Function

    public debug: boolean = false;
    private key: string = ""

    constructor(key, callback: Function) {
        this.key = key
        this._callback = callback
    }

    public observe(obj: object, obKeys?: string[]) {
        // @ts-ignore
        let path = this.debug && [obj.__proto__.constructor.name]
        if (obKeys) {
            this.observeByKeys(obj, obKeys, path)
        }
        else {
            this.observeObj(obj, path)
        }
    }

    public static getObKeysByDB(dbMap: any, obMap?: any) {
        if (!obMap) return Object.keys(dbMap)
        let resultMap = {}
        if (!dbMap) dbMap = obMap
        for (let key in dbMap) {
            let mapKeys = obMap[key]
            if (mapKeys === undefined) {
                resultMap[key] = true
                continue
            }
            if (mapKeys === null) {
                continue
            }
            if (typeof mapKeys == 'string') {
                resultMap[mapKeys] = true
            }
            else if (Array.isArray(mapKeys)) {
                for (let k of mapKeys) {
                    resultMap[k] = true
                }
            }
            else {
                twlog.error("obMap value must be string or array", mapKeys)
            }
        }
        return Object.keys(resultMap)
    }

    public static addKey(obj: any, key: string | number, value: any) {
        obj[key] = value
        if (obj.__ob__) {
            obj.__ob__.observe(obj, [key])
            obj.__ob__._callback()
        }
    }

    public static delKeys(obj: any, ...keys) {
        for (let key of keys) {
            delete obj[key]
        }
        if (obj.__ob__) {
            obj.__ob__._callback()
        }
    }

    private check(obj, key, path) {
        if (localConfig.release) return

        if (!obj.hasOwnProperty(key)) {
            console.error("ob undefined key!!!!", path, key)
        }
        // let des = Object.getOwnPropertyDescriptor(obj, key)
        // if (des && (des.get || des.set)) {
        //     console.error("ob reDefined get/set!!!!!", path, key, des, des.get || des.set)
        // }
        this.debug && twlog.info("@@@@@@@@@@@@@@", path, key)
    }

    private observeByKeys(obj: object, obKeys: string[], path) {
        let ob = this
        Object.defineProperty(obj, FLAG, { //标记
            value: this,
            enumerable: false,
            writable: true,
            configurable: true
        })

        for (let key of obKeys) {
            let curPath = path && path.concat([key])
            let value = obj[key]
            this.check(obj, key, path)
            Object.defineProperty(obj, key, {
                get: () => {
                    return value
                },
                set: (newValue) => {
                    if (newValue === value) return
                    let old = value
                    value = newValue
                    ob._callback && ob._callback(curPath, newValue, old)
                    ob.observeObj(value, curPath)
                },
                enumerable: true,
                configurable: true
            });
            this.observeObj(value, curPath)
        }
    }

    private isObj(obj) {
        return typeof obj == 'object' && obj != null
    }

    private observeObj(obj, path) {
        if (!this.isObj(obj)) return
        if (obj[FLAG] === this) {
            return
        }
        else if (obj[FLAG] && obj[FLAG] !== this) {
            twlog.error("ob cover", this.key, obj[FLAG].key)
        }

        if (Array.isArray(obj)) {
            this.overrideArrayProto(obj, path)
        }
        let obKeys = []
        if (typeof obj.toDB == 'function') {
            if (typeof obj.getObMap == 'function') {
                obKeys = Observer.getObKeysByDB(obj.toDB(true), obj.getObMap())
            }
            else {
                obKeys = Observer.getObKeysByDB(obj.toDB(true))
            }
        }
        else {
            for (let key in obj) {
                let value = obj[key]
                if (typeof value == 'function') continue;
                obKeys.push(key)
            }
        }
        this.observeByKeys(obj, obKeys, path)
    }

    private overrideArrayProto(array, path) {
        const arrayProto = Array.prototype;
        const arrayMethods = Object.create(arrayProto);
        let ob = this
        methodsToPatch.forEach(function (method) {
            const original = arrayProto[method]
            Object.defineProperty(arrayMethods, method, {
                value: function (...args) {
                    let old = ob.debug && this.slice()

                    const result = original.apply(this, args);
                    let inserted
                    switch (method) {
                        case 'push':
                        case 'unshift':
                            inserted = args
                            break
                        case 'splice':
                            inserted = args.slice(2)
                            break
                    }
                    if (inserted) {
                        if (method == 'push' || method == 'unshift') {
                            ob.observeByKeys(this, [String(this.length - 1)], path)
                        }
                        else if (method == 'splice') {
                            ob.observeByKeys(this, inserted.map((v, index) => String(index + args[0])), path)
                        }
                    }
                    ob._callback && ob._callback(path && path.concat([method]), this, old);
                    return result
                },
                enumerable: true,
                configurable: true
            });
        })
        array['__proto__'] = arrayMethods;
    }
}

// let test = () => {
//     let ob = new Observer((path, n, o) => {
//         console.log("obTest---------------", path, o, "->", n)
//     })

//     let a = [{a: 2}, { a: 1 }, {a: 3}]
//     ob.observe(a)
//     // a.push({ b: 2 })
//     // a.splice(0, 1)
//     a.sort((x, y)=>{
//         return x.a - y.a
//     })
//     console.log(a)
//     console.log(JSON.stringify(a))
//     console.log(Object.keys(a[0]))
// }
// test()