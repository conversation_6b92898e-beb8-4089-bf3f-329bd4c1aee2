
export default class NodePool {

    protected poolMap: {[key: string]: cc.Node[] } = {}

    public clear() {
        this.poolMap = {}
    }

    public get(template: cc.Node, parent: cc.Node) {
        let pool = this.poolMap[template.uuid]
        if (!pool) {
            pool = this.poolMap[template.uuid] = []
        }
        let node = pool.pop()
        if (!node) {
            node = cc.instantiate2(template, parent)
        }
        node.active = true
        return node
    }

    public put(template: cc.Node, node: cc.Node) {
        let pool = this.poolMap[template.uuid]
        if (!pool) {
            pool = this.poolMap[template.uuid] = []
        }
        node.active = false
        pool.push(node)
    }
}