// 自定义伪随机
export default class Random {

    public seed: number = 0

    constructor(seed?: number) {
        this.init(seed)
    }

    public init(seed?: number) {
        this.seed = seed || Date.now()
        return this
    }

    private numn() {
        this.seed = (this.seed * 9301 + 49297) % 233280
        return this.seed / 233280.0
    }

    public get(min: number = 0, max: number = Number.MAX_SAFE_INTEGER) {
        let num = min + (max - min) * this.numn()
        return num
    }

    public get01() {
        return this.numn()
    }

    //随机一个整数
    public int(min: number = 0, max: number = Number.MAX_SAFE_INTEGER) {
        let num = min + (max + 1 - min) * this.numn()
        num = Math.min(max, Math.floor(num))
        return num
    }

    // 概率
    public chance(odds: number, mul: number = 100): boolean {
        return odds > 0 && this.int(0, 100 * mul) <= odds * mul
    }

    public randomArray<T>(array: T[]): T[] {
        for (let i = array.length - 1; i >= 0; --i) {
            let randomIndex = this.int(0, 1e10 + 7) % (i + 1);
            let a = array[randomIndex];
            let b = array[i];
            array[i] = a;
            array[randomIndex] = b;
        }
        return array
    }
}