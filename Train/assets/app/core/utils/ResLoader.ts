import { game<PERSON><PERSON><PERSON> } from "../../script/common/helper/GameHelper"
import { localConfig } from "../../script/common/LocalConfig"
import { Bundle } from "../../script/common/constant/Enums"
// import { resConfig } from "../../script/common/ResConfig"

const openLog = localConfig.openLog

/**
* 资源加载器 2.4.x
*/
class ResLoader {

    private _debug: number = 0 //是否打印日志
    public set debug(val) {
        if (!openLog) return
        if (val) {
            this._debug++
        }
        else {
            this._debug--
        }
    }
    public get debug() {
        return this._debug > 0
    }

    private delayTime: number = 0

    public _error: number = 0 //是否打印错误日志
    public set error(val) {
        if (!openLog) return
        if (val) {
            this._error++
        }
        else {
            this._error--
        }
    }
    public get error() {
        return this._error > 0
    }

    // 远程资源列表
    private remoteAssetMap: Map<string, cc.Asset> = new Map<string, cc.Asset>()

    // 加载时间
    private loadTimeMap: Map<number, number> = new Map<number, number>()
    private __load_id: number = 0 // 加载ID
    private __load_urls: {} = {} //临时记录当前加载的资源
    private __temp_doesn_res: any = {}

    private waitTextureCallbackMap: {} = {}
    private loadTextureMap: {} = {}

    private spriteFrameRefMap: {} = {}
    private textureUrlMap: {} = {}
    private spriteInfoMap: {} = {}
    private partSysInfoMap: {} = {}
    private textureSpriteFrameMap: {} = {}
    private markReloadSpriteFrameMap: {} = {}

    private waitLoadSpriteFrameMap: {} = {}
    private textrueMap: {} = {}//用来判断纹理是否被其他地方释放了
    private ignoreSpriteFrameMap: {} = {}

    private urlBundleMap: Map<string, string> = new Map<string, string>()
    private bundleMap: { [key: string]: cc.AssetManager.Bundle } = {}

    private _isLoadResBundle: boolean = false
    private _bdLoadMap: Map<string, number> = new Map<string, number>()

    public init() {
        this.ignoreSpriteFrameMap['0275e94c-56a7-410f-bd1a-fc7483f7d14a'] = true

        // for (let bundle in resConfig) {
        //     let urls = resConfig[bundle]
        //     for (let url of urls) {
        //         this.urlBundleMap.set(url, bundle)
        //     }
        // }
        this.debug = true
        this.error = true
    }

    public clean() {
        this._debug = 0
        this._error = 0

        this.remoteAssetMap.clear()
        this.loadTimeMap.clear()
        this.__load_id = 0
        this.__load_urls = {}
        this.__temp_doesn_res = {}

        this.waitTextureCallbackMap = {}
        this.loadTextureMap = {}

        this.spriteFrameRefMap = {}
        this.textureUrlMap = {}
        this.spriteInfoMap = {}
        this.partSysInfoMap = {}
        this.textureSpriteFrameMap = {}
        this.markReloadSpriteFrameMap = {}

        this.waitLoadSpriteFrameMap = {}
        this.textrueMap = {}
        this.ignoreSpriteFrameMap = {}

        this.init()
    }

    private getLoadId() {
        return ++this.__load_id
    }

    public printError(msg: any | string, ...subst: any[]) {
        if (!openLog) {
            return
        }
        if (this.debug && this.error) {
            twlog.error(msg, ...subst)
        }
    }

    public printInfo(msg: any | string, ...subst: any[]) {
        if (!openLog) {
            return
        }
        if (this.debug) {
            twlog.info(msg, ...subst)
        }
    }

    // 解析参数
    private makeLoadResArgs(params: any[]): any {
        const len = params.length
        if (len < 1 || typeof (params[0]) !== 'string') {
            this.printError('makeLoadResArgs error', params)
            return { url: '' }
        }
        const args: any = { url: params[0] }
        for (let i = 1; i < len; i++) {
            const param = params[i]
            if (i === 1 && cc.js.isChildClassOf(param, cc.Asset)) {
                args.type = param
            } else if (typeof (param) === 'function') {
                args.onProgess = param
            } else if (param instanceof cc.AssetManager.Bundle) {
                args.bundle = param
            }
        }
        return args
    }

    // public async loadRes(url: string): Promise<cc.Asset>;
    // public async loadRes(url: string, onProgess: Function): Promise<cc.Asset>;
    // public async loadRes(url: string, type: typeof cc.Asset): Promise<cc.Asset>;
    // public async loadRes(url: string, type: typeof cc.Asset, onProgess: Function): Promise<cc.Asset>;
    public async loadRes(...params: any): Promise<any> {
        const id = this.getLoadId()
        openLog && this.debug && this.loadTimeMap.set(id, Date.now())
        const asset = await this.load(...params)
        return await this.loadResComplete(id, params[0], asset)
    }
    public async load(...params: any) {
        const { url, type, onProgess } = this.makeLoadResArgs(params)
        return new Promise<any>(resolve => {
            let bundle = this.getBundleByUrl(url)
            let res = null
            if (bundle) {
                res = bundle.get(url, type)
                if (res && res.refCount > 0) {
                    return resolve(res)
                }
            }
            if (res && res.refCount <= 0) {
                gameHelper.setTimeout(() => {
                    this.__load(url, type, onProgess, resolve)
                }, 0)
            } else {
                this.__load(url, type, onProgess, resolve)
            }
        })
    }
    private __load(url: string, type: typeof cc.Asset, onProgess: any, onComplete: Function) {
        if (this.__temp_doesn_res[url]) {
            return onComplete(null)
        }
        let bundle = this.getBundleByUrl(url)
        if (!bundle) {
            this.startLoadBundle(this.getBundleNameByUrl(url), false)
            // twlog.info(url, this.getBundleNameByUrl(url))
            gameHelper.setTimeout(() => {
                this.__load(url, type, onProgess, onComplete)
            }, 200)
            return
        }
        if (this.__load_urls[url]) {
            this.__load_urls[url]++
        } else {
            this.__load_urls[url] = 1
        }
        // console.log(bundle.name, url)
        bundle.load(url, type, onProgess, (err, asset) => {
            if (!err) {
                if (this.delayTime > 0) {
                    gameHelper.setTimeout(()=>{
                        this.__load_urls[url]--
                        onComplete(asset)
                    }, Math.round(this.delayTime * (1 + ut.randomRange(-0.1, 0.1))))
                }
                else {
                    this.__load_urls[url]--
                    onComplete(asset)
                }
            } else if (err.message.includes(`doesn't contain`)) {
                this.printError('loadRes error -> ' + url, err.message)
                this.__load_urls[url]--
                this.__temp_doesn_res[url] = true
                onComplete(null)
            } else if (this.__load_urls[url] > 0) {
                this.printInfo('loadRes error! try reload. ' + url, err.message)
                gameHelper.setTimeout(() => {
                    if (this.__load_urls[url] > 0) {
                        this.__load(url, type, onProgess, onComplete) //尝试重新加载
                    } else {
                        onComplete(null)
                    }
                }, 100)
            } else {
                onComplete(null)
            }
        })
    }

    // 放弃加载
    public giveupLoad(url: string) {
        this.__load_urls[url] = 0
    }

    // public async loadResDir(url: string): Promise<cc.Asset[]>;
    // public async loadResDir(url: string, onProgess: Function): Promise<cc.Asset[]>;
    // public async loadResDir(url: string, type: typeof cc.Asset): Promise<cc.Asset[]>;
    // public async loadResDir(url: string, type: typeof cc.Asset, onProgess: Function): Promise<cc.Asset[]>;
    public async loadResDir(...params: any): Promise<any[]> {
        const id = this.getLoadId()
        this.debug && this.loadTimeMap.set(id, Date.now())
        const assets = await this.loadDir(...params)
        return await Promise.all(assets.map(m => {
            return this.loadResComplete(id, params[0] + '/' + m.name, m)
        }))
    }
    public loadDir(...params: any) {
        const { url, type, onProgess, bundle } = this.makeLoadResArgs(params)
        return new Promise<any[]>(resolve => this.__loadDir(url, type, onProgess, resolve, bundle))
    }
    private __loadDir(url: string, type: typeof cc.Asset, onProgess: any, onComplete: Function, bundle: cc.AssetManager.Bundle = null) {
        if (!bundle) {
            bundle = cc.resources
        }
        if (!bundle || !bundle['loadDir']) {
            return onComplete([])
        }
        // console.log(url, type)
        bundle.loadDir(url, type, onProgess, (err, assets) => {
            if (err) {
                if (err.message.includes(`doesn't contain`)) {
                    this.printError('loadRes error -> ' + url, err.message)
                    onComplete([])
                } else {
                    this.printInfo('loadRes error! try reload. ' + url, err.message)
                    gameHelper.setTimeout(() => {
                        this.__loadDir(url, type, onProgess, onComplete) //尝试重新加载
                    }, 100)
                }
            } else {
                onComplete(assets)
            }
        })
    }

    // 加载JPG
    public async loadRemote(url: string, ext: string) {
        return new Promise<any>(resolve => {
            let res = this.remoteAssetMap.get(url)
            if (res) {
                return resolve(res)
            }
            this.__loadRemote(url, ext, resolve)
        })
    }
    private __loadRemote(url: string, ext: string, onComplete: Function) {
        cc.assetManager.loadRemote(url, { ext: ext }, (err, asset) => {
            if (err) {
                if (!err.message.includes(`download failed`)) {
                    this.printError('loadRes error -> ' + url, err.message)
                    onComplete(null)
                } else {
                    this.printInfo('loadRes error! try reload. ' + url, err.message)
                    gameHelper.setTimeout(() => {
                        this.__loadRemote(url, ext, onComplete) //尝试重新加载
                    }, 100)
                }
            } else {
                this.remoteAssetMap.set(url, asset)
                onComplete(asset)
            }
        })
    }

    private __get_sprites(node: cc.Node) {
        const spr = node.getComponent(cc.Sprite)
        if (spr) {
            const spriteFrame = spr.spriteFrame
            if (spriteFrame) {
                //@ts-ignore
                let uid = spriteFrame._uuid
                let url = this.textureUrlMap[uid]
                if (url) {
                    //@ts-ignore
                    let sid = spr.uuid
                    if (!this.spriteInfoMap[uid]) {
                        this.spriteInfoMap[uid] = {}
                    }
                    this.spriteInfoMap[uid][sid] = spr

                    if (!this.textureSpriteFrameMap[url]) {
                        this.textureSpriteFrameMap[url] = {}
                    }
                    this.textureSpriteFrameMap[url][uid] = spriteFrame
                }
            }
        }

        //粒子系统
        const pSys = node.getComponent(cc.ParticleSystem)
        if (pSys) {
            const spriteFrame = pSys.spriteFrame
            if (spriteFrame) {
                //@ts-ignore
                let uid = spriteFrame._uuid
                let url = this.textureUrlMap[uid]
                if (url) {
                    //@ts-ignore
                    let sid = pSys.uuid
                    if (!this.partSysInfoMap[uid]) {
                        this.partSysInfoMap[uid] = {}
                    }
                    this.partSysInfoMap[uid][sid] = pSys

                    if (!this.textureSpriteFrameMap[url]) {
                        this.textureSpriteFrameMap[url] = {}
                    }
                    this.textureSpriteFrameMap[url][uid] = spriteFrame
                }

                this.ignoreSpriteFrameMap[uid] = true
            }
        }

        const children = node.children
        for (let i = 0, l = children.length; i < l; i++) {
            this.__get_sprites(children[i])
        }
    }

    private async _addSpriteFrameRefDep(uid, resAry, exclude) {
        exclude[uid] = true
        let res = cc.assetManager.assets.get(uid)
        if (res instanceof cc.SpriteFrame) {
            if (this.spriteFrameRefMap[uid]) {
                this.spriteFrameRefMap[uid]++
                // console.log("@@引用计数++", res.name, this.spriteFrameRefMap[uid])
            }
            else {
                this.spriteFrameRefMap[uid] = 1

                //第一次被引用计数，检查图片是否被释放
                let url = this.textureUrlMap[uid]
                let spriteFrame = res as cc.SpriteFrame
                let needLoad = false

                if (!url) {
                    let tex = spriteFrame.getTexture()
                    if (tex && tex.isValid) {
                        url = this.textureUrlMap[uid] = tex.nativeUrl
                    }
                }

                let setTexture = (tex: cc.Texture2D) => {
                    tex.addRef()
                    spriteFrame.setTexture(tex, spriteFrame.getRect(), spriteFrame.isRotated(), spriteFrame.getOffset(), spriteFrame.getOriginalSize())

                    if (this.spriteInfoMap[uid]) {
                        for (const key in this.spriteInfoMap[uid]) {
                            const sprite = this.spriteInfoMap[uid][key] as cc.Sprite
                            sprite.spriteFrame = null
                            sprite.spriteFrame = spriteFrame
                        }
                    }
                    //粒子系统必须重新创建精灵，否则未激活到激活状态可能会报错?
                    if (this.partSysInfoMap[uid]) {
                        for (const key in this.partSysInfoMap[uid]) {
                            const pSys = this.partSysInfoMap[uid][key] as cc.Sprite
                            pSys.spriteFrame = null
                            pSys.spriteFrame = spriteFrame
                        }
                    }
                }

                // console.log("@@引用计数=1", res.name)

                if (this.textrueMap[url]) {
                    let tex = spriteFrame.getTexture()

                    if ((!tex || !tex.isValid)) {
                        if (this.textrueMap[url].isValid) {
                            this.markReloadSpriteFrameMap[uid] = true
                            setTexture(this.textrueMap[url])

                            // console.log("##@@资源设置", spriteFrame.name, url)
                        }
                        else {
                            needLoad = true
                        }
                    }
                }
                else {
                    let tex = spriteFrame.getTexture()
                    if (tex && tex.isValid) {
                        this.textrueMap[url] = tex
                    }
                }

                if (needLoad && !this.waitLoadSpriteFrameMap[uid]) {
                    this.markReloadSpriteFrameMap[uid] = true

                    this.waitLoadSpriteFrameMap[uid] = true
                    // console.log("##@@资源加载", spriteFrame.name, url)

                    await new Promise<void>(resolve => {
                        let doLoad = () => {
                            this.loadTexture2D(url, (tex: cc.Texture2D) => {
                                if (tex) {
                                    this.textrueMap[url] = tex
                                    delete this.waitLoadSpriteFrameMap[uid]

                                    setTexture(tex)

                                    resolve()
                                }
                                else {
                                    gameHelper.setTimeout(() => {
                                        doLoad()
                                    }, 100)
                                }
                            })
                        }

                        doLoad()
                    })
                }
            }
        }
        else if (res instanceof cc.Prefab) {
            resAry.push(res)
        }

        let deps = cc.assetManager.dependUtil.getDeps(uid)

        await Promise.all(deps.map(cuid => {
            if (exclude[cuid]) {
                return
            }
            return this._addSpriteFrameRefDep(cuid, resAry, exclude)
        }))
    }

    private async addSpriteFrameRefDep(asset: cc.Asset) {
        //@ts-ignore
        let uid = asset._uuid
        let tempPrefabs = []
        await this._addSpriteFrameRefDep(uid, tempPrefabs, {})

        tempPrefabs.forEach(prefab => {
            this.__get_sprites(prefab.data)
        });

        //clear
        tempPrefabs = null
    }

    private __del_sprites(node: cc.Node) {
        const spr = node.getComponent(cc.Sprite)
        if (spr) {
            const spriteFrame = spr.spriteFrame
            if (spriteFrame) {
                //@ts-ignore
                let uid = spriteFrame._uuid
                let url = this.textureUrlMap[uid]
                if (url) {
                    //@ts-ignore
                    let sid = spr.uuid
                    if (this.spriteInfoMap[uid]) {
                        delete this.spriteInfoMap[uid][sid]
                    }
                    else {
                        twlog.upLog.setFilterMsg("resLoader")
                        twlog.error('missing uid', uid, sid)
                    }
                }
            }
        }

        //粒子系统
        const pSys = node.getComponent(cc.ParticleSystem)
        if (pSys) {
            const spriteFrame = pSys.spriteFrame
            if (spriteFrame) {
                //@ts-ignore
                let uid = spriteFrame._uuid
                let url = this.textureUrlMap[uid]
                if (url) {
                    //@ts-ignore
                    let sid = pSys.uuid
                    if (this.partSysInfoMap[uid]) {
                        delete this.partSysInfoMap[uid][sid]
                    }
                    else {
                        twlog.upLog.setFilterMsg("resLoader")
                        twlog.error('missing uid', uid, sid)
                    }
                }
            }
        }

        const children = node.children
        for (let i = 0, l = children.length; i < l; i++) {
            this.__del_sprites(children[i])
        }
    }

    private _decSpriteFrameRefDep(uid, resAry, exclude) {
        exclude[uid] = true
        let res = cc.assetManager.assets.get(uid)
        if (res instanceof cc.SpriteFrame) {
            //没有被忽略的才减少计数
            if (!this.ignoreSpriteFrameMap[uid]) {
                if (this.spriteFrameRefMap[uid] > 0) {
                    this.spriteFrameRefMap[uid]--
                    // console.log("@@dec 引用计数--", res.name, this.spriteFrameRefMap[uid])
                    if (this.spriteFrameRefMap[uid] == 0) {
                        //如果是重新加载的资源，那么需要重新释放
                        if (this.markReloadSpriteFrameMap[uid]) {
                            this.spriteFrameRefMap[uid]++
                            this.onReleaseCacheSpriteFrame(uid)
                        }
                        else {
                            let url = this.textureUrlMap[uid]
                            if (this.textureSpriteFrameMap[url]) {
                                delete this.textureSpriteFrameMap[url][uid]
                            }
                        }
                    }
                }
                else {
                    console.error("ref error", uid, res)
                }
            }
        }
        else if (res instanceof cc.Prefab) {
            resAry.push(res)
        }

        let deps = cc.assetManager.dependUtil.getDeps(uid)
        deps.forEach(cuid => {
            if (exclude[cuid]) {
                return
            }
            this._decSpriteFrameRefDep(cuid, resAry, exclude)
        });
    }

    private decSpriteFrameRefDep(asset: cc.Asset) {
        //@ts-ignore
        let uid = asset._uuid
        let tempPrefabs = []
        this._decSpriteFrameRefDep(uid, tempPrefabs, {})

        tempPrefabs.forEach(prefab => {
            this.__del_sprites(prefab.data)
        })

        tempPrefabs = null
    }

    // 加载资源完成
    public async loadResComplete(id: number, url: string, asset: cc.Asset) {
        if (asset) {
            // 添加引用
            asset.addRef()
            //
            await this.addSpriteFrameRefDep(asset)
            // 打印
            if (openLog && this.debug && id) {
                const now = Date.now()
                const time = now - (this.loadTimeMap.get(id) || now)
                this.loadTimeMap.delete(id)
                this.printInfo(`loadRes -> ${url} [${asset.refCount}] ${time}ms`)
            }
        }
        return asset
    }

    private decRefAsset(asset: cc.Asset) {
        // 减少引用并尝试自动释放
        // @ts-ignore
        asset.decRef(false)

        if (asset.refCount <= 0) {
            cc.assetManager.releaseAsset(asset)
        }
    }

    // 释放资源
    public releaseRes(url: string, type?: typeof cc.Asset) {
        let bundle = this.getBundleByUrl(url)
        let asset = bundle.get(url, type), isRemote = false
        if (!asset) {
            asset = this.remoteAssetMap.get(url)
            isRemote = true
        }
        if (!asset) {
            this.printError(`releaseRes asset is null ${url}`)
            return 0
        }

        this.decSpriteFrameRefDep(asset)
        asset.decRef()

        const refCount = asset.refCount
        // 如果是远程资源 还要删除缓存
        if (isRemote && refCount <= 0) {
            this.remoteAssetMap.delete(url)
        }
        // 打印
        // this.printInfo(`releaseRes -> ${url} [${refCount}]`)
        return refCount
    }

    // 释放所有引用
    public releaseAsset(url: string, type?: typeof cc.Asset) {
        let bundle = this.getBundleByUrl(url)
        let asset = bundle.get(url, type), isRemote = false;
        if (!asset) {
            asset = this.remoteAssetMap.get(url)
            isRemote = true
        }
        if (!asset) {
            this.printError(`releaseAsset asset is null ${url}`)
            return
        }
        // 直接强行释放
        cc.assetManager.releaseAsset(asset)
        // 如果是远程资源 还要删除缓存
        if (isRemote) {
            this.remoteAssetMap.delete(url)
        }
        // 打印
        // this.printInfo(`releaseAsset -> ${url} [0]`)
    }

    public getAssetInfo(uuid: string) : Record<string, any> {
        let bundle = this.getBundleByUrl(uuid)
        return bundle.getAssetInfo(uuid)
    }

    public async preload(path: string, type: typeof cc.Asset, onProgess: Function = null, onComplete: Function = null) {
        let doPreload = (callback) => {
            let bundle = this.getBundleByUrl(path)
            bundle.preload(path, type, (finish, total) => {
                onProgess && onProgess(finish / total)
            }, (err) => {
                if (err) {
                    if (err.message.includes(`doesn't contain`)) {
                        this.printError('preloadRes error -> ' + path, err.message)
                        return
                    }
                    twlog.error(path, err)
                    gameHelper.setTimeout(() => {
                        doPreload(callback)
                    }, 100)
                }
                else {
                    callback()
                }
            })
        }

        return new Promise<void>((resolve) => {
            onComplete && onComplete()
            doPreload(resolve)
        })
    }

    public async preloadDir(path: string, bundle: cc.AssetManager.Bundle = null) {
        if (!bundle) {
            bundle = cc.resources
        }
        let doPreload = (callback) => {
            bundle.preloadDir(path, (err) => {
                if (err) {
                    twlog.error(err)
                    gameHelper.setTimeout(() => {
                        doPreload(callback)
                    }, 100)
                }
                else {
                    callback()
                }
            })
        }
        return new Promise<void>((resolve) => {
            doPreload(resolve)
        })
    }

    public async loadBundle(name: string): Promise<cc.AssetManager.Bundle> {
        let doPreload = (callback) => {
            cc.assetManager.loadBundle(name, (err, bd: cc.AssetManager.Bundle) => {
                if (err) {
                    twlog.error(err)
                    gameHelper.setTimeout(() => {
                        doPreload(callback)
                    }, 100)
                }
                else {
                    callback(bd)
                }
            })
        }

        return new Promise((resolve) => {
            doPreload(resolve)
        })
    }

    private __loadTexture2D(url: string) {
        cc.assetManager.loadRemote(url, { type: cc.Texture2D }, (err, res: cc.Texture2D) => {
            if (!err) {
                this.loadTextureMap[url] = res
            } else if (err.message.includes(`doesn't contain`)) {
                this.printError('loadRes error -> ' + url, err.message)
            } else {
                this.printInfo('loadRes error! try reload. ' + url, err.message)
                gameHelper.setTimeout(() => {
                    return this.__loadTexture2D(url) //尝试重新加载
                }, 100)
            }
            this.waitTextureCallbackMap[url]?.forEach(cb => cb(res))
            delete this.waitTextureCallbackMap[url]
        })
    }

    private loadTexture2D(url: string, onComplete: (tex: cc.Texture2D) => void) {
        if (this.loadTextureMap[url]) {
            return onComplete(this.loadTextureMap[url])
        }
        if (this.waitTextureCallbackMap[url]) {
            return this.waitTextureCallbackMap[url].push(onComplete)
        }
        this.waitTextureCallbackMap[url] = [onComplete]

        this.__loadTexture2D(url)
    }

    private onReleaseCacheSpriteFrame(uid) {
        let res = cc.assetManager.assets.get(uid)
        if (res instanceof cc.SpriteFrame) {
            //没有被忽略的图才减少计数
            if (!this.ignoreSpriteFrameMap[uid]) {
                if (this.spriteFrameRefMap[uid] > 0) {
                    this.spriteFrameRefMap[uid]--

                    // console.log("@@@@计数--", res.name, this.spriteFrameRefMap[uid], uid)

                    if (this.spriteFrameRefMap[uid] == 0) {
                        let spriteFrame = res as cc.SpriteFrame
                        let tex = spriteFrame.getTexture()

                        if (tex && tex.isValid) {
                            let url = tex.nativeUrl

                            delete this.markReloadSpriteFrameMap[uid]

                            this.decRefAsset(tex)
                            if (tex.refCount <= 0) {
                                if (this.loadTextureMap[url]) {
                                    delete this.loadTextureMap[url]
                                }

                                // console.log("@@@@资源释放", spriteFrame.name, url)
                                if (this.textureSpriteFrameMap[url]) {
                                    for (const key in this.textureSpriteFrameMap[url]) {
                                        const spriteFrame = this.textureSpriteFrameMap[url][key] as cc.SpriteFrame
                                        spriteFrame.setTexture(null, spriteFrame.getRect(), spriteFrame.isRotated(), spriteFrame.getOffset(), spriteFrame.getOriginalSize())
                                    }

                                }
                            }
                        }
                    }
                }
                else {
                    console.error("ref error", uid, res)
                }
            }
        }
    }

    private _releaseCacheSpriteFrame(uid, exclude) {
        exclude[uid] = true
        this.onReleaseCacheSpriteFrame(uid)

        let deps = cc.assetManager.dependUtil.getDeps(uid)
        deps.forEach(cuid => {
            if (exclude[cuid]) {
                return
            }
            this._releaseCacheSpriteFrame(cuid, exclude)
        });
    }

    public releaseCacheSpriteFrame(prefab: cc.Prefab) {
        if (!prefab.isValid) {
            return
        }

        // @ts-ignore
        let uid = prefab._uuid
        this._releaseCacheSpriteFrame(uid, {})
    }

    public async retainSpriteFrame(prefab: cc.Prefab) {
        if (!prefab.isValid) {
            return
        }

        // @ts-ignore
        let uid = prefab._uuid
        await this._addSpriteFrameRefDep(uid, [], {})
    }

    private _resotreSpriteFrame(uid, exclude) {
        exclude[uid] = true
        let res = cc.assetManager.assets.get(uid)
        if (res instanceof cc.SpriteFrame) {
            if (this.spriteFrameRefMap[uid]) {
                this.spriteFrameRefMap[uid]++
                // console.log("@@引用计数++ resotre", res.name, this.spriteFrameRefMap[uid])
            }
            else {
                this.spriteFrameRefMap[uid] = 1
                let url = this.textureUrlMap[uid]
                if (url) {
                    let spriteFrame = res as cc.SpriteFrame
                    let tex = spriteFrame.getTexture()
                    if (tex && tex.isValid) {//如果纹理还在，说明纹理没被释放，需要归还计数
                        tex.addRef()
                    }
                }
            }
        }

        let deps = cc.assetManager.dependUtil.getDeps(uid)

        deps.forEach(cuid => {
            if (exclude[cuid]) {
                return
            }
            return this._resotreSpriteFrame(cuid, exclude)
        })
    }

    public resotreSpriteFrame(prefab: cc.Prefab) {
        if (!prefab.isValid) {
            return
        }

        // @ts-ignore
        let uid = prefab._uuid
        this._resotreSpriteFrame(uid, {})
    }

    public async loadBundleByName(name): Promise<cc.AssetManager.Bundle> {
        while (true) {
            let load = new Promise<cc.AssetManager.Bundle>((resolve) => {
                cc.assetManager.loadBundle(name, (err, bundle) => {
                    if (err) {
                        console.error("loadBundleError: ", name, err)
                        resolve(null)
                    } else {
                        resolve(bundle)
                    }
                })
            })
            let bundle = await load
            if (bundle) return bundle;
            await new Promise(resolve => setTimeout(resolve, 100))
        }
    }

    private async loadSubpackage(sub_name) {
        if (!ut.isMiniGame()) {
            return
        }

        if (wx.loadSubpackage) {
            let self = this
            let onLoadSubpackage = (callback) => {
                const loadTask = wx.loadSubpackage({
                    name: sub_name,
                    success: function (_res) {
                        callback()
                    },
                    fail: function (_res) {
                        gameHelper.setTimeout(function () {
                            onLoadSubpackage(callback)
                        }, 200)
                    }
                })

                loadTask.onProgressUpdate(res => {
                    let lastPercent = self._bdLoadMap[sub_name]
                    let percent = Math.min(res.totalBytesWritten / res.totalBytesExpectedToWrite, 0.99)
                    if (percent > lastPercent) {
                        self._bdLoadMap[sub_name] = percent
                    }

                    // console.log('下载进度', sub_name, res.progress)
                    // console.log('已经下载的数据长度', sub_name, res.totalBytesWritten)
                    // console.log('预期需要下载的数据总长度', sub_name, res.totalBytesExpectedToWrite)
                })
            }

            return new Promise<void>((resolve) => {
                onLoadSubpackage(resolve)
            })
        }
        else {
            // @ts-ignore
            require(sub_name + '/game.js');
        }
    }

    //获取子包加载进度 0 ~ 1
    public getBundleProgress(bdType: Bundle | string): number {
        return this._bdLoadMap[bdType] || 0
    }

    public async startLoadBundle(bdType: Bundle | string, isAwait: boolean = true) {
        let loadPer = this._bdLoadMap[bdType]
        if (loadPer) {
            if (!isAwait) {
                return
            }
            while (this._bdLoadMap[bdType] < 1) {
                await ut.waitTimeout(200)
            }
            return
        }
        this._bdLoadMap[bdType] = 0.01

        if (bdType === Bundle.RES) {
            await this.startLoadResBundle()
        }
        else {
            await this.loadSubpackage(bdType)

            let bundle = await loader.loadBundleByName(bdType)
            loader.addBundle(bdType, bundle)
        }

        this._bdLoadMap[bdType] = 1
    }

    public isLoadResBundle() {
        return this._isLoadResBundle
    }

    private async startLoadResBundle() {
        if (!this._bdLoadMap[Bundle.START]) {
            this.startLoadBundle(Bundle.START)
        }

        while (this._bdLoadMap[Bundle.START] < 1) {
            await ut.waitTimeout(200)
        }

        await loader.loadBundleByName(cc.AssetManager.BuiltinBundleName.RESOURCES)
        this._isLoadResBundle = true
    }

    public addBundle(name: Bundle | string, bundle: cc.AssetManager.Bundle) {
        this.bundleMap[name] = bundle
    }

    public getBundle(name: string) {
        return this.bundleMap[name]
    }

    public getBundleByUrl(url: string): cc.AssetManager.Bundle {
        let bundleName = this.urlBundleMap.get(url)
        if (bundleName) {
            return this.getBundle(bundleName)
        }
        return cc.resources
    }

    public getBundleNameByUrl(url: string): string {
        let bundleName = this.urlBundleMap.get(url)
        if (bundleName) {
            return bundleName
        }
        return Bundle.RES
    }

    public getDirWithPath(path: string, type?: typeof cc.Asset) {
        return cc.resources.getDirWithPath(path, type)
    }
}

export const loader = new ResLoader()
loader.init()