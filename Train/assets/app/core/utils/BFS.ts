// 定义格子状态的枚举
enum GridState {
    NONE = 0,   // 空
    BLOCK = 1   // 阻挡
}

// 定义坐标点接口
interface Point {
    x: number;
    y: number;
}

// BFS工具类
export class BFS {
    // 四个移动方向
    private static readonly directions: Point[] = [
        { x: -1, y: 0 },
        { x: 1, y: 0 },
        { x: 0, y: -1 },
        { x: 0, y: 1 }
    ];

    /**
     * 广度优先搜索 给定一个二维数组，起点和终点，找到一条从起点到终点的最短路径
     * @param grid 二维网格
     * @param start 起点
     * @param target 终点
     * @returns Point数组表示的路径，如果不可达则返回null
     */
    public static find(grid: number[][], start: Point, target: Point): Point[] | null {
        const queue: Point[] = [start]
        const visited: boolean[][] = Array(grid.length).fill(0).map(() => Array(grid[0].length).fill(false));
        const prev: Point[][] = Array(grid.length).fill(0).map(() => Array(grid[0].length).fill(null));

        visited[start.y][start.x] = true

        while (queue.length > 0) {
            let point = queue.shift()

            if (point.x == target.x && point.y == target.y) {
                let path: Point[] = []
                while (point != start) {
                    path.unshift(point)
                    point = prev[point.y][point.x]
                }
                path.unshift(start)
                return path
            }
            for (let i = 0; i < 4; i++) {
                let newX = point.x + this.directions[i].x
                let newY = point.y + this.directions[i].y

                if (newX >= 0 && newX < grid[0].length && newY >= 0 && newY < grid.length && grid[newY][newX] == GridState.NONE && !visited[newY][newX]) {
                    visited[newY][newX] = true
                    prev[newY][newX] = point
                    queue.push({ x: newX, y: newY })
                }
            }
        }
        return null

    }

    /**
     * 广度优先搜索 给定一个二维数组，起点和终点，判断终点是否可达
     * @param grid 二维网格
     * @param start 起点
     * @param target 终点
     * @returns 是否可达
     */
    public static isReachableWithStart(grid: number[][], start: Point, target: Point): boolean {
        return this.find(grid, start, target) !== null;
    }

    /**
     * 广度优先搜索 给定一个二维数组和终点，判断终点是否可达
     * @param grid 二维网格
     * @param target 终点
     * @returns 是否可达
     */
    public static isReachable(grid: number[][], target: Point): boolean {
        const lineLen = grid[0].length;
        const line = Array(lineLen).fill(0);
        const tmp = [line, ...grid];

        const adjustedTarget = {
            x: target.x,
            y: target.y + 1
        };

        for (let x = 0; x < lineLen; x++) {
            if (grid[0][x] === GridState.NONE) {
                if (this.find(tmp, { x, y: 1 }, adjustedTarget) !== null) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 找到网格中可达的最深点
     * @param grid 二维网格
     * @returns [x, y] 最深点的坐标
     */
    public static findDeepestPoint(grid: number[][]): [number, number] {
        let maxY = -1;
        let deepestPoint: Point | null = null;

        for (let x = 0; x < grid[0].length; x++) {
            for (let y = 0; y < grid.length; y++) {
                const target: Point = { x, y };
                if (this.isReachable(grid, target) && y > maxY) {
                    maxY = y;
                    deepestPoint = target;
                }
            }
        }

        if (!deepestPoint) {
            throw new Error("No reachable point found in grid");
        }

        return [deepestPoint.x, deepestPoint.y];
    }
}


window["BFS"] = BFS