import CoreEventType from "../event/CoreEventType"

var wxlog = null
var filterMap = {}

class uplogHelper {
    info(...params: any) {
        if (!wxlog) return
        wxlog.info(wxlog, ...params)
        twlog.info(wxlog, ...params)
    }
    warn(...params: any) {
        if (!wxlog) return
        wxlog.warn(wxlog, ...params)
        twlog.info(wxlog, ...params)
    }
    error(...params: any) {
        if (wxlog) {
            wxlog.error(wxlog, ...params)
            twlog.error(wxlog, ...params)
        }
        else {
            //感觉没啥用，先不传了
            // if (params.length > 0) { 
            //     let msg = JSON.stringify(params)
            //     msg = msg.paramsring(0, 2048)
            //     mc.modelMgr.get<ErrorReportModel>('errorReport').reportError("", msg)
            // }
        }
    }
    setFilterMsg(msg) { // 从基础库2.7.3开始支持
        if (!wxlog || !wxlog.setFilterMsg) return
        if (typeof msg !== 'string') return
        wxlog.setFilterMsg(msg)
    }
    addFilterMsg(msg) { // 从基础库2.8.1开始支持
        if (!wxlog || !wxlog.addFilterMsg) return
        if (typeof msg !== 'string') return
        if (filterMap[msg]) return
        filterMap[msg] = true
        wxlog.addFilterMsg(msg)
    }
}

class Logger {

    private _open: boolean = true //是否打开

    private _upLog : uplogHelper = null

    public get upLog() : uplogHelper {
        if (!this._upLog) {
            if (typeof wx !== 'undefined') {
                wxlog = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null
            }
            this._upLog = new uplogHelper()
        }
        return this._upLog
    }

    public get open() {
        return this._open
    }

    public set open(val: boolean) {
        this._open = val
    }

    public debug(msg: any | string, ...subst: any[]) {
        if (CC_DEV) {
            this.info(msg, ...subst)
        }
    }

    public info(msg: any | string, ...subst: any[]) {
        if (!this._open) {
            return
        }
        const text = this.wrapLogger(msg, subst)
        if (!cc.sys.isMobile) {
            const log = console.log || cc.log
            log.call(this, text)
        } else {
            console.log(text)
        }
        eventCenter.emit(CoreEventType.MVC_LOGGER_PRINT, 'info', text)
    }

    public error(msg: any | string, ...subst: any[]) {
        if (!this._open) {
            return
        }
        const text = this.wrapLogger(msg, subst)
        if (!cc.sys.isMobile) {
            const error = console.error || cc.error
            error.call(this, text)
        } else {
            console.error(text)
        }
        eventCenter.emit(CoreEventType.MVC_LOGGER_PRINT, 'error', text)
    }

    private wrapLogger(msg: any | string, subst: any[]): string {
        return '[' + ut.dateFormat('h:mm:ss') + '] ' + this.formatter(msg) + ' ' + subst.join2(m => this.formatter(m), ' ')
    }

    private formatter(val: any): string {
        if (val === null) {
            return 'null'
        } else if (val === undefined) {
            return 'undefined'
        } else if (Array.isArray(val)) {
            return '[' + val.join2(m => this.formatter(m), ',') + ']'
        } else if (typeof (val) === 'object') {
            return this.warpObject(val)
        } else {
            return String(val)
        }
    }

    private warpObject(obj: any): string {
        try {
            if (obj.__classname__) {
                return `${obj.__classname__} { name: ${obj.name} }`
            }
            return JSON.stringify(obj)
        } catch (error) {
            return '无法解析'
        }
    }
}

window['twlog'] = new Logger()