/**
 * 全局事件驱动
 */
export default class EventCenter {

    private __events: Map<string, EventItem[]> = new Map<string, EventItem[]>()// 事件列表
    private __emit_types: string[] = []
    private __off_types: string[] = []

    /**
     * 派发事件
     * @param  type
     * @param  data
     */
    public emit(type: number | string, ...params: any): void {
        type = String(type)
        const list = this.__events.get(type)
        if (!list || list.length === 0) {
            return
        }
        this.__emit_types.push(type)
        for (let i = 0, l = list.length; i < l; i++) {
            const it = list[i]
            if (it) {
                it.callback.call(it.target, ...params)
            }
        }
        // 清理为null的
        if (this.__off_types.length > 0 && this.__off_types.remove(type)) {
            for (let i = list.length - 1; i >= 0; i--) {
                if (!list[i]) {
                    list.splice(i, 1)
                }
            }
            if (list.length === 0) {
                this.__events.delete(type)
            }
        }
        this.__emit_types.remove(type)
    }

    /**
     * 请求事件
     * @param type 
     * @param params 
     */
    public get(type: number | string, ...params: any) {
        const list = this.__events.get(String(type))
        if (!list || list.length === 0) {
            return null
        }
        for (const it of list) {
            let a = it.callback.call(it.target, ...params)
            if (a != null) return a
        }
    }

    /**
     * 异步 请求事件
     * @param type
     */
    public async req(type: number | string, ...params: any) {
        return this.get(type, ...params)
    }

    /**
     * 监听事件
     * @param  type
     * @param  callback
     * @param  target
     */
    public on(type: number | string, callback: Function, target?: any): void {
        type = String(type)
        let list = this.__events.get(type)
        if (!list) {
            list = []
            this.__events.set(type, list)
        }
        if (list.some(m => m && m.callback == callback && m.target == target)) {
            return twlog.error('repeat listen', type, (target ? target.name : 'null') + '.' + callback.name)
        }
        list.push({ callback: callback, target: target })
    }

    /**
     * 监听一次
     * @param type 
     * @param callback 
     * @param target 
     */
    public once(type: number | string, callback: Function, target?: any): void {
        type = String(type)
        let fired = false, self = this
        function g(...params: any) {
            self.off(type, g)
            if (!fired) {
                fired = true
                callback.call(target, ...params)
            }
        }
        this.on(type, g)
    }

    /**
     * 异步等待
     * @param type
     */
    public async wait(type, checkEnd?: (...params) => boolean) {
        type = String(type)
        return new Promise(r => {
            let fired = false, self = this;
            let g = (...params) => {
                if (!checkEnd || checkEnd(...params)) {
                    self.off(type, g)
                    if (!fired) {
                        fired = true
                        r(params)
                    }
                }
            }
            self.on(type, g)
        })
    }

    /**
     * 删除一个事件
     * @param  type
     * @param  callback
     * @param  target
     */
    public off(type: number | string, callback?: Function, target?: any): void {
        type = String(type)
        const list = this.__events.get(type)
        if (!list) {
            return
        }
        if (!callback) {
            this.__events.delete(type)
            return
        }
        for (let i = list.length - 1; i >= 0; i--) {
            const it = list[i]
            if (it && it.target == target && it.callback == callback) {
                if (this.__emit_types.has(type)) {
                    list[i] = null
                    this.__off_types.push(type)
                    // twlog.error('try at emit in off type=' + type)
                } else {
                    list.splice(i, 1)
                }
                break
            }
        }
        if (list.length === 0) {
            this.__events.delete(type)
        }
    }

    // 打印监听列表
    public print(): void {
        cc.log('events:', this.__events)
    }

    public clean() {
        this.__events.clear()
    }
}

window['eventCenter'] = new EventCenter()