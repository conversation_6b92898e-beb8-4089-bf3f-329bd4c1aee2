import BasePnlCtrl from "./base/BasePnlCtrl"
import BaseWindCtrl from "./base/BaseWindCtrl"
import BaseNoticeCtrl from "./base/BaseNoticeCtrl"
import BaseWdtCtrl from "./base/BaseWdtCtrl"
import BaseLogCtrl from "./base/BaseLogCtrl"
import BaseModel from "./base/BaseModel"
import WindCtrlMgr from "./manage/WindCtrlMgr"
import ViewCtrlMgr from "./manage/ViewCtrlMgr"
import ModelMgr from "./manage/ModelMgr"
import CoreEventType from "./event/CoreEventType"
import ViewLayerCtrl from "./layer/ViewLayerCtrl"
import WindLayerCtrl from "./layer/WindLayerCtrl"
import NoticeLayerCtrl from "./layer/NoticeLayerCtrl"
import NoticeCtrlMgr from "./manage/NoticeCtrlMgr"
import { languageMgr } from "./manage/LanguageMgr"
import BaseCmptCtrl from "./base/BaseCmptCtrl"

export default class CCMvc {

    public GameNameSpace: string = 'game'

    public readonly BasePnlCtrl = BasePnlCtrl
    public readonly BaseWindCtrl = BaseWindCtrl
    public readonly BaseNoticeCtrl = BaseNoticeCtrl
    public readonly BaseWdtCtrl = BaseWdtCtrl
    public readonly BaseCmptCtrl = BaseCmptCtrl
    public readonly BaseLogCtrl = BaseLogCtrl
    public readonly BaseModel = BaseModel

    public modelMgr = null
    public Event = null

    private __windLayerCtrl: WindLayerCtrl = null
    private __viewLayerCtrl: ViewLayerCtrl = null
    private __noticeLayerCtrl: NoticeLayerCtrl = null
    private __lockNode = null
    private __touchNode = null

    private __temp_models: any = {}
    private __priorityMap: any = {}

    private lockRefCnt: number = 0
    private _isPressed: boolean = false

    public static require() { }

    public init(name: string, root: cc.Node, lang?: string, changeLang?: boolean) {
        // window['__errorHandler'] = this.onErrorHandler.bind(this)
        this.GameNameSpace = name || 'game'
        languageMgr.init(lang, changeLang)
        // 清理所有事件
        eventCenter.clean()
        // 创建各个视图层
        this.__windLayerCtrl = this.createNode('Wind', 'default', root, 1).addComponent(WindLayerCtrl).__init(new WindCtrlMgr())
        // this.__windLayerCtrl.addComponent(cc.SafeArea);
        this.__viewLayerCtrl = this.createNode('View', 'ui', root, 2).addComponent(ViewLayerCtrl).__init(new ViewCtrlMgr())
        // this.__viewLayerCtrl.addComponent(cc.SafeArea);
        this.__noticeLayerCtrl = this.createNode('Notice', 'ui', root, 3).addComponent(NoticeLayerCtrl).__init(new NoticeCtrlMgr())
        // 创建锁定触摸节点
        this.__lockNode = this.createNode('LockNode', 'ui', root, 4)
        this.__lockNode.addComponent(cc.BlockInputEvents)
        this.__lockNode.active = false

        this.__touchNode = this.createNode('TouchNode', 'ui', root, 5)
        this.initTouchNode()
        
        // 事件
        this.Event = CoreEventType
        // 初始化模型
        if (this.__temp_models) {
            this.modelMgr = new ModelMgr()
            this.modelMgr.init(this.__temp_models, this.__priorityMap)
            this.modelMgr.create()
            this.__temp_models = undefined
        } else if (this.modelMgr) {
            this.modelMgr.create()
        }
        // twlog.info('mvc core init!')
        // const win = cc.winSize, view = cc.view.getFrameSize(), safe = cc.sys.getSafeAreaRect()
        // twlog.info('WinSize=' + win.width + ',' + win.height, 'ViewSize=' + view.width + ',' + view.height, 'SafeSzie=' + safe.width + ',' + safe.height)
    }

    private createNode(name: string, group: string, root: cc.Node, zIndex: number) {
        let node = root.FindChild(name)
        if (node) {
            node.destroy()
        }
        node = new cc.Node(name)
        node.parent = root
        node.zIndex = zIndex
        node.width = cc.winSize.width
        node.height = cc.winSize.height
        node.group = group
        return node
    }

    // 错误
    private onErrorHandler(filename: any, line: any, msg: any) {
        twlog.error(filename, line, msg)
        eventCenter.emit(CoreEventType.MVC_ERROR_MSG, { name: filename, error: msg, stack: line })
    }

    private initTouchNode() {
        this.__touchNode.on(cc.Node.EventType.TOUCH_START, ()=>{
            this._isPressed = true
        }, this)
        this.__touchNode.on(cc.Node.EventType.TOUCH_END, ()=>{
            this._isPressed = false
        }, this)
        this.__touchNode.SetSwallowTouches(false)
    }

    public getWindNode() { return this.__windLayerCtrl.node }
    public getViewNode() { return this.__viewLayerCtrl.node }
    public getNoticeNode() { return this.__noticeLayerCtrl.node }

    public getViewMgr() { return this.__viewLayerCtrl.getCtrlMgr() }

    // 设置获取当前wind
    public get currWind(): BaseWindCtrl {
        return this.__windLayerCtrl?.getCurrWind()
    }

    // 获取当前wind名字
    public get currWindName(): string {
        let wind = this.__windLayerCtrl.getCurrWind()
        return wind ? wind.key : 'null'
    }

    public get preWindName(): string {
        return this.__windLayerCtrl.getPreWindName()
    }

    public get isGoingtoNextWind(): boolean {
        return this.__windLayerCtrl?.getNextWindName() != null
    }

    // 锁定触摸
    public lockTouch(val: boolean) {
        if (val == true) this.lockRefCnt++
        else if (this.lockRefCnt > 0) {
            this.lockRefCnt--
        }
        this.__lockNode.active = this.lockRefCnt > 0
    }

    // 添加模型装饰器
    public addmodel(type: string, priority: number = 0) {
        const self = this
        return function (target: Function) {
            self.__temp_models[type] = target
            self.__priorityMap[type] = priority
        }
    }

    // 切换语言
    public set lang(val: string) {
        languageMgr.lang = val
    }
    // 返回当前语言
    public get lang() {
        return languageMgr.lang
    }

    // 获取当前打开的列表
    public getOpenPnls() {
        return this.__viewLayerCtrl.getOpenPnls()
    }

    // 从缓存中取得一个pnl
    public getPnl<T extends BasePnlCtrl>(name: string): T {
        return this.__viewLayerCtrl.getPnl(name)
    }

    // 加载队列中是否有某个pnl
    public isPnlInQueue(name: string) {
        return this.__viewLayerCtrl.hasLoadQueue(name)
    }

    public isPressed() {
        return this._isPressed
    }
}

// @ts-ignore
window['mc'] = new CCMvc()