import CoreEventType from "../event/CoreEventType"

// 日志
export default class BaseLogCtrl extends cc.Component {

    onLoad() {
        this.node.zIndex = 100
        this.node.group = 'ui'
        eventCenter.on(CoreEventType.MVC_LOGGER_PRINT, this.onLoggerListener, this)
        this.onCreate()
    }

    onDestroy() {
        eventCenter.off(CoreEventType.MVC_LOGGER_PRINT, this.onLoggerListener, this)
        this.onClean()
    }

    public close() {
        this.node.destroy()
    }

    public onCreate() {

    }

    public onClean() {

    }

    public onLoggerListener(type: string, content: string) {
    }
}