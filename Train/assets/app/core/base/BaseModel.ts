
// 基础数据模型
export default class BaseModel {

    public type: string = ''

    constructor(type: string) {
        this.type = type
    }

    public __create() {
        // twlog.info(this.constructor['name'] + '(' + this.type + ')' + ' create!')
        this.onCreate()
    }

    public init() {
    }

    public __clean() {
        // twlog.info(this.constructor['name'] + '(' + this.type + ')' + ' clean!')
        this.onClean()
    }

    public onCreate() {
    }

    public onEnable() {
        
    }

    public onClean() {
    }

    public update(dt) {
    }

    public emit(type: string | number, ...params: any) {
        eventCenter.emit(type, ...params)
    }

    public getModel<T>(key: string): T {
        return mc.modelMgr.get<T>(key)
    }
}