const { ccclass, menu, requireComponent } = cc._decorator;

@ccclass
@menu('多语言组件/RichLetterLabel')
@requireComponent(cc.RichText)
export default class RichLetterLabel extends cc.Component {
    protected _label: cc.RichText = null

    protected get label() {
        if (!this._label) {
            this._label = this.getComponent(cc.RichText)
        }
        return this._label
    }

    public onLoad() {
        const font = assetsMgr.getFont("jcyt_600W")
        if (font) {
            this.label.font = font
        }
    }
}