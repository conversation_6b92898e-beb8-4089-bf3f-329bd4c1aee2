import BaseViewCtrl from "./BaseViewCtrl"

/**
 * 基础窗口
 */
export default class BaseWindCtrl extends BaseViewCtrl {

    public key: string = ''// 模块名
    public isClean: boolean = true

    public async __create(...params: any) {
        this._state = 'create'
        this.__listens = []
        this.__listenMaps()
        this.__register('create')
        await this.onCreate(...params)
    }

    public __enter(...params: any) {
        if (this._state !== 'enter') {
            this._state = 'enter'
            this.__register('enter')
        }
        this.onEnter(...params)
    }

    public __leave() {
        this._state = 'leave'
        this.__unregister('enter')
        this.onLeave()
    }

    public __clean() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
    }

    public async onCreate(...params) {
    }

    public onEnter(...params: any) {
    }

    public onLeave() {
    }

    public onClean() {
        assetsMgr.releaseTempResByTag(this.key)
    }

    public setParam(opts: WindParam) {
        this.isClean = opts.isClean === undefined ? this.isClean : opts.isClean
    }
}