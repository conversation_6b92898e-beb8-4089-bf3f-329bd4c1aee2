import BaseViewCtrl from "./BaseViewCtrl";

// 基础挂件控制器
export default class BaseWdtCtrl extends BaseViewCtrl {

    onLoad() {
        this.loadProperty()
        this._state = 'create'
        this.__listenMaps()
        this.__register()
        this.onCreate()
    }

    public addListener(type: string, cb: Function, target?: any) {
        this.__listens.push({ type: type, cb: cb, target: target })
        eventCenter.on(type, cb, target)
    }

    onDestroy() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
        super.onDestroy()
    }

    public onCreate() {
    }

    public onClean() {
    }
}