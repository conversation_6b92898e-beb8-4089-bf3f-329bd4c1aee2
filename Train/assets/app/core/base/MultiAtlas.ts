const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu('自定义组件/MultiAtlas')
export default class MultiAtlas extends cc.Component {

    @property([cc.SpriteAtlas])
    private atlas: cc.SpriteAtlas[] = []

    public setAtlas(idx: number | boolean) {
        let i = typeof (idx) === 'number' ? idx : (idx ? 1 : 0)
        if (i >= this.atlas.length) {
            return
        }
        let rt = this.node.Component(cc.RichText)
        if (rt) {
            rt.string = ""
            rt.imageAtlas = this.atlas[i]
        }
    }
}

cc.MultiAtlas = MultiAtlas
