import BaseViewCtrl from "./BaseViewCtrl";

export default class BaseNoticeCtrl extends BaseViewCtrl {

    public async __create() {
        this._state = 'create'
        this.node.group = 'ui'
        this.__listenMaps()
        this.__register('create')
        await this.onCreate()
    }

    public open() {
        if (this._state === 'enter') {
            return
        }
        this._state = 'enter'
        this.node.active = true
        this.__register('enter')
    }

    public hide() {
        if (this._state === 'remove') {
            return
        }
        this._state = 'remove'
        this.node.active = false
        this.__unregister('enter')
    }

    public onDestroy() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
    }
    
    public async onCreate() {
    }

    public onClean() {
    }

    protected __wrapListenMaps(listens: any[], out: any[], target: any): any[] {
        listens.forEach(map => {
            let data = { type: '', cb: null, target: target, tag: 'create' }
            for (let key in map) {
                let val = map[key]
                if (typeof (val) === 'function') {
                    data.type = key
                    data.cb = val
                } else if (key === 'tag') {
                    data.tag = val
                }
            }
            out.push(data)
        })
        return out
    }
}