import BaseMvcCtrl from "./BaseMvcCtrl"

export default class BaseLayerCtrl extends BaseMvcCtrl {

    private __listens: any[] = []// 监听列表

    __preload() { }

    public __init(mgr?: any) {
        this.setCtrlMgr(mgr)
        this.__wrapListenMaps(this.listenEventMaps(), this.__listens, this).forEach(({ type, cb, target }) => eventCenter.on(type, cb, target))
        this.onCreate()
        return this
    }

    public __clean() {
        this.__listens.forEach(({ type, cb, target }) => eventCenter.off(type, cb, target))
        this.onClean()
    }

    public onCreate() {
    }

    public onClean() {
    }

    public listenEventMaps(): any[] {
        return []
    }

    public setCtrlMgr(mgr: any) {
    }
}