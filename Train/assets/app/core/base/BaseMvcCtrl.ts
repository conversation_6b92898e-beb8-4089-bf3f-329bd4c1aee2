
const CmptTypeFix = {
    n: { fix: 'Node_', type: cc.Node },
    l: { fix: 'Lbl_', type: cc.Label },
    s: { fix: 'Spr_', type: cc.Sprite },
    b: { fix: 'Btn_', type: cc.Button },
    t: { fix: 'Tge_', type: cc.Toggle },
    a: { fix: 'Ani_', type: cc.Animation },
    sv: { fix: 'Sv_', type: cc.ScrollView },
    rt: { fix: 'Rt_', type: cc.RichText },
    tc: { fix: 'Tc_', type: cc.ToggleContainer },
    sp: { fix: 'Sp_', type: sp.Skeleton },
    eb: { fix: 'Eb_', type: cc.EditBox },
}

const EventTypeFix = {
    be: cc.Button,
    te: cc.Toggle,
    se: cc.Slider,
    tce: cc.ToggleContainer,
    nbe: cc.Button,
}

// 所有控制器的基础类
export default class BaseMvcCtrl extends cc.Component {

    public _isLoadProperty: boolean = false

    // 预加载
    __preload() {
        this.loadProperty()
    }

    // 记载属性
    public loadProperty() {
        if (this._isLoadProperty) {
            return
        }
        // 遍历节点
        const children = this.node.children
        for (let i = 0, l = children.length; i < l; i++) {
            this.__detectionNodeNames(children[i])
        }
        // 检测没有赋值的属性
        // const arr = ['Node_', 'Lbl_', 'Spr_', 'Btn_', 'Tge_', 'Sv_', 'Wdt_', 'Rt_', 'Ani_']
        // Object.getOwnPropertyNames(this).forEach(key => {
        //     if (arr.some(m => key.endsWith(m)) && this[key] === null) {
        //         twlog.error(this['__classname__'] + '.' + key + ' 没有赋值!!!')
        //     }
        // })
        this._isLoadProperty = true
    }

    private __isEvent(val: string) {
        return !!EventTypeFix[val.split('@')[0]]
    }

    private __isVar(val: string) {
        return CmptTypeFix[val] || val === 'w' || val === 'wg'
    }

    private __getHeadVar(arr: string[]) {
        let head = arr[0]
        arr.shift()
        while (arr.length > 0) {
            const val = arr[0]
            if (this.__isVar(val) || this.__isEvent(val)) {
                break
            }
            head += ut.initialUpperCase(val)
            arr.shift()
        }
        return head
    }

    private __detectionNodeNames(node: cc.Node) {
        const arr = node.name.split('_')
        let hasWdt = false
        if (arr.length >= 2 && !!arr[0]) {
            let head = this.__getHeadVar(arr)
            let hasW = !!arr.remove('w')
            for (let i = 0, l = arr.length; i < l; i++) {
                const it = arr[i]
                const ct = CmptTypeFix[it]
                if (ct) {// 组件
                    const vname = head + ct.fix
                    if (this[vname] !== undefined) {
                        if (ct.fix === 'Node_') {
                            this[vname] = node
                        } else {
                            const cmpt = node.getComponent(ct.type)
                            if (cmpt) {
                                this[vname] = cmpt
                            } else {
                                twlog.error(vname + ' 没有对应的组件 at=' + this['__classname__'])
                            }
                        }
                    } else {
                        twlog.error(vname + ' 没有找到对应的属性名 at=' + this['__classname__'])
                    }
                } else if (it === 'wg') {// 挂件
                    let wdt = node.getComponent(mc.BaseWdtCtrl)
                    if (!wdt) {
                        wdt = this.__addWdtComponent(node, ut.initialUpperCase(head) + 'WdtCtrl')
                    }
                    if (hasW) {
                        const vname = head + 'Wdt_'
                        if (this[vname] !== undefined) {
                            this[vname] = wdt
                        }
                    }
                    hasWdt = true
                } else {// 事件
                    const [e, data] = it.split('@')
                    const et = EventTypeFix[e]
                    if (et) {
                        if (e === 'nbe') {
                            node.children.forEach(it => this.__addClickEvent(it, head, et, data))
                        } else {
                            this.__addClickEvent(node, head, et, data)
                        }
                    }
                }
            }
        }
        if (!hasWdt) {
            const children = node.children
            for (let i = 0, l = children.length; i < l; i++) {
                this.__detectionNodeNames(children[i])
            }
        }
    }

    protected __addClickEvent(node: cc.Node, head: string, cmptType: typeof cc.Component, data: string) {
        const fname = 'onClick' + ut.initialUpperCase(head)
        if (this[fname] && typeof (this[fname]) === 'function') {
            const cmpt = node.getComponent(cmptType)
            if (cmpt) {
                const events = this.__getEvents(cmpt)
                if (events) {
                    events[0] = this.__newEventHandler(fname, data)
                } else {
                    twlog.error(fname + ' 没有对应的events at=' + this['__classname__'] + '.' + node.name)
                }
            } else {
                twlog.error(fname + ' 没有对应的组件 at=' + this['__classname__'] + '.' + node.name)
            }
        } else {
            twlog.error(fname + ' 没有找到对应的方法名 at=' + this['__classname__'] + '.' + node.name)
        }
    }

    protected __getEvents(cmpt: cc.Component) {
        if (cmpt instanceof cc.Toggle || cmpt instanceof cc.ToggleContainer) {
            return cmpt.checkEvents
        } else if (cmpt instanceof cc.Button) {
            return cmpt.clickEvents
        } else if (cmpt instanceof cc.Slider) {
            return cmpt.slideEvents
        }
        return null
    }

    protected __newEventHandler(handler: string, data: string) {
        const eventHandler = new cc.Component.EventHandler()
        eventHandler.target = this.node
        eventHandler.component = this['__classname__']
        eventHandler.handler = handler
        eventHandler.customEventData = data || ''
        return eventHandler
    }

    private __addWdtComponent(node: cc.Node, className: string) {
        if (!cc.js.getClassByName(className)) {
            twlog.error('addWdtComponent error! not found class ' + className)
            return null
        }
        let wdt = node.getComponent(className)
        if (!wdt) {
            wdt = node.addComponent(className)
        }
        return wdt
    }

    protected __wrapListenMaps(listens: any[], out: any[], target: any): any[] {
        listens.forEach(map => {
            let data = { type: '', cb: null, target: target, tag: 'enter' }
            for (let key in map) {
                let val = map[key]
                if (typeof (val) === 'function') {
                    data.type = key
                    data.cb = val
                } else if (key === 'tag') {
                    data.tag = val
                }
            }
            out.push(data)
        })
        return out
    }
}