import BaseViewCtrl from "./BaseViewCtrl"
import CoreEventType from "../event/CoreEventType"

// 基础UI视图控制器
export default class BasePnlCtrl extends BaseViewCtrl {

    public key: string = ''// 传入名
    public mod: string = ''// 模块名
    public url: string = ''// UI地址

    public windTag: string = ''// 
    public isClean: boolean = true
    public isAct: boolean = true //是否播放动作
    public isMask: boolean = true //是否显示遮照
    public maskName: string = 'PNL_MASK'
    public maskOpacity: number = 0.7;// 半透明蒙版透明度
    public Index: number = 0
    public adaptWidth: number = 600 //适应宽度距离
    public adIndex: number = -1 //banner广告下标, -1表示没广告

    public mask: cc.Node = null //当前的遮照

    public async __create(...params: any) {
        this._state = 'create'
        this.node.group = 'ui'
        this.__listenMaps()
        this.__register('create')
        await this.onCreate(...params)
    }

    public __enter(...params: any) {
        if (this._state !== 'enter') {
            this._state = 'enter'
            this.__register('enter')
        }
        this.onEnter(...params)
    }

    public __remove() {
        this._state = 'remove'
        this.__unregister('enter')
        this.onRemove()
    }

    public __clean() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
    }

    public async onCreate(...params) {
    }

    public onEnter(...params: any) {
    }

    public onRemove() {
    }

    public onClean() {

    }

    public hide() {
        this.emit(CoreEventType.HIDE_PNL, this)
    }

    public close() {
        this.emit(CoreEventType.CLOSE_PNL, this)
    }

    public setOpacity(val: number) {
        this.node.opacity = val
        if (this.mask) {
            // this.mask.opacity = Math.min(val, 150)
        }
    }

    public setParam(opts: PnlParam) {
        this.isClean = opts.isClean === undefined ? this.isClean : opts.isClean
        this.isAct = opts.isAct === undefined ? this.isAct : opts.isAct
        this.isMask = opts.isMask === undefined ? this.isMask : opts.isMask
        this.maskName = opts.maskName === undefined ? this.maskName : opts.maskName
        this.maskOpacity = opts.maskOpacity == undefined ? this.maskOpacity : opts.maskOpacity;
        this.Index = opts.Index === undefined ? this.Index : opts.Index
        this.adaptWidth = opts.adaptWidth === undefined ? this.adaptWidth : opts.adaptWidth
        this.adIndex = opts.adIndex === undefined ? this.adIndex : opts.adIndex
    }
}