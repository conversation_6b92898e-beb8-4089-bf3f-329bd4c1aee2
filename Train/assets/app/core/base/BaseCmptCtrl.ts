import BaseViewCtrl from "./BaseViewCtrl";

// 基础Cmpt控制器
export default class BaseCmptCtrl extends BaseViewCtrl {

    public loadProperty() {
    }

    onLoad() {
        this._state = 'create'
        this.__listenMaps()
        this.__register('create')
        this.onCreate()
    }

    public onEnable() {
        if (this._state !== 'enter') {
            this._state = 'enter'
            this.__register('enter')
        }
        this.onEnter()
    }

    public onDisable() {
        this._state = 'remove'
        this.__unregister('enter')
        this.onRemove()
    }

    onDestroy() {
        this._state = 'clean'
        this.__unregister()
        this.onClean()
        super.onDestroy()
    }

    public onCreate() {
    }

    public onEnter() {
    }

    public onRemove() {
    }

    public onClean() {
    }
}