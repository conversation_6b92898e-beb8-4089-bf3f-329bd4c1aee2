const { ccclass, menu, requireComponent } = cc._decorator;

@ccclass
@menu('多语言组件/LetterLabel')
@requireComponent(cc.Label)
export default class LetterLabel extends cc.Component {
    protected _label: cc.Label = null

    protected get label() {
        if (!this._label) {
            this._label = this.getComponent(cc.Label)
        }
        return this._label
    }

    public onLoad() {
        const font = assetsMgr.getFont("jcyt_600W")
        if (font) {
            this.label.font = font
        }
        // this.label.cacheMode = cc.Label.CacheMode.CHAR //描边会出问题 故取消
    }

    protected onEnable(): void {
        if (this.label.enableBold) {
            console.warn("不要用bold", this.node.getPath())
            this.label.enableBold = false
        }
    }
}