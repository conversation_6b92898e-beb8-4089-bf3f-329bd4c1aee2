import BaseLayerCtrl from "../base/BaseLayerCtrl";
import WindCtrlMgr from "../manage/WindCtrlMgr";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WindLayerCtrl extends BaseLayerCtrl {

    private ctrlMgr: WindCtrlMgr = null

    public listenEventMaps() {
        return [
            { [CoreEventType.GOTO_WIND]: this.onGotoWind },
            { [CoreEventType.PRELOAD_WIND]: this.onPreloadWind },
            { [CoreEventType.CLEAN_CACHE_WIND]: this.onCleanCacheWind },
            { [CoreEventType.RELOAD_WIND]: this.reloadWind },
            { [CoreEventType.ACTIVE_WIND]: this.activeWind },
        ]
    }

    public onCreate() {

    }

    public onClean() {

    }

    public setCtrlMgr(mgr: WindCtrlMgr) {
        this.ctrlMgr = mgr
        this.ctrlMgr.node = this.node
    }

    // 获取当前场景
    public getCurrWind() {
        return this.ctrlMgr.currWind
    }

    public getNextWindName() {
        return this.ctrlMgr.nextWindName
    }

    public getPreWindName() {
        return this.ctrlMgr.preWindName
    }

    private onGotoWind(key: string, ...params: any) {
        this.ctrlMgr.goto(key, ...params)
    }

    private onPreloadWind(key: string, complete?: Function, progress?: (done: number, total: number) => void) {
        this.ctrlMgr.preLoad(key, progress, complete)
    }

    public reloadWind() {
        this.ctrlMgr.reload()
    }

    public activeWind(active) {
        this.ctrlMgr.activeWind(active)
    }

    // 清理所有缓存中的wind
    private onCleanCacheWind() {
        this.ctrlMgr.cleanCacheWind()
    }
}