import BaseLayerCtrl from "../base/BaseLayerCtrl";
import ViewCtrlMgr from "../manage/ViewCtrlMgr";
import BasePnlCtrl from "../base/BasePnlCtrl";
import CoreEventType from "../event/CoreEventType";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ViewLayerCtrl extends BaseLayerCtrl {

    private ctrlMgr: ViewCtrlMgr = null

    public listenEventMaps() {
        return [
            { [CoreEventType.OPEN_PNL]: this.onOpenPnl },
            { [CoreEventType.HIDE_PNL]: this.onHidePnl },
            { [CoreEventType.HIDE_TOP_PNL]: this.onHideTopPnl },
            { [CoreEventType.HIDE_ALL_PNL]: this.onHideAllPnl },
            { [CoreEventType.CLOSE_PNL]: this.onClosePnl },
            { [CoreEventType.CLOSE_ALL_PNL]: this.onCloseAllPnl },
            { [CoreEventType.CLOSE_MOD_PNL]: this.onCloseModPnl },
            { [CoreEventType.PRELOAD_PNL]: this.onPreloadPnl },
            { [CoreEventType.CLEAN_ALL_UNUSED]: this.onCleanAllUnused },
            { [CoreEventType.GIVEUP_LOAD_PNL]: this.onGiveupLoadPnl },
            { [CoreEventType.GIVEUP_LOAD_PNL_BY_NAME]: this.onGiveupLoadPnlByName },
        ]
    }

    public onCreate() {
        this.node.group = 'ui'
    }

    public onClean() {

    }

    public setCtrlMgr(mgr: ViewCtrlMgr) {
        this.ctrlMgr = mgr
        this.ctrlMgr.node = this.node
    }

    public getCtrlMgr() {
        return this.ctrlMgr
    }

    public getOpenPnls() {
        // return this.ctrlMgr.getOpened()
    }
    public getPnl<T extends BasePnlCtrl>(name: string): T {
        return this.ctrlMgr.__getForCache<T>(name);
    }

    public hasLoadQueue(name: string) {
        return this.ctrlMgr.hasLoadQueue(name)
    }

    private onOpenPnl(key: string | BasePnlCtrl, ...params: any) {
        this.ctrlMgr.show(key, ...params)
    }

    private onHidePnl(key: string | BasePnlCtrl) {
        this.ctrlMgr.hide(key)
    }

    private onHideTopPnl() {
        this.ctrlMgr.hideTop();
    }

    private onHideAllPnl(val?: string, ignores?: string) {
        this.ctrlMgr.hideAll(val, ignores)
    }

    private onClosePnl(key: string | BasePnlCtrl) {
        this.ctrlMgr.clean(key)
    }

    private onCloseAllPnl(val?: string, ignores?: string) {
        this.ctrlMgr.cleanAll(val, ignores)
    }

    private onCloseModPnl(mod: string) {
        this.ctrlMgr.cleanByMod(mod)
    }

    private onPreloadPnl(key: string, params?: any[], complete?: Function, progress?: (done: number, total: number) => void) {
        this.ctrlMgr.preloadPnl(key, params).then(pnl => {
            complete && complete(pnl);
            progress && progress(1, 1);
        })
    }

    private onCleanAllUnused() {
        this.ctrlMgr.cleanAllUnused()
    }

    private onGiveupLoadPnl(id: number) {
        this.ctrlMgr.giveupLoadById(id)
    }

    private onGiveupLoadPnlByName(name: string) {
        this.ctrlMgr.giveupLoadByName(name)
    }
}