import BaseLayerCtrl from "../base/BaseLayerCtrl";
import CoreEventType from "../event/CoreEventType";
import NoticeCtrlMgr from "../manage/NoticeCtrlMgr";

const { ccclass, property } = cc._decorator;

@ccclass
export default class NoticeLayerCtrl extends BaseLayerCtrl {

    private ctrlMgr: NoticeCtrlMgr = null

    public listenEventMaps() {
        return [
            { [CoreEventType.LOAD_ALL_NOTICE]: this.onLoadAllNotice },
            { [CoreEventType.LOAD_NOTICE]: this.loadNotice },
        ]
    }

    public onCreate() {
        this.node.group = 'ui'
    }

    public onClean() {

    }

    public setCtrlMgr(mgr: NoticeCtrlMgr) {
        this.ctrlMgr = mgr
        this.ctrlMgr.node = this.node
    }

    private onLoadAllNotice(complete?: Function, progress?: (done: number, total: number) => void) {
        this.ctrlMgr.loadAll('view/notice', complete, progress)
    }

    private loadNotice(name, eventName, ...params) {
        this.ctrlMgr.load(name, eventName, ...params)
    }
}