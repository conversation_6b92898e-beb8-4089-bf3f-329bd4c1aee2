import { cryptoHelper } from "../../script/common/crypto/CryptoHelper"
import CoreEventType from "../event/CoreEventType"

const STORE_KEY = "__@hotUpdateInfo"

/**
 * 热更新管理
 */
class HotUpdateMgr {

    private _am: any = null
    private _updating: boolean = false // 是否更新中
    private _hotStorage: string = ''// 热更新存档文件名字

    private tempCostTime: number = 0

    private newVersion: string = null

    protected downloadManifestError: boolean = false

    public get searchPaths() {
        return CC_JSB ? jsb.fileUtils.getSearchPaths() : []
    }

    public setSearchPaths(paths: string[]) {
        if (CC_JSB) {
            localStorage.setItem('HotUpdateSearchPaths', JSON.stringify(paths));
            jsb.fileUtils.setSearchPaths(paths)
        }
    }

    public addSearchPath(path: string) {
        let searchPaths = this.searchPaths
        if (searchPaths.indexOf(path) === -1) {
            if (CC_JSB) {
                jsb.fileUtils.addSearchPath(path, true)
                localStorage.setItem('HotUpdateSearchPaths', JSON.stringify(this.searchPaths));
            }
        }
    }

    public getWritablePath(path: string) {
        const root = CC_JSB ? jsb.fileUtils.getWritablePath() : './'
        return path ? root + path : root
    }

    public isFileExist(path: string) {
        return CC_JSB ? jsb.fileUtils.isFileExist(path) : false
    }

    public getStringFromFile(path: string) {
        return CC_JSB ? jsb.fileUtils.getStringFromFile(path) : ''
    }

    public writeStringToFile(data: string, path: string) {
        if (CC_JSB) {
            let pathArry = path.split("/");
            let dirPath = "";
            for (let i = 0; i < pathArry.length - 1; i++) {
                dirPath += pathArry[i] + "/";
                if (!jsb.fileUtils.isDirectoryExist(dirPath)) {
                    let success = jsb.fileUtils.createDirectory(dirPath);
                    if (!success) {
                        return false;
                    }
                }
            }
            return jsb.fileUtils.writeStringToFile(data, path);
        }
    }

    public getHotStorage() {
        if (!this._hotStorage) {
            this._hotStorage = mc.GameNameSpace + "-hot-update";
        }
        return this._hotStorage
    }

    public getTempHotStorage() {
        return mc.GameNameSpace + "-hot-update_temp"
    }

    private getOriginManifestPath() {
        return assetsMgr.transform({ bundle: "manifest", path: "project" }, { ext: ".manifest", __isNative__: true });
    }

    private getCacheManifestPath() {
        return "project.manifest";
    }

    private getTempManifestPath() {
        return this.getWritablePath(this.getTempHotStorage() + "/project.manifest.temp")
    }

    public versionCompareHandle(versionA: string, versionB: string): number {
        twlog.info("JS Custom Version Compare: version A is " + versionA + ', version B is ' + versionB)
        const vA = versionA.split('.')
        const vB = versionB.split('.')
        for (let i = 0; i < vA.length; ++i) {
            const a = parseInt(vA[i] || '0')
            const b = parseInt(vB[i] || '0')
            if (a === b) {
                continue
            } else {
                return a - b
            }
        }
        return vB.length > vA.length ? -1 : 0
    }

    public setVerifyCallback(path: string, asset: jsb.ManifestAsset) {
        let compressed = asset.compressed;
        let expectedMD5 = asset.md5;
        let relativePath = asset.path;
        if (compressed) {
            return true;
        }
        else {
            // @ts-ignore
            let file = jsb.fileUtils.getDataFromFile(path);
            let md5 = cryptoHelper.md5(file);
            if (expectedMD5 === md5) {
                // twlog.info("Verification passed : " + relativePath + ' (' + expectedMD5 + ')');
                return true;
            }
            else {
                twlog.info("Verification fail : " + relativePath + ' (' + expectedMD5 + ')' + ' (' + md5 + ')')
                return false;
            }
        }
    }

    private release() {
        if (this._am) {
            this._am.setEventCallback(null)
            this._am = null
        }
    }

    private createAssetsManager(packageUrl: string) {
        const manifestUrl = this.getOriginManifestPath()
        const storagePath = this.getWritablePath(this.getHotStorage() + '/')

        twlog.info('hotupdate originManifest path:', manifestUrl)
        twlog.info('hotupdate storage path:', storagePath)

        // @ts-ignore
        const am = new jsb.AssetsManager(manifestUrl, storagePath)
        am.setVersionCompareHandle(this.versionCompareHandle.bind(this))
        am.setVerifyCallback(this.setVerifyCallback.bind(this));
        am.setEventCallback(this.updateCallback.bind(this))
        // @ts-ignore
        if (am.getState() === jsb.AssetsManager.State.UNINITED) {
            let str = JSON.stringify({
                'packageUrl': packageUrl,
                'remoteManifestUrl': packageUrl + 'project.manifest',
                'remoteVersionUrl': packageUrl + 'version.manifest',
                'version': '0.0.0',
                'assets': {},
                'searchPaths': [],
            })
            // @ts-ignore
            let manifest = new jsb.Manifest(str, storagePath)
            am.loadLocalManifest(manifest, storagePath)
            twlog.info('not manifest!!!')
        } else {
            twlog.info('find manifest!!!')
        }

        if (cc.sys.os === cc.sys.OS_ANDROID) {
            // @ts-ignore
            am.setMaxConcurrentTask(2)
        }
        return am
    }



    private printError(msg: string) {
        twlog.info('【Update Error】', msg)
    }

    private printLog(msg: string) {
        twlog.info('【Update Info】', msg)
    }

    private updateCallback(event: any) {
        let failed = false, finish = false
        const code = event.getEventCode()
        switch (code) {
            case jsb.EventAssetsManager.ERROR_UPDATING:
                this.onError(code, 'Asset update error: ' + event.getAssetId() + ', ' + event.getMessage())
                break
            case jsb.EventAssetsManager.ERROR_DECOMPRESS:
                this.onError(code, event.getMessage())
                break
            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:
                failed = true
                this.onError(code, 'No local manifest file found, hot update skipped.')
                break
            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:
            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:
                failed = true
                this.onError(code, 'Fail to download manifest file, hot update skipped.')
                this.downloadManifestError = true
                break
            case jsb.EventAssetsManager.NEW_VERSION_FOUND:
                this.printLog('New version found.')
                break
            case jsb.EventAssetsManager.UPDATE_FAILED:
                failed = true
                this.onError(code, 'Update Fail.')
                break
            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:
                this.printLog('Already up to date with the latest remote version.')
                this.setSearchPaths(this.searchPaths)
                finish = true
                break
            case jsb.EventAssetsManager.UPDATE_PROGRESSION:
                // this.printLog('Update Progression', event.getDownloadedBytes(), event.getTotalBytes(), event.getPercent())
                break
            case jsb.EventAssetsManager.UPDATE_FINISHED:
                this.printLog('Update finished. ')
                this.onFinish(event)
                finish = true
                break
        }
        if (failed || finish) {
            this._am && this._am.setEventCallback(null)
            this._updating = false
        }
        if (finish) {
            this.setSearchPaths(this.searchPaths)
        }
        // this.printLog('updateCallback failed=', failed, 'finish=', finish, 'event=', event.getEventCode())
        eventCenter.emit(CoreEventType.HOT_UPDATE_EVENT, event, failed)
    }

    onError(code, msg) {
        this.printError(msg)
    }

    // 开始更新
    public start(packageUrl: string, manifestUrl: string, version: string) {
        if (!cc.sys.isNative) {
            return
        }
        if (this._updating) {
            return twlog.info('HotUpdateMgr is updating, did NOT valid to run....')
        }

        if (this._am) {
            this.release()
        }
        
        this.onStart(version)

        this.updateManifestUrl(packageUrl, manifestUrl);
        this.setSearchPaths(this.searchPaths)

        this._am = this.createAssetsManager(packageUrl)
        if (!this._am) {
            return twlog.error('AssetsManager create failed!')
        }
        this._am.update()
        this._updating = true
    }

    // 中断下载
    public abort() {
        if (this._updating) {
            this._updating = false
            this.release()
            twlog.info('中止下载...')
        }
    }

  // 重新下载
  public async redownload() {
    this._updating = true
    this._am.setEventCallback(this.updateCallback.bind(this))

    if (this.downloadManifestError) {
        this.downloadManifestError = false
        await ut.waitNextFrame()
        this._am.update()
    }
    else {
        this._am.downloadFailedAssets()
    }
}

    // 获取当前版本
    public getVersion(): string {
        if (!CC_JSB) {
            return '0.0.0'
        }
        try {
            const path = this.getWritablePath(`${this.getHotStorage()}/project.manifest`)
            let content = this.getStringFromFile(path)
            if (!content) {
                content = this.getStringFromFile(this.getOriginManifestPath())
            }
            if (content) {
                const json = JSON.parse(content)
                return json ? json.version : '0.0.0'
            }
            return '0.0.0'
        } catch (error) {
            return '0.0.0'
        }
    }

    // 清楚缓存
    public cleanCache() {
        if (!cc.sys.isNative) {
            return twlog.error('the platform not support.')
        }
        const storagePath = this.getWritablePath(this.getHotStorage() + '/')
        if (storagePath) {
            if (jsb.fileUtils.isDirectoryExist(storagePath)) {
                jsb.fileUtils.removeDirectory(storagePath)
            } else {
                twlog.error(`path:-->${storagePath} not exist`)
            }
        } else {
            twlog.error('storagePath not exist')
        }
    }

    // 转换字节数
    public convertBytesToString(bytes: number): string {
        if (bytes >= 1073741824) {
            return (bytes / 1073741824).toFixed(2) + 'GB'
        } else if (bytes >= 1048576) {
            return (bytes / 1048576).toFixed(2) + 'MB'
        } else if (bytes >= 1024) {
            return (bytes / 1024).toFixed(2) + 'KB'
        } else {
            return bytes + 'B'
        }
    }

    //更新manifest的远端地址，根据服务器动态替换
    private updateManifestUrl(packageUrl: string, manifestUrl: string) {
        if (!packageUrl) return;

        let update = (path) => {
            let content = this.getStringFromFile(path);
            if (!content) return;
            let json = JSON.parse(content)
            twlog.info("updateManifest", json.remoteManifestUrl);
            json.remoteManifestUrl = manifestUrl + "project.manifest";
            json.packageUrl = packageUrl;
            let success = this.writeStringToFile(JSON.stringify(json), path);
            if (!success) { //不可写，新建一个可写路径覆盖
                const storagePath = this.getWritablePath(this.getHotStorage() + "/")
                let filePath = storagePath + path;
                success = this.writeStringToFile(JSON.stringify(json), filePath);
                if (success) {
                    this.addSearchPath(storagePath)
                }
            }
        }

        update(this.getCacheManifestPath());
        update(this.getOriginManifestPath());
    }

    public isUpdating() { return this._updating }


    private onStart(version: string) {
        this.newVersion = version
    }

    private onFinish(event) {
    }
}

window['hotUpdateMgr'] = new HotUpdateMgr()
