import BaseNoticeCtrl from "../base/BaseNoticeCtrl"
import { loader } from "../utils/ResLoader"

export default class NoticeCtrlMgr {

    public node: cc.Node = null

    private caches: Map<string, BaseNoticeCtrl> = new Map<string, BaseNoticeCtrl>()

    public async loadAll(root: string, complete?: Function, progress?: (done: number, total: number) => void) {
        loader.debug = false
        const assets = await loader.loadResDir(root, cc.Prefab, progress)
        await Promise.all(assets.map(pfb => {
            if (!this.caches.get(pfb.name)) {
                this.ready(pfb)
            } 
        }))
        loader.debug = true
        complete && complete()
    }

    public async load(name: string, eventName?: string, ...params) {
        if (!name.endsWith('Not')) {
            name += 'Not'
        }
        let url = `view/notice/${name}`
        if (!this.caches.get(name)) {
            let prefab = await loader.loadRes(url, cc.Prefab)
            if (!this.caches.get(name)) {
                this.ready(prefab)
            }
        }
        if (eventName) {
            eventCenter.emit(eventName, ...params)
        }
    }

    private ready(pfb) {
        this.caches.set(pfb.name, pfb)
        const it: cc.Node = cc.instantiate2(pfb, this.node)
        const className = it.name + `Ctrl`
        if (!cc.js.getClassByName(className)) {
            return twlog.error('loadNotice error! not found class ' + className)
        }
        let notice = it.getComponent(className)
        if (!notice) {
            notice = it.addComponent(className)
        }
        if (!notice || !(notice instanceof BaseNoticeCtrl)) {
            return twlog.error('loadNotice error! not found class ' + className)
        }
        notice.hide()
        return notice.__create()
    }
}