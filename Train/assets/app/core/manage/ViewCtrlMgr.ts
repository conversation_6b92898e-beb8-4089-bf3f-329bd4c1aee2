import { DEFAULT_IGNORES } from "../../script/common/constant/Constant"
import { anim<PERSON><PERSON><PERSON> } from "../../script/common/helper/AnimHelper"
import BasePnlCtrl from "../base/BasePnlCtrl"
import CoreEventType from "../event/CoreEventType"
import { loader } from "../utils/ResLoader"

const ROOT_MAX_SCALE = 1.2

export default class ViewCtrlMgr {

    public node: cc.Node = null

    private caches: Map<string, BasePnlCtrl> = new Map<string, BasePnlCtrl>()
    private opened: BasePnlCtrl[] = []
    private masks: Map<string, cc.Node[]> = new Map<string, cc.Node[]>();// 遮罩列表

    private loadQueues: LoadPnlInfo[] = [] //当前加载队列
    private loadId: number = 0

    private _ad: any = null
    private get ad() { return this._ad || (this._ad = mc.modelMgr.get('ad')) }

    // 添加到加载队列
    private addLoadQueue(name: string, params?) {
        return this.loadQueues.add({ id: ++this.loadId, name: name, url: '', params })
    }

    public hasLoadQueue(name: string) {
        return this.loadQueues.has('name', name)
    }

    private async __load(name: string, info: LoadPnlInfo) {
        // console.time('load ' + name)
        let pfb: cc.Prefab = null, url: string = '', mod: string = ''
        let [wind, pnl] = name.split('/')
        if (!pnl) {// 没有输入模块的情况下
            // 先默认从当前模块找
            const head = ut.initialUpperCase(wind).replace('Pnl', '')
            mod = mc.currWindName
            info.url = url = `view/${mod}/${head}Pnl`
            pfb = await loader.loadRes(url, cc.Prefab)
            if (!pfb) {
                // 如果没有就从common里面找
                mod = 'common'
                info.url = url = `view/${mod}/${head}Pnl`
                pfb = await loader.loadRes(url, cc.Prefab)
            }
        } else {
            const head = ut.initialUpperCase(pnl).replace('Pnl', '')
            mod = wind
            info.url = url = `view/${mod}/${head}Pnl`
            pfb = await loader.loadRes(url, cc.Prefab)
        }
        // console.timeEnd('load ' + name)
        if (!this.loadQueues.remove('id', info.id)) {
            pfb = null
        }
        if (!pfb) {
            twlog.error('loadPnl error! not found pfb ' + name)
        }
        return Promise.resolve({ mod, url, pfb })
    }

    // 加载一个Pnl
    public async __loadPnl(name: string, info: LoadPnlInfo): Promise<BasePnlCtrl> {
        const { mod, url, pfb } = await this.__load(name, info)
        if (!pfb) {
            return Promise.resolve(null)
        }
        const it = cc.instantiate2(pfb, this.node)
        const className = it.name + `Ctrl`
        if (!cc.js.getClassByName(className)) {
            twlog.error('loadPnl error! not found class ' + className)
            return Promise.resolve(null)
        }
        let pnl = it.getComponent(className)
        if (!pnl) {
            pnl = it.addComponent(className)
        }
        if (!pnl || !(pnl instanceof BasePnlCtrl)) {
            twlog.error('loadPnl error! not found class ' + className)
            return Promise.resolve(null)
        }
        pnl.key = name
        pnl.mod = mod
        pnl.url = url
        this.caches.set(url, pnl)
        // 这里检查一下 是否还没有加载属性
        if (!pnl._isLoadProperty) {
            twlog.error('load pnl error! not load property. at=' + className)
            // @ts-ignore
            if (ut.isMiniGame() && !(typeof GameGlobal !== 'undefined' && typeof GameGlobal.isTest !== 'undefined' && GameGlobal.isTest)) {
                wx.uma.trackEvent('pnlNotLoadProperty', { className: className })
            }
            pnl.loadProperty()
        }
        it.active = false
        let params = info.params || []
        await pnl.__create(...params)
        return Promise.resolve(pnl)
    }

    // 获取缓存的Pnl
    public __getForCache<T extends BasePnlCtrl>(name: string): T {
        let [wind, key] = name.split('/'), ui = null
        if (!key) {
            const head = ut.initialUpperCase(wind).replace('Pnl', '')
            ui = this.caches.get(`view/${mc.currWindName}/${head}Pnl`)
            if (!ui) {
                ui = this.caches.get(`view/common/${head}Pnl`)
            }
        } else {
            const head = ut.initialUpperCase(key).replace('Pnl', '')
            ui = this.caches.get(`view/${wind}/${head}Pnl`)
        }
        return ui
    }

    // 预加载
    public async preloadPnl(name: string, params?) {
        let pnl = this.__getForCache(name)
        if (!pnl && !this.hasLoadQueue(name)) {
            pnl = await this.__loadPnl(name, this.addLoadQueue(name, params))
        }
        return pnl
    }

    private pushPnl(ui: BasePnlCtrl) {
        this.opened.remove('url', ui.url)
        this.opened.push(ui)

        // 重新给所有ui排序
        for (let i = 0, l = this.opened.length; i < l; i++) {
            const pnl = this.opened[i]
            const zIndex = pnl.node.zIndex = pnl.Index || (i + 1) * 10
            if (pnl.mask) {
                pnl.mask.zIndex = zIndex - 1
            }
        }
    }

    private popPnl(ui: BasePnlCtrl) {
        this.opened.remove('url', ui.url)
    }

    private getMask(ui: BasePnlCtrl) {
        if (!this.masks.has(ui.maskName)) this.masks.set(ui.maskName, []);

        const it = this.masks.get(ui.maskName).pop() || cc.instantiate(assetsMgr.getPrefab(ui.maskName))
        it.setContentSize(cc.winSize)
        it.opacity = 255

        if (ui.maskName == "MASK_BLUR") {
            ui.maskOpacity = 1
            let rt = new cc.RenderTexture()
            rt.initWithSize(cc.winSize.width, cc.winSize.height, cc.RenderTexture.DepthStencilFormat.RB_FMT_S8)

            let render = (camera)=>{
                if (camera && camera.activeInHierarchy) {
                    let cmpt = camera.Component(cc.Camera)
                    cmpt.targetTexture = rt
                    cmpt.render()
                    cmpt.targetTexture = null
                }
            }
            render(cc.find("Canvas/Bg Camera"))
            render(cc.find("Canvas/Main Camera"))
            render(cc.find("Canvas/PlanetCamera"))
            render(cc.find("Canvas/UI Camera"))

            let spf = it.Component(cc.Sprite).spriteFrame
            spf = new cc.SpriteFrame(rt)
            it.Component(cc.Sprite).spriteFrame = spf
            spf.setFlipY(true)
        }
    
        it.opacity = 255 * ui.maskOpacity;
        it.parent = this.node
        it.zIndex = ui.node.zIndex - 1
        ui.mask = it
        it.active = true
    }

    private putMask(ui: BasePnlCtrl) {
        if (!this.masks.has(ui.maskName)) this.masks.set(ui.maskName, []);
        if (ui && ui.mask) {
            ui.mask.parent = null
            this.masks.get(ui.maskName).push(ui.mask)
            ui.mask = null
        }
    }

    // 播放显示的动作
    private async playShowAction(ui: BasePnlCtrl) {
        if (ui.node.x !== 0) {
            const widget = ui.getComponent(cc.Widget)
            widget.updateAlignment()
            widget.enabled = false
        }
        const root = ui.Child('root') || ui.Child('root_n') || ui.node
        return animHelper.playShowPnlAction(root)
    }

    // 适应大小
    private adaptRootSize(ui: BasePnlCtrl) {
        // if (!ui) {
        //     return this.ad.hideAllBannerAd()
        // }
        // const root = ui.Child('root') || ui.Child('root_n')
        // if (!root) {
        //     return this.ad.hideAllBannerAd()
        // }
        // let oy = root['_oy']
        // if (oy === undefined) {
        //     oy = root['_oy'] = root.y
        // }
        // const data = this.ad.checkBannerAd(ui.adIndex, root) || { offsetY: 0, maxAdaptScale: ROOT_MAX_SCALE }
        // root.y = oy + data.offsetY
        // const wsize = cc.winSize
        // const dsize = cc.view.getDesignResolutionSize()
        // const rsize = root.getContentSize()
        // // 算出高度比例
        // let scale = (rsize.height / dsize.height * wsize.height) / rsize.height
        // // 如果宽度超过了
        // const width = wsize.width - ui.adaptWidth
        // if (rsize.width * scale > width) {
        //     scale = width / rsize.width
        // }
        // root.scale = Math.min(data.maxAdaptScale, scale)
    }

    // 显示一个UI
    public async show(name: string | BasePnlCtrl, ...params: any): Promise<BasePnlCtrl> {
        mc.lockTouch(true)
        let ui: BasePnlCtrl = null
        if (typeof (name) === 'string') {
            ui = this.__getForCache(name)
            if (!ui && !this.hasLoadQueue(name)) {
                const data = this.addLoadQueue(name)
                data.params = params
                eventCenter.emit(CoreEventType.LOAD_BEGIN_PNL, data)
                ui = await this.__loadPnl(name, data)
                eventCenter.emit(CoreEventType.LOAD_END_PNL, data)
            }
        } else if (name.isValid && name._state !== 'clean') {
            ui = name
        } else {
            mc.lockTouch(false)
            return this.show(name.key, ...params)
        }
        if (!ui) {
            mc.lockTouch(false)
            return Promise.resolve(null)
        }
        if (!ui.getActive()) {
            this.pushPnl(ui)
            ui.isMask && this.getMask(ui)
            ui.setActive(true)
            ui.__enter(...params)
            this.adaptRootSize(ui)
            // ui.setOpacity(255)
            // 发送进入事件
            eventCenter.emit(CoreEventType.PNL_ENTER, ui)
            if (ui.isAct) {
                await this.playShowAction(ui)
            } else {
                ui.node.scale = 1
                await ut.waitNextFrame()//等一帧阻挡点击
            }
        }/*  else {
            this.pushPnl(ui)
            ui.__enter(...params)
        } */
        ui.windTag = mc.currWind ? mc.currWind.key : '';
        mc.lockTouch(false)
        return Promise.resolve(ui)
    }

    // 隐藏一个Pnl
    public hide(val: BasePnlCtrl | string) {
        const ui: BasePnlCtrl = val instanceof BasePnlCtrl ? val : this.__getForCache(val)
        if (!ui) {
            this.giveupLoadByName(val as string)
            return
        }
        let top = this.opened.last()
        this.popPnl(ui)
        this.putMask(ui)
        if (ui.getActive()) {
            ui.__remove()
            ui.setActive(false)
            eventCenter.emit(CoreEventType.PNL_LEAVE, ui)
        }
        // 如果关闭的最后一个UI 就看下面还有UI没有
        if (top === ui) {
            this.adaptRootSize(this.opened.last())
        }
    }

    public hideTop() {
        let top = this.opened.last()
        if (!top) return;
        this.hide(top);
    }

    public getTopPnl(ignores?: string[]) {
        let defaultIgnores = DEFAULT_IGNORES
        ignores = defaultIgnores.concat(ignores || [])
        let pnls = this.opened.filter(pnl => !ignores.includes(pnl.key))
        let top = pnls.max(pnl => pnl.node.zIndex)
        return top
    }

    public hasOpenPnl(ignores?: string[]) {
        let defaultIgnores = DEFAULT_IGNORES
        ignores = defaultIgnores.concat(ignores || [])
        let pnl = this.opened.find(pnl => !ignores.includes(pnl.key) && pnl.node.active)
        if (pnl) return true
        let info = this.loadQueues.find(m => !ignores.includes(m.name))
        return !!info
    }

    // 隐藏所有Pnl
    public hideAll(val?: string, ignores?: string) {
        if (!val) {
            const arr = ignores ? ignores.split('|') : []
            this.caches.forEach(m => arr.indexOf(m.key) === -1 && this.hide(m))
            for (let info of this.loadQueues) {
                if (arr.indexOf(info.name) == -1) {
                    this.giveupLoadByName(info.name)
                }
            }
        } else {
            val.split('|').forEach(m => this.hide(m))
        }
    }

    // 关闭一个Pnl
    public clean(val: BasePnlCtrl | string) {
        const ui: BasePnlCtrl = val instanceof BasePnlCtrl ? val : this.__getForCache(val)
        if (!ui || !ui.isClean) {
            return
        }
        this.hide(ui)
        ui.__clean()
        ui.node.destroy()
        this.caches.delete(ui.url)
        loader.releaseRes(ui.url, cc.Prefab)
    }

    // 关闭所有Pnl
    public cleanAll(val?: string, ignores?: string) {
        if (!val) {
            // this.caches.forEach(m => this.clean(m))
            // 只清除当前wind下的打开的
            const arr = ignores ? ignores.split('|') : []
            this.caches.forEach(m => m.windTag == mc.currWind.key && arr.indexOf(m.key) === -1 && this.clean(m))
            for (let info of this.loadQueues) {
                if (arr.indexOf(info.name) == -1) {
                    this.giveupLoadByName(info.name)
                }
            }
        } else {
            val.split('|').forEach(m => {
                this.clean(m)
                this.giveupLoadByName(m)
            })
        }
    }

    // 关闭pnl根据模块
    public cleanByMod(mod: string) {
        this.caches.forEach(m => m.mod === mod && this.clean(m))
    }

    // 清理所有未打开的Pnl
    public cleanAllUnused() {
        this.caches.forEach(m => !m.getActive() && this.clean(m))
        this.cleanLoadQueue()
    }

    // 清理加载队列
    private cleanLoadQueue() {
        while (this.loadQueues.length > 0) {
            loader.giveupLoad(this.loadQueues.shift().url)
        }
    }

    // 放弃加载
    public giveupLoadByName(name: string) {
        const data = this.loadQueues.remove('name', name)
        data && loader.giveupLoad(data.url)
    }

    // 放弃当前加载
    public giveupLoadById(id: number) {
        const data = this.loadQueues.remove('id', id)
        data && loader.giveupLoad(data.url)
    }
}