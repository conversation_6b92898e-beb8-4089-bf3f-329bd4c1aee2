import BaseWindCtrl from "../base/BaseWindCtrl"
import CoreEventType from "../event/CoreEventType"
import { loader } from "../utils/ResLoader"

export default class WindCtrlMgr {

    public node: cc.Node = null

    public currWind: BaseWindCtrl = null
    public nextWindName: string = null
    public preWindName: string = null
    private nextWind: BaseWindCtrl = null
    private caches: Map<string, cc.Node> = new Map<string, cc.Node>()

    // 清理缓存中的wind
    public cleanCacheWind() {
        this.caches.forEach((node, key) => {
            node.getComponent(`${ut.initialUpperCase(key)}WindCtrl`).__clean()
            node.destroy()
            // 释放临时资源
            // assetsMgr.releaseTempResByTag(key)
            // 最后释放场景
            loader.releaseRes(`view/${key}/${ut.initialUpperCase(key)}Wind`, cc.Prefab)
        })
        this.caches.clear()
    }

    // 加载wind
    private async load(key: string, progress?: ProcessCallback) {
        const pfb: cc.Prefab = await loader.loadRes(`view/${key}/${ut.initialUpperCase(key)}Wind`, cc.Prefab, progress)
        if (!pfb) {
            return twlog.error('加载场景出错')
        }
        // 音效
        await audioMgr.loadByMod(key);
        return this.ready(cc.instantiate(pfb), key)
    }

    private putWind(wind: BaseWindCtrl, next: BaseWindCtrl) {
        // 释放音效
        audioMgr.releaseByMod(wind.key)
        // 释放所有pnl
        eventCenter.emit(CoreEventType.CLOSE_ALL_PNL)
        // 先调用离开
        wind.__leave()
        // 释放场景
        if (wind.isClean) {
            wind.__clean()
            wind.node.destroy()
            // 释放临时资源
            assetsMgr.releaseTempResByTag(wind.key)
            // 最后释放场景
            loader.releaseRes(`view/${wind.key}/${ut.initialUpperCase(wind.key)}Wind`, cc.Prefab)
        } else {
            wind.node.parent = null
            this.caches.set(wind.key, wind.node)
        }
    }

    // 准备场景
    private async ready(it: cc.Node, key: string) {
        const className = `${ut.initialUpperCase(key)}WindCtrl`
        if (!cc.js.getClassByName(className)) {
            twlog.error('load wind error! not found class ' + className)
            return Promise.resolve()
        }
        let wind = it.getComponent(className)
        if (!wind) {
            wind = it.addComponent(className)
        }
        if (!wind || !(wind instanceof BaseWindCtrl)) {
            twlog.error('load wind error! not found class ' + className)
            return Promise.resolve()
        }
        it.parent = this.node
        this.nextWind = wind
        this.nextWind.key = key
        this.nextWind.node.zIndex = 0
        // 这里检查一下 是否还没有加载属性
        if (!this.nextWind._isLoadProperty) {
            twlog.error('load wind error! not load property. at=' + className)
            this.nextWind.loadProperty()
        }
        this.nextWind.setActive(false)
        // 如果是从缓存里面取的那就不初始化了
        if (this.caches.has(key)) {
            this.caches.delete(key)
        } else {
            // return this.nextWind.__create()
        }
    }

    // 显示场景
    public show(...params: any) {
        if (!this.nextWind) {
            return
        }
        // 关闭当前场景
        this.currWind && this.putWind(this.currWind, this.nextWind)
        // gc
        cc.sys.garbageCollect()
        // 进入下个场景
        this.currWind = this.nextWind
        this.nextWind = null
        this.nextWindName = null
        this.currWind.setActive(true)
        this.currWind.node.zIndex = 10
        this.currWind.__enter(...params)
        // 发送进入场景事件
        eventCenter.emit(CoreEventType.WIND_ENTER, this.currWind)
    }

    // 预加载场景
    public async preLoad(name: string, progress: ProcessCallback, complete: Function) {
        if ((this.currWind && this.currWind.key === name) || (this.nextWind && this.nextWind.key === name)) {
            progress && progress(1, 1)
            complete && complete()
            return // 如果已经有了直接返回
        }
        let _done: number = 0, _total: number = 1
        await this.load(name, (done, total) => {
            _done = done
            _total = total + 1
            progress && progress(_done, _total)
        })
        await this.nextWind.__create()
        _done = _total
        progress && progress(_done, _total)
        complete && complete()
    }

    // 加载一个场景
    public async goto(name: string, ...params: any) {
        // 是否已经打开了
        if (this.currWind && this.currWind.key === name) {
            // return this.currWind.__enter(...params)
            return this.reload()
        }
        if (this.nextWindName == name) return
        this.nextWindName = name
        // 是否已经预加载了
        if (this.nextWind && this.nextWind.key === name) {
            return this.show(...params)
        }
        mc.lockTouch(true)
        let it = this.caches.get(name) //是否有缓存

        //这里先暂时注释掉
        // audioMgr.stopBGM()

        //await eventCenter.get(CoreEventType.LOAD_BEGIN_WIND);
        let info = { cb: (done, tot) => { } }
        let progress = (done, tot) => {
            info.cb(done, tot)
        }
        eventCenter.emit(CoreEventType.LOAD_BEGIN_WIND, info);
        it ? await this.ready(it, name) : await this.load(name, progress);
        let preWindName = this.currWind?.key
        this.currWind && this.putWind(this.currWind, this.nextWind)
        this.preWindName = preWindName
        this.currWind = null
        await this.nextWind.__create(...params)
        this.show(...params);
        progress(1, 1)
        await ut.wait(0.5) //hack
        eventCenter.emit(CoreEventType.LOAD_END_WIND);
        mc.lockTouch(false)
    }

    public async reload() {
        mc.lockTouch(true)
        await eventCenter.get(CoreEventType.LOAD_BEGIN_WIND);
        this.currWind && this.putWind(this.currWind, this.nextWind)
        await this.load(this.currWind.key)
        await this.nextWind.__create()
        this.currWind = null
        this.show()
        eventCenter.emit(CoreEventType.LOAD_END_WIND);
        mc.lockTouch(false)
    }

    public activeWind(active: boolean) {
        let wind = this.currWind
        wind.setActive(active)
        if (active) {
            this.caches.delete(wind.key)
        }
        else {
            this.caches.set(wind.key, wind.node)
        }
    }
}