import { game<PERSON>elper } from '../../script/common/helper/GameHelper'
import ButtonEx from '../component/ButtonEx'
import { loader } from "../utils/ResLoader"

/**
 * 音频管理中心
 */
class AudioMgr {

    private __bgmVolume: number = 1 //背景音乐 音量
    private __sfxVolume: number = 1 //特效音乐 音量
    private __isPause: boolean = false

    private __bgmAudioID: number = -1 //背景音乐 ID
    private __bgmVolumeRatio: number = 1 //背景比例
    private __caches: AudioAsset[] = []

    private __sfxs_infos: any[] = [] //当前播放的特效列表

    private onAudioInterruptionBegin: any = null
    private onAudioInterruptionEnd: any = null

    public init() {
        ButtonEx.DefaultClickPath = 'click';
        if (ut.isMiniGame()) {
            let onAudioInterruptionBegin = () => {
                this.pauseAll();
                (this.__bgmAudioID >= 0) && cc.audioEngine.pause(this.__bgmAudioID)
            }

            let onAudioInterruptionEnd = () => {
                this.resumeAll();
                (this.__bgmAudioID >= 0) && cc.audioEngine.resume(this.__bgmAudioID)
            }

            wx.onAudioInterruptionBegin(onAudioInterruptionBegin);
            this.onAudioInterruptionBegin = onAudioInterruptionBegin;

            wx.onAudioInterruptionEnd(onAudioInterruptionEnd);
            this.onAudioInterruptionEnd = onAudioInterruptionEnd;
        }
    }

    public pauseAll() {
        this.__isPause = true
        this.__sfxs_infos.forEach(m => (m.id !== undefined) && cc.audioEngine.pause(m.id))
        if (this.__bgmAudioID >= 0) cc.audioEngine.pause(this.__bgmAudioID)
    }

    public resumeAll() {
        this.__isPause = false
        if (this.__bgmVolume > 0 && this.__bgmAudioID >= 0) {
            cc.audioEngine.resume(this.__bgmAudioID)
        }
        if (this.__sfxVolume > 0) {
            this.__sfxs_infos.forEach(m => (m.id !== undefined) && cc.audioEngine.resume(m.id))
        }
    }

    public stopAll() {
        cc.audioEngine.stopAll()
    }

    public get bgmVolume() {
        return this.__bgmVolume
    }
    public set bgmVolume(val: number) {
        this.__bgmVolume = val
        gameHelper.setting.music = val;
        if (this.__bgmAudioID >= 0) {
            if (val <= 0) {
                cc.audioEngine.pause(this.__bgmAudioID)
            } else {
                cc.audioEngine.resume(this.__bgmAudioID)
            }
            cc.audioEngine.setVolume(this.__bgmAudioID, val * this.__bgmVolumeRatio)
        }
    }

    public setTempBgmVolume(val: number) {
        if (this.__bgmAudioID >= 0) {
            cc.audioEngine.setVolume(this.__bgmAudioID, val * this.__bgmVolumeRatio)
        }
    }

    public get sfxVolume() {
        return this.__sfxVolume
    }
    public set sfxVolume(val: number) {
        this.__sfxVolume = val
        gameHelper.setting.effect = val;
        this.__sfxs_infos.forEach(m => (m.id !== undefined) && cc.audioEngine.setVolume(m.id, val))
    }

    // 加载单个声音
    public async load(urls: string | string[]) {
        urls = Array.isArray(urls) ? urls : [urls]
        for (let i = 0, l = urls.length; i < l; i++) {
            const asset = await this.__load(urls[i])
            asset && this.__addAudio(asset.mod, asset.url, asset.audio)
        }
    }

    // 预加载    
    public async preload(url: string) {
        const data = await this.__load(url)
        if (data.audio) {
            this.__addAudio(data.mod, data.url, data.audio)
        } else {
            twlog.error('load error url=', url)
        }
    }

    // 加载对于模块的音效
    public async loadByMod(mod?: string) {
        mod = mod || mc.currWindName
        const url = 'sound/' + mod
        const audios: cc.AudioClip[] = await loader.loadResDir(url, cc.AudioClip)
        audios.forEach(audio => this.__addAudio(mod, url + '/' + audio.name, audio))
    }

    // 释放音效
    public release(val: string) {
        let asset = this.__getForCache(val)
        if (asset) {
            this.__caches.remove('url', asset.url)
            if (!this.__sfxs_infos.remove('url', asset.url)) {
                this.__sfxs_infos.remove('url', asset.audio.nativeUrl)
            }
            loader.releaseRes(asset.url, cc.AudioClip)
        }
    }

    // 释放对应mod的音效
    public releaseByMod(mod?: string) {
        mod = mod || mc.currWindName
        for (let i = this.__caches.length - 1; i >= 0; i--) {
            const asset = this.__caches[i]
            if (asset.mod === mod) {
                this.__caches.splice(i, 1)
                if (!this.__sfxs_infos.remove('url', asset.url)) {
                    this.__sfxs_infos.remove('url', asset.audio.nativeUrl)
                }
                loader.releaseAsset(asset.url, cc.AudioClip)
            }
        }
    }

    // 释放所有声音
    public releaseAll() {
        while (this.__caches.length > 0) {
            loader.releaseAsset(this.__caches.pop().url, cc.AudioClip)
        }
        this.__sfxs_infos.length = 0
    }

    // 添加音效
    private __addAudio(mod: string, url: string, audio: cc.AudioClip) {
        if (!this.__caches.some(m => m.url === url)) {
            this.__caches.push({ mod: mod, url: url, audio: audio })
        }
    }

    // 加载
    private async __load(val: string) {
        let [wind, name] = val.split('/')
        let audio: cc.AudioClip = null, mod: string = '', url: string = ''
        if (!name) {// 没有输入模块的情况下 先默认从当前模块找
            mod = mc.currWindName
            url = `sound/${mod}/${wind}`
            loader.error = false
            audio = await loader.loadRes(url, cc.AudioClip)
            loader.error = true
            if (!audio) {// 如果没有就从common里面找
                mod = 'common'
                url = `sound/${mod}/${wind}`
                audio = await loader.loadRes(url, cc.AudioClip)
            }
        } else {
            mod = wind
            url = `sound/${mod}/${name}`
            audio = await loader.loadRes(url, cc.AudioClip)
        }
        return { mod, url, audio }
    }

    // 获取缓存音效
    private __getForCache(val: string) {
        let [wind, name] = val.split('/')
        let asset: AudioAsset = null, url: string = ''
        if (!name) {
            url = `sound/${mc.currWindName}/${wind}`
            asset = this.__caches.find(m => m.url === url)
            if (!asset) {
                url = `sound/common/${wind}`
                asset = this.__caches.find(m => m.url === url)
            }
        } else {
            // url = `sound/${wind}/${name}`
            asset = this.__caches.find(m => m.url === val)
        }
        return asset
    }

    // 获取音效
    private __getAudio(val: string) {
        let audio = assetsMgr.getAudio(val)
        if (audio) {
            return audio
        }
        let asset = this.__getForCache(val)
        return asset ? asset.audio : null
    }

    // 播放音乐
    public playBGM(url: string | cc.AudioClip, volume: number = 1) {
        if (!url) {
            return
        }
        this.stopBGM()
        if (url instanceof cc.AudioClip) {
            return this.__playBGM(url, volume)
        }
        let audio = this.__getAudio(url)
        if (audio) {// 播放
            this.__playBGM(audio, volume)
        } else {
            this.__load(url).then(m => {
                if (m.audio) {
                    this.__addAudio(m.mod, m.url, m.audio)
                    this.__playBGM(m.audio, volume)
                } else {
                    cc.error('playBGM error url=', url)
                }
            })
        }
    }

    private __playBGM(audio: cc.AudioClip, volume: number) {
        this.__bgmVolumeRatio = volume
        this.__bgmAudioID = cc.audioEngine.play(audio, true, this.bgmVolume * this.__bgmVolumeRatio)
        if (this.bgmVolume === 0 || this.__isPause) {
            cc.audioEngine.pause(this.__bgmAudioID)
        }
    }

    public stopBGM() {
        (this.__bgmAudioID >= 0) && cc.audioEngine.stop(this.__bgmAudioID)
    }

    // 播发音效
    public async playSFX(url: string | cc.AudioClip, opts?: { volume: number, startTime: number, loop: boolean, onComplete: Function, tag: string }) {
        if (!url) {
            return -1
        } else if (this.sfxVolume <= 0 && !opts?.loop) {
            return -1
        }
        const tag = opts?.tag || ''
        if (url instanceof cc.AudioClip) {
            this.__sfxs_infos.push({ url: url.nativeUrl, tag: tag })
            return this.__playSFX(url, url.nativeUrl, opts)
        }
        let audio = this.__getAudio(url)
        if (audio) {
            this.__sfxs_infos.push({ url: url, tag: tag })
            return this.__playSFX(audio, url, opts)
        }
        this.__sfxs_infos.push({ url: url, tag: tag })
        let asset = await this.__load(url)
        if (asset) {
            this.__addAudio(asset.mod, asset.url, asset.audio)
            return this.__playSFX(asset.audio, url, opts)
        }
        twlog.error('playSFX error url=', url)
        return -1
    }

    private __playSFX(audio: cc.AudioClip, url: string, opts?: { volume: number, startTime: number, loop: boolean, onComplete: Function, tag: string }) {
        const tag = opts?.tag || ''
        const info = this.__sfxs_infos.find(m => m.url === url && m.tag === tag)
        if (!info) {
            return
        }
        const loop = !!opts?.loop, volume = opts?.volume ?? 1, startTime = opts?.startTime ?? 0, onComplete = opts?.onComplete
        const audioId = info.id = cc.audioEngine.play(audio, loop, this.sfxVolume * volume)
        if (!loop) { //只有不是循环的才会有回调
            cc.audioEngine.setFinishCallback(audioId, () => {
                this.__sfxs_infos.remove('id', audioId)
                onComplete && onComplete()
            })
        }
        if (startTime) {
            cc.audioEngine.setCurrentTime(audioId, startTime)
        }
        if (this.__isPause) {
            cc.audioEngine.pause(audioId)
        }
        return audioId
    }

    public stopSFX(val: number | string | cc.AudioClip, tag: string = '') {
        if (typeof (val) === 'number') {
            cc.audioEngine.stop(val)
            this.__sfxs_infos.remove('id', val)
        } else if (typeof (val) === 'string') {
            this.__sfxs_infos.delete(m => m.url === val && m.tag === tag).forEach(m => cc.audioEngine.stop(m.id))
        } else if (val instanceof cc.AudioClip) {
            this.__sfxs_infos.delete(m => m.url === val.url && m.tag === tag).forEach(m => cc.audioEngine.stop(m.id))
        }
    }

    /**
     * clean
     */
    public clean() {
        if (this.onAudioInterruptionBegin) {
            wx.offAudioInterruptionBegin(this.onAudioInterruptionBegin);
            this.onAudioInterruptionBegin = null;
        }

        if (this.onAudioInterruptionEnd) {
            wx.offAudioInterruptionEnd(this.onAudioInterruptionEnd);
            this.onAudioInterruptionEnd = null;
        }
    }
}

window['audioMgr'] = new AudioMgr();