import { db<PERSON>el<PERSON> } from "../../script/common/helper/DatabaseHelper";
import { gameHelper } from "../../script/common/helper/GameHelper";

const NAMESPACE_LEN = '__data_len'
const NAMESPACE_VER = '__data_ver'
const NAMESPACE_DATE = '__data_date'
const DATA_CLEAR = '__@clearAll__'
const DATA_VERSION = '__@#_version__'
const KEY_COUNT = 5
const ORG_KEY_PREFIX = "__@"//不会上传到服务器
const ORG_SAVE_KEY_PREFIX = "__@#_"//会上传到服务器

// 注意
// 除了setOrgItem接口外，其他存储不支持中文或者特殊符号
// 如果需要保存中文，务必使用b64Decode接口转码

//atob btoa 封装
var b64ch = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
var b64chs = Array.prototype.slice.call(b64ch);
var b64tab = (function (a) {
    var tab = {};
    a.forEach(function (c, i) { return tab[c] = i; });
    return tab;
})(b64chs);
var b64re = /^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;
var _fromCC = String.fromCharCode.bind(String);


// 存储封装
class StorageMgr {
    private version: number = 1;//版本号，从1开始存档发生结构性变化时候才需要上传这个

    private _dataMap: object = null;//数据
    private _keyDataMap: object = {};
    private _dirtyMap: object = {};
    private _saveLazyQue: string[] = [];

    private _dataLenMap: object = {};
    private _tempKeyMap: object = {};
    private _failAry: string[] = [];
    private _saveWorking: boolean = false

    private _objMap: object = {};//数据

    private _saveOrgMap: object = {};

    private __key(index: number | string): string {
        return mc.GameNameSpace + '_' + index
    }

    private __getDataKey(key: string, forSet: boolean, val: any = null): string {
        let retKey = this._keyDataMap[key];
        if (!retKey) {
            if (!forSet && this._tempKeyMap[key]) {
                return null;
            }
            for (let i = 0; i < KEY_COUNT; i++) {
                let dataKey = this.__key(i);
                if (this._dataMap[dataKey][key]) {
                    retKey = dataKey;
                    break;
                }
            }
        }
        if (!retKey) {
            if (!forSet) {
                this._tempKeyMap[key] = true;
                return null;
            }
            let minKey;
            let minLen = -1;
            for (let i = 0; i < KEY_COUNT; i++) {
                let dataKey = this.__key(i);
                let dataLen = this._dataMap[dataKey][NAMESPACE_LEN];
                if (dataLen < minLen || minLen == -1) {
                    minLen = dataLen;
                    minKey = dataKey;
                }
            }
            retKey = minKey;
            this._keyDataMap[key] = retKey;

            let len = val ? this.__getObjLen(val) : 0;
            this._dataMap[retKey][NAMESPACE_LEN] += len;
            this._dataLenMap[key] = len;

            if (this._tempKeyMap[key]) {
                delete this._tempKeyMap[key];
            }
        }

        return retKey;
    }

    private getVersion(): number {
        const val = Number(this.getOrgItem(DATA_VERSION))
        return isNaN(val) ? 0 : val
    }

    private setVersion(version: number) {
        this.setOrgItem(DATA_VERSION, String(version))
    }

    public async init() {
        if (!this._dataMap) {
            let needClear = this.getOrgItem(DATA_CLEAR);
            if (needClear === '1') {
                dbHelper.clear()
            }

            this._dataMap = {}

            //初始化版本号
            if (!this.getVersion()) {
                this.setVersion(this.version)
            }

            let initData = async (keyCount) => {
                for (let i = 0; i < keyCount; i++) {
                    let dataKey = this.__key(i);
                    let value = await this.getItemAsync(dataKey);
                    if (value) {
                        try {
                            this._dataMap[dataKey] = JSON.parse(value);
                        } catch (error) {
                            twlog.upLog.addFilterMsg('storage.__init')
                            twlog.upLog.error(error, dataKey, value)
                            this._dataMap[dataKey] = {};
                            console.error("initParseError", dataKey, value, error)
                        }
                    }

                    if (!this._dataMap[dataKey]) {
                        this._dataMap[dataKey] = {};
                    }

                    for (const key in this._dataMap[dataKey]) {
                        const data = this._dataMap[dataKey][key];
                        this._dataLenMap[key] = this.__getObjLen(data);
                    }

                    this._dataMap[dataKey][NAMESPACE_LEN] = value ? value.length : 0
                    if (!this._dataMap[dataKey][NAMESPACE_VER]) {
                        this._dataMap[dataKey][NAMESPACE_VER] = 0
                    }
                }
            }

            await initData(KEY_COUNT)

            let prefix = mc.GameNameSpace + "_"

            //初始化手动桶
            for (let i = 0; i < localStorage.length; i++) {
                let dataKey = localStorage.key(i)
                if (dataKey.startsWith(prefix)) { //前缀为hotel_开头，且后缀不为数字，则判断为obj类型
                    let [_, suffix] = dataKey.split(prefix)
                    if (isNaN(Number(suffix))) {
                        let value = await this.getItemAsync(dataKey)
                        if (value) {
                            try {
                                value = JSON.parse(value)
                                if (typeof value == "object") {
                                    this._objMap[dataKey] = value
                                }
                            } catch (error) {
                                console.error("initParseError2", dataKey, value, error)
                            }
                        }
                    }
                    else if (dataKey.startsWith(ORG_SAVE_KEY_PREFIX)) {
                        this._saveOrgMap[dataKey] = this.getOrgItem(dataKey)
                    }
                }
            }

            this._updateAuxiliary();
        }
    }

    private __get(key: string) {
        let dataKey = this.__getDataKey(key, false);
        if (!dataKey) {
            return null;
        }
        return this._dataMap[dataKey][key];
    }

    //粗略估计对象长度，为了效率不需要特别精确
    private __getObjLen(object: any) {
        let type = typeof object
        if (type == 'string') {
            return object.length
        }
        else if (type == 'function') {
            return 0
        }
        else if (type == 'object') {
            let count = 0
            for (const key in object) {
                if (Object.prototype.hasOwnProperty.call(object, key)) {
                    const element = object[key];
                    count += key.length + this.__getObjLen(element)
                }
            }
            return count
        }
        else {
            return 2
        }
    }

    private __set(key: string, val: any) {
        let dataKey = this.__getDataKey(key, true, val);
        this._dataMap[dataKey][key] = val;
        this._dataMap[dataKey][NAMESPACE_VER]++
        if (!this._dataMap[dataKey][NAMESPACE_DATE]) {
            this._dataMap[dataKey][NAMESPACE_DATE] = gameHelper.now()
        }

        if (!this._dirtyMap[dataKey]) {
            this._dirtyMap[dataKey] = true;
            this._saveLazyQue.push(dataKey);
        }
    }

    private _updateAuxiliary() {
        gameHelper.setInterval(() => {
            this.saveLazy();
        }, 100)
    }

    //数据格式有强要求 支持中文特殊符号
    public setOrgItem(dataKey: string, dataValue: string) {
        if (!dataKey.startsWith('__@')) {
            throw new Error('need start with __')
        }
        try {
            localStorage.setItem(dataKey, dataValue);

            if (dataKey.startsWith(ORG_SAVE_KEY_PREFIX)) {
                this._saveOrgMap[dataKey] = dataValue
            }
        } catch (error) {
            twlog.upLog.addFilterMsg('storage.setOrgItem')
            twlog.upLog.error(error)
        }
    }

    private setItem(dataKey: string, dataValue: string) {
        try {
            if (this._dataMap && this._dataMap[dataKey]) {
                this._dataMap[dataKey][NAMESPACE_LEN] = dataValue.length
            }
            // console.warn("setItem", dataKey, dataValue, this.compress(dataValue))
            localStorage.setItem(dataKey, this.compress(dataValue));
        } catch (error) {
            console.error("setItemError", dataKey, dataValue, error)
            twlog.upLog.addFilterMsg('storage.setItem')
            twlog.upLog.error(error)

            this.doSvaeFail(dataKey, error)
        }
    }

    private async setItemAsync(dataKey: string, dataValue: string): Promise<void> {
        // console.log("setItemAsync start")
        if (this._dataMap && this._dataMap[dataKey]) {
            this._dataMap[dataKey][NAMESPACE_LEN] = dataValue.length
        }
        return new Promise<void>(resolve => {
            wx.setStorage({
                key: dataKey,
                data: this.compress(dataValue),
                encrypt: true,
                success: () => {
                    resolve()
                },
                fail: (error) => {
                    twlog.upLog.addFilterMsg('storage.setItemAsync')
                    twlog.upLog.error(error)
                    console.log("setItemAsync fail", error)

                    this.doSvaeFail(dataKey, error)
                    resolve()
                }
            })
        })
    }

    private doSvaeFail(dataKey: string, error: any) {
        this._failAry.push(dataKey)
        if (typeof error === 'object') {
            error = error.error || error.message
        }
        eventCenter.emit('STORAGE_SAVE_FAIL', error || 'unknow')
    }

    private getItem(dataKey: string) {
        let value = null;
        try {
            value = localStorage.getItem(dataKey)
        } catch (error) {
            console.error("getItem error", error)

            twlog.upLog.addFilterMsg('storage.getFail')
            twlog.upLog.error(error)
        }

        if (value === "") value = null

        return this.decompress(value);
    }

    private async getItemAsync(dataKey: string): Promise<any> {
        if (ut.isMiniGame()) {
            return new Promise<void>(resolve => {
                wx.getStorage({
                    key: dataKey,
                    encrypt: true,
                    success: (res) => {
                        let value = res.data
                        if (value === "") value = null
                        resolve(this.decompress(value))
                    },
                    fail: (_error) => {
                        resolve(null)
                    }
                })
            })
        }
        else {
            return this.getItem(dataKey)
        }
    }

    public getOrgItem(dataKey: string) {
        let value;
        try {
            value = localStorage.getItem(dataKey)
        } catch (error) {
            value = ""

            twlog.upLog.addFilterMsg('storage.getOrgItem')
            twlog.upLog.error(error)
        }

        return value
    }

    private removeItem(dataKey: string) {
        localStorage.removeItem(dataKey);
    }

    private handleFailSave() {
        if (this._failAry.length > 0) {
            this._failAry.forEach(dataKey => {
                this._dirtyMap[dataKey] = true
                this._saveLazyQue.push(dataKey)
            });

            this._failAry.length = 0
        }
    }

    public save() {
        if (!this._dataMap) {
            return
        }
        let isDirty = false;
        for (const dataKey in this._dirtyMap) {
            isDirty = true;
            if (this._objMap[dataKey]) {
                this.setItem(dataKey, JSON.stringify(this._objMap[dataKey]))
            }
            else {
                this.setItem(dataKey, JSON.stringify(this._dataMap[dataKey]))
            }
        }
        if (isDirty) {
            this._dirtyMap = {};
            this._saveLazyQue.length = 0;
            this.handleFailSave()
        }
    }

    private saveLazy() {
        if (!this._dataMap || this._saveWorking) {
            return
        }
        this._saveWorking = true;

        if (ut.isMiniGame()) {
            (async () => {
                let todoList = []
                while (this._saveLazyQue.length > 0) {
                    let dataKey = this._saveLazyQue.pop();
                    todoList.push(dataKey)
                }

                let list = []
                let lenCount = 0
                while (todoList.length > 0) {
                    let dataKey = todoList.pop();
                    let dataValue
                    if (this._objMap[dataKey]) {
                        dataValue = JSON.stringify(this._objMap[dataKey])
                    }
                    else {
                        dataValue = JSON.stringify(this._dataMap[dataKey])
                    }
                    //如果没被保存
                    if (this._dirtyMap[dataKey]) {
                        list.push(this.setItemAsync(dataKey, dataValue))
                        this._dirtyMap[dataKey] = false;
                        lenCount += dataValue.length
                        if (lenCount >= 3000) {
                            lenCount = 0

                            if (todoList.length > 0) {
                                await ut.waitNextFrame()
                            }
                        }
                    }
                }

                if (list.length > 0) {
                    await Promise.all(list)
                }

                this._saveWorking = false
                this.handleFailSave()
            })()
        }
        else {
            this.syncSaveLazy()

            this._saveWorking = false
            this.handleFailSave()
        }
    }

    public syncSaveLazy() {
        if (!this._dataMap) {
            return
        }
        while (this._saveLazyQue.length > 0) {
            let dataKey = this._saveLazyQue.pop();
            if (this._objMap[dataKey]) {
                this.setItem(dataKey, JSON.stringify(this._objMap[dataKey]))
            }
            else {
                this.setItem(dataKey, JSON.stringify(this._dataMap[dataKey]))
            }

            this._dirtyMap[dataKey] = false;
        }
    }

    public loadString(key: string) {
        const val = this.__get(key)
        return val;
    }
    public saveString(key: string, val: string) {
        this.__set(key, val)
    }

    public loadNumber(key: string) {
        const val = this.__get(key)
        return val ? Number(val) : null
    }
    public saveNumber(key: string, val: number) {
        this.__set(key, String(val))
    }

    public changeNumber(key: string, val: number) {
        let num = this.loadNumber(key) || 0
        this.saveNumber(key, val + num)
    }

    public loadBool(key: string): boolean {
        const val = this.__get(key)
        return val ? val === '1' : null
    }
    public saveBool(key: string, val: boolean) {
        this.__set(key, val ? '1' : '0')
    }

    public loadObject(key: string): object {
        let dataKey = this.__key(key)
        return this._objMap[dataKey] && this._objMap[dataKey][key]
    }

    public saveObject(key: string, val: object) {
        // console.log("@@@saveObject", key, val)

        let dataKey = this.__key(key);
        if (!this._objMap[dataKey]) {
            this._objMap[dataKey] = {}
        }
        if (!this._objMap[dataKey][NAMESPACE_VER]) {
            this._objMap[dataKey][NAMESPACE_VER] = 0
        }
        if (!this._objMap[dataKey][NAMESPACE_DATE]) {
            this._objMap[dataKey][NAMESPACE_DATE] = gameHelper.now()
        }
        this._objMap[dataKey][key] = val;
        this._objMap[dataKey][NAMESPACE_VER]++

        if (!this._dirtyMap[dataKey]) {
            this._dirtyMap[dataKey] = true;
            this._saveLazyQue.push(dataKey);
        }
    }

    public reset() {
        this._dataMap = null
        this._objMap = {}
        this._keyDataMap = {}
        this._dirtyMap = {}
        this._dataLenMap = {}
        this._tempKeyMap = {}
        this._objMap = {}
        this._saveLazyQue.length = 0
        this._failAry.length = 0
        this._saveWorking = false
    }

    public clear() {
        localStorage.clear()
        this.reset()
    }

    public getStorageInfo() {
        let infoMap = {}

        function gao(mapObj) {
            for (const key in mapObj) {
                infoMap[key] = mapObj[key]
            }
        }

        gao(this._dataMap)
        gao(this._objMap)
        gao(this._saveOrgMap)

        return infoMap;
    }

    public getFromRecord(_keys: string, record) {
        let keys = _keys.split(".");
        for (let dataKey in record) {
            if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
            let data = record[dataKey];//数据块
            let curValue = data;
            let found = false;
            for (let i = 0; i < keys.length; i++) {
                let key = keys[i];
                if (curValue[key] !== undefined) {
                    found = true;
                    if (i == 0 && typeof curValue[key] == 'string') {
                        try {
                            curValue = JSON.parse(curValue[key]);
                        } catch (error) {
                            curValue = curValue[key]
                        }
                    }
                    else {
                        curValue = curValue[key];
                    }
                }
                else {
                    found = false;
                    break;
                }
            }
            if (found) {
                return curValue;
            }
        }
    }

    //todo 需要自己实现关键key
    // public getCheckInfoFromRecord(record?) {
    //     record = record || this.getStorageInfo()
    //     let checkInfo: any = {}
    //     let dataMap = {}
    //     for (let dataKey in record) {
    //         let data = record[dataKey]
    //         if (dataKey.startsWith(ORG_KEY_PREFIX) && typeof data != 'object') continue
    //         dataMap[dataKey] = {}
    //         dataMap[dataKey][NAMESPACE_VER] = data[NAMESPACE_VER]
    //         dataMap[dataKey][NAMESPACE_DATE] = data[NAMESPACE_DATE]
    //     }
    //     checkInfo.heart = this.getFromRecord("global.heart", record) || 0
    //     checkInfo.playTime = this.getFromRecord("user.playTime", record) || 0
    //     checkInfo.dataMap = dataMap
    //     return checkInfo
    // }

    public async setStorageInfo(record, serverVer) {
        try {
            this.setOrgItem(DATA_CLEAR, '1');

            let info = JSON.parse(record)

            for (let key in info) {
                if (key.startsWith(ORG_KEY_PREFIX)) {
                    this.setOrgItem(key, info[key]);
                }
                else {
                    this.setItem(key, JSON.stringify(info[key]));
                }
            }

            this.removeItem(DATA_CLEAR)

            this.reset()
            await this.init()

            return true;
        } catch (error) {
            this.removeItem(DATA_CLEAR)
            console.error("setStorageInfo", error)
            console.error(record)
            return false;
        }
    }

    public removeData(key: string, dataKey?: string) {
        dataKey = dataKey || this.__getDataKey(key, false);
        if (!dataKey) {
            return;
        }
        if (this._dataMap[dataKey] != null) {
            delete this._dataMap[dataKey][key];

            this._dirtyMap[dataKey] = true;

            if (this._tempKeyMap[key]) {
                delete this._tempKeyMap[key];
            }
            if (this._dataLenMap[key]) {
                this._dataMap[dataKey][NAMESPACE_LEN] -= this._dataLenMap[key];
                delete this._dataLenMap[key];
            }
        }
    }

    //如果存档有使用中文，必须转义
    public b64Encode(str: string): string {
        return this.btoaPolyfill(encodeURIComponent(str));
    }
    public b64Decode(str: string): string {
        return decodeURIComponent(this.atobPolyfill(str));
    }

    private compress(s) {
        return s
        if (s == null) return null

        try {
            var dict = {};
            var data = (s + "").split("");
            var out = [];
            var currChar;
            var phrase = data[0];
            var code = 256;
            for (var i = 1; i < data.length; i++) {
                currChar = data[i];
                if (dict[phrase + currChar] != null) {
                    phrase += currChar;
                }
                else {
                    out.push(phrase.length > 1 ? dict[phrase] : phrase.charCodeAt(0));
                    dict[phrase + currChar] = code;
                    code++;
                    phrase = currChar;
                }
            }
            out.push(phrase.length > 1 ? dict[phrase] : phrase.charCodeAt(0));
            for (var i = 0; i < out.length; i++) {
                out[i] = String.fromCharCode(out[i]);
            }
            return out.join("");
        } catch (error) {
            twlog.upLog.addFilterMsg('storage.decompress')
            twlog.upLog.error("decompress error", error)
            return s
        }
    }

    private decompress(s) {
        return s
        if (s == null) return null

        try {
            var dict = {};
            var data = (s + "").split("");
            var currChar = data[0];
            var oldPhrase = currChar;
            var out = [currChar];
            var code = 256;
            var phrase;
            for (var i = 1; i < data.length; i++) {
                var currCode = data[i].charCodeAt(0);
                if (currCode < 256) {
                    phrase = data[i];
                }
                else {
                    phrase = dict[currCode] ? dict[currCode] : (oldPhrase + currChar);
                }
                out.push(phrase);
                currChar = phrase.charAt(0);
                dict[code] = oldPhrase + currChar;
                code++;
                oldPhrase = phrase;
            }
            return out.join("");
        } catch (error) {
            twlog.upLog.addFilterMsg('storage.decompress')
            twlog.upLog.error("decompress error", error)
            return s
        }
    }

    private btoaPolyfill(bin: string) {
        var u32, c0, c1, c2, asc = '';
        var pad = bin.length % 3;
        for (var i = 0; i < bin.length;) {
            if ((c0 = bin.charCodeAt(i++)) > 255 ||
                (c1 = bin.charCodeAt(i++)) > 255 ||
                (c2 = bin.charCodeAt(i++)) > 255)
                throw new TypeError('invalid character found');
            u32 = (c0 << 16) | (c1 << 8) | c2;
            asc += b64chs[u32 >> 18 & 63]
                + b64chs[u32 >> 12 & 63]
                + b64chs[u32 >> 6 & 63]
                + b64chs[u32 & 63];
        }
        return pad ? asc.slice(0, pad - 3) + "===".substring(pad) : asc;
    }

    private atobPolyfill(asc: string) {
        // console.log('polyfilled');
        asc = asc.replace(/\s+/g, '');
        if (!b64re.test(asc))
            throw new TypeError('malformed base64.');
        asc += '=='.slice(2 - (asc.length & 3));
        var u24, bin = '', r1, r2;
        for (var i = 0; i < asc.length;) {
            u24 = b64tab[asc.charAt(i++)] << 18
                | b64tab[asc.charAt(i++)] << 12
                | (r1 = b64tab[asc.charAt(i++)]) << 6
                | (r2 = b64tab[asc.charAt(i++)]);
            bin += r1 === 64 ? _fromCC(u24 >> 16 & 255)
                : r2 === 64 ? _fromCC(u24 >> 16 & 255, u24 >> 8 & 255)
                    : _fromCC(u24 >> 16 & 255, u24 >> 8 & 255, u24 & 255);
        }
        return bin;
    }
}

// @ts-ignore
window['storageMgr'] = new StorageMgr()