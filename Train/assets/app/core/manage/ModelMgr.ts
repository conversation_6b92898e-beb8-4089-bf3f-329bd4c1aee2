import BaseModel from "../base/BaseModel"

// 数据模型管理
export default class ModelMgr {

    private models: Map<string, BaseModel> = new Map<string, BaseModel>()
    private priorityMap: any = {}

    // 静态添加模型
    public init(map: any, priorityMap?) {
        this.priorityMap = priorityMap
        for (let key in map) {
            this.__add(new map[key](key))
        }
    }

    public create() {
        this.models.forEach(m => m.__create())
    }

    private __add(model: BaseModel) {
        if (!model.type) {
            return twlog.error('model type=null!')
        }
        if (this.models.has(model.type)) {
            return twlog.error('出现相同的model type=' + model.type)
        }
        this.models.set(model.type, model)
    }

    // 添加模型
    public add(...params: BaseModel[]) {
        params.forEach(m => this.__add(m))
    }

    // 获取模型
    public get<T>(key: string): T {
        let model: any = this.models.get(key)
        if (!model) {
            twlog.error('get model error! not found ' + key)
        }
        return model
    }

    // 替换模型
    public reset(model: BaseModel) {
        if (!model.type) {
            return twlog.error('model type=null!')
        }
        const it = this.models.get(model.type)
        if (it) {
            it.__clean()
        }
        this.models.set(model.type, model)
    }

    public getModels() {
        return this.models
    }

    public initModels() {
        let list = []
        this.models.forEach((model, name) => {
            list.push({model, name})
        })
        list.sort((a, b)=>{
            let pa = this.priorityMap[a.name]
            let pb = this.priorityMap[b.name]
            return pb - pa
        })
        for (let {model} of list) {
            model.init()
        }
        for (let {model} of list) {
            model.onEnable()
        }
    }
}