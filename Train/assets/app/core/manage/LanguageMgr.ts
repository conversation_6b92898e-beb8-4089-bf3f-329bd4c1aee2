import { startHelper } from "../../../startScene/StartHelper"
import CoreEventType from "../event/CoreEventType"

/**
 * 语言管理
 */
class LanguageMgr {

    private _change: boolean = false //是否实时切换
    private _lang: string = '' //当前语言

    public init(lang: string, change: boolean = true) {
        this._lang = lang
        this._change = !!change
    }

    public get change() { return this._change }

    public get lang() { return this._lang }
    public set lang(val: string) {
        this._lang = val
        localStorage.setItem(startHelper.getSaveLangKey(), val)
        eventCenter.emit(CoreEventType.LANGUAGE_CHANGED, this._lang)
    }
}

export const languageMgr = new LanguageMgr()

if (CC_DEV) {
    window["languageMgr"] = languageMgr
}