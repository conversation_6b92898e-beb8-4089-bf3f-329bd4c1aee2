import {loader} from "../utils/ResLoader";
import {languageMgr} from "../manage/LanguageMgr";
import {startHelper} from "../../../startScene/StartHelper";
import CoreEventType from "../event/CoreEventType";
import {localConfig} from "../../script/common/LocalConfig";
import CryptoJS from "../../script/common/crypto/CryptoJS";

/**
 * 资源管理
 *
 * 目录结构
 *
 * common
 *      image
 *      json
 *      prefab
 *      sound
 * tmp
 *      image
 *      prefab
 *      sound
 */
type TempAssetQueueInfo = {
    cb: Function;
    tag: string;
}

class AssetsMgr {

    private images = new Map<string, cc.SpriteFrame>()// 永久存在图片
    private jsons = new Map<string, JsonConfData<any>>()// 公共配置文件
    private prefabs = new Map<string, cc.Prefab>()// 公共预制体
    private audios = new Map<string, cc.AudioClip>()// 公共声音
    private materials = new Map<string, cc.Material>()// 公共材质
    private fonts = new Map<string, cc.Font>()// 公共字体
    private anims = new Map<string, cc.AnimationClip>() //公共动画资源

    private temps = new Map<string, TempAssetData>()// 临时缓存资源
    private loadTempQueueMap = new Map<string, TempAssetQueueInfo[]>() //加载队列 防止一个资源同时被多个加载

    private __onProgessCallback: Function = null
    private __totalProgess: number = 0
    private __lastProgess: any = null
    private __tempProgess: any = null
    private __curPercent: number = 0

    public localConfigMd5: {[key:string]:string} = {}

    public async init(onProgess?: Function) {
        this.__onProgessCallback = onProgess
        this.temps.clear()
        this.debug = true
        this.__curPercent = 0
        this.__lastProgess = {}
        this.__tempProgess = {}

        const arr = [this.loadJsons(), this.loadImages(), this.loadPrefab(), this.loadFont(), this.loadAudio(), this.loadMaterial(), this.loadAnim()]
        this.__totalProgess = 1 / arr.length
        await Promise.all(arr)
        if (this.__onProgessCallback) {
            this.__onProgessCallback(1)
        }
        this.debug = true
        this.__onProgessCallback = null
        this.__tempProgess = null
        eventCenter.on(CoreEventType.LANGUAGE_CHANGED, this.onLanguageChanged, this)
    }

    public set debug(val: boolean) { loader.debug = val }
    public get debug() { return loader.debug }

    // 语言切换
    private onLanguageChanged(lang: string) {
        //需要重新加载语言包
        //todo 需要加一个标记，表示是否完成
        //需要清空已经加载好的资源 todo需要测试
        // this.fonts.forEach(it => loader.releaseRes(it.url, cc.Font))
        // this.loadFont()
        //todo 完成标记
    }

    // 初始化的加载进度
    private onInitLoadProgess(key: string, done: number, total: number) {
        if (!this.__onProgessCallback) {
            return
        }
        let pre = this.__tempProgess[key]
        if (pre === undefined) {
            pre = this.__tempProgess[key] = 0
        }
        let p = done / total
        if (this.__lastProgess[key] && this.__lastProgess[key] >= p) {
            return
        }
        this.__lastProgess[key] = p
        const curr = p * this.__totalProgess
        const diff = Math.max(curr - pre, 0)
        this.__tempProgess[key] = curr
        this.__curPercent += diff
        if (this.__curPercent > 1) {
            this.__curPercent = 1
        }
        this.__onProgessCallback(this.__curPercent)
    }

    // 初始json文件配置
    private async loadJsons() {
        this.jsons.clear()
        let jsonPath = "common/json/"
        let jsons = loader.getDirWithPath(jsonPath, cc.JsonAsset)
        let done = 0
        await ut.promiseMap(jsons, async ({ path }) => {
            let asset: cc.JsonAsset = await loader.load(path)
            this.onInitLoadProgess('json', ++done, jsons.length)

            if (asset) {
                let m = asset
                if (!localConfig.release){
                    let end = path.lastIndexOf("/")
                    if (end >= 0) end +=1
                    const name = path.substring(end) + ".json"
                    let jsonStr = JSON.stringify(m.json,null, 2)
                    this.localConfigMd5[name] = CryptoJS.MD5(jsonStr).toString()
                }
                const dataIdMap = {}
                if (m.json.length > 0) {
                    if (m.json[0]['id'] !== undefined) {
                        m.json.forEach((m) => {
                            dataIdMap[m.id] = m;
                        })
                    }
                    else if (m.json[0]['key'] !== undefined) {
                        let map = {}
                        m.json.forEach((m) => {
                            if (!map[m.key]) map[m.key] = 0
                            map[m.key]++
                            let id = m.key + "_" + map[m.key]
                            m.id = id
                            dataIdMap[id] = m
                        })
                    }
                }
                let [_, name] = path.split(jsonPath)
                if (name.includes("lang/")) { //临时这么处理
                    name = name.split("lang/")[1]
                }
                this.jsons.set(name, {
                    datas: m.json,
                    // @ts-ignore
                    dataIdMap: dataIdMap,
                    getById(id: string | number) {
                        return this.dataIdMap[id]
                    },
                    get(key: string, value: any) {
                        return this.datas.filter(m => m[key] === value)
                    },
                    set(key: string, value: any) {
                        if (this.dataIdMap[key]) return
                        this.dataIdMap[key] = value
                        this.datas.push(value)
                    }
                })
            }
        })
    }

    // 载入AppFont资源
    private async initAppFontRes() {
        let bd = cc.resources
        // 载入Font
        let loadFont = new Promise(resolve => {
            resolve(null)
            // let lang = startHelper.getLocalLang()
            // bd.loadDir('font/' + lang, cc.Font,
            //     (finish: number, total: number) => {
            //         let lastPercent = 0
            //         let percent = Math.min(finish / total, 1) * 0.05
            //         if (percent > lastPercent) {
            //             lastPercent = percent
            //             this.progress1 = percent
            //         }
            //     },
            //     (err, assets) => {
            //         if (err) {
            //             resolve(false)
            //         } else {
            //             assets.forEach(m => startHelper.setAppFont(m.name, m))
            //             resolve(true)
            //         }
            //     }
            // )
        })
        // 载入Json
        let loadJson = new Promise(resolve => {
            bd.load(
                ['common/json/lang/LanguageLoading'], cc.JsonAsset,
                (finish: number, total: number) => {
                },
                (err, assets: any[]) => {
                    if (err) {
                        resolve(false)
                    } else {
                        assets.forEach(m => startHelper.setAppFontJson(m.name, m))
                        resolve(true)
                    }
                }
            )
        })

        await Promise.all([loadFont, loadJson])
    }

    // 初始通用图片集
    private async loadImages() {
        this.images.clear()
        const assets: cc.SpriteFrame[] = await loader.loadResDir('common/image', cc.SpriteFrame, (done, total) => this.onInitLoadProgess('image', done, total))
        assets.forEach(m => this.images.set(m.name, m))
    }

    // 初始化预制体
    private async loadPrefab() {
        this.prefabs.clear()
        const assets: cc.Prefab[] = await loader.loadResDir('common/prefab', cc.Prefab, (done, total) => {
            this.onInitLoadProgess('prefab', done, total)
        })
        assets.forEach(m => this.prefabs.set(m.name, m))
    }

    // 初始化声音
    private async loadAudio() {
        this.audios.clear()
        const assets: cc.AudioClip[] = await loader.loadResDir('common/sound', cc.AudioClip, (done, total) => this.onInitLoadProgess('sound', done, total))
        assets.forEach(m => this.audios.set(m.name, m))
    }

    // 材质
    private async loadMaterial() {
        this.materials.clear()
        const assets: cc.Material[] = await loader.loadResDir('common/material', cc.Material, (done, total) => this.onInitLoadProgess('material', done, total))
        assets.forEach(m => this.materials.set(m.name, m))
    }

    // 字体
    private async loadFont() {
        this.fonts.clear()
        const assets: cc.Font[] = await loader.loadResDir('common/font/' + languageMgr.lang, cc.Font, (done, total) => this.onInitLoadProgess('font', done, total))
        assets.forEach(m => this.fonts.set(m.name, m))
    }

    private async loadAnim() {
        this.anims.clear()
        const assets: cc.AnimationClip[] = await loader.loadResDir('common/anim', cc.AnimationClip, (done, total) => this.onInitLoadProgess('anim', done, total))
        assets.forEach(m => this.anims.set(m.name, m))
    }

    // 获取图片
    public getImage(key: string): cc.SpriteFrame {
        return this.images.get(key)
    }

    // 获取json配置
    public getJson<T>(key: string) {
        return this.jsons.get(key) as JsonConfData<T>
    }
    public getJsonData<T>(key: string, id: string | number) {
        const cfg = this.checkJsonData<T>(key, id)
        if (!cfg && CC_DEV) cc.warn('no id:', id, 'in json:', key)
        return cfg
    }
    public checkJsonData<T>(key: string, id: string | number) {
        const json = this.getJson<T>(key)
        return json && json.getById(id)
    }

    // 获取预制体
    public getPrefab(key: string): cc.Prefab {
        return this.prefabs.get(key)
    }

    // 获取声音
    public getAudio(key: string): cc.AudioClip {
        return this.audios.get(key)
    }

    // 获取材质
    public getMaterial(key: string): cc.Material {
        return this.materials.get(key)
    }

    // 获取字体
    public getFont(key: string): cc.Font {
        return this.fonts.get(key)
    }

    public getAnim(key: string): cc.AnimationClip {
        return this.anims.get(key)
    }

    // 根据key获取文本
    public lang(key: string, ...params: any[]) {
        if (!key) {
            return ''
        }
        let key2 = this.getLangKey(key)
        let [name, id] = key2.split('.')
        const json = this.getJsonData(name, id) || {}
        const val = json[languageMgr.lang]
        if (val !== undefined) {
            const _params = []
            params.forEach(m => Array.isArray(m) ? _params.pushArr(m) : _params.push(m))
            return ut.stringFormat(val, this.updateParams(_params, languageMgr.lang))
        }
        return key
    }

    public getLangKey(id: string, prefix: string = "LanguageTemplate") {
        if (id.indexOf('.') !== -1) return id
        return `${prefix}.${id}`
    }

    // 刷新参数
    private updateParams(params: any[], lang: string) {
        return params.map(m => {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                const [name, id] = m.split('.')
                const json = this.getJsonData(name, id) || {}
                const val = json[lang]
                return val !== undefined ? val : m
            }
            return m
        })
    }

    // 根据类型获取文件夹名字
    private makeTypeName(type: typeof cc.Asset): string {
        const name = type.prototype['__cid__']
        if (name === 'cc.Prefab') {
            return 'prefab/'
        } else if (name === 'cc.SpriteFrame' || name === 'cc.Texture2D') {
            if (name == 'cc.Texture2D') {
                twlog.error("不再支持 cc.Texture2D，请使用cc.SpriteFrame")
            }
            return 'image/'
        } else if (name === 'cc.AudioClip') {
            return 'sound/'
        }
        else if (name === 'sp.SkeletonData') {
            return 'spine/'
        }
        else if (name == 'cc.AnimationClip') {
            return "anim/"
        }
        return ''
    }

    // 加载临时资源
    public async loadTempRes(name: string, type: typeof cc.Asset, tag?: string, progress?: Function): Promise<any> {
        const it = this.temps.get(name)
        if (it) {
            return this.addTempResRefs(it, tag)
        }
        return new Promise(resolve => {
            const url = name.startsWith('tmp/') ? name : 'tmp/' + this.makeTypeName(type) + name
            // 放入队列
            const queues = this.loadTempQueueMap.get(url)
            if (queues) {
                return queues.push({ cb: resolve, tag: tag })
            }
            this.loadTempQueueMap.set(url, [{ cb: resolve, tag: tag }])
            // 开始加载
            loader.load(url, type, progress).then(asset => {
                const queues2 = this.loadTempQueueMap.get(url)
                if (!queues2) {
                    return loader.printError('加载错误 not funcs? url=' + url)
                } else {
                    queues2.forEach(m => {
                        if (asset) {
                            this.addTempRes(name, url, asset, type, m.tag).then(() => m.cb(asset))
                        } else {
                            m.cb(asset)
                        }
                    })
                }
                this.loadTempQueueMap.delete(url)
            })
        })
    }

    // 加载临时资源 文件夹
    public async loadTempRseDir(key: string, type: typeof cc.Asset, tag: cc.Component | string = ''): Promise<any[]> {
        if (typeof tag != 'string') {
            tag = tag.uuid || ''
        }
        tag = tag as string
        const head = 'tmp/' + this.makeTypeName(type)
        const assets = await loader.loadDir(head + key, type)
        this.debug = false
        for (let i = 0, l = assets.length; i < l; i++) {
            const asset: cc.Asset = assets[i]
            const name = key + '/' + asset.name
            this.addTempRes(name, head + name, asset, type, tag)
        }
        this.debug = true
        // if (!cc.isValid(context, true) || !cc.isValid(context.node, true)) {
        //     twlog.error(head, "loadTempRseDir cancel")
        //     return new Promise(resolve => { })
        // }
        return assets
    }

    // 加载远程图片
    public async loadRemote(url: string, ext: string, tag?: string): Promise<cc.SpriteFrame> {
        if (!url) {
            return Promise.resolve(null)
        }
        let it = this.temps.get(url)
        if (it) {
            return new cc.SpriteFrame(this.addTempResRefs(it, tag) as cc.Texture2D)
        }
        const asset = await loader.loadRemote(url, ext)
        if (!asset) {
            return Promise.resolve(null)
        }
        if (!(asset instanceof cc.Texture2D)) {
            loader.printError('加载错误 格式不对 url=' + url)
            return Promise.resolve(null)
        }
        // 添加临时资源
        await this.addTempRes(url, url, asset, cc.Texture2D, tag)
        return new cc.SpriteFrame(asset)
    }

    // 添加临时资源
    private async addTempRes(name: string, url: string, asset: cc.Asset, type: typeof cc.Asset, tag: string) {
        let it = this.temps.get(name)
        if (!it) {
            // 计入缓存
            it = { name: name, asset: asset, url: url, type: type, tagCount: 0, refs: {} }
            this.temps.set(name, it)

            this.addTempResRefs(it, tag)

            // 添加一次资源本身的引用计数
            await loader.loadResComplete(0, url, asset)
        }
        else {
            this.addTempResRefs(it, tag)
        }
    }

    // 添加临时资源引用
    private addTempResRefs(it: TempAssetData, tag: string) {
        tag = tag || ''
        if (it.refs[tag]) {
            it.refs[tag]++
        }
        else {
            it.tagCount++
            it.refs[tag] = 1
        }

        // 打印
        // loader.printInfo(`loadTempRes -> ${it.url} [${it.refs[tag]}] <${tag}>`)
        return it.asset
    }

    // 删除临时资源引用
    private removeTempResRef(it: TempAssetData, tag: string) {
        if (it.refs[tag] !== undefined) {
            delete it.refs[tag]
            it.tagCount--

            if (it.tagCount <= 0) {
                this.temps.delete(it.name)
                loader.releaseRes(it.url, it.type)
            } else {
                // loader.printInfo(`removeTempRes -> ${it.url} [0] <${tag}>`)
            }
        }
    }

    // 释放临时资源
    public releaseTempRes(name: string, tag?: string) {
        const it = this.temps.get(name)
        if (!it) {
            return loader.printInfo('try release null res[' + name + ']')
        }
        tag = tag || ''
        let ref = it.refs[tag]
        if (!ref) {
            // loader.printError('release error not ref[' + tag + '] at ' + it.url)
            return
        }
        ref = it.refs[tag] = ref - 1
        if (ref <= 0) {
            this.removeTempResRef(it, tag)
        } else {
            // loader.printInfo(`removeTempRes -> ${it.url} [${ref}] <${tag}>`)
        }
    }

    // 强行释放资源
    public releaseTempAsset(name: string) {
        const it = this.temps.get(name)
        if (!it) {
            return loader.printInfo('try release null res[' + name + ']')
        }
        this.temps.delete(name)
        loader.releaseAsset(it.url, it.type)
    }

    // 释放所有标记的临时资源
    public releaseTempResByTag(tag: string) {
        this.temps.forEach(it => this.removeTempResRef(it, tag))
    }

    /**
    * 根据path，uuid等找资源url, 更详细的参数参考源码
    * @param params eg: {path: "manifest/project", bundle: "resources"}
    * @param option eg: {ext: .manifest}
    */
    public transform(params: { path: string, bundle: string } | string, option: { ext?: string, __isNative__?: boolean } | any = {}): string {
        // @ts-ignore
        return cc.assetManager._transform(params, option)
    }

    public getInfoWithPath(bundleName: string, path: string) {
        //@ts-ignore
        return cc.assetManager.getBundle(bundleName)?._config.getInfoWithPath(path)
    }

    // 加载一次性资源 不缓存
    // 注意：需要和releaseOnceRes成对使用
    public async loadOnceRes(name: string, type: typeof cc.Asset) {
        const url = 'tmp/' + this.makeTypeName(type) + name
        const asset = await loader.load(url, type)
        asset && asset.addRef()
        return asset
    }

    public releaseOnceRes(name: string, type: typeof cc.Asset) {
        const url = 'tmp/' + this.makeTypeName(type) + name
        loader.releaseRes(url, type)
    }

}

//@ts-ignore
window['assetsMgr'] = new AssetsMgr()
