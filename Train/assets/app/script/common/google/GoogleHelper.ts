import { UserType } from "../constant/Enums";
import Jsb<PERSON><PERSON> from "../event/JsbEvent";
import { jsbHelper } from "../helper/JsbHelper";

 class GoogleHelper {

    async login() {
        let res = await jsbHelper.call(JsbEvent.GOOGLE_LOGIN)
        if (res.error) {
            // reportHelper.reportError('google nativelogin error', res)
            return { errcode: res.code || -10086, msg: res.message }
        } else {
            return { userId: res.userId, token: res.token, avatarUrl: res.photoUrl, nickName: res.nickName };
        }
    }
}

export const googleHelper = new GoogleHelper();
