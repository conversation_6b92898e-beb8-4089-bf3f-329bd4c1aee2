type CurveEvent = {
    ratio: number,
    func: string,
    params?: (number | string | boolean)[]
}
export default class CurvePath {
    private target: cc.Node = null

    private curve: any = null

    private animCilp: cc.AnimationClip = null

    private len: number = 0

    private events: CurveEvent[] = []

    public init(animCilp: cc.AnimationClip) {
        this.target = new cc.Node()
        this.animCilp = animCilp
        let keyframes = animCilp.curveData.props.position
        let curve = animCilp.createPropCurve(this.target, "position", keyframes)

        let values: cc.Vec2[] = curve.values
        let lensSum = [0]
        let temp = cc.v2()
        for (let i = 1; i < values.length; i++) {
            let len = values[i].sub(values[i - 1], temp).mag()
            lensSum[i] = lensSum[i - 1] + len
        }
        this.len = lensSum[values.length - 1]

        if (this.len <= 0) {

        }

        let events = animCilp.events
        let eventIndex = 0
        let eventRatios = []
        for (let i = 1, l = keyframes.length; i < l; i++) {
            let keyframe = keyframes[i];
            let preKeyframe = keyframes[i - 1]
            for (; eventIndex < events.length; eventIndex++) {
                let { frame } = events[eventIndex]
                if (frame < keyframe.frame) {
                    let ratio = (frame - preKeyframe.frame) / (keyframe.frame - preKeyframe.frame)
                    eventRatios.push({ ki: i, ratio })
                }
                else {
                    break
                }
            }
        }

        for (let i = 1, l = keyframes.length; i < l; i++) {
            let keyframe = keyframes[i];
            let ratio = keyframe.frame / this.duration;
            let index = curve._findFrameIndex(curve.ratios, ratio)
            keyframe.frame = lensSum[index] / this.len * animCilp.duration
        }

        for (let i = 0; i < events.length; i++) {
            let { ki, ratio } = eventRatios[i]
            let frame = keyframes[ki - 1].frame + (keyframes[ki].frame - keyframes[ki - 1].frame) * ratio
            let { func, params } = events[i]
            this.events.push({ ratio: frame / this.duration, func, params })
        }

        this.curve = animCilp.createPropCurve(this.target, "position", keyframes)

        return this
    }

    public getPosition(ratio, out?) {
        if (this.curve) {
            this.curve.sample(0, ratio)
            return this.target.getPosition(out)
        }
        return cc.v2()
    }

    public getEventByRatio(preRatio, ratio) {
        for (let i = this.events.length - 1; i >= 0; i--) {
            let event = this.events[i]
            if (preRatio <= event.ratio && event.ratio < ratio) {
                return event
            }
        }
    }

    public findEvent(funcName, params?) {
        return this.events.find(event => event.func == funcName && event.params == params)
    }

    public getEvents() {
        return this.events
    }

    public get duration() {
        return this.animCilp?.duration || 0
    }

    private get frameRate() {
        return this.animCilp ? this.animCilp['frameRate'] : 0
    }

    private get allFrame() {
        return this.animCilp ? this.duration * this.frameRate : 0
    }

    public getCurveRatio(): Array<number> {
        return this.curve ? this.curve.ratios : null;
    }

    public getCurveValues(): Array<cc.Vec2> {
        return this.curve ? this.curve.values : null;
    }

    public getLen() {
        if (this.len > 0) return this.len
        if (!this.curve) return 0
        let values: cc.Vec2[] = this.curve.values
        let temp = cc.v2()
        for (let i = 1; i < values.length; i++) {
            this.len += values[i].sub(values[i - 1], temp).mag()
        }
        return this.len
    }

    /**获取弧线于x轴点角度 */
    public getAngle(ratio: number) {
        let postion = this.getPosition(ratio);
        let nextOffset = (1 / this.allFrame)
        if (ratio == 1) { //往前走没点了，往后取
            nextOffset = -nextOffset
        }
        let nextFramePostion = this.getPosition(ratio + nextOffset);
        let len = nextFramePostion.sub(postion).mag();
        let heightDis = Math.abs(nextFramePostion.x - postion.x);
        let radian = Math.acos(heightDis / len);
        let angle = ut.radian2angle(radian);
        return nextFramePostion.x < postion.x ? 180 - angle : angle;
    }

    public getStartPos() {
        return this.getPosition(0)
    }
    
    public getEndPos() {
        return this.getPosition(1)
    }
}