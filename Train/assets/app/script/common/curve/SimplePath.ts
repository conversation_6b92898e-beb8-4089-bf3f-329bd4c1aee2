export default class SimplePath {

    private len: number = 0
    private startPos: cc.Vec2 = null
    private endPos: cc.Vec2 = null

    public init(startPos: cc.Vec2, endPos: cc.Vec2) {
        this.startPos = startPos.clone()
        this.endPos = endPos.clone()
        this.len = startPos.sub(endPos).mag()
        return this
    }

    public getPosition(ratio) {
        if (this.len == 0) return this.endPos.clone()
        if (this.startPos && this.endPos) {
            return this.startPos.lerp(this.endPos, ratio)
        }
        return cc.v2()
    }

    public getLen() {
        return this.len
    }

    public getStartPos() {
        return this.startPos
    }
    
    public getEndPos() {
        return this.endPos
    }
}