export default class CurveLerp {
    private target: cc.Node = null

    private curve: any = null

    private animCilp: cc.AnimationClip = null

    public init(animCilp: cc.AnimationClip) {
        this.animCilp = animCilp
        this.target = new cc.Node()
        let keyframes = animCilp.curveData.props.scale
        this.curve = this.animCilp.createPropCurve(this.target, "scale", keyframes)

        return this
    }

    public getValue(ratio) {
        if (this.curve) {
            this.curve.sample(0, ratio)
            return this.target.scaleY
        }
    }
}