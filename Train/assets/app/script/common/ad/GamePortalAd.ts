export class GamePortalAd {
    private ad: any;
    private isReady: boolean = false;

    public async load() {
        if (!ut.isMiniGame() || !wx.createGamePortal || this.ad) {
            return;
        }
        
        let now = new Date().getTime();

        let ad = wx.createGamePortal({
            adUnitId : "PBgAA5LEaGYg5c9M",
        });
        console.log("initGamePort cost: ", new Date().getTime() - now);

        try {
            this.isReady = await ad.load();
        } catch (error) {
            twlog.error(error);
        }
        this.ad = ad;
    }

    public async show(): Promise<boolean> {
        if (this.isReady) {
            try {
                await this.ad.show();
            } catch (error) {
                twlog.error(error);
            }
            return true;
        }
        else {
            return false;
        }
    }
}