import CoreEventType from "../../../core/event/CoreEventType";
import { Msg } from "../../../proto/msg-define";
import ConditionObj from "../../model/common/ConditionObj";
import { ConditionType, ItemID } from "../constant/Enums";
import { cfgHelper } from "../helper/CfgHelper";
import { gameHelper } from "../helper/GameHelper";
import { viewHelper } from "../helper/ViewHelper";
import { NativeBannerAd, WxBannerAd } from "./BannerAd";
import { GamePortalAd } from "./GamePortalAd";
import { NativeInterstitialAd, WxInterstitialAd } from "./InterstitialAd";
import { WxRewardAd, NativeRewardAd } from "./RewardAd";

const UPDATE_INTERVAL = 500;
const ROOT_MAX_SCALE = 1.2;

@mc.addmodel('ad')
export default class AdModel extends mc.BaseModel {
    private rewardAd: WxRewardAd | NativeRewardAd = null;
    private interstitialAd: WxInterstitialAd | NativeInterstitialAd = null;
    public bannerAd: WxBannerAd | NativeBannerAd = null;
    private gamePortalAd: GamePortalAd = null;

    public bannerOffsetY: number = -1

    public data: proto.IAd = null

    // 广告奖励剩余次数
    public getRemainTimes(adType: proto.AdType): number { return this.getMaxTimes(adType) - (this.data[adType] || 0) }
    // 广告奖励最大次数
    public getMaxTimes(adType: proto.AdType): number { return (cfgHelper.getMiscData("ad")?.[adType])?.count || 0 }
    public deductTimes(adType: proto.AdType) { this.data[adType] = (this.data[adType] || 0) + 1 }

    //延迟调用
    init() {
        return
        if (ut.isMiniGame()) {
            this.rewardAd = new WxRewardAd();
            // this.interstitialAd = new WxInterstitialAd();
            this.bannerAd = new WxBannerAd();
            // this.gamePortalAd = new GamePortalAd();
        }
        else if (ut.isMobile()) {
            this.rewardAd = new NativeRewardAd();
            this.interstitialAd = new NativeInterstitialAd();
            this.bannerAd = new NativeBannerAd();
        }
        this.load();
    }

    private load() {
        this.intervalLoad(this.rewardAd);
        this.intervalLoad(this.interstitialAd);
        this.intervalLoad(this.bannerAd);

        if (this.gamePortalAd) {
            this.gamePortalAd.load();
        }
    }

    private intervalLoad(ad) {
        if (!ad) return;
        const dt = UPDATE_INTERVAL / 100;
        (async () => {
            ad.load(dt);
            await ut.waitTimeout(UPDATE_INTERVAL);
        })()
    }

    //激励广告
    public isRewardAdReady(): boolean {
        return this.rewardAd && this.rewardAd.isReady();
    }

    @ut.addLock
    public async showRewardAd(index: number = 0): Promise<boolean> {
        return await this.rewardAd.show(index);
    }

    //游戏入口广告(only wx)
    @ut.addLock
    public async showGamePortalAd(): Promise<boolean> {
        if (!this.gamePortalAd) return;
        return await this.gamePortalAd.show();
    }

    public async getRewardByAdType(adType: proto.AdType) {
        if (this.getRemainTimes(adType) <= 0) {
            viewHelper.showAlert("ore_watch_ad_no_times_tip")
            return false
        }
        const bol = await new Promise<boolean>((resolve, reject) => {
            viewHelper.showWatchAd(adType, (bol: boolean) => resolve(bol))
        })
        if (!bol) return false

        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_GetAdRewardMessage, { type: adType })
        if (!r) return false
        if (r.code != 0) {
            viewHelper.showNetError(r.code)
            return false
        }
        this.deductTimes(adType)
        switch (adType) {
            case proto.AdType.RecoveryOreBreakItem:
                await gameHelper.grantRewardAndShowUI(new ConditionObj().init(ConditionType.PROP, ItemID.NormalBreak, gameHelper.ore.getNormalBreakMaxNum()), true, { fly: false })
                break;
            case proto.AdType.RecoveryTrainEnergy:
                const energy = proto.Energy.decode(r.data.value)
                energy && gameHelper.world.energy.updateInfo(energy)
            default:
                break;
        }
        return true
    }

}