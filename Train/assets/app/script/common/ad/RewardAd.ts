
/**
 * 广告会先load，等到展示的时候调用show
 * 广告会一直加载，因为即使加载成功，太长时间不看也会过期
 */

import { AdState } from "../constant/Enums";
import JsbEvent from "../event/JsbEvent";
import { dbHelper } from "../helper/DatabaseHelper";
import { jsbHelper } from "../helper/JsbHelper";
import { wxHelper } from "../wx/WxHelper";
import { promisefyOnce } from "../wx/WxPromise";

type AdInfo = {
    state: AdState;
    reloadTime: number; //重新加载时间
    adOutOfUseTimeOut: number; //ad没有被使用的时间，加载成功后太久没使用可能过期
    videoAd?: any; //小程序视频广告实例
}

export class WxRewardAd {
    private ad: AdInfo;
    private multitonAd: AdInfo;
    private inited: boolean = false;

    public init() {
        let adUnitId = "adunit-215c66b6811a46f7";
        if (typeof qq !== 'undefined') {
            adUnitId = "3d9a8a1479d45e42f756ae907f548021";
        }
        this.ad = this.initAd(adUnitId);

        //多实例广告
        if (wxHelper.compareVersion("2.8.0") < 0 || typeof qq !== 'undefined') {
            return;
        }
        adUnitId = "adunit-4d1072f3f76d7da4";
        this.multitonAd = this.initAd(adUnitId, true);
    }

    //初始化小程序视频广告，会将广告实例存下来
    private initAd(adUnitId: string, multiton: boolean = false) {
        let ad = {
            state: AdState.LOADING,
            reloadTime: 0,
            adOutOfUseTimeOut: 0,
            videoAd: null,
        }

        let videoAd = wx.createRewardedVideoAd({
            adUnitId: adUnitId,
            multiton: multiton,
        })
        videoAd.onLoad(() => {
            console.log("@@video onLoad")

            ad.state = AdState.LOAD_SUCCESS;
            ad.adOutOfUseTimeOut = 0;
        })

        videoAd.onError(err => {
            console.log("@@loadError", err)
            ad.state = AdState.WAIT;

            if (err) {
                if (err.errMsg == "no reward" || err.errCode == 0 || err.errCode == 1004) {
                    ad.reloadTime -= 15;
                }
                else {
                    ad.reloadTime -= 30;
                }
            }
        })
        ad.videoAd = videoAd;
        return ad;
    }

    public isReady() {
        if (this.ad) {
            if (this.multitonAd && this.multitonAd.state == AdState.LOAD_SUCCESS) {
                return true;
            }
            return this.ad.state == AdState.LOAD_SUCCESS;
        }
        else {
            return false;
        }
    }

    public load(dt) {
        if (!this.inited) {
            this.inited = true;
            let now = new Date().getTime();
            this.init();
            // console.log("initReward cost: ", new Date().getTime() - now);
        }

        let updateAdInfo = (adInfo)=>{
            if (!adInfo) return;
            if (adInfo.state == AdState.WAIT) {
                adInfo.reloadTime += dt;
                if (adInfo.reloadTime >= 15) {
                    adInfo.reloadTime = -2 * Math.random();
                    adInfo.state = AdState.LOADING;
                    adInfo.videoAd.load().catch(function (err) {
                        // console.log("loadError", err)
                    })
                }
            }
            else if (adInfo.state == AdState.LOADING) {
                adInfo.adOutOfUseTimeOut += dt;
                if (adInfo.adOutOfUseTimeOut >= 40) {
                    adInfo.state = AdState.WAIT;
                    adInfo.adOutOfUseTimeOut = 0;
    
                    adInfo.videoAd.load().catch(function (err) {
                        // console.log("loadError", err)
                    })
                }
            }
        }

        updateAdInfo(this.ad);
        updateAdInfo(this.multitonAd);
    }

    public async show(index: number): Promise<boolean> {
        let adInfo = this.ad;
        //微信广告的激励视频是个单例  目前可以多例了!
        let videoAd = adInfo.videoAd;

        if (this.multitonAd) {
            if (adInfo.state != AdState.LOAD_SUCCESS) {
                adInfo = this.multitonAd;
                videoAd = adInfo.videoAd;
            }
            else if (this.multitonAd.state == AdState.LOAD_SUCCESS) {
                let indexes = [1, 2, 5, 6, 10]
                if (indexes.indexOf(index) > -1) {
                    adInfo = this.multitonAd;
                    videoAd = adInfo.videoAd;
                }
            }
        }

        let isPause = null
        function pause() {
            if (isPause) {
                return;
            }
            isPause = true;
            cc.game.pause()
        }

        function resume() {
            if (!isPause) {
                return
            }
            isPause = null
            cc.game.resume()
        }

        try {
            await videoAd.load();
            pause()
            await videoAd.show();
            let res = await promisefyOnce(videoAd.onClose, videoAd.offClose) as {isEnded: boolean};
            adInfo.state = AdState.WAIT;
            resume()
            // 用户点击了【关闭广告】按钮
            // 小于 2.1.0 的基础库版本，res 是一个 undefined
            if (res && res.isEnded || res === undefined) {
                // 正常播放结束，可以下发游戏奖励
                return true;
            } else {
                // 播放中途退出，不下发游戏奖励
                return false;
            }

        } catch (error) {
            adInfo.state = 0;
            twlog.error(error);
            resume()
            return false;
        }
    }
}

export class NativeRewardAd {
    private ready: boolean = false;
    private waitTime: number = 0;
    private state: AdState;

    public isReady() {
        let isVaild = true;
        if (cc.sys.os == cc.sys.OS_IOS) {
            // isVaild = jsb.reflection.callStaticMethod("AdSDKManager", "isRewardVideoADReady");
        }
        else {
            // isVaild = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/AppActivity", "isRewardVideoADReady", "()Z");
        }
        return this.ready && isVaild;
    }

    public load(dt) {
        if (this.state !== AdState.LOADING && this.state !== AdState.LOAD_SUCCESS && this.state !== AdState.WAIT) {
            this.state = AdState.LOADING;
            jsbHelper.cast(JsbEvent.LOAD_REWARD_VIDEO_AD, (res) => {
                let code: string = res.status;
                if (code === "0") {
                    this.state = AdState.LOAD_SUCCESS;
                    this.ready = true;
                }
                else if (code === "-1") {
                    this.state = AdState.LOAD_FAIL;
                    this.ready = false;
                }
                else if (code === "-3") {
                    this.state = AdState.WAIT;
                    this.ready = false;
                }
                else {
                    console.log("load ads unknow code is " + code + " " + typeof (code));
                    this.state = AdState.WAIT;
                    this.ready = false;
                }
            })
        }
        else if (this.state === AdState.WAIT) {
            this.waitTime += dt;
            if (this.waitTime >= 25) {
                this.waitTime = 0;
                this.state = AdState.LOAD_FAIL;
            }
        }
    }

    public async show(any?: any): Promise<boolean> {
        let res = await jsbHelper.call(JsbEvent.SHOW_REWARD_VIDEO_AD);
        let code: string = res.status;
        if (code === "0") {
            this.state = AdState.PLAY_SUCCESS;
            this.ready = false;
            return true;
        }
        else if (code === "-1") {
            this.state = AdState.PLAY_FAIL;
            this.ready = false;
            return false;
        }
        else {
            console.log("show rewardAD unknow code is " + code + " " + typeof (code));
            return false;
        }
    }
}