import { AdState } from "../constant/Enums";
import JsbE<PERSON> from "../event/JsbEvent";
import { jsbHelper } from "../helper/JsbHelper";
import { wxHelper } from "../wx/WxHelper";

const GAME_BANNER_ID = "1000";
const GRID_BANNER_ID = "10000";

type AdInfo = {
    showCount: number; //展示次数
    hide: boolean;
    state: AdState;
    showTime: number; //展示时间
    reloadDelay: number; //重新加载延迟时间
    adUnitId: string;
    lock: boolean;
    ad: any; //banner广告实例
}

export class WxBannerAd {
    private inited: boolean = false;
    private bannerAdMap: object = {};

    private initAdMap() {
        let KEY_LIST = ['adunit-5984be478e3b33e6'];
        if (typeof qq !== 'undefined') {
            KEY_LIST = ['1a4c77a4839e4735c785a38250091102', "42aa1c057a0c0295a133c02724c37a3d", "b104d1aa872cf69d511668bff9082062", "6f94a14f77dac309b1289b740fd63b01"];
        }

        for (let index = 0; index < KEY_LIST.length; index++) {
            this.bannerAdMap[index] = KEY_LIST[index];
        }

        //互推组件
        // if (wx.createGameIcon && wx.createGameBanner) {
        //     if (Math.random() < 0.3) {
        //         this.bannerAdMap[GAME_BANNER_ID] = "PBgAA5LEaGYlNq_o";
        //     }
        //     else {
        //         this.bannerAdMap[GAME_BANNER_ID] = "PBgAA5LEaGYoQymM";
        //     }
        // }
        // else if (wx.createGameIcon) {
        //     this.bannerAdMap[GAME_BANNER_ID] = "PBgAA5LEaGYlNq_o";
        // }
        // else if (wx.createGameBanner) {
        //     this.bannerAdMap[GAME_BANNER_ID] = "PBgAA5LEaGYoQymM";
        // }

        // //格子广告
        // if (wx.createGridAd) {
        //     this.bannerAdMap[GRID_BANNER_ID] = "adunit-f3213d477c7beaf3";
        // }
    }

    public load(dt: number) {
        if (!this.inited) {
            this.inited = true;
            this.initAdMap();
        }

        for (const index in this.bannerAdMap) {
            let badInfo = this.bannerAdMap[index];
            let needLoad = true;
            if (badInfo && typeof badInfo === "object") {
                needLoad = this.updateAdInfo(badInfo, dt);
            }

            if (needLoad) {
                this.loadAd(index);
            }
        }
    }

    loadAd(index: string) {
        let badInfo = this.bannerAdMap[index];
        let adUnitId = badInfo;
        let systemInfo = wxHelper.getSystemInfo();

        badInfo = {
            showCount: 0,
            hide: true,
            state: AdState.LOADING,
            showTime: 0,
            reloadDelay: 30 + Math.random() * 30,
            adUnitId: adUnitId,
        }

        this.bannerAdMap[index] = badInfo;

        let scaleX = systemInfo.screenWidth / 960;
        let adHeight = scaleX * 334;

        try {
            let bannerAd;

            if (index != GAME_BANNER_ID && index != GRID_BANNER_ID) {
                bannerAd = wx.createBannerAd({
                    adUnitId: adUnitId,
                    style: this.getStyle()
                })
            }
            else if (index == GRID_BANNER_ID) {
                bannerAd = wx.createGridAd({
                    adUnitId: adUnitId,
                    style: this.getStyle()
                })
            }
            else {
                if (adUnitId == "PBgAA5LEaGYlNq_o") {
                    const size = 52;
                    const offsetX = size * 0.5 + (72 - size) * 0.5 + 2;
                    bannerAd = wx.createGameIcon({
                        adUnitId: adUnitId,
                        count: 3,
                        style: [
                            {
                                left: systemInfo.screenWidth * 0.25 - offsetX,
                                top: systemInfo.screenHeight - adHeight - 12,
                                size: size,
                                color: "#FFFFFF",
                            },

                            {
                                left: systemInfo.screenWidth * 0.5 - offsetX,
                                top: systemInfo.screenHeight - adHeight - 12,
                                size: size,
                                color: "#FFFFFF",
                            },

                            {
                                left: systemInfo.screenWidth * 0.75 - offsetX,
                                top: systemInfo.screenHeight - adHeight - 12,
                                size: size,
                                color: "#FFFFFF",
                            }
                        ]
                    })
                }
                else {
                    bannerAd = wx.createGameBanner({
                        adUnitId: adUnitId,
                        style: this.getStyle()
                    })
                }
            }

            bannerAd.onError(err => {
                console.log(bannerAd, err)
                badInfo.state = AdState.LOAD_FAIL;
                if (err) {
                    if (err.errMsg == "no Banner" || err.errCode == 0 || err.errCode == 1004) {
                        badInfo.reloadDelay += 120;
                    }
                    else {
                        badInfo.reloadDelay += 60;
                    }
                }
            })

            bannerAd.onLoad(() => {
                // console.log('banner 广告加载成功 ', adUnitId)
                badInfo.loadTimeCount = 0;
                badInfo.state = AdState.LOAD_SUCCESS;
            })

            badInfo.bannerAd = bannerAd;
        } catch (error) {
            console.error(error);
        }
    }

    private updateAdInfo(badInfo, dt) {
        if (badInfo.state == AdState.LOADING || badInfo.lock) {
            return;
        }

        if (badInfo.state == AdState.LOAD_SUCCESS) {
            badInfo.loadTimeCount += dt;
        }

        if (!badInfo.hide) {
            badInfo.showTime += dt;

            if (badInfo.showTime > 90) {
                badInfo.hide = true;
                let p = badInfo.bannerAd.hide();
                if (p) {
                    p.catch(function (err) {
                        console.error(err);
                    });
                }

                badInfo.showTime = 0;
            }
            return;
        }

        if (badInfo.state == AdState.LOAD_FAIL) {
            badInfo.reloadDelay -= dt;
            if (badInfo.reloadDelay <= 0) {
                try {
                    badInfo.bannerAd.destroy();
                    badInfo = badInfo.adUnitId;
                } catch (error) {
                    twlog.error(error);
                }
                return true;
            }
        }
    }

    public show(index: number) {
        let badInfo = this.bannerAdMap[index];
        let orgBadInfo = badInfo;
        let gameBannerInfo = this.bannerAdMap[GAME_BANNER_ID];
        let gridBannerInfo = this.bannerAdMap[GRID_BANNER_ID];

        if (index == 3 && typeof gridBannerInfo === "object" && !gridBannerInfo.lock && gridBannerInfo.state == AdState.LOAD_SUCCESS && !gridBannerInfo.show) {
            if (typeof badInfo === "object" && !badInfo.lock && !badInfo.show && !badInfo.lastShowId) {
                if (badInfo.state != AdState.LOAD_SUCCESS || Math.random() < 0.05) {
                    orgBadInfo.lastShowId = GRID_BANNER_ID;
                    badInfo = gridBannerInfo;
                }
                else {
                    orgBadInfo.lastShowId = null;
                }
            }
        }

        if (typeof gameBannerInfo === "object" && !gameBannerInfo.lock && gameBannerInfo.state == AdState.LOAD_SUCCESS && !gameBannerInfo.show) {
            if (typeof badInfo === "object" && !badInfo.lock && !badInfo.show && !badInfo.lastShowId) {
                if (badInfo.state != AdState.LOAD_SUCCESS || Math.random() < 0.2) {
                    orgBadInfo.lastShowId = GAME_BANNER_ID;
                    badInfo = gameBannerInfo;
                }
                else {
                    badInfo.lastShowId = null;
                }
            }
        }

        if (typeof badInfo === "object" && !badInfo.lock) {
            if (badInfo.state == AdState.LOAD_SUCCESS && !badInfo.show && !badInfo.lastShowId) {
                badInfo.lock = true;
                badInfo.hide = false;
                badInfo.bannerAd.show().then(() => {
                    badInfo.showCount++;
                    badInfo.lock = null;
                    badInfo.show = true;

                    if (badInfo.hide) {
                        this.hide(index);
                    }
                }).catch(function (err) {
                    twlog.error(err);
                    badInfo.lock = null;
                });
            }
        }
    }

    public hide(index: number) {
        let badInfo = this.bannerAdMap[index];
        let orgBadInfo = badInfo;
        let lastShowId = null;
        if (typeof badInfo === "object") {
            lastShowId = badInfo.lastShowId;
            if (lastShowId) {
                badInfo = this.bannerAdMap[lastShowId];
            }
            else {
                lastShowId = index;
            }
        }

        if (typeof badInfo === "object" && !badInfo.lock) {
            badInfo.lock = true;
            let p = badInfo.bannerAd.hide();
            if (p) {
                p.then(() => {
                    badInfo.showTime = 0;
                    badInfo.hide = true;
                    badInfo.lock = null;
                    orgBadInfo.lastShowId = null;
                    badInfo.show = null;

                    // console.log(badInfo.loadTimeCount);
                    if (badInfo.showCount >= 6 && badInfo.loadTimeCount >= 120) {
                        try {
                            badInfo.bannerAd.destroy();
                        } catch (error) {
                            console.error(error);
                        }
                        this.bannerAdMap[lastShowId] = badInfo.adUnitId;
                    }
                }).catch(function (err) {
                    console.error(err);
                    badInfo.lock = null;
                });
            }
            else {
                badInfo.showTime = 0;
                badInfo.hide = true;
                badInfo.lock = null;
                orgBadInfo.lastShowId = null;
                badInfo.show = null;

                if (badInfo.showCount >= 6) {
                    try {
                        badInfo.bannerAd.destroy();
                    } catch (error) {
                        console.error(error);
                    }
                    this.bannerAdMap[lastShowId] = badInfo.adUnitId;
                }
            }
        }
        else if (typeof badInfo === "object") {
            badInfo.hide = true;
        }
    }
    
    public hideAll() {
        for (let index in this.bannerAdMap) {
            this.hide(parseInt(index));
        }
    }

    public getResSize() {
        return cc.size(960, 334)
    }

    public getStyle() {
        let {screenWidth, screenHeight} = wxHelper.getSystemInfo();
        let scale = Math.min(cc.winSize.width / screenWidth, cc.winSize.height / screenHeight)
        const maxHeight = 400
        let adHeight = maxHeight / scale
        let {width, height} = this.getResSize()
        let adWidth = cc.misc.clampf(adHeight / height * width, 300, screenWidth)
        adHeight = adWidth / width * height;
        return {
            left: (screenWidth - adWidth) / 2,
            top: screenHeight - adHeight - 12,
            width: adWidth,
            height: adHeight
        }
    }

    public getBoundary(): cc.Rect {
        let {screenWidth, screenHeight} = wxHelper.getSystemInfo();
        let scale = Math.min(cc.winSize.width / screenWidth, cc.winSize.height / screenHeight)
        let style = this.getStyle()
        return cc.rect(style.left * scale, (screenHeight - style.top - style.height) * scale, style.width * scale, style.height * scale)
    }
}

export class NativeBannerAd {
    private waitTime: number = 0;
    private state: AdState;

    load(dt) {
        if (this.state !== AdState.LOADING && this.state !== AdState.LOAD_SUCCESS && this.state !== AdState.WAIT) {
            this.state = AdState.LOADING;
            jsbHelper.cast(JsbEvent.LOAD_BANNER_AD, (res) => {
                let code: string = res.status;
                if (code === "0") {
                    this.state = AdState.LOAD_SUCCESS;
                }
                else if (code === "-1") {
                    this.state = AdState.LOAD_FAIL;
                }
                else if (code === "-3") {
                    this.state = AdState.WAIT;
                }
                else {
                    console.log("load Banner unknow code is " + code + " " + typeof (code));
                    this.state = AdState.WAIT;
                }
            })
        }
        else if (this.state === AdState.WAIT) {
            this.waitTime += dt;
            if (this.waitTime >= 30) {
                this.waitTime = 0;
                this.state = AdState.LOAD_FAIL;
            }
        }
    }

    public isReady() {
        return this.state == AdState.LOAD_SUCCESS;
    }

    public show(any?: any) {
        if (!this.isReady()) return;
        jsbHelper.cast(JsbEvent.SHOW_BANNER_AD);
    }

    public hide(any?: any) {
        if (!this.isReady()) return;
        jsbHelper.cast(JsbEvent.HIDE_BANNER_AD);
    }

    public getBoundary(): cc.Rect {
        return cc.rect(0, 0, 0, 0)
    }

    public hideAll() {

    }
}