import JsbEvent from "../event/JsbEvent";
import { dbHelper } from "../helper/DatabaseHelper";
import { wxHelper } from "../wx/WxHelper";
import { promisefyOnce } from "../wx/WxPromise";
import { AdState } from "../constant/Enums"
import { jsbHelper } from "../helper/JsbHelper";

type WxAdInfo = {
    ad: any; //广告实例
    timeCount: number; //播放间隔
    isReady: boolean; //是否可播
    isShow: boolean; //播放中 
}

export class WxInterstitialAd {
    private ads: WxAdInfo[];
    private inited: boolean = false;

    public initAd() {
        if (!wx.createInterstitialAd || wxHelper.compareVersion("2.6.4") < 0) {
            return;
        }

        this.ads = [];

        // 创建插屏广告实例，提前初始化
        const KEY_LIST = ['adunit-8dc97b84097a1891'];

        for (let i = 0; i < KEY_LIST.length; i++) {
            let ad = wx.createInterstitialAd({
                adUnitId: KEY_LIST[i],
            });

            let info = {
                ad: ad,
                timeCount: 60,
                isReady: false,
                isShow: false,
            }
            this.ads.push(info);

            ad.onLoad(() => {
                info.isReady = true;
            })

            ad.onError((res) => {
                twlog.error(res);
            })
        }
    }

    public isReady(index: number): boolean {
        if (!this.ads) {
            return false;
        }
        let info = this.ads[index];
        if (!info || !info.isReady || info.timeCount < 15 || info.isShow) {
            return false;
        }
        return true;
    }

    public load(dt) {
        if (!this.inited) {
            this.inited = true;
            this.initAd();
        }

        if (this.ads) {
            for (let i = 0; i < this.ads.length; i++) {
                let info = this.ads[i];
                info.timeCount += dt;
            }
            return;
        }
    }

    public async show(index: number): Promise<boolean> {
        if (!this.isReady(index)) {
            return false;
        }
        let info = this.ads[index];
        try {
            info.isShow = true;
            await info.ad.show();
            await promisefyOnce(info.ad.onClose, info.ad.offClose);

            info.isReady = false;
            info.timeCount = 0;
            info.isShow = false;

            return true;
        } catch (error) {
            twlog.error(error);
            info.isShow = false;
            info.timeCount = 10;
            // userData.sendMessage("showToast",localize.getText("toast_insertAd_short"));
            return false;
        }
    }
}

export class NativeInterstitialAd {
    private ready: boolean = false;
    private waitTime: number = 0;
    private state: AdState;

    public isReady(any?: any): boolean {
        return this.ready;
    }

    public load(dt) {
        if (this.state !== AdState.LOADING && this.state !== AdState.LOAD_SUCCESS && this.state !== AdState.WAIT) {
            this.state = AdState.LOADING;
            jsbHelper.cast(JsbEvent.LOAD_INTERSTITIAL_AD, (code: string) => {
                if (code === "0") {
                    this.state = AdState.LOAD_SUCCESS;
                    this.ready = true;
                }
                else if (code === "-1") {
                    this.state = AdState.LOAD_FAIL;
                    this.ready = false;
                }
                else if (code === "-3") {
                    this.state = AdState.WAIT;
                    this.ready = false;
                }
                else {
                    console.log("load ads unknow code is " + code + " " + typeof (code));
                    this.state = AdState.WAIT;
                    this.ready = false;
                }
            })
        }
        else if (this.state === AdState.WAIT) {
            this.waitTime += dt;
            if (this.waitTime >= 30) {
                this.waitTime = 0;
                this.state = AdState.LOAD_FAIL;
            }
        }
    }

    public async show(any?: any): Promise<boolean> {
        let code = await jsbHelper.call(JsbEvent.SHOW_INTERSTITIAL_AD);
        if (code === "0") {
            this.state = AdState.PLAY_SUCCESS;
            this.ready = false;
            return true;
        }
        else if (code === "-1") {
            this.state = AdState.PLAY_FAIL;
            this.ready = false;
            return false;
        }
        else {
            console.log("show ads unknow code is " + code + " " + typeof (code));
            return false;
        }
    }
}