const { ccclass, property, requireComponent } = cc._decorator;

@ccclass
@requireComponent(cc.Sprite)
export default class FlashSpriteShaderCtrl extends cc.Component {

    private maxTime: number = 0.8 //扫一次的时间
    private maxCount: number = 1 //扫几次
    private interval: number = 1

    private delay: number = 0
    private elapsed: number = 0
    private count: number = 0

    private material: cc.Material = null
    private preMaterial: cc.Material = null
    private sprite: cc.Sprite = null

    private cb: Function = null

    @property(cc.Material)
    light: cc.Material = null

    onLoad() {
        this.sprite = this.getComponent(cc.Sprite)
        this.preMaterial = this.sprite.getMaterial(0)
        let material = this.light || assetsMgr.getMaterial('flash_build_sprite');
        //@ts-ignore
        this.material = cc.MaterialVariant.create(material, this.sprite)
    }

    // 设置材质
    public play(cb?: Function) {
        if (!this.sprite) {
            return
        }
        this.sprite.setMaterial(0, this.material)
        this.material.setProperty('t', 0)
        this.elapsed = 0
        this.delay = 0
        this.count = this.maxCount
        this.cb = cb
    }

    // 还原
    public stop() {
        this.count = 0
        this.sprite.setMaterial(0, this.preMaterial)
        if (this.cb) {
            this.cb()
            this.cb = null
        }
    }

    update(dt: number) {
        if (this.count === 0) {
            return
        }
        if (this.delay > 0) {
            this.delay -= dt
            return
        }
        this.elapsed += dt
        if (this.elapsed < this.maxTime) {
            this.material.setProperty('t', this.elapsed / this.maxTime)
        } else {
            this.delay = this.interval
            this.count -= 1
            this.elapsed = 0
            if (this.count <= 0) {
                this.stop()
            } else {
                // audioMgr.playSFX('common/sound07')
            }
        }
    }
}