/**
 * 处理微信app相关 
 */
import JsbEvent from "../event/JsbEvent"
import { jsbHelper } from "../helper/JsbHelper";

class WxAppHelper {
    // 拉起第三方
    public async login() {
        let map = { "scope": "snsapi_userinfo", "state": ut.getRandomString(16) }
        let res = await jsbHelper.call(JsbEvent.WX_APP_LOGIN, map) || {}
        if (res.result == "success" && res.code) {
            return { code: res.code }
        } else {
            if (res.errcode !== "-2000" && res.errcode !== "-4000") {
                // reportHelper.reportError('wxApp nativelogin error', res)
            }
            return { errcode: res.errcode || -10086 }
        }
    }

    public async shareToTimeLine(data: { title: string, url: string, imgUrl: string }) {
        if (data.imgUrl.startsWith('http')) {
            let { result } = await this.callShare(JsbEvent.WX_SHARE_LINK, data) || { result: false }
            return result
        } else {
            let { result } = await this.shareImage(data.imgUrl) || { result: false }
            return result
        }
    }

    public async shareMiniGame(title: string, imgUrl: string, queryInfo?: any) {
        // let path = netMgr.encodeURIObj(queryInfo, true);
        // let miniprogramType = 0
        // if (!localConfig.release) {
        //     miniprogramType = 1
        // }
        // let map = { "title": title, miniprogramType, "imgUrl": imgUrl, "path": path };
        // return this.callShare(JsbEvent.WX_SHARE_MINIGAME, map)
    }

    private async callShare(event: string, map: any) {
        let result = await Promise.race([jsbHelper.call(event, map), this.onShow()]) || { result: true }
        return result
    }

    private onShow(): Promise<void> {
        return new Promise((resolve) => {
            let cb = () => {
                cc.game.off(cc.game.EVENT_SHOW, cb, this)
                resolve()
            }
            cc.game.on(cc.game.EVENT_SHOW, cb, this)
        })
    }

    public async shareImage(imgUrl: string, scene = 'timeLine') {
        let map = { "imgPath": imgUrl, scene };
        let result = await Promise.race([jsbHelper.call(JsbEvent.WX_SHARE_IMAGE, map), this.onShow()]) || { result: true }
        return result
    }

    public async savePhoto(imgPath: string) {
        let map = { "imgPath": imgPath };
        return await jsbHelper.call(JsbEvent.SAVE_PHOTO, map);
    }

    public async photoPermitStatus() {
        return await jsbHelper.call(JsbEvent.SAVE_PHOTO_PERMIT);
    }

    public async photoPermit() {
        return await jsbHelper.call(JsbEvent.PHOTO_PERMIT);
    }

    public isInstall() {
        return !!jsb.reflection.callStaticMethod('WXApiManager', 'isWeChatHas')
    }
}

export const wxAppHelper = new WxAppHelper()