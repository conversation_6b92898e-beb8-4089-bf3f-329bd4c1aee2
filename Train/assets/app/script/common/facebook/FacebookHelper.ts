/**
 * 处理海外脸书登录 相关 
 */
import JsbEvent from "../event/JsbEvent"
import { jsbHelper } from "../helper/JsbHelper"

class FacebookHelper {
    public async login() {
        let res = await jsbHelper.call(JsbEvent.FACEBOOK_LOGIN) || {}
        if (res.result == "success") {
            return { userId: res.uid, token: res.token }
        } else {
            if (res.errcode !== "-2") { //已安装
                // reportHelper.reportError('facebook nativelogin error', res)
            }
            return { errcode: res.errcode || -10086 }
        }
    }

    // 需要传的参数 url title
    public async shareLink(data: { url: string, title: string }) {
        let res = await jsbHelper.call(JsbEvent.FACEBOOK_SHARE_LINK, data)
        if (res.result == "success") {
            return true
        } 
        return false
    }

    //需要传的参数 path title
    public async sharePhoto(data: { path: string, title: string }) {
        let res = await jsbHelper.call(JsbEvent.FACEBOOK_SHARE_PHOTO, data);
        if (res.result == "success") {
            return true
        } 
        return false
    }

    public async share(data: { title: string, url: string, imgUrl: string }) {
        if (data.imgUrl && !data.imgUrl.startsWith('http')) {
            return this.sharePhoto({ path: data.imgUrl, title: data.title })
        } else {
            return this.shareLink(data)
        }
    }
}

export const facebookHelper = new FacebookHelper()