/**
 * 持久化数据
 */
class DatabaseHelperOld {

    private map: Map<string, { cb: Function, target: any, data: string, json: any, _ver: number, saveByDirty: boolean }> = new Map()
    private delay: number = 0

    public register(key: string, ver: number, callback: Function, target: any, saveByDirty?: boolean) {
        const data = storageMgr.loadString(key) ?? ''
        const json = data ? JSON.parse(data) : null
        this.map.set(key, { cb: callback, target: target, data: data, json: null, _ver: ver || 1, saveByDirty: saveByDirty })
        return json
    }

    public update(dt: number) {
        if (this.delay > 0) {
            this.delay -= dt
            return
        }
        this.delay = 0.1
        this.map.forEach((m, k) => {
            const json = m.cb.call(m.target, dt)
            if (!json) {
                return
            }
            json._ver = m._ver || 1
            const data = JSON.stringify(json)
            if (m.data === data) {
                return
            }
            m.data = data
            storageMgr.saveString(k, data)
        })

        this.map.forEach((m, k)=>{
            let old = storageMgr.loadString(k)
            let newV = storageMgr.loadObject(k.substring(2))
            newV = JSON.stringify(newV)
            if (old != newV) {
                console.error("diff", k, old, newV)
            }
            else {
                // console.log('same', k)
            }
        })
    }

    public clear() {
        this.map.clear()
    }

}

export const dbHelperOld = new DatabaseHelperOld()