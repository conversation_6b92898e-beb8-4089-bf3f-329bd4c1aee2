import { game<PERSON>elper } from "../../common/helper/GameHelper";

const MIN_WAIT_TIME = 5;
const MAX_LEN = 10;

class ErrorReportHelper {
    private errors: any[] = []
    private errorMap: {} = {}
    private isReporting: boolean = false
    private reportWaitTime: number = MIN_WAIT_TIME

    public init() {
        if (ut.isMiniGame()) {
            wx.onError((error) => {
                this.addException(JSON.stringify(error), null);
            })
        } else if (ut.isMobile()) {
            window['__errorHandler'] = (errorMessage, file, line, message, error) => {
                this.addException(JSON.stringify({
                    errorMessage,
                    line,
                    file,
                    message,
                    error,
                }), 'app');
            };
        }

        this.check();
    }

    private addException(exception: string, type: string) {
        if (this.errors.length > MAX_LEN) {
            return;
        }

        let level = 1
        try {
            if (mc.currScene == 'start') {
                level = 9
            } else if (mc.currScene == 'loading') {
                level = 8
            } else if (mc.currScene == 'main') {
                level = 5
            }
        } catch (error) {

        }
        let name = type + exception
        if (!this.errorMap[name]) {
            this.errors.push({
                type,
                exception,
                level,
                timestamp: gameHelper.now(),
            })
            this.errorMap[name] = true
        }
    }

    private async check() {
        while (true) {
            await ut.wait(this.reportWaitTime);
            await this.tryReport();
        }
    }

    private async tryReport() {
        if (this.isReporting || this.errors.length <= 0) {
            return;
        }

        this.isReporting = true;
        await this.report();
        this.isReporting = false;
    }

    private async report() {
        const size = 4;
        let errors = this.errors.slice(0, size);
        let uid = gameHelper.user.getUid();
        let platform = cc.sys.platform;
        let version = gameHelper.getVersion()
        let { code } = await gameHelper.net.post("/report/error", { uid, platform, errors, version }) || {};
        if (code === 0) {
            this.errors.splice(0, size);
            this.reportWaitTime = MIN_WAIT_TIME;
        } else {
            this.reportWaitTime = Math.min(this.reportWaitTime * 2, 1 * ut.Time.Minute);
        }
    }

    public reportError(name: string, message: any) {
        this.addException(JSON.stringify({
            name,
            message,
        }), 'diy')
    }
}

export const errorReportHelper = new ErrorReportHelper()

if (CC_DEV) {
    window["errorReportHelper"] = errorReportHelper
}