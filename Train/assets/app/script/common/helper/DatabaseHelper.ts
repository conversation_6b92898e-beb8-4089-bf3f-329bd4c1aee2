import Observer from "../../../core/utils/Observer"
import { localConfig } from "../LocalConfig"
import { dbHelperOld } from "./DatabaseHelperOld"

/**
 * 新的保存模块
 * 用法：接口参数key, ver, callback跟之前的一样
 * target：指监听的对象，会递归去监听它里面的子属性，一般就传this
 * obKeys：指定监听哪些target上的属性，一般可以不传，默认自动调用toDB和getObMap计算
 * 除非一个model需要定义多个toDB接口(一个model注册两个db的情况)， 必要时可以使用Observer.getObKeysByDB 进行辅助求解
 * 对于target上的子属性，如果是对象or类，需要在里面实现toDB()方法，返回需要监听的key，如果不实现，默认监听全部属性
 * 对于toDB返回的key，如果并不是对象上的key，可以实现getObMap做二次映射
 * 如 a = {
 *      _x: 1
 *      toDB() {
 *         return {x: this._x}
 *     }
 *      getObMap() {
 *          return {
 *              x: "_x"
 *          }
 *      }
 * }
 * 
 * 开发是可以配置localConfig.debugDBHelper = true，会用老的dbHelper来进行数据对拍
 * 
 * 注意---
 * 无法监听到的情况：对于object或者array来说，后续新建或删除的key无法被监听到
 * 如let a = {x: 1} a.x = 1 可以监听到； a.y = 1 不能监听到
 * 如 let a = [0]  a[0] = 1 or a.push(1) 可以监听到；a[1] = 1 不能监听到
 * 如let a = {x : 1} a.x = null 可以被监听，delete a.x  不能被监听
 * 对于不能监听的情况，目前只能通过手动调用 Observer.delKeys Observer.addKey来解决
 * 如 a = {x: 1} a[y] = 1 改成 Observer.addKey(a, y, 1)
 *  delete a[x] 改成 Observer.delKeys(a, "x")
 */

class DatabaseHelper {

    private map: Map<string, { cb: Function, target, _ver: number }> = new Map()

    private dirtyMap: Map<string, boolean> = new Map()

    private saveFreCheck: Map<string, { lastTime: number, count: number }> = new Map() //频繁写入检测

    public register(key: string, ver: number, callback: Function, target: object, obKeys?: string[]) {
        let data = this.load(key, ver)
        const old_ver = data._ver
        let ob = new Observer(key, (path, newVaue, old) => {
            ob.debug && twlog.info("ob-----", path, old, "->", newVaue) //debug
            this.setDirty(key)
        })
        ob.observe(target, obKeys)
        this.map.set(key, { cb: callback, target, _ver: ver || 1 })
        this.setDirty(key)

        if (localConfig.debugDBHelper) {
            dbHelperOld.register("__" + key, ver, callback, target) //数据对拍用
            // const data = dbHelperOld.register(key, ver, callback, target) || {}
            // return data
        }
        if (old_ver !== data._ver) {
            const new_ver = 'new_ver_' + data._ver + '__old_ver_' + old_ver
            data._ver = old_ver
        }
        return data
    }

    public forceSave(key: string) {
        const m = this.map.get(key)
        if (m) {
            let info = this.map.get(key)
            let data = info.cb.call(info.target)
            data._ver = info._ver
            this.save(key, data)
            storageMgr.save()
            this.dirtyMap.set(key, false)
        }
    }

    // 每帧刷新
    public update(dt) {
        this.dirtyMap.forEach((val, key) => {
            if (!val) return
            // console.log("update", key)
            let info = this.map.get(key)
            if (!info) return
            let data = info.cb.call(info.target)
            data._ver = info._ver
            this.save(key, data)
            this.dirtyMap.set(key, false)

            localConfig.debugDBHelper && this.freqCheck(key)
        })

        localConfig.debugDBHelper && dbHelperOld.update(dt)
    }

    public clear() {
        this.map.clear()
        this.dirtyMap.clear()

        localConfig.debugDBHelper && dbHelperOld.clear()

        storageMgr.clear()
    }

    public setDirty(key: string) {
        this.dirtyMap.set(key, true)
    }

    private save(key, data) {
        if (localConfig.debugDBHelper) {
            data = ut.deepClone(data)
        }
        storageMgr.saveObject(key, data)
    }

    private load(key: string, ver: number) {
        let data = storageMgr.loadObject(key) || { _ver: ver }
        if (typeof data == 'string') { //fix
            data = JSON.parse(data)
            this.save(key, data)
        }
        if (localConfig.debugDBHelper) {
            data = ut.deepClone(data)
        }
        return data
    }

private freqCheck(key) {
        let info = this.saveFreCheck.get(key)
        let now = Date.now()
        if (!info) {
            this.saveFreCheck.set(key, { lastTime: now, count: 1 })
        }
        else {
            if (now - info.lastTime < 1000) {
                info.count++
                if (info.count > 20) {
                    twlog.error("dbHelper 持久化过于频繁", key)
                }
            }
            else {
                info.count = 0
            }
            info.lastTime = now
            this.saveFreCheck.set(key, info)
        }
    }
}

export const dbHelper = new DatabaseHelper()