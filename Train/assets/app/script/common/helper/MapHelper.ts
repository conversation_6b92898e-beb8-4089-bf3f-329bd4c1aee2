import { MapType } from "../constant/Enums"
import { TILE_SIZE, TILE_SIZE_HALF, SKEW_SIZE_HALF, SKEW_SIZE, DIR_POINTS_8 } from "../constant/Constant"

/**
 * 地图
 */
class MapHelper {

    /**
     * 根据网格坐标获取像素坐标
     * @param point [传入的point不会被改变]
     * @param type 
     */
    public getPixelByPoint(point: cc.Vec2, type?: MapType, out?: cc.Vec2) {
        out = out || cc.v2()
        if (type === MapType.SKEW) {
            out.x = (point.x - point.y) * SKEW_SIZE_HALF.width
            out.y = (point.x + point.y) * SKEW_SIZE_HALF.height + SKEW_SIZE_HALF.height
        } else if (type === MapType.SKEW_L) {
            out.x = -(point.x * SKEW_SIZE_HALF.width + SKEW_SIZE_HALF.width * 0.5)
            out.y = point.y * SKEW_SIZE.height + SKEW_SIZE_HALF.height * 0.5 - point.x * SKEW_SIZE_HALF.height
        } else if (type === MapType.SKEW_R) {
            out.x = point.x * SKEW_SIZE_HALF.width + SKEW_SIZE_HALF.width * 0.5
            out.y = point.y * SKEW_SIZE.height + SKEW_SIZE_HALF.height * 0.5 - point.x * SKEW_SIZE_HALF.height
        } else {
            point.mul(TILE_SIZE, out).addSelf(TILE_SIZE_HALF)
        }
        return out
    }

    /**
     * 根据像素位置获取网格坐标
     * @param pos 
     * @param type 
     */
    public getPointByPixel(pos: cc.Vec2, type?: MapType, out?: cc.Vec2) {
        out = out || cc.v2()
        if (type === MapType.SKEW) {
            let x = pos.x / SKEW_SIZE.width
            let y = pos.y / SKEW_SIZE.height
            out.x = Math.ceil(x + y) - 1
            out.y = Math.ceil(y - x) - 1
        } else if (type === MapType.SKEW_L) {
            out.x = Math.floor(-pos.x / SKEW_SIZE_HALF.width)
            out.y = Math.floor((pos.y - pos.x / SKEW_SIZE_HALF.width * SKEW_SIZE_HALF.height) / SKEW_SIZE.height)
        } else if (type === MapType.SKEW_R) {
            out.x = Math.floor(pos.x / SKEW_SIZE_HALF.width)
            out.y = Math.floor((pos.y + pos.x / SKEW_SIZE_HALF.width * SKEW_SIZE_HALF.height) / SKEW_SIZE.height)
        } else {
            out.x = Math.floor(pos.x / TILE_SIZE)
            out.y = Math.floor(pos.y / TILE_SIZE)
        }
        return out
    }

    // 获取实际的网格点
    public getActPointByPixel(pos: cc.Vec2, type: MapType, out?: cc.Vec2) {
        out = out || cc.v2()
        if (type === MapType.SKEW) {
            let x = pos.x / SKEW_SIZE.width
            let y = pos.y / SKEW_SIZE.height
            out.x = x + y
            out.y = y - x
        } else {
            out.x = pos.x / TILE_SIZE
            out.y = pos.y / TILE_SIZE
        }
        return out
    }

    /**
     * 转换地图实际大小
     */
    public convertMapSize(type: MapType, grid: cc.Size) {
        if (type === MapType.SKEW) {
            return cc.size((grid.width + grid.height) * SKEW_SIZE_HALF.width, (grid.width + grid.height) * SKEW_SIZE_HALF.height)
        } else if (type === MapType.SKEW_L || type === MapType.SKEW_R) {
            return cc.size(grid.width * SKEW_SIZE_HALF.width, grid.height * SKEW_SIZE.height + grid.width * SKEW_SIZE_HALF.height)
        } else {
            return cc.size(grid.width * TILE_SIZE, grid.height * TILE_SIZE)
        }
    }

    // 获取地图的矩形顶点
    public getMapPoints(type: MapType, grid: cc.Size, size: cc.Size) {
        const wh = size.width * 0.5
        const hh = size.height * 0.5
        if (type === MapType.SKEW) {
            return [cc.v2(), cc.v2(wh, hh), cc.v2(0, size.height), cc.v2(-wh, hh)]
        } else if (type === MapType.SKEW_L) {
            return [cc.v2(), cc.v2(-size.width, -grid.width * SKEW_SIZE_HALF.height), cc.v2(-size.width, grid.height * SKEW_SIZE.height - grid.width * SKEW_SIZE_HALF.height), cc.v2(0, grid.height * SKEW_SIZE.height)]
        } else if (type === MapType.SKEW_R) {
            return [cc.v2(), cc.v2(size.width, -grid.width * SKEW_SIZE_HALF.height), cc.v2(size.width, grid.height * SKEW_SIZE.height - grid.width * SKEW_SIZE_HALF.height), cc.v2(0, grid.height * SKEW_SIZE.height)]
        } else {
            return [cc.v2(), cc.v2(size.width, 0), cc.v2(size.width, size.height), cc.v2(0, size.height)]
        }
    }

    /**
     * 拷贝路径
     * @param outs 
     * @param paths 
     */
    public clonePath(outs: cc.Vec2[], paths: cc.Vec2[]) {
        for (let i = 0; i < paths.length; i++) {
            if (i < outs.length) {
                outs[i].set(paths[i])
            } else {
                outs.push(paths[i].clone())
            }
        }
        outs.splice(paths.length, outs.length - paths.length)
        return outs.length > 0
    }

    // 修正速度
    public amendMoveSpeed(speed: cc.Vec2, position: cc.Vec2, targetPosition: cc.Vec2, dt: number) {
        const targetDiffx = targetPosition.x - position.x
        const targetDiffy = targetPosition.y - position.y
        const dx = speed.x * dt
        const dy = speed.y * dt
        if (Math.abs(dx) <= Math.abs(targetDiffx)) {
            position.x += dx
        } else {
            position.x += targetDiffx
        }
        if (Math.abs(dy) <= Math.abs(targetDiffy)) {
            position.y += dy
        } else {
            position.y += targetDiffy
        }
        return position
    }

    // 获取拖拽时候的高
    public getDragOffsetY() {
        return SKEW_SIZE_HALF.height - 2
    }

    // 根据占地面积获取外圈
    public getOutersByPoints(points: cc.Vec2[]) {
        const arr: cc.Vec2[] = []
        for (let i = 0, l = points.length; i < l; i++) {
            const point = points[i]
            DIR_POINTS_8.forEach(m => {
                const x = point.x + m.point.x
                const y = point.y + m.point.y
                if (arr.some(p => p.equals2(x, y)) || points.some(p => p.equals2(x, y))) {
                    return
                }
                arr.push(cc.v2(x, y))
            })
        }
        return arr
    }

    // 根据大小生成站位点
    public genPointsBySize(size: cc.Vec2, start?: cc.Vec2): cc.Vec2[] {
        start = start || cc.v2()
        const arr = []
        for (let x = 0; x < size.x; x++) {
            for (let y = 0; y < size.y; y++) {
                arr.push(cc.v2(start.x + x, start.y + y))
            }
        }
        return arr
    }


    // 获取弧线坐标列表
    public getArcPoints(start: cc.Vec2, row: number, initcol: number, out: cc.Vec2[]) {
        out.length = 0
        for (let r = 0; r < row; r++) {
            const sy = start.y - r * 100
            const col = initcol + r
            for (let c = -col; c <= col; c++) {
                const x = start.x + c * 80
                const y = sy + c * c * 4
                out.push(cc.v2(x, y))
            }
        }
        return out
    }

    // 比较两个points数组是否一样
    public pointsEquals(arr1: cc.Vec2[], arr2: cc.Vec2[]) {
        if (arr1.length !== arr2.length) {
            return false
        }
        for (let i = 0, l = arr1.length; i < l; i++) {
            if (!arr1[i].equals(arr2[i])) {
                return false
            }
        }
        return true
    }

    //随机一个尽量远离已有点的坐标
    public sparsePosRandom(randomFunc: Function, curPosArr: cc.Vec2[] = [], minDis: number = 30, randomTimes: number = 10) {
        let x, y, bx, by
        let bestLen = -1
        minDis = minDis * minDis
        let succ = false
        while (randomTimes > 0) {
            randomTimes--
            let now = randomFunc()
            x = now.x
            y = now.y

            let isSuit = true
            let minLen = 1000000
            curPosArr.forEach(pos => {
                let len = pos.sub(now).magSqr()
                if (len < minDis) {
                    isSuit = false
                    if (len < minLen) {
                        minLen = len
                    }
                }
            });
            if (isSuit) {
                succ = true
                break
            }
            if (minLen > bestLen) {//更新不符合中，最合适的【最短距离最远的】
                bx = x
                by = y
                bestLen = minLen
            }
            else {
                x = bx
                y = by
            }
        }
        return cc.v2(x, y)
    }
}

export const mapHelper = new MapHelper()

if (CC_DEV) {
    window['mapHelper'] = mapHelper
}