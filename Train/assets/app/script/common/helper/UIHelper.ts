import ConditionObj from "../../model/common/ConditionObj"
import BaseBubbleCmpt from "../../view/cmpt/bubble/BaseBubbleCmpt"
import { Condition, InstanceLevelCfg, TypeIds } from "../constant/DataType"
import { BuildAttr, ConditionType, ItemID, UIFunctionType, ValueType } from "../constant/Enums"
import { gameHelper } from "./GameHelper"
import { resHelper } from "./ResHelper"
import { richHelper } from "./RichHelper"
import { timeHelper } from "./TimeHelper"
import { unlockHelper } from "./UnlockHelper"
import { viewHelper } from "./ViewHelper"

/**
 * 界面相关帮助方法
 */
class UIHelper {
    public setCostItems(costContent: cc.Node, buyCost: any[], keyTag: string) {
        let buyCosts = gameHelper.toConditions(buyCost)
        costContent.Items(buyCosts, (it, data) => {
            resHelper.loadIconByCondInfo(data, it.Child('icon'), keyTag)
            this.setLockCount(it.Child('count'), data)
            if (!it.getComponent(cc.Button)) return
            this.regClickPropBubble(it, data)
        })
        return buyCosts
    }
    public setLockCount(node: cc.Node, cond: ConditionObj) {
        let lb = node.Component(cc.Label)
        let max = cond.num
        let cur = gameHelper.getNumByCondition(cond)
        node.Component(cc.MultiColor).setColor(cur >= max ? 0 : 1)
        if (cond.type != ConditionType.PROP) {
            lb.string = ut.simplifyMoney(max)
        } else {
            if (cond.id == ItemID.WATER || cond.id == ItemID.ELECTRIC) {
                lb.string = ut.simplifyMoney(max)
            } else {
                lb.string = `${cur}/${max}`
            }
        }
    }
    public regClickPropBubble(it: cc.Node, data: Condition | ConditionObj, reserveBubble?: BaseBubbleCmpt) {
        it.off('click')
        it.on('click', () => { viewHelper.showBubble("RewardBubble", it, data, reserveBubble) })
    }

    public checkBuyCost(buyCost: Condition[]) {
        let conds = gameHelper.toConditions(buyCost), listFail = []
        if (!gameHelper.checkConditions(conds, listFail)) {
            gameHelper.showFailTips(listFail)
            return false
        }
        return true
    }

    public setRoleIcon(spNode: cc.Node, id: number, keyTag: string) {
        return resHelper.loadRoleCircleIcon(id, spNode.Component(cc.Sprite), keyTag)
    }

    public setTimeMailBig(spNode: cc.Node, propId: number, keyTag: string) {
        let url = `timeMail/xinjianneirong_big_${propId}`
        resHelper.setSpf(url, spNode, keyTag)
    }
    public setTimeMailSmall(spNode: cc.Node, propId: number, keyTag: string) {
        let url = `timeMail/xinjianneirong_small_${propId}`
        resHelper.setSpf(url, spNode, keyTag)
    }
    public setTimeMailHead(spNode: cc.Node, roleId: number, keyTag: string) {
        let url = `timeMail/character_fang_${roleId}`
        resHelper.setSpf(url, spNode, keyTag)
    }
    public setTimeMailHead2(spNode: cc.Node, roleId: number, keyTag: string) {
        let url = `timeMail/character_icon_${roleId}`
        resHelper.setSpf(url, spNode, keyTag)
    }

    // 需解锁设施名
    public setLockTipBuilds(rt: cc.RichText, unlock: TypeIds[]) {
        let names = gameHelper.getBuildNamesByAryTypeIds(unlock)
        rt.setLocaleKey("food_guiText_4", richHelper.richColor(names, "f95e5d"))
    }

    // 需要判断0不显示
    public setLabelNum0(it: cc.Node, cond: Condition) {
        let num = cond.num
        let bol = num != null && num > 0
        it.active = bol
        if (!bol) return
        it.Component(cc.Label).string = this.getShowNum(cond)
    }

    public getShowNum(cond: Condition | ConditionObj) {
        if (!cond.num) return ""
        if (cond.isHide) return ""
        return ut.simplifyMoney(cond.num)
    }

    public scrollToItemHorizontal(scrollView: cc.ScrollView, i: number, perWidth: number = 0.85) {
        let content = scrollView.content
        let max = content.width - scrollView.node.width
        if (max <= 0) return
        let it = content.children[i]
        if (!it) return
        let per = (it.x - it.width * perWidth) / max
        scrollView.stopAutoScroll()
        scrollView.scrollTo(cc.v2(per, 0))
    }

    /**<=0最左 >=1最右*/
    public calculateScrollToAnchorX(scrollView: cc.ScrollView, i: number, backOffset: number = 0) {
        let content = scrollView.content
        let max = content.width - scrollView.node.width
        if (max <= 0) return 0
        let layout = content.getComponent(cc.Layout)
        if (!layout) return 0
        let itemSize = content.children[0]?.getContentSize()
        if (!itemSize) return 0
        let cur = i * itemSize.width + (i - 1) * layout.spacingX + layout.paddingLeft - backOffset
        return cur / max
    }

    /**<=0最下 >=1最上*/
    public calculateScrollToAnchorY(scrollView: cc.ScrollView, i: number, backOffset: number = 0) {
        let content = scrollView.content
        let max = content.height - scrollView.node.height
        if (max <= 0) return 1
        let layout = content.getComponent(cc.Layout)
        if (!layout) return 1
        let itemSize = content.children[0]?.getContentSize()
        if (!itemSize) return 1
        let cur = i * itemSize.height + (i - 1) * layout.spacingY + layout.paddingTop - backOffset
        return 1 - cur / max
    }

    public setIconNum(node: cc.Node, cond: ConditionObj, tag: string) {
        resHelper.loadIconByCondInfo(cond, node.Child('icon'), tag)
        node.Child('num', cc.Label).string = uiHelper.getShowNum(cond)
        node.Child('num', cc.MultiColor).setColor(!gameHelper.checkCondition(cond))
    }

    public setBuildAttrsText(text: cc.Node, type: BuildAttr, val: number) {
        text.setLocaleKey('trainItemBuild_guiText_2', `+${val}`)
    }

    public hideCurrency(currencyNode: cc.Node) {
        currencyNode.children.forEach(n => n.active = false)
    }
    public showCurrency(currencyNode: cc.Node, aryCost: Condition[]) {
        let dic = {
            [ConditionType.HEART]: 'HeartUI',
            [ConditionType.DIAMOND]: 'DiamondUI',
            [ConditionType.STAR_DUST]: 'StarDustUI',
            [ConditionType.ELECTRIC]: 'ElectricUI',
            [ConditionType.WATER]: 'WaterUI',
        }
        aryCost.forEach(({ type, id, num }) => {
            let realType = type
            if (type == ConditionType.PROP) {
                if (!!id && id == ItemID.ELECTRIC) {
                    realType = ConditionType.ELECTRIC
                }
                else if (!!id && id == ItemID.WATER) {
                    realType = ConditionType.WATER
                }
            }
            let key = dic[realType]
            if (key && num > 0) {
                let node = currencyNode.Child(key)
                if (node) {
                    node.active = true
                }
            }
        })
    }
    /**ValueType配置转为展示字符串*/
    public getShowValueType(valueType: ValueType, value: number) {
        let pre = value >= 0 ? '+' : '-'
        let str = ''
        if (valueType == ValueType.INT) {
            str = String(value)
        } else if (valueType == ValueType.PER) {
            str = `${value * 100}%`
        }
        return pre + str
    }
    /**
     * 角色选中热区
     * 角色体型> =标准热区的：点击响应热区 = 角色体型
     * 角色体型 < 标准热区的：点击响应热区 = 标准热区
    */
    public setRoleButtonSize(btn: cc.Node, sk?: cc.Node, option?: {
        /**角色标准宽*/
        width?: number
        /**角色标准高*/
        height?: number
        /**角色缩放比例 默认1.0*/
        scale?: number
        /**按钮最大宽*/
        widthLimit?: number
        /**按钮最大高*/
        heightLimit?: number
        /**按钮标准高(需要动态调整锚点)*/
        btnHeightMax?: number
        /**下增加高度*/
        btnHeightDown?: number
    }) {
        if (!cc.isValid(btn)) return
        if (sk) {
            let width = option?.width || 236
            let height = option?.height || 328
            let scale = option?.scale || 1.0
            let w = Math.max(sk.width, width) * scale
            let h = Math.max(sk.height, height) * scale
            if (option?.widthLimit) {
                w = Math.min(w, option?.widthLimit)
            }
            if (option?.heightLimit) {
                h = Math.min(h, option?.heightLimit)
            }
            btn.width = w
            btn.height = h + (option?.btnHeightDown || 0)
            let btnHeightMax = option?.btnHeightMax
            if (btnHeightMax > 0) {
                btn.anchorY = 0.5 - (btn.height - btnHeightMax) * 0.5 / btn.height
            }
        }
        // this.showRoleButtonSize(btn)
    }
    private showRoleButtonSize(btn: cc.Node) {
        let name = '_show_area'
        let it = btn.Child(name)
        if (!it) {
            it = new cc.Node(name)
            it.parent = btn
            let g = it.addComponent(cc.Graphics)
            g.fillColor.a = 100
        }
        it.zIndex = -1
        let width = btn.width, height = btn.height
        let anchor = btn.getAnchorPoint()
        let g = it.getComponent(cc.Graphics)
        g.clear()
        g.fillRect(width * (0 - anchor.x), height * (0 - anchor.y), width, height)
        g.stroke()
    }
    /**
     * 适用item/drag/role/body/sp的结构
     * @param btnHeightMax drag标准高
     * @param btnHeightDown drag下增高(即drag比sk高多少)
    */
    public setRoleDragSize(sk: cc.Node, btnHeightMax: number, btnHeightDown: number) {
        if (!cc.isValid(sk)) return
        let body = sk.parent
        let role = body.parent
        let drag = role.parent
        let item = drag.parent
        let scale = role.scaleY * body.scaleY * sk.scaleY
        this.setRoleButtonSize(drag, sk, { scale, widthLimit: item.width, btnHeightMax, btnHeightDown })
    }
    /**
     * 副本的开采倒计时
     * 适用结构:
     * cc.Label: layout/time
     * cc.Mask: per/bg
    */
    public updateInstanceTimeAndPer(root: cc.Node, data: InstanceLevelCfg, call: () => void) {
        // let time = root.Child('layout/time', cc.Label)
        // let info = { cur: 0, max: data.instanceRewardTime * 60 }//单位秒
        // let per = root.Child('per')
        // let width = per.children[0].width
        // let showPer = () => {
        //     info.cur = gameHelper.instance.getSweepOverTimeSec(data)
        //     per.width = (info.max - info.cur) / info.max * width
        // }
        // showPer()
        // time.unscheduleAllCallbacks()
        // time.setLocaleUpdate(() => { return timeHelper.getTimeShortText(info.cur) })
        // time.scheduleUpdate(() => {
        //     showPer()
        //     time.Component(cc.LocaleLabel).updateString()
        //     if (gameHelper.instance.isSweepOver(data)) {
        //         time.unscheduleAllCallbacks()
        //         call()
        //     }
        // })
    }

    public checkInstanceBattleBg(data: InstanceLevelCfg, it: cc.Node) {
        if (!data) return
        const ins = it.Child("instance")
        const iBg = ins.Child("bg")
        if (iBg.Data == data) {
            return
        }
        iBg.children.forEach(c => c.active = false)
        iBg.Data = data
        const baseNode = iBg.Child(data.weather)
        baseNode.active = true
    }

    public checkInstanceBattleWeather(data: InstanceLevelCfg, weatherNode: cc.Node, buff) {
        if (!weatherNode) return
        if (!data) return void (weatherNode.active = false)
        weatherNode.active = true
        weatherNode.Child("icon").getComponent(cc.MultiFrame).setFrame(data.weather)
        const content = weatherNode.Child("content", cc.RichText)
        content.setLocaleUpdate(() => buff.getSkills()[0].getDescStr())
    }

    public updateTransportBattleBg(it: cc.Node, isNight: boolean) {
        const bg = it.Child("transport")
        if (!bg) return void console.error("updateTransportBattleBg error")
        
        bg.Child("ground/day/bg1",cc.MultiFrame).setFrame(!isNight)
        bg.Child("ground/day/bg2",cc.MultiFrame).setFrame(!isNight)
    }

}

export const uiHelper = new UIHelper()
