import { startHelper } from "../../../../startScene/StartHelper"
import { TaEvent } from "../event/TaEvent"
import { localConfig } from "../LocalConfig"
import { gameHelper } from "./GameHelper"

// 数数科技 上报
class TaHelper {

    private _ta: ThinkingAnalyticsAPI = null
    private DEBUG_MODE: string = 'none' //"none": 不开启Debug, "debug": 开启 Debug 模式，并入库, "debugOnly": 开启 Debug 模式，不入库
    private OPEN: boolean = localConfig.ta?.open

    private readonly TICK_INTERVAL: number = 30 * 60 //定时上报间隔
    private tick: number = 0

    private get ta() {
        if (this.isIgnore()) return
        if (!this._ta) {
            this.DEBUG_MODE = localConfig.ta.debugMode
            const ta = window['ta_twomiles'] //如果初始化的时候已经生成过ta实例，直接拿来用
            const appId = localConfig.release ? '38cc120446494691998a54146d7556a9' : 'debug-appid'
            this._ta = ta || startHelper.newThinkingAnalyticsAPI({ appId, debugMode: this.DEBUG_MODE })
            this.timeEvent(TaEvent.TA_GAME_HIDE)
            cc.game.on(cc.game.EVENT_SHOW, function () {
                this.track(TaEvent.TA_GAME_SHOW, { source: gameHelper.getEnterQuery() ? 1 : 0 })
                this.timeEvent(TaEvent.TA_GAME_HIDE)
            }, this)
            cc.game.on(cc.game.EVENT_HIDE, function () {
                this.track(TaEvent.TA_GAME_HIDE)
                this.timeEvent(cc.game.EVENT_SHOW)
            }, this)

            // 设置静态属性
            this.ta.setSuperProperties({
                client_version: gameHelper.getVersion(),
            })
        }
        return this._ta
    }

    public init() {
        if (this.isIgnore()) {
            return
        } else if (!window['ta_twomiles']) {
            this.ta.init()
        }
    }

    public getDistinctId() {
        return this.ta?.getDistinctId()
    }

    public getOs() {
        try {
            return this.ta?.getPresetProperties()?.os || 'none'
        } catch (error) {
            console.error(error)
            return 'none'
        }
    }

    public getOsVersion() {
        try {
            return this.ta?.getPresetProperties()?.osVersion || ''
        } catch (error) {
            console.error(error)
            return ''
        }
    }

    public getOsAndVersion(): string {
        const os = this.getOs(), version = this.getOsVersion()
        return version ? os + ';' + version : os
    }

    // 只有正式国内环境才需要上报ta
    private isIgnore() {
        if (!this.OPEN) {
            return true
        } else if (this.DEBUG_MODE === 'none') {
            return cc.sys.isBrowser
        }
        return false
    }

    public login(id: string) {
        if (this.isIgnore()) {
            return
        }
        this.ta.login(id)
    }

    public logout() {
        if (this.isIgnore()) {
            return
        }
        this.ta.logout()
    }

    public update(dt: number) {
        if (this.isIgnore()) {
            return
        }
        this.tick += dt
        if (this.tick >= this.TICK_INTERVAL) {
            this.tick -= this.TICK_INTERVAL
            this.trackTick()
        }
    }

    // 上报定时数据
    private trackTick() {
    }

    public userSet(data: any) {
        if (this.isIgnore()) {
            return
        }
        this.ta.userSet(data)
    }

    public userSetOnce(data: any) {
        if (this.isIgnore()) {
            return
        }
        this.ta.userSetOnce(data)
    }

    // 上报一个事件
    public track(eventId: string, properties?: any) {
        properties = properties || {}
        if (this.isIgnore()) {
            return
        }
        this.ta.track(eventId, properties, new Date(), (res: any) => {
            twlog.info("taHelper.track", eventId, res)
        })
    }

    // 记录事件时长
    public timeEvent(eventId: string) {
        if (this.isIgnore()) {
            return
        }
        this.ta.timeEvent(eventId)
    }
}

export const taHelper = new TaHelper()
if (cc.sys.isBrowser) {
    window['taHelper'] = taHelper
}