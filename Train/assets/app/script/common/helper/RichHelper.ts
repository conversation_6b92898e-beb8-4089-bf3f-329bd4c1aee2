import ConditionObj from "../../model/common/ConditionObj"
import { ItemCfg } from "../constant/DataType"
import { CarriageID, CarriageType, ConditionType, ItemID, LifeSkillEffectType } from "../constant/Enums"
import { gameHelper } from "./GameHelper"

/**
 * 富文本相关帮助方法
 */
class RichHelper {
    private dicCurrenyType: { [key: string]: string } = {
        [ConditionType.HEART]: 'heart',
        [ConditionType.WATER]: 'water',
        [ConditionType.DIAMOND]: 'gem',
        [ConditionType.ELECTRIC]: 'electric',
        [ConditionType.STAR_DUST]: 'star',
    }
    private dicEffectCurrenyType: { [key: string]: string } = {
        [LifeSkillEffectType.HEART]: 'heart',
        [LifeSkillEffectType.WATER]: 'water',
        [LifeSkillEffectType.ELECTRICITY]: 'electric',
        [LifeSkillEffectType.STAR]: 'star',
        [LifeSkillEffectType.TIP]: 'star',
    }
    public getCurrenyName(type: ConditionType) {
        let str = this.dicCurrenyType[type]
        if (str) return str
        cc.error("getCurrenyName unknown type", type)
        return ''
    }
    public getEffectCurrenyName(type: LifeSkillEffectType) {
        let str = this.dicEffectCurrenyType[type]
        if (str) return str
        cc.error("getEffectCurrenyName unknown type", type)
        return ''
    }
    public richImage(type: ConditionType) {
        let str = this.getCurrenyName(type)
        if (!str) return ''
        return `<img src="${str}"/>`
    }
    public richImageProp(id: number) {
        let cfg = assetsMgr.getJsonData<ItemCfg>('Item', id)
        if (!cfg) return ''
        return `<img src="${cfg.icon}"/>`
    }
    public richImageConditionObj(data: ConditionObj) {
        let type = data.type
        if (type == ConditionType.PROP) {
            let id = data.id
            if (id == ItemID.WATER) {
                return this.richImage(ConditionType.WATER)
            } else if (id == ItemID.ELECTRIC) {
                return this.richImage(ConditionType.ELECTRIC)
            } else {
                return ''
            }
        } else {
            return this.richImage(type)
        }
    }
    public getRichImageByCarriage(id: CarriageID, index?: number) {
        let icon
        if (id == CarriageID.ENGINE) {
            icon = richHelper.richImage(ConditionType.ELECTRIC)
        }
        else if (id == CarriageID.WATER) {
            icon = richHelper.richImage(ConditionType.WATER)
        }
        else if (gameHelper.train.getCarriageById(id)?.getType() == CarriageType.DORM) {
            icon = index % 2 == 0 ? richHelper.richImage(ConditionType.HEART) : richHelper.richImage(ConditionType.STAR_DUST)
        }
        return icon
    }
    public richColor(data: number | string, color: string) {
        return `< color=#${color}> ${data} </c>`
    }
    public richBold(str: string) {
        return `<b>${str}</b>`
    }
    // 富文本图片高度与文本高度不一致时使用
    public richSetImgSize(rt: cc.RichText, kp: KeyParams, sizeRich: number, sizeImage: number) {
        kp.rich = `<size=${sizeRich}>{0}`
        rt.fontSize = sizeImage
        rt.setKeyParams(kp)
    }
}

export const richHelper = new RichHelper()
if (cc.sys.isBrowser) {
    window['richHelper'] = richHelper
}