
class TimeHelper {
    // 只保留两个时间单位
    public getTimeText(second: number) {
        second = Math.floor(second)
        if (second < 60) {
            return this.getTimeSecond(second)
        }
        let sec = second % 60
        let min = Math.floor(second / 60)
        if (min < 60) {
            return this.getTimeMinute(min) + this.checkSecond(sec)
        }
        let hour = Math.floor(min / 60)
        if (hour < 24) {
            return this.getTimeHour(hour) + this.checkMinute(min % 60)
        }
        let day = Math.floor(hour / 24)
        return day + assetsMgr.lang("common_timeUnit_day") + this.checkHour(hour % 24)
    }

    private checkHour(time: number, short?) {
        if (time == 0) return ''
        return this.getTimeHour(time, short)
    }
    private checkMinute(time: number, short?) {
        if (time == 0) return ''
        return this.getTimeMinute(time, short)
    }
    private checkSecond(time: number, short?) {
        if (time == 0) return ''
        return this.getTimeSecond(time, short)
    }

    private getTimeHour(time: number, short?) {
        return time + assetsMgr.lang(short ? 'common_timeUnitShort_hour' : 'common_timeUnit_hour')
    }
    private getTimeMinute(time: number, short?) {
        return time + assetsMgr.lang(short ? 'common_timeUnitShort_minute' : 'common_timeUnit_minute')
    }
    private getTimeSecond(time: number, short?) {
        return time + assetsMgr.lang(short ? 'common_timeUnitShort_second' : 'common_timeUnit_second')
    }

    // xx时xx分xx秒
    public getTimeShortText(second: number) {
        let short = true
        second = Math.floor(second)
        if (second < 60) {
            return this.getTimeSecond(second, short)
        }
        let sec = second % 60
        let min = Math.floor(second / 60)
        if (min < 60) {
            return this.getTimeMinute(min, short) + this.checkSecond(sec, short)
        }
        let hour = Math.floor(min / 60)
        if (hour < 24) {
            return this.getTimeHour(hour, short) + this.checkMinute(min % 60, short)
        }
        let day = Math.floor(hour / 24)
        return day + assetsMgr.lang("common_timeUnit_day") + this.checkHour(hour % 24, short)
    }
    private getMinSec(second: number) {
        let min = Math.floor(second / 60)
        let sec = second % 60
        let str1 = min > 0 ? min + assetsMgr.lang('common_timeUnitShort_minute') : ''
        let str2 = sec > 0 ? sec + assetsMgr.lang('common_timeUnitShort_second') : ''
        return str1 + str2
    }

    // 天/时/分/秒
    public getTimeOnlyOne(second: number) {
        if (second < 0) second = 0
        if (second < 60) {
            return second + assetsMgr.lang('common_timeUnitShort_second')
        } else if (second < 3600) {
            let min = Math.floor(second / 60)
            return min + assetsMgr.lang('common_timeUnitShort_minute')
        } else if (second < 86400) {
            let hour = Math.floor(second / 3600)
            return hour + assetsMgr.lang('common_timeUnitShort_hour')
        } else {
            let day = Math.floor(second / 86400)
            return day + assetsMgr.lang('common_timeUnitShort_day')
        }
    }

    /** 周一 - 周日 : 0 - 6 */
    public getWeekDay(msd: number) {
        let date = new Date(msd)
        let num = date.getDay()//Sunday - Saturday : 0 - 6
        return (num + 6) % 7
    }

    /**
     * 周几(逻辑上刷新的时间)
     * @param resetMs 几点刷新(距离0点的毫秒数)
     */
    public getRefreshWeek(msd: number, resetMs: number) {
        return this.getWeekDay(msd - resetMs)
    }

    /** 周几 */
    public getWeekLang(msd: number) {
        let idx = this.getWeekDay(msd)
        return assetsMgr.lang(`common_days_${idx + 1}`)
    }

    /**
     * 周几、几、几
     * @param days 周一到周日:0-6
     */
    public getWeekManyLang(days: number[]) {
        let str = ''
        days.forEach((num, idx) => {
            if (num > 0) {
                if (str.length > 0) str += assetsMgr.lang('common_guiText_21')
                str += assetsMgr.lang(`number_${idx + 1}`)
            }
        })
        return str
    }

}

export const timeHelper = new TimeHelper()
window['timeHelper'] = timeHelper
