class Counter {
    private time: number = 0
    public value: number = 0
    public acc: number = 0
    public callTimes: number = 0

    start(now?) {
        this.time = now || performance.now()
    }

    end(now?) {
        now = now || performance.now()
        this.value = now - this.time
    }

    public add() {
        this.acc += this.value
    }
}

class Profiler {
    private recordMap: {[key: string]: Counter} = {}

    public logic: Counter = new Counter()
    public render: Counter = new Counter()
    public renderNodeSet = new Set()

    showTime: number = 0

    init() {
        cc.director.on(cc.Director.EVENT_BEFORE_UPDATE, this.beforeUpdate.bind(this))
        cc.director.on(cc.Director.EVENT_AFTER_UPDATE, this.afterUpdate.bind(this))
        cc.director.on(cc.Director.EVENT_BEFORE_DRAW, this.beforeDraw.bind(this))
        cc.director.on(cc.Director.EVENT_AFTER_DRAW, this.afterDraw.bind(this))

        this.initLogic()
        this.initRender()
    }

    private initLogic() {
        let start = (key)=> this.start(key)
        let end = (key)=> this.end(key)
        this.hook(cc.director["_compScheduler"], "updatePhase", start, end)
        this.hook(cc.director["_scheduler"], "update", start, end, "_schedulerUpdate")
        this.hook(cc.director["_compScheduler"], "lateUpdatePhase", start, end)
    }

    private initRender() {
        let _proto = cc["RenderFlow"].prototype
        let start = (key, node)=> {
            this.renderNodeSet.add(node)
            this.render.acc++
        }
        let funcs = ["_localTransform", "_worldTransform", "_updateRenderData", "_opacity", "_color", "_render", "_children", "_postRender"]
        for (let func of funcs) {
            this.hook(_proto, func, start)
        }
    }

    private hook(obj, funcName, start?, end?, key?) {
        key = key || funcName
        let self = this
        let func = obj[funcName]
        obj[funcName] = function(...params){
            start && start.call(self, key, ...params)
            let res = func.call(this, ...params)
            end && end.call(self, key, ...params)
            return res
        }
    }

    public start(key) {
        if (!this.recordMap[key]) {
            this.recordMap[key] = new Counter()
        }
        this.recordMap[key].start()
    }

    public end(key) {
        this.recordMap[key]?.end()
    }

    public addStart(key) {
        this.start(key)
    }

    public addEnd(key) {
        this.end(key)
        this.recordMap[key].add()
    }

    private beforeUpdate () {
        // let now = cc.director["_lastUpdate"]
        this.logic.start()
        for (let key in this.recordMap) {
            this.recordMap[key].acc = 0
        }
    }
    
    private afterUpdate (dt) {
        this.logic.end()
    }

    private show() {
        console.log(`tot: ${ut.toRound(this.logic.value)}ms`)
        for (let key in this.recordMap) {
            let info = this.recordMap[key]
            let val = info.acc || info.value
            console.log(`${key}: cost ${ut.toRound(val)}ms, per: ${ut.toRound(val / this.logic.value * 100)}%`)
        }
        console.log(`render count: ${this.render.acc}`, `node count: ${this.renderNodeSet.size}`)
    }

    private beforeDraw() {
        this.renderNodeSet.clear()
        this.render.acc = 0
    }
    
    private afterDraw (dt) {
        this.showTime += dt
        if (this.showTime > 1) {
            this.showTime = 0
            this.show()
        }
    }
}

export const profiler = new Profiler()

if (CC_DEV) {
    window["profiler"] = profiler
}