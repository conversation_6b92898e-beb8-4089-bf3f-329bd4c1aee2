import { util } from "../../../core/utils/Utils";
import JsbEvent from "../event/JsbEvent";
import { gameHelper } from "./GameHelper";

export default class JsbHelper {

    private callMap: { [key: string]: any[] } = {}

    private iapCfg: { [key: string]: string } = {}

    /**
     * 
     * @param event 
     * @param parameters 
     * @description 注意：多次调用同一个事件，只按调用顺序返回，因为native侧很难用reqId的方式；对于有状态的请求，尽量保证同时只存在一个调用
     */
    public async call(event: string, jsonObj?: any): Promise<any> {
        if (!this.callMap[event]) {
            this.callMap[event] = []
        }
        return new Promise((cb) => {
            this.callMap[event].push({ cb })
            this.send(event, JSON.stringify(jsonObj));
        })
    }

    public cast(event: string, jsonObj?: any): any {
        this.send(event, JSON.stringify(jsonObj));
    }

    // 统一生成json字符串形式传给native
    private send(event: string, jsonStr?: string) {
        if (!jsonStr || jsonStr == "") {
            jsonStr = "{}";
        }
        if (ut.isAndroid()) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/JsbHelper", "request", "(Ljava/lang/String;Ljava/lang/String;)V", event, jsonStr);
        }
        else if (ut.isIos()) {
            jsb.reflection.callStaticMethod("jsbHelp", "request:JsonStr:", event, jsonStr);
        }
    }

    /**
     * 
     * @param event 
     * @param callback 
     * @param target 
     * @description 注册事件，用于native主动调用的场景
     */
    public on(event: string, callback?: Function, target?: any) {
        eventCenter.on(event, callback, target);
    }

    public off(event: string, callback?: Function, target?: any) {
        eventCenter.off(event, callback, target);
    }

    /* 
     * @param event 
     * @param jsonStr 
     * @description native回调时调用
     */
    public emit(event: string, json?: object) {
        twlog.info("jsbEmit", event, JSON.stringify(json))
        if (json) {
            for (let key in json) {
                if (json[key] == "true") {
                    json[key] = true;
                } else if (json[key] == "false") {
                    json[key] = false;
                }
            }
        }
        eventCenter.emit(event, json);

        let calls = this.callMap[event] || []
        let call = calls.shift()
        if (call) {
            call.cb(json)
        }
    }

    private getDeepLinkParams() {
        let params = ''
        if (ut.isAndroid()) {
            const res = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/AppsFlyerHelper', 'getDeepLinkParams', '()Ljava/lang/String;')
            const obj = JSON.parse(res || '{}')
            if (!obj.error) {
                params = obj.param
            }
        } else if (ut.isIos()) {
            const res = jsb.reflection.callStaticMethod('AppsflyerHelper', 'getDeepLinkParams', null)
            if (res && res !== 'error') {
                params = res
            }
        }
        if (!params) {
            return null
        }
        const [uid, type, date] = params.split('|')
        return { uid, type, date: Number(date) ?? undefined }
    }

    // 获取安装参数
    public async getInstallParams() {
        if (!cc.sys.isNative) {
            return null
        } else if (gameHelper.isGLobal()) { //海外
            return this.getDeepLinkParams()
        }
        const res = await jsbHelper.call(JsbEvent.GET_INSTALL_PARAMS)
        if (!res.error) {
            return res
        }
        return null
    }

    // 获取启动参数
    public getAwakeParams() {
        if (!cc.sys.isNative) {
            return null
        } else if (gameHelper.isGLobal()) {
            return this.getDeepLinkParams()
        } else if (ut.isAndroid()) {
            const res = jsb.reflection.callStaticMethod('org/cocos2dx/javascript/OpenInstallHelper', 'getAwakeParams', '()Ljava/lang/String;')
            const obj = JSON.parse(res || '{}')
            return !obj.error ? obj : null
        } else if (ut.isIos()) {
            const res = jsb.reflection.callStaticMethod('OpenInstallHelper', 'getAwakeParams', null)
            if (res !== 'error') {
                try {
                    return JSON.parse(res)
                } catch (error) { }
            }
        }
        return null
    }

    /**
     * @param key 
     * @param content value
     * @param service ios必须传 类似key 在ios中 service就好像给宏取名字,表示存储的这个东西是做什么的.通过这两个key就可以指定唯一性 可以暂时当成另一个key
     * @description 存数据到设备手机上，并且不会因为应用被卸载而删除
     * @returns Bool
     */
    public async saveDeviceData(key: String, content: String, service?: String) {
        if (ut.isIos()) {
            let { result } = await this.call(JsbEvent.SAVE_DEVICE_DATA, { key, content, service })
            return !!result
        } else {
            // android to do 
            return false
        }
    }

    // 获取存储在设备手机上的数据 返回null为没有存储数据
    public async getDeviceData(key: String, service?: String) {
        if (ut.isIos()) {
            let { result } = await this.call(JsbEvent.GET_DEVICE_DATA, { key, service })
            if (result === 'null') return ''
            return result
        } else {
            // android to do 
            return ''
        }
    }

    // 删除存储在设备手机上的数据
    public async delDeviceData(key: String, service?: String) {
        if (ut.isIos()) {
            let { result } = await this.call(JsbEvent.DEL_DEVICE_DATA, { key, service })
            return !!result
        } else {
            // android to do 
            return false
        }
    }

    // 获取消息推送token
    public async getFcmToken() {
        if (!cc.sys.isNative) {
            return { err: true }
        }
        let { token } = await this.call(JsbEvent.GET_NOTICE_TOKEN)
        // console.log('getFcmToken', token)
        if (token === 'null') {
            return { err: true }
        }
        return { token }
    }

    // 跳转到手机的九万亩的app设置页面 这个没有返回结果 因为不太好获取跳转之后返回游戏的时机
    public openSelfSetting() {
        if (cc.sys.isNative) {
            this.cast(JsbEvent.OPEN_APP_SETTING)
        }
    }

    // 检测是否有通知的权限 返回true or false
    public async checkNoticePer() {
        if (!cc.sys.isNative) {
            return false
        }
        let { result } = await this.call(JsbEvent.CHECK_NOTICE_PER)
        return !!result
    }

    public async getLangOrderList(eventName: string) {
        if (!cc.sys.isNative) {
            return { result: [] }
        }
        let { error, result, nextIndex } = await this.call(eventName)
        if (error) return { error, result }
        while (nextIndex) {
            let info = await this.call("GET_MORE_LIST_INDEX", { nextIndex })
            if (info.error) break
            nextIndex = info.nextIndex
            result = result.concat(info.result)
        }
        console.log("result is ", result.length)
        return { error, result }
    }

    public async getPackageSign() {
        if (!CC_JSB) return
        let res = await this.call(JsbEvent.GET_PACKAGE_SIGN)
        if (res && res.result) return res.result
    }

}

export const jsbHelper = window['jsbHelper'] = new JsbHelper()