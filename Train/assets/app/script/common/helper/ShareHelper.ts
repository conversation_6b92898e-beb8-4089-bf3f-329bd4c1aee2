import { wxHelper } from "../wx/WxHelper";
import { gameHelper } from "./GameHelper";

class ShareHelper {
    private shareFailCount: number = 0
    private defaultShareList = [
        {
            title: "开始营业",
            url: "https://twgame-hotel-1257666530.file.myqcloud.com/share/fenxiang_1.png"
        },
        {
            title: "开始营业",
            url: "https://twgame-hotel-1257666530.file.myqcloud.com/share/fenxiang_2.png"
        },
        {
            title: "开始营业",
            url: "https://twgame-hotel-1257666530.file.myqcloud.com/share/fenxiang_3.png"
        },
    ]

    public init () {
        if (ut.isMiniGame()) {
            wx.showShareMenu({
                withShareTicket: true,
                menus : ["shareAppMessage", "shareTimeline"],
            })
    
            let wxShare = wx.uma || wx
            wxShare.onShareAppMessage(() => {
                let randomInfo = this.getRandomShareInfo();
                return {
                    title: randomInfo.title,
                    imageUrl : randomInfo.url,
                }
            })

            if (wx.onShareTimeline) {
                wx.onShareTimeline(() => {
                    let randomInfo = this.getRandomShareInfo();
                    return {
                        title: randomInfo.title,
                        imageUrl : randomInfo.url,
                    }
                })
            }
        }
    }

    public async shareUnlockCustomer(customerName: string) {
        let title = assetsMgr.lang("share.unlockCus", customerName);
        return await this.share(title, null, null, false);
    }

    @ut.addLock
    public async share(title?: string, imgUrl?: string, queryInfo?: { uid?: string }, loose: boolean = true) {
        if (!title || !imgUrl) {
            let randomInfo = this.getRandomShareInfo();
            title = title || randomInfo.title
            imgUrl = imgUrl || randomInfo.url
        }
        let uid = gameHelper.user.getUid();

        queryInfo = queryInfo || {}
        queryInfo.uid = uid;
        let result = true;
        if (ut.isMiniGame()) {
            result = await wxHelper.share(title, imgUrl, queryInfo, loose);
        }
        if (this.shareFailCount > 1) {
            this.shareFailCount = 0;
            return true;
        }
        if (result == false) {
            this.shareFailCount++;
        }
        else {
            this.shareFailCount = 0;
        }
        return result;
    }

    public getRandomShareInfo() {
        let index = ut.randomIndex(this.defaultShareList.length);
        return this.defaultShareList[index];
    }

}

export const shareHelper = new ShareHelper();