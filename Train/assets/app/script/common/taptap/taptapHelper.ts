import JsbEvent from "../event/JsbEvent";
import { jsbHelper } from "../helper/JsbHelper";

class TaptapHelper {

    public async login() {
        let res = await jsbHelper.call(JsbEvent.TAPTAP_LOGIN)
        if (res.error) {
            // reportHelper.reportError('google nativelogin error', res)
            return { errcode: res.code || -10086, msg: res.message }
        } else {
            return { kid: res.kid, mac_key: res.mac_key };
        }
    }

    public async checkRealName(id: string) {
        let res = await jsbHelper.call(JsbEvent.CHECK_REAL_NAME, { id })
        return res.code
    }
}

export const taptapHelper = new TaptapHelper();
