/**
 * 与native通信的事件
 */
export default {
    INIT_AD: "INIT_AD", //初始化激励视频广告
    LOAD_REWARD_VIDEO_AD: "LOAD_REWARD_VIDEO_AD", //加载激励视频广告
    SHOW_REWARD_VIDEO_AD: "SHOW_REWARD_VIDEO_AD", //播放激励视频广告
    REWARD_AD_ON_ERROR: "REWARD_AD_ON_ERROR", //激励视频广告出错

    LOAD_INTERSTITIAL_AD: "LOAD_INTERSTITIAL_AD", //加载插屏广告
    SHOW_INTERSTITIAL_AD: "SHOW_INTERSTITIAL_AD", //展示插屏广告
    INTERSTITIAL_AD_ON_ERROR: "INTERSTITIAL_AD_ON_ERROR", //插屏广告出错

    LOAD_BANNER_AD: "LOAD_BANNER_AD", //加载banner广告
    SHOW_BANNER_AD: "SHOW_BANNER_AD", //展示banner广告
    HIDE_BANNER_AD: "HIDE_BANNER_AD", //隐藏banner广告

    COPY_TO_CLIPBOARD: "COPY_TO_CLIPBOARD", //拷贝内容到剪贴板
    OPEN_TAP_TAP_TALK: "OPEN_TAP_TAP_TALK", //打开taptap游戏圈

    APPLE_LOGIN: "APPLE_LOGIN", //苹果登录
    WX_APP_LOGIN: "WX_APP_LOGIN", //微信app登录

    WX_SHARE_LINK: "WX_SHARE_LINK", //微信app分享链接
    UMENG_ENENT: "UMENG_ENENT", //发送友盟事件

    REQUEST_PERMISSION: "REQUEST_PERMISSION", //请求相关权限

    GET_PACKAGE_SIGN: "GET_PACKAGE_SIGN", // 获取包签名

    WX_SHARE_MINIGAME: "WX_SHARE_MINIGAME", //分享小程序
    WX_SHARE_IMAGE: "WX_SHARE_IMAGE", //分享图片

    SAVE_PHOTO: "SAVE_PHOTO", //保存图片
    SAVE_PHOTO_PERMIT: "SAVE_PHOTO_PERMIT", //查询保存图片的权限
    PHOTO_PERMIT: "PHOTO_PERMIT", //授权 图片保存
    GDTACTION_EVENT: "GDTACTION_EVENT", //腾讯 事件上报 https://developers.adnet.qq.com/doc/ios/union/union_data_detector_guide

    FACEBOOK_LOGIN: "FACEBOOK_LOGIN",
    TWITTER_LOGIN: "TWITTER_LOGIN",
    TWITTER_SHARE: "TWITTER_SHARE",

    FACEBOOK_SHARE_LINK: "FACEBOOK_SHARE_LINK",
    FACEBOOK_SHARE_PHOTO: "FACEBOOK_SHARE_PHOTO",

    GDTACTION_EVENT_JSON: { "reward_ad_load_time": "gt_ad_request" },

    OPEN_WEIBO: "OPEN_WEIBO", //打开对应app 不只是微博

    INIT_PAY: "INIT_PAY",
    GDTACTION_START: "GDTACTION_START",

    IAP_INIT: "IAP_INIT",
    GOOGLE_PAY: "GOOLE_PAY",
    APPLE_PAY: "APPLE_PAY",
    GET_LOST_ORDER_LIST: "GET_LOST_ORDER_LIST",
    CONSUME_ORDER: "CONSUME_ORDER",

    GET_DEVICE_TOKEN : "GET_DEVICE_TOKEN",

    GET_DEVICE_INFO: "GET_DEVICE_INFO", //获取设备信息
    /**注册成功上报openinstall 后台*/
    REPORT_REGISTER: "REPORT_REGISTER",
    /**获取安装参数 */
    GET_INSTALL_PARAMS: "GET_INSTALL_PARAMS",
    /**谷歌登录 */
    GOOGLE_LOGIN: "GOOGLE_LOGIN",
    /**taptap登录 */
    TAPTAP_LOGIN: "TAPTAP_LOGIN",

    /* af事件上报 目前只有海外有*/
    AF_EVENT: "AF_EVENT",
    /** 打开app权限设置界面 */
    OPEN_APP_SETTING: "OPEN_APP_SETTING",

    GET_HOST_ADDRESS: "GET_HOST_ADDRESS",

    GET_AD_INFO: "GET_AD_INFO",

    WEIBO_SHARE: "WEIBO_SHARE",

    /** gdt广告上传openid */
    SEND_GDT_OPENID: "SEND_GDT_OPENID",

    LOAD_NATIVE_AD: "LOAD_NATIVE_AD", // 加载原生广告
    SHOW_NATIVE_AD: "SHOW_NATIVE_AD", // 播放原生广告
    CLOSE_NATIVE_AD: "CLOSE_NATIVE_AD", // 关闭原生广告

    CHECK_NOTICE_ENABLED: "CHECK_NOTICE_ENABLED", // 判断推送权限是否开启 ios
    GO_TO_SETTING: "GO_TO_SETTING", // 跳转到设置界面 ios

    INS_SHARE: "INS_SHARE", // instagram 分享
    OPEN_KEFU: "OPEN_KEFU", //  打开微信客服
    INIT_SDK: "INIT_SDK", // 初始化所有sdk 因为国内安卓要求 在玩家同意隐私政策后才能初始化sdk
    MEMORY_WARNING: "MEMORY_WARNING", //清缓存
    MEMORY_WARNING_INIT: "MEMORY_WARNING_INIT", //清缓存初始化
    GDTACTION_EVENT_EXPOSE_AD: "GDTACTION_EVENT_EXPOSE_AD", //上报gdt 视频广告曝光事件
    CHECK_PERMISSION: "CHECK_PERMISSION", //安卓授权事件
    TRACKING_EVENT: "TRACKING_EVENT", //热云事件
    PANGLE_EVENT: "PANGLE_EVENT", //穿山甲归因事件

    FACEBOOK_EVENT: 'FACEBOOK_EVENT', //facebook 事件上报 目前只有海外有
    FIREBASE_EVENT: 'FIREBASE_EVENT', //firebase 事件上报 目前只有海外有
    APPFLYER_EVENT: 'APPFLYER_EVENT', //appflyer 事件上报 目前只有海外有

    SAVE_DEVICE_DATA: 'SAVE_DEVICE_DATA', //将一些数据存在设备中，即使app被卸载数据也存在 目前只有ios有
    GET_DEVICE_DATA: "GET_DEVICE_DATA", // 获取存在设备中的数据
    DEL_DEVICE_DATA: "DEL_DEVICE_DATA", // 删除存在设备中的数据

    GET_NOTICE_TOKEN: "GET_NOTICE_TOKEN", // 获取通知的令牌
    CHECK_NOTICE_PER: "CHECK_NOTICE_PER", // 检测是否有通知的权限

    APPSFLYER_TE: "APPSFLYER_TE", // 将数数的id传给af 达成数据互通
    START_APPSFLYER: "START_APPSFLYER", //  启动af(同时传数数的游客id) 所以现在af必须得跟数数一起启动

    CHECK_REAL_NAME: "CHECK_REAL_NAME", // 检测是否实名
}