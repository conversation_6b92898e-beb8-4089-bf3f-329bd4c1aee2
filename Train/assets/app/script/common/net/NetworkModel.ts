import { Msg } from "../../../proto/msg-define"
import EventType from "../../common/event/EventType"
import NetEvent from "../../common/event/NetEvent"
import { localConfig } from "../LocalConfig"
import { NET_ERROR_CODE } from "../constant/Constant"
import { gameHelper } from "../helper/GameHelper"
import { viewHelper } from "../helper/ViewHelper"
import { ReceiveMsgMap, SendMsgMap } from "../../../proto/msg-map";
import { startHelper } from "../../../../startScene/StartHelper"

// 网络请求返回信息
type ResponseInfo = {
    err: string
    data?: any
}

type ReqCallbackData = {
    route: string
    cb: Function
    wait: boolean
    msg: any,
}

type ValueOf<T extends any, K extends keyof T> = T[K]

@mc.addmodel('net')
export default class NetworkModel extends mc.BaseModel {

    private client: any = null //Socket
    private reqId: number = 0 //请求id
    private reqMap: Map<number, ReqCallbackData> = new Map<number, ReqCallbackData>() //请求列表
    private events: any[] = [] //当前事件列表

    private isEventGame: boolean = false
    private kick: boolean = false //是否被踢

    private _prop: any = null

    private retryReconnectCnt: number = 0

    public onCreate() {
        if (!this.isEventGame) {
            this.isEventGame = true
            cc.game.on(cc.game.EVENT_HIDE, this.onGameHide, this)
            if (typeof wx !== 'undefined') {
                wx.onShow(this.onGameShow.bind(this))
            } else {
                cc.game.on(cc.game.EVENT_SHOW, this.onGameShow, this)
            }
        }
    }

    private onGameShow(options: any) {
        this.emit(EventType.EVENT_GAME_SHOW, options)
        gameHelper.isShow = true
        this.tryReconnect()
    }

    private onGameHide() {
        this.emit(EventType.EVENT_GAME_HIDE)
        gameHelper.isShow = false
    }

    public clean() {
        this.offAll()
        this.reset()
    }

    public setKick(val: boolean = true) {
        this.kick = val
        this.offAll()
    }

    public reset() {
        this.kick = false
        this.reqId = 0
        this.reqMap.clear()
    }

    // 连接网络
    public async connect(prop?: any, isReconnect?: boolean) {
        if (this.isKick()) return
        viewHelper.showNet()
        prop = prop || this.getConfig()
        this._prop = prop
        twlog.info('mqant connect:', prop.host + ':' + prop.port)
        let succ = await new Promise<boolean>(resolve => {
            this.close()
            const clientId = 'mqttjs_' + Math.random().toString(16).substring(2, 10)
            this.client = new Paho.MQTT.Client(prop.host, prop.port, '/mqtt', clientId)
            this.client.connect({
                onSuccess: (evt: any) => { //连接成功
                    twlog.info('connect success!', isReconnect)
                    if (!isReconnect) {
                        this.reset()
                    }
                    resolve(true)
                },
                onFailure: (evt: any) => { //连接错误
                    resolve(false)
                },
                mqttVersion: 3,
                useSSL: !!prop.useSSL,
                cleanSession: true,
                keepAliveInterval: 30, //心跳
            })
            // 注册连接断开处理事件
            this.client.onConnectionLost = (evt: any) => {
                if (evt.errorCode === 0 || evt.errorCode === 5) {
                    return
                }
                twlog.info('onConnectionLost', evt.errorCode)
                this.client = null
                this.emit(NetEvent.NET_DISCONNECT)
                this.tryReconnect()
            }
            // 注册消息接收处理事件
            this.client.onMessageArrived = this.recvMessage.bind(this)
        })

        viewHelper.hideNet()

        this.on(Msg.S2C_LogoutMessage, (notify) => {
            let msg
            let reason = notify.reason
            if (reason == 1 || reason == 2) {
                if (reason == 1) {
                    msg = "login_tips_14"
                }
                else if (reason == 2) {
                    msg = "login_tips_15"
                }
                viewHelper.showLoadMessageBox(msg, () => {
                    gameHelper.gameRestart()
                }, null, { lockClose: true })
            }
            else if (reason == 3) {
                viewHelper.showLoadMessageBox("login_tips_16", null, null, { lockClose: true })
            }
            twlog.error("服务器强制离线.", reason)
            this.setKick(true)
        }, this)
        window["debugHelper"]?.listen()
        return succ
    }

    public async tryReconnect() {
        if (this.isKick()) return
        if (this.isConnected()) return
        if (!gameHelper.isLogin || !gameHelper.isShow) return
        let isReconnecting = this.retryReconnectCnt > 0
        this.retryReconnectCnt = 10 //重置一下重试次数
        if (isReconnecting) {
            return
        }
        viewHelper.showNet()
        while (this.retryReconnectCnt--) {
            let succ = await gameHelper.net.reconnect()
            if (succ) {
                gameHelper.user.checkMd5()
                viewHelper.hideNet()
                this.retryReconnectCnt = 0
                return
            }
            else if (this.retryReconnectCnt > 1) {
                await ut.wait(2)
            }
        }
        viewHelper.showReconnect()
        viewHelper.hideNet()
    }

    private async reconnect() {
        if (this.isKick()) return
        let succ = true
        if (!this.isConnected()) {
            succ = await this.connect(this._prop, true)
        } 
        if (!succ) return false
        let deleteIds = []
        this.reqMap.forEach((info, id) => {
            if (info.route == Msg.C2S_LoginByTokenMessage || info.route.startsWith("gate/") || info.route == Msg.C2S_GetPlayerInfoMessage) {
                deleteIds.push(id)
            }
        })
        for (let id of deleteIds) {
            let info = this.reqMap.get(id)
            if (info.wait) {
                viewHelper.hideNet()
            }
            this.reqMap.delete(id)
        }
        succ = await gameHelper.user.reLogin()
        if (!succ) return false

        this.reSend()
        eventCenter.emit(NetEvent.NET_RECONNECT)
        return true
    }

    public reSend() {
        this.reqMap.forEach((info, id) => {
            let route = info.route + '/' + id
            let msg = info.msg
            let data = msg ? msg.constructor.encode(msg).finish() : ""
            this._send(route, data)
        })
    }

    private _send(route, data) {
        if (localConfig.markQuest) {
            let key = "__@markQuest"
            let list = storageMgr.getOrgItem(key)
            if (!list) {
                list = []
            }
            else {
                list = JSON.parse(list)
            }
            list.push({ route, data, time: gameHelper.now() })
            storageMgr.setOrgItem(key, JSON.stringify(list))
        }
        this.client.send(route, data, 1)
    }

    // 关闭网络
    public close() {
        if (this.isConnected()) {
            twlog.info('network close')
            this.client.disconnect()
        }
        this.client = null
    }

    public isConnected() {
        return !!this.client?.isConnected()
    }

    // 主动ping
    public ping() {
        this.client?.ping()
    }

    /**
     * @param route
     * @param msg
     * @description
     * @example
     * const msg = new proto.C2S_TalentLevelUpMessage({passengerId: model.id, id: data.attr})
     * const x = await gameHelper.net.requestWithMsg(Msg.C2S_TalentLevelUpMessage, msg)
     */
    public async requestWithMsg<K extends keyof typeof SendMsgMap, V extends ValueOf<typeof SendMsgMap, K>, R extends ValueOf<typeof ReceiveMsgMap, K>>(route: K, msg: InstanceType<V>, wait?: boolean): Promise<InstanceType<R>> {
        const binary = await this.request(route, msg, wait)
        const element = ReceiveMsgMap[route];
        const message = element.decode(binary);
        if (message && Object.prototype.hasOwnProperty.call(message, 'code')) {
            // const code = message['code']
            // console.log(route, code)
        }
        return message as InstanceType<R>
    }
    /**
     * @param route
     * @param arg
     * @description
     * @example
     * const x = await gameHelper.net.requestWithData(Msg.C2S_TalentLevelUpMessage, {passengerId: model.id, id: data.attr})
     */
    public async requestWithData<K extends keyof typeof SendMsgMap, V extends ValueOf<typeof SendMsgMap, K>, R extends ValueOf<typeof ReceiveMsgMap, K>>(route: K, ...arg: ConstructorParameters<V>): Promise<InstanceType<R>> {
        const msgType: V = SendMsgMap[route] as V;
        const msg = msgType.create(...arg) as InstanceType<V>
        return await this.requestWithMsg(route, msg, false)
    }

    public async requestWithDataWait<K extends keyof typeof SendMsgMap, V extends ValueOf<typeof SendMsgMap, K>, R extends ValueOf<typeof ReceiveMsgMap, K>>(route: K, ...arg: ConstructorParameters<V>): Promise<InstanceType<R>> {
        const msgType: V = SendMsgMap[route] as V;
        const msg = msgType.create(...arg) as InstanceType<V>
        return await this.requestWithMsg(route, msg, true)
    }

    public async request(route: string, msg: any, wait?: boolean): Promise<Uint8Array> {
        if (localConfig.isNetLog) {
            twlog.info("net request -> ", route)
        }
        return new Promise<Uint8Array>(resolve => {
            this.reqId += 1
            this.reqMap.set(this.reqId, { cb: resolve, wait: !!wait, route, msg })
            route = route + '/' + this.reqId
            !!wait && viewHelper.showNet()
            if (this.isConnected()) {
                let data = msg ? msg.constructor.encode(msg).finish() : ""
                this._send(route, data)
            }
        })
    }

    private recvMessage(evt: any) {
        const [moduleType, func, msgid] = evt.destinationName.split('/')
        //cc.log(moduleType, func, evt.payloadBytes.length + 'B')
        if (msgid) {
            this.recvMessageById(msgid, evt.payloadBytes)
        } else if (moduleType) {
            // try一次转换
            const msgType = ReceiveMsgMap[moduleType]
            if (msgType) {
                let emitData = null
                try {
                    emitData = msgType.decode(evt.payloadBytes);
                } catch (e) {
                    emitData = null
                }
                if (!emitData) {
                    emitData = evt.payloadBytes
                }
                this.emit(moduleType, emitData)
            } else {
                this.emit(moduleType, evt.payloadBytes)
            }
        } else {
            twlog.error('recvMessage error msg:', evt.destinationName)
        }
    }

    private recvMessageById(msgid, payloadBytes) {
        const id = parseInt(msgid)
        const req = this.reqMap.get(id)
        if (req) {
            if (localConfig.isNetLog) {
                twlog.info("net recv -> ", req.route)
            }
            this.reqMap.delete(id)
            req.wait && viewHelper.hideNet()
            req.cb && req.cb(payloadBytes)
        }
    }

    public failAll() {
        let msg = new proto.S2C_ErrorResultMessage({ code: NET_ERROR_CODE.DIS_CONNECT })
        let data = proto.S2C_ErrorResultMessage.encode(msg).finish()
        this.reqMap.forEach((req, key) => {
            this.reqMap.delete(key)
            req.wait && viewHelper.hideNet()
            req.cb && req.cb(data)
        })
    }

    private cleanReqMap() {
        this.reqMap.forEach(m => {
            m.wait && viewHelper.hideNet()
        })
        this.reqMap.clear()
    }

    // private disConnectError(code) {
    //     let msg = new proto.S2C_ErrorResultMessage({code})
    //     return proto.S2C_ErrorResultMessage.encode(msg).finish()
    // }

    // 监听
    public on<K extends keyof typeof ReceiveMsgMap, R extends ValueOf<typeof ReceiveMsgMap, K>>(route: K, cb: (msg: InstanceType<R>) => void, target?: any) {
        eventCenter.off(route, cb, target)
        eventCenter.on(route, cb, target)
        this.events.push({ route, cb, target })
    }

    // 注销监听
    public off(route: string, cb?: Function, target?: any) {
        eventCenter.off(route, cb, target)
        this.events.delete(m => m.route === route && m.cb.toString() == m.cb.toString() && m.target == m.target)
    }

    public offAll() {
        this.events.forEach(m => eventCenter.off(m.route, m.cb, m.target))
        this.events.length = 0
    }

    public isKick() {
        return this.kick
    }

    private getConfig() {
        if (localConfig.sever) return localConfig.sever
        if (localConfig.release) {
            return { host: 'train.twomiles.cn', port: 8060, useSSL: true, http: "https://train.twomiles.cn" }
        } else {
            return { host: 'train-test.twomiles.cn', port: 4653, http: "http://train-test.twomiles.cn:8181" }
        }
    }

    public async get(path: string, data?: any, retry?) {
        return await this._httpRequest(path, data, "GET", retry);
    }

    public async post(path: string, data?: any, retry?) {
        return await this._httpRequest(path, data, "POST", retry);
    }

    private _httpRequest(path: string, data: any, method: string, retry): Promise<any> {
        if (path[0] != "/") {
            path = "/" + path;
        }
        for (let key in data) {
            if (data[key] == null) {
                delete data[key];
            }
        }
        let { http } = this.getConfig()
        const url = http + path;
        return this.httpRequest(url, data, method, retry)
    }

    public async httpRequest(url: string, data: any, method: string, retry: number = 3, timeout = 6000): Promise<any> {
        let posUrl = null;
        // 组织请求参数
        if (typeof data == 'object') {
            if (method == "GET") {
                url += this.encodeURIObj(data, true);
            }
            else {
                posUrl = JSON.stringify(data);
            }
        }

        let request = function () {
            return new Promise(resolve => {
                let xhr = new XMLHttpRequest();
                xhr.open(method, url, true);

                if (method == "POST") {
                    xhr.setRequestHeader('Content-Type', 'application/json');
                }

                xhr.timeout = timeout;
                xhr.ontimeout = function (e) {
                    timeout *= 1.5
                    twlog.error('http timeout', url)
                    resolve({ code: -501 })
                }
                xhr.onerror = function (e) {
                    //@ts-ignore
                    let responseErrorBuffer: string = xhr.responseErrorBuffer || ""
                    twlog.error('http disconnect error: ' + responseErrorBuffer, url);
                    resolve({ code: -502 })
                }

                xhr.onreadystatechange = function () {
                    let response = xhr.responseText;
                    if (xhr.readyState == 4) {
                        if (xhr.status == 200 && response) {
                            try {
                                let respData = JSON.parse(response);
                                resolve(respData)
                            } catch (error) {
                                resolve({ code: -503 })
                            }
                        }
                        else {
                            resolve({ code: -504 });
                        }
                    }
                };

                xhr.send(posUrl);
            })
        }

        while (retry > 0) {
            retry--;
            let res = await request();
            if (res) {
                return res;
            }
            else {
                await ut.wait(0.5);
            }
        }
    }

    public encodeURIObj(obj: object = {}, question: boolean = false) {
        let kvs = []
        let url = "";
        for (let k in obj) {
            kvs.push(encodeURIComponent(k) + '=' + encodeURIComponent(obj[k]));
        }
        if (kvs.length > 0) {
            if (question) {
                url += '?';
            }
            url += kvs.join('&');
        }
        return url
    }
}
