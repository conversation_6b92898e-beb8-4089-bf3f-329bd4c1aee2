/**
 * 处理IOS 苹果登录相关 
 */
import JsbEvent from "../event/JsbEvent"
import { jsbHelper } from "../helper/JsbHelper"

class AppleHelper {

    public async login() {
        let res = await jsbHelper.call(JsbEvent.APPLE_LOGIN)
        let result: string = res.result
        if (result == "success") {
            return { code: res.code, nickName: res.nickname, token: res.token, userId: res.userId }
        } else if (result == "logined") {
            let userId = res.userId
            return { userId: userId, logined: true }
        } else {
            if (res.errcode !== "-200001") { //已安装
                // reportHelper.reportError('apple nativelogin error', res)
            }
            return { errcode: res.errcode || -10086 }
        }
    }

    /**
     * 返回值为0就是未授权 返回值为非0就不用再授权
     * 返回值为0：尚未授权
     * 返回值为1：某种情况下开启了限制广告追踪
     * 返回值为2：已经拒绝授权
     * 返回值为3：已经同意授权
     * 返回值为99：未知情况 属于出错了 可以当返回值2处理
     * 返回值为100：苹果系统低于ios14 那就不用处理
     */
    public isAttceptAtt() {
        return jsb.reflection.callStaticMethod("jsbHelp", "isAcceptAtt")
    }

    // 返回值为0表示玩家同意授权
    public async requestAtt() {
        return await jsbHelper.call(JsbEvent.REQUEST_PERMISSION)
    }

    // 返回值为0表示玩家同意授权
    public async openAppSetting() {
        return await jsbHelper.call(JsbEvent.OPEN_APP_SETTING)
    }

    // 返回true表示要显示苹果登录按钮
    public isShowAppleButton() {
        return jsb.reflection.callStaticMethod("jsbHelp", "isAvailableIos13")
    }
}


export const appleHelper = new AppleHelper()