import ByteArrayMD5 from "./ByteArrayMD5"
import CryptoJ<PERSON> from "./CryptoJS"
import JSEncrypt from "./JSEncrypt"

const publicDer = "-----BEGIN PUBLIC KEY-----MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMZ0MINl1LEgBMZ+TL9I1CkEyb4dSsiW/afzX/6miJz+X1jf0yEgwW2Gk4HS2xqOolOEUm1NptTpTb3246qVQjcCAwEAAQ==-----END PUBLIC KEY-----"

class CryptoHelper {

    private publicCrypt: JSEncrypt = null

    constructor() {
        this.publicCrypt = new JSEncrypt()
        this.publicCrypt.setPublicKey(publicDer)
    }

    /**
     * @param str 
     * @param aes 使用aes加密，如果字符串过长，需要设成true
     * @description 可以加密长字符串，原理是用aes随机密钥加密长字符串，公钥加密aes的密钥
     */
    public pkEncrypt(str: string, aes: boolean = true): {encryStr: string, encryKey: string} | string { 
        if (aes) {
            let aesKey = ut.getRandomString(32) //随机aes密钥
            let encryStr = CryptoJS.AES.encrypt(str, aesKey).toString() //aes加密原串
            let encryKey = this.publicCrypt.encrypt(aesKey) //公钥加密aes的密钥
            return {encryStr: encryStr, encryKey: encryKey}
        }
        return this.publicCrypt.encrypt(str)
    }

    /**
     * @param data 
     * @param sign
     * @description 公钥验签
     */
    public pkVerify(data: any, sign: string): boolean {
        return this.publicCrypt.verify(data, sign, CryptoJS.MD5)
    }

    public md5(data: string | Uint8Array) {
        if (data instanceof Uint8Array) {
            return ByteArrayMD5(data);
        }
        return CryptoJS.MD5(data).toString();
    }

}

export const cryptoHelper = new CryptoHelper()