import { asyncMethods } from './WxAsyncMethods'

// polyfill for finally
if (!Promise.prototype.finally) {
	Promise.prototype.finally = function (callback) {
		let P = this.constructor
		return this.then(
			value => P.resolve(callback()).then(() => value),
			reason => P.resolve(callback()).then(() => { throw reason })
		)
	}
}

// core method
export const promisify = (api) => {
	return (args = {}) => {
		return new Promise((resolve, reject) => {
			api({
				fail: reject,
				success: resolve,
				...args,
			})
		})
	}
}

export const promisifyAll = () => {
	if (typeof wx == "undefined") return;
	let promise = {}
	Object.keys(wx).forEach(key => {
		if (asyncMethods.indexOf(key) >= 0) {
			promise[key] = promisify(wx[key])
		} else if (key !== 'createSignal') {
			promise[key] = wx[key]
		}
	})
	return promise;
}

/**
 * 
 * @param onfunc 
 * @param offFunc 
 * @description 
 * @example promise化 onXXX的监听方式，适用于只监听一次的场景，所以需要传入offFunc
 * ad.onClose(callback) ad.offClose(callback) -> await promisefyOn(ad.onClose, ad.offClose)
 */
export const promisefyOnce = (onfunc: Function, offFunc: Function) => {
	return new Promise(resolve => {
		let callback = (...args)=>{
			offFunc(callback);
			resolve(...args);
		}
		onfunc(callback);
	}).catch(error=>{
		throw error;
	})
}