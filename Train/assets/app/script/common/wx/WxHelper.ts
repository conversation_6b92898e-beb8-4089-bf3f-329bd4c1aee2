/**
 * 处理微信相关
 */
import { promisefyOnce, promisifyAll } from "./WxPromise"
import { gameHelper } from "../helper/GameHelper";
import { viewHelper } from "../helper/ViewHelper";
import EventType from "../event/EventType";

class WxHelper {

    private systemInfo: any = null;
    private buttons: any = {}
    private toAppBanner: any = null

    private userInfo: any = null

    public init() {
        if (typeof wx === 'undefined') {
            return
        }
        if (wx.onMemoryWarning != null) {
            wx.onMemoryWarning(() => {
                // gameHelper.onMemoryWarning()
                wx.triggerGC()
            });
        }

        // // 设置定向分享参数（这里假设1代表的是邀请新玩家场景值）
        // wx.setMessageToFriendQuery && wx.setMessageToFriendQuery({
        //     shareMessageToFriendScene: 1
        // });

        // //定向分享的回调
        // wx.onShareMessageToFriend && wx.onShareMessageToFriend((res) => {
        //     if (!res.success) {
        //         viewHelper.showAlert("邀请失败，请重试")
        //     }
        // })

        // this.checkShareMessageToFriend(this.getEnterInfo())

        // wx.onShow(info => {
        //     this.checkShareMessageToFriend(info)
        //     // 监听玩家截屏
        //     if (wx.onUserCaptureScreen != null) {
        //         wx.onUserCaptureScreen(() => {
        //             eventCenter.emit(EventType.GAME_CAPTURE_SCREEN)
        //         })
        //     }
        // })
        // wx.onHide(() => {
        //     // 移除监听玩家截屏
        //     if (wx.offUserCaptureScreen != null) {
        //         wx.offUserCaptureScreen(() => {
        //         })
        //     }
        // })
        // // 监听玩家截屏 初始化时监听，在每一次前后台切换时，on和off
        // if (wx.onUserCaptureScreen != null) {
        //     wx.onUserCaptureScreen(() => {
        //         eventCenter.emit(EventType.GAME_CAPTURE_SCREEN)
        //     })
        // }
    }

    public async login() {
        let code = await wxHelper.getCode()
        if (code == null) {
            return {errcode: -1}
        }
        let info = await wxHelper.getBaseUserInfo()
        return {code, nickName: info?.nickName, avatarUrl: info?.avatarUrl}
    }

    public async getCode(retry: number = 3): Promise<string | null> {
        try {
            let { code } = await wxPro.login();
            return code;
        } catch (error) {
            if (!ut.isMiniGame()) {
                console.error("网页版不支持连接正式服");
                await ut.wait(5000);
            }
            if (retry > 0) {
                await ut.wait(0.5);
                console.error("getCode retry", error);
                return await this.getCode(retry - 1);
            } else {
                console.error("getCode fail", error);
            }
        }
    }

    public getLaunchInfo(): any {
        let info: any = {};
        try {
            const launchInfo = wx.getLaunchOptionsSync();
            let query = launchInfo.query;
            if (query && Object.keys(query).length > 0) {
                info.launchInfo = query
            }
        } catch (e) {
            twlog.error("getLaunchOptionsSync", e)
            twlog.upLog.error("getLaunchOptionsSync", e)
        }

        return info;
    }

    public getEnterInfo() {
        try {
            if (typeof qq != "undefined") {
                return qq.getLaunchOptionsSync()
            } else {
                return wx.getEnterOptionsSync()
            }
        } catch (e) {
            twlog.error("getEnterInfo", e)
            twlog.upLog.error("getEnterInfo", e)
            return null
        }
    }

    // 校验 session key
    public async checkSession() {
        return new Promise(resolve => {
            try {
                wx.checkSession({
                    success(res) {
                        resolve(0)
                    },
                    fail(res) {
                        resolve(-1)
                    }
                })
            } catch (error) {
                resolve(-1)
            }
        })
    }

    public async checkAuthorize(scopes: string[] = ["userInfo"]) {
        try {
            let res = await wxPro.getSetting();
            return res && res.authSetting && scopes.every(scope => res.authSetting[`scope.${scope}`]);
        } catch (error) {
            twlog.error(error);
            return false;
        }
    }

    public async getUserInfo(withCredentials?: boolean) {
        try {
            let userInfo
            if (typeof qq != "undefined") {
                if (this.userInfo) {
                    return this.userInfo
                }
                userInfo = await wxPro.getUserInfo({ withCredentials })
            } else {
                userInfo = await wxPro.getUserInfo()
            }
            if (userInfo) {
                this.userInfo = userInfo.userInfo
            }
            return userInfo
        } catch (error) {
            twlog.error(error);
        }
    }

    public async getBaseUserInfo(onlyCache?: boolean) { //nickName，头像那些
        if (this.userInfo || onlyCache) {
            return this.userInfo
        }
        await this.getUserInfo()
        return this.userInfo
    }

    public async request(info: { url: string, data: any, method: string }, retry: number = 3) {
        try {
            let res = await wxPro.request(info);
            if (res.data && typeof (res.data) == 'string' && res.data.indexOf("html") != -1) {
                return null;
            }
            else {
                return res.data;
            }
        } catch (error) {
            twlog.error(info.url, error);
            if (retry > 0) {
                ut.wait(0.5);
                return this.request(info, retry - 1);
            }
        }
    }

    /**
     * 
     * @param v2 eg: "2.8.0"
     * @description 判断是否大于或小于某个版本, 大于返回1，小于返回-1, 相等返回0
     */
    public compareVersion(v2: string) {
        if (typeof wx === 'undefined') {
            return -1;
        }
        let systemInfo = this.getSystemInfo();

        let v1 = systemInfo.SDKVersion;

        if (!v1 || !v2) {
            return -1;
        }

        const vv1 = v1.split('.')
        const vv2 = v2.split('.')
        const len = Math.max(vv1.length, vv2.length)

        while (vv1.length < len) {
            vv1.push('0')
        }
        while (vv2.length < len) {
            vv2.push('0')
        }

        for (let i = 0; i < len; i++) {
            const num1 = parseInt(vv1[i])
            const num2 = parseInt(vv2[i])

            if (num1 > num2) {
                return 1
            } else if (num1 < num2) {
                return -1
            }
        }

        return 0
    }

    public getSystemInfo() {
        if (!this.systemInfo) {
            this.systemInfo = wx.getSystemInfoSync();
        }
        return this.systemInfo;
    }

    public getRect(node: cc.Node) {
        let nodeRec = node.getBoundingBoxToWorld();
        nodeRec.x -= 5;
        nodeRec.y -= 5;
        nodeRec.width += 10;
        nodeRec.height += 10;

        let frameSize = cc.view.getFrameSize();
        let winSize = cc.winSize;

        let height = nodeRec.height / winSize.width * frameSize.width;
        let width = nodeRec.width / winSize.width * frameSize.width;
        let left = nodeRec.x / winSize.width * frameSize.width;
        let top = frameSize.height - (nodeRec.y + nodeRec.height) / winSize.height * frameSize.height;
        return { height, width, left, top };
    }

    public createGameClubButton(node: cc.Node, args?: any) {
        return this.createBtn("createGameClubButton", node, args);
    }

    public createUserInfoButton(node: cc.Node, args?: any) {
        return this.createBtn("createUserInfoButton", node, args);
    }

    public createFeedbackButton(node: cc.Node, args?: any) {
        return this.createBtn("createFeedbackButton", node, args);
    }

    //wx toast，默认提示版本问题
    public createModalBtn(node: cc.Node, title: string = "提示", content: string = "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。") {
        let btn = node.getComponent(cc.Button);
        if (!btn) {
            btn = node.addComponent(cc.Button);
        }
        node.on("click", () => {
            wx.showModal({ title, content })
        })
    }

    public createBtn(api: string, node: cc.Node, args?: any) {
        if (!wx[api]) {
            return this.createModalBtn(node);
        }
        let { height, width, left, top } = this.getRect(node);
        args = args || {
            type: 'text',
            text: '',
            style: {
                left: left,
                top: top,
                width: width,
                height: height,
                lineHeight: 0,
                backgroundColor: '',
                color: '#ffffff',
                textAlign: 'center',
                fontSize: 16,
                borderRadius: 4
            }
        }
        if (typeof qq != "undefined") {
            args.withCredentials = true
        }
        let btn = wx[api](args);
        ut.waitNextFrame().then(() => {
            let { left, top } = this.getRect(node);
            btn.style.left = left;
            btn.style.top = top;
        })
        if (!node.isValid) {
            return
        }
        if (typeof qq != "undefined") {
            btn.onTap((res) => {
                if (res.errMsg && res.errMsg.split(':')[1] === 'ok') {
                    this.userInfo = res.userInfo
                }
            })
        }
        this.buttons[node.uuid] = btn
        return btn;
    }

    public destroyButton(uuid: string) {
        const btn = this.buttons[uuid]
        delete this.buttons[uuid]
        if (btn) {
            btn.destroy()
        }
    }

    public destroyAllButton() {
        for (let key in this.buttons) {
            this.buttons[key].destroy()
        }
        this.buttons = {}
    }

    public markScene() {
        if (wx.markScene) {
            wx.markScene({ "sceneId": 0 })
        }
    }

    public async share(title: string, imgUrl: string, queryInfo: { uid?: string } = {}, loose: boolean = true) {
        // let queryStr = netMgr.encodeURIObj(queryInfo);
        // let wxShare = wx.uma || wx
        // let isQQ = (typeof qq != 'undefined')
        // if (loose) { //不需要校验结果
        //     wxShare.shareAppMessage({
        //         title: title,
        //         imageUrl: imgUrl,
        //         query: queryStr,
        //     })
        //     return true;
        // }
        // else {
        //     if (ut.isAndroid() && !isQQ) {
        //         imgUrl = await netMgr.getUrl("share/onShare", { uid: queryInfo.uid, imgUrl: imgUrl, timestamp: new Date().getTime() });
        //     }
        //     wxShare.shareAppMessage({
        //         title: title,
        //         imageUrl: imgUrl,
        //         query: queryStr,
        //     })

        //     await promisefyOnce(wx.onShow, wx.offShow);

        //     if (ut.isIos() || isQQ) {
        //         return true
        //     }
        //     if (ut.isAndroid()) {
        //         let { status } = await netMgr.get("share/getStatus", { uid: queryInfo.uid }) || { status: -1 };
        //         return status === 0;
        //     }
        //     else {
        //         return true
        //     }
        // }
    }

    //云测试环境
    isTest() {
        //@ts-ignore
        return typeof GameGlobal !== "undefined" && typeof GameGlobal.isTest !== "undefined" && GameGlobal.isTest
    }

    public async uploadScoce(starSum: number) {
        let updateTime = Math.floor(new Date().getTime() / 1000);
        let KV = [{ key: "starSum", value: starSum + "" }];

        let wxRankValue = {
            "wxgame": {
                "score": starSum,
                "update_time": updateTime,
            },
        }
        KV.push({ key: "starRank", value: JSON.stringify(wxRankValue) })

        try {
            await wxPro.setUserCloudStorage({ KVDataList: KV })
        } catch (error) {
            console.error("uploadDataToRank fail", error)
        }
    }

    //检查定向分享
    private checkShareMessageToFriend(enterInfo: { query?: any }) {
        let { query } = enterInfo || {}
        if (query && query.shareMessageToFriendScene == '1') { //如果是从定向进来的
            // wxOpenData.post('reciveInvite')
        }
    }

    // 是否勾了总是订阅的按钮
    // private async isAlwaysSubscribe(type: SubscribeType) {
    //     if (wx.getSetting) {
    //         try {
    //             let res = await wx.getSetting({ withSubscriptions: true })
    //             if (res.mainSwitch && res.itemSettings[type] == 'accept') {
    //                 return true
    //             }
    //             return false
    //         } catch (error) {
    //             twlog.error('isAlwaysSubscribe', error)
    //         }
    //     }
    //     return false
    // }

    //拉起订阅弹窗接口
    public async requestSubscribeMessage(tmpId) {
        try {
            if (typeof qq != "undefined") {
                let wait1 = new Promise((success, fail) => {
                    qq.subscribeAppMsg({
                        tmplIds: [tmpId],
                        subscribe: true,
                        success(res) {
                            success(res)
                        },
                        fail(res) {
                            fail(res)
                        }
                    })
                })
                let wait2 = new Promise(resolve => setTimeout(resolve, 10000))
                const res = await Promise.race([wait1, wait2])
                if (res && res[tmpId] == "accept") {
                    return true
                } else {
                    return false
                }
            }

            if (!wx.requestSubscribeMessage) {
                return false
            }

            let res = await wx.requestSubscribeMessage({ tmplIds: [tmpId] })
            if (res[tmpId] == 'accept') {
                return true
            }
            else {
                twlog.info("requestSubscribeMessage", res)
            }
        } catch (error) {
            let errCode = error.errCode ?? -1000
            viewHelper.showAlert('toast.subscribe_fail', { params: [errCode] })
            twlog.error("requestSubscribeMessage", error)
        }
        return false
    }

    // 跳转小程序
    public async navigateToMiniProgram(appId, path) {
        if (!wx.navigateToMiniProgram) {
            return viewHelper.showAlert("请先升级微信")
        }

        try {
            let result = await wx.navigateToMiniProgram({
                appId,
                path
            })

            return result
        } catch (error) {
            return false
        }
    }

    public isToAppBannerLoad() {
        return this.toAppBanner && this.toAppBanner.isLoad
    }

    public loadToAppBanner() {
        if (this.toAppBanner) {
            return
        }

        let id
        if (ut.isIos()) {
            id = 'adunit-f92323265793e281'
        }
        else if (ut.isAndroid()) {
            id = 'adunit-d195ede7f99f8217'
        }
        else {
            return
        }

        let adIns = wx.createBannerAd({
            adUnitId: id,
            style: {
                left: 0,
                top: 0,
                width: 320
            }
        })

        this.toAppBanner = {
            adIns: adIns,
            isLoad: false,
        }

        adIns.onError(err => {
            twlog.error('loadToAppBanner error', id, err)
        })

        adIns.onLoad(() => {
            this.toAppBanner.isLoad = true
        })
    }

    public shoowToAppBanner(node) {
        if (!this.toAppBanner) {
            return null
        }
        if (this.toAppBanner.isInit) {
            this.toAppBanner.adIns.show()
            return this.toAppBanner.adIns
        }
        this.toAppBanner.isInit = true

        let { height, width, left, top } = this.getRect(node);

        if (width < 300) {
            left -= (300 - width) * 0.5
            width = 300
        }

        let bannerAd = this.toAppBanner.adIns
        bannerAd.style.left = left
        bannerAd.style.top = top
        bannerAd.style.width = width

        bannerAd.onResize(res => {
            bannerAd.show()
        })

        return bannerAd
    }

    public hideToAppBannerLoad() {
        return this.toAppBanner && this.toAppBanner.adIns.hide()
    }

    public async getFileList(callback?: Function) {
        let fs = wx.getFileSystemManager()
        let fileList = []

        let maxCos = 0
        let totTime = 0

        let s = performance.now()

        let isDirectory = async (path) => {
            return new Promise((resolve) => {
                let start = performance.now()
                fs.stat({
                    path,
                    success: res => {
                        let dt = performance.now() - start
                        totTime += dt
                        if (maxCos < dt) {
                            maxCos = dt
                            console.log('stat', path, res.stats.size, dt)
                        }
                        resolve(res.stats.isDirectory())
                    },
                    fail: (res) => {
                        console.error("isDir fail", path, res)
                    }
                })
            })
        }

        let readDir = (dirPath) => {
            return new Promise(resolve => {
                let start = performance.now()
                fs.readdir({
                    dirPath,
                    success: async (res) => {
                        console.log('readdir', dirPath, res.files.length, performance.now() - start)
                        for (let file of res.files) {
                            let fullPath = dirPath + "/" + file
                            let isDir = await isDirectory(fullPath)
                            if (isDir) {
                                await readDir(fullPath)
                            }
                            else {
                                fileList.push(fullPath)
                                callback && callback(fileList.length)
                            }
                        }
                        resolve(0)
                    },

                    fail: function (res) {
                        console.error('readdir fail', dirPath, res)
                        resolve(0)
                    }
                })
            })
        }

        await readDir(wx.env.USER_DATA_PATH + '/gamecaches')
        console.log("done", fileList.length, totTime, performance.now() - s)
        return fileList
    }

    // 是否已订阅
    public async getWhatsNewSubscriptionsSetting() {
        if (!wx.getSetting) return false
        try {
            const key = 'SYS_MSG_TYPE_WHATS_NEW'
            let res = await wxPro.getSetting({ withSubscriptions: true }) // 消息类型，1=游戏更新提醒，目前只有这种类型
            if (res && res.subscriptionsSetting && res.subscriptionsSetting.itemSettings) {
                if (res.subscriptionsSetting.itemSettings['undefined'] === 'accept'
                    || res.subscriptionsSetting.itemSettings[key] === 'accept') { // 非相应基础库的真机返回竟然是undefined 离谱
                    return true
                } /* else if (res.subscriptionsSetting.itemSettings['undefined'] === 'reject'
                || res.subscriptionsSetting.itemSettings[key] === 'accept') { // 提醒通知被关闭
                    return false
                } */
            }
            return false
        } catch (error) {
            return false
        }
    }

    // 拉起订阅
    public async requestSubscribeSystemMessage() {
        if (!wx.requestSubscribeSystemMessage) return false
        try {
            const key = 'SYS_MSG_TYPE_WHATS_NEW'
            let res = await wxPro.requestSubscribeSystemMessage({ msgTypeList: [key] })
            if (res) {
                const ok = res.errMsg.split(':')[1] === 'ok', accept = res[key] === 'accept'
                if (ok && accept) {
                    return true
                }
            }
            return false
        } catch (error) {
            return false
        }
    }
}

window["wxPro"] = promisifyAll();

export const wxHelper = new WxHelper();