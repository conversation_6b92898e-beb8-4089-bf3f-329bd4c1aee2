import MultiColor from "../../../core/component/MultiColor";
import MultiFrame from "../../../core/component/MultiFrame";
import ConditionObj from "../../model/common/ConditionObj";
import { AchievementCfg, AchievementTypeCfg } from "../../common/constant/DataType";
import { game<PERSON>elper } from "../../common/helper/GameHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { AchievementType } from "../../common/constant/Enums";
import MultiMaterial from "../../../core/component/MultiMaterial";

const { ccclass } = cc._decorator;

@ccclass
export default class AchieveItemWdtCtrl extends mc.BaseWdtCtrl {

    //@autocode property begin
    protected bgNode_: cc.Node = null // path://bg_n
    protected iconNode_: cc.Node = null // path://icon_n
    protected numLbl_: cc.Label = null // path://num_l
    protected descLbl_: cc.Label = null // path://desc_l
    protected nameLbl_: cc.Label = null // path://name_l
    protected rewardIconNode_: cc.Node = null // path://rewardIcon_n
    protected rewardNumNode_: cc.Node = null // path://rewardNum_n
    protected badgeNode_: cc.Node = null // path://badge_n
    protected badgeLbl_: cc.Label = null // path://badge_l
    //@end

    public init(data: AchievementCfg) {
        let over = gameHelper.achieve.isTaskComplete(data.id)
        let cfgT = cfgHelper.getAchievementTypeCfg(data.typeId)
        this.badgeNode_.active = over
        this.badgeLbl_.node.active = over
        this.rewardNumNode_.active = !over
        this.rewardIconNode_.active = !over
        this.numLbl_.node.active = !over
        if (over) {
            this.initOver(data)
        } else {
            this.initReward(data)
            this.initBgNum(data)
        }
        this.initLabel(cfgT, data)
        this.initIcon(data)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------
    private initOver(cfg: AchievementCfg) {
        this.bgNode_.Component(MultiFrame).setFrame(2)
        this.initClick(this.node, false)
        this.badgeLbl_.setLocaleUpdate(() => {
            let msd = gameHelper.achieve.getCompleteTime(cfg.id)
            if (!msd) return ''
            return ut.dateFormat(assetsMgr.lang('achievement_guiText_2'), msd)
        })
    }
    private initBgNum(cfg: AchievementCfg) {
        let dic = gameHelper.achieve.getAchieveCurMax(cfg)
        let bol = dic && dic.cur >= dic.max
        let idx = bol ? 1 : 0
        this.bgNode_.Component(MultiFrame).setFrame(idx)
        this.numLbl_.Component(MultiColor).setColor(idx)
        this.numLbl_.string = dic ? `${dic.cur}/${dic.max}` : ''
        this.rewardNumNode_.Component(MultiColor).setColor(idx)
        this.initClick(this.node, bol)
    }
    private initClick(it: cc.Node, bol: boolean) {
        it.off('click')
        it.Component(cc.Button).interactable = bol
        if (!bol) return
        it.on('click', async () => {
            let cfg = it.Data as AchievementCfg
            if (!cc.isValid(this)) return
            gameHelper.achieve.completeAchieveAndGetRewardBySever(cfg.id)
        })
    }
    private initLabel(cfg: AchievementTypeCfg, data: AchievementCfg) {
        this.nameLbl_.setLocaleKey(cfg.name, data.lv)
        this.descLbl_.setLocaleKey(cfg.simple, gameHelper.achieve.getSimpleParams(data))
    }
    private initReward(cfg: AchievementCfg) {
        let data = cfg.reward[0]
        let cond = new ConditionObj().init2(data)
        resHelper.loadIconByCondInfo(cond, this.rewardIconNode_, this.getTag())
        uiHelper.setLabelNum0(this.rewardNumNode_, data)
    }
    private initIcon(cfg: AchievementCfg) {
        let icon = cfg.icon
        let url =`achieve/${icon}`
        this.iconNode_.scale = 1
        this.iconNode_.Component(MultiMaterial).setMaterial(0)
        if (cfg.typeId == AchievementType.PLANET) {
            url = `profile/icon/${icon}`
            this.iconNode_.scale = 0.2
            let planet = gameHelper.planet.getPlanet(Number(cfg.target[0].id))
            if (!planet || planet.isHide()) {
                this.iconNode_.Component(MultiMaterial).setMaterial(1, {color: ut.colorCodeToNormAry("#f3dcb2")})
            }
        }
        resHelper.setSpf(url, this.iconNode_, this.getTag())
    }
}
