import EventType from "../../common/event/EventType";
import AchieveItemWdtCtrl from "./AchieveItemWdtCtrl";
import { AchievementCfg } from "../../common/constant/DataType";
import { gameHelper } from "../../common/helper/GameHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class AchievePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected scrollViewSv_: cc.ScrollView = null // path://root/scrollView_sv
    //@end

    private isWillUpdate: boolean = false

    public listenEventMaps() {
        return [
            { [EventType.ACHIEVE_UPDATE]: this.onWillUpdateAchieve, tag: 'create' },
            { [EventType.ACHIEVE_COMPLETE]: this.onWillUpdateAchieve, tag: 'create' },
        ]
    }

    public onEnter() {
        this.updateView()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    update() {
        if (this.isWillUpdate) {
            this.isWillUpdate = false
            this.updateView()
        }
    }
    private onWillUpdateAchieve() {
        this.isWillUpdate = true
    }

    // ----------------------------------------- custom function ----------------------------------------------------
    private updateView() {
        let ary1 = this.getAryData()
        let ary2 = gameHelper.achieve.getCompleteAchieves()
        let list = ary1.concat(ary2)
        this.scrollViewSv_.List(list.length, (it, i) => {
            let cfg = list[i]
            it.Data = cfg
            it.Component(AchieveItemWdtCtrl).init(cfg)
        })
    }
    private getAryData() {
        let ary = gameHelper.achieve.getCurAchieves()
        if (ary.length <= 1) return ary
        return this.sortData(ary)
    }
    private sortData(ary: AchievementCfg[]): AchievementCfg[] {
        let list1 = []
        let list2 = []
        for (const cfg of ary) {
            if (gameHelper.achieve.checkAchieveCanComplete(cfg)) {
                list1.push(cfg)
            } else {
                list2.push(cfg)
            }
        }
        this.sort2Data(list1)
        this.sort2Data(list2)
        return list1.concat(list2)
    }
    private sort2Data(ary: AchievementCfg[]) {
        if (ary.length <= 1) return
        ary.sort((a, b) => {
            let ta = cfgHelper.getAchievementTypeCfg(a.typeId)
            let tb = cfgHelper.getAchievementTypeCfg(b.typeId)
            return ta.priority - tb.priority
        })
    }
}
