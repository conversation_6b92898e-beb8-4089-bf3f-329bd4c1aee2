import CarriageMap from "../../model/train/common/CarriageMap";
import MapGridView from "./MapGridView";

const { ccclass, executionOrder, property, executeInEditMode } = cc._decorator;

@ccclass
@executionOrder(-100)
@executeInEditMode()
export default class CarriageGridEditView extends cc.Component {

    private map: CarriageMap = null

    @property
    gridSize: number = 20

    @property(cc.Size)
    size = cc.size(108, 13)

    @property(cc.Vec2)
    basePoint = cc.v2(0, 50)

    @property(cc.Node)
    buildNode: cc.Node = null

    onLoad() {
    }

    public initCarriage(map: CarriageMap) {
        this.map = map
        this.draw()
    }

    protected onEnable(): void {
        if (CC_EDITOR) {
            this.init()
        }
    }

    public init() {
        this.map = new CarriageMap().init()
        this.map.setGridSize(this.gridSize)
        this.map.setSize(this.size)
        this.map.setBasePoint(this.basePoint)

        this.updateMap()
        this.draw()
    }

    private getLocalColliderPoints(collider) {
        let cmpt = collider.Component(cc.PolygonCollider)
        if (!cmpt) return []
        let nodePos = collider.getPosition()
        let points = cmpt.points.map((p) => {
            let x = p.x + cmpt.offset.x + nodePos.x
            let y = p.y + cmpt.offset.y + nodePos.y
            return cc.v2(x, y)
        })
        return points
    }

    private updateMapByPolygon(polygonVertexs: cc.Vec2[]) {
        let points = this.map.getPointsByPolygon(polygonVertexs)
        this.map.updatePoints(points, true)
    }

    private updateMap() {
        let root = this.buildNode || this.node.parent.Child('root')
        if (!root) return
        for (let child of root.children) {
            let colliders = child.Child('colliders')?.children || [child.Child('collider')].filter(c => !!c)
            for (let collider of colliders) {
                let points = this.getLocalColliderPoints(collider)
                points = points.map((p) => {
                    return ut.convertToNodeAR(child, this.node, cc.v2(p))
                })
                this.updateMapByPolygon(points)
            }

        }
    }

    // 绘制
    private draw() {
        let node = this.Child('map_grid')
        if (!node) return
        let mapGridView = node.Component(MapGridView)
        mapGridView.init(this.map)
    }
}