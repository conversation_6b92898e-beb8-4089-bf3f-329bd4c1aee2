import { MapJsonItem } from "../../common/constant/DataType";
import { MapSceneType, MapType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import DrawItemCmpt from "./DrawItemCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class MapEditor extends cc.Component {

    private root: cc.Node = null
    private item: cc.Node = null
 
    private gridSize: cc.Size = null// 格子大小
    private items: DrawItemCmpt[] = []

    private edit: boolean = true

    onLoad() {
        this.root = this.FindChild('root')
        this.item = this.root.children[0]

    }

    public init(gridSize, wallPoints, edit = true) {
        this.gridSize = gridSize
        this.draw(wallPoints)

        this.edit = edit

        if (this.edit) {
            this.initTouch()
        }
    }

    public initTouch() {
        let onTouch = (event)=>{
            let localtion = event.getLocation()
            let worldPos = cc.v3(cc.Camera.main.getScreenToWorldPoint(localtion)).toVec2()
            this.onClickMap(worldPos)
        }
        this.node.on(cc.Node.EventType.TOUCH_END, onTouch, this)
    }

    // 绘制
    private draw(wallPoints) {
        wallPoints = wallPoints || []
        this.root.removeAllChildren()
        this.items.length = 0
        for (let x = 0, w = this.gridSize.width; x < w; x++) {
            for (let y = 0, h = this.gridSize.height; y < h; y++) {
                const point = cc.v2(x, y)
                let isWall = !!wallPoints.find(p => p.x == x && p.y == y)
                const it = cc.instantiate2(this.item, this.root).getComponent(DrawItemCmpt).init(point, isWall)
                this.items.push(it)
            }
        }
    }

    private onClickMap(location: cc.Vec2) {
        this.items.forEach(m => m.click(location))
    }

    public toDB() {
        let data = []
        for (let item of this.items) {
            let cmp = item.Component(DrawItemCmpt)
            if (cmp.isWall()) {
                data.push(cmp.toPoint())
            }
        }
        return data
    }
}