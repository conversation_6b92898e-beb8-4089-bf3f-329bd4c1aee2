import { TILE_SIZE_HALF } from "../../common/constant/Constant";
import { MapType } from "../../common/constant/Enums";
import { mapHelper } from "../../common/helper/MapHelper";


const { ccclass, property } = cc._decorator;

@ccclass
export default class DrawItemCmpt extends cc.Component {

    private g: cc.Graphics = null

    private point: cc.Vec2 = cc.v2()
    private state: number = -1

    private polygonPoints: cc.Vec2[] = []// 周围的四个点
    private colliderPolygonPoints: cc.Vec2[] = []// 用于碰撞的点
    private parentPosition: cc.Vec2 = cc.v2()

    onLoad() {
        this.g = this.getComponent(cc.Graphics)
    }

    public init(point: cc.Vec2, isWall: boolean = false) {
        this.point.set(point)
        this.state = -1
        let pos = mapHelper.getPixelByPoint(this.point, null)
        this.node.setPosition(pos)
        this.initPolygonPoints(pos)
        this.change(isWall ? 1 : 0)
        this.FindChild('val', cc.Label).string = point.Join()
        return this
    }

    public isWall() {
        return this.state === 1
    }

    public toPoint() {
        return { x: this.point.x, y: this.point.y }
    }

    public hasPoint(point: any) {
        return this.point.equals(point)
    }

    private initPolygonPoints(pos: cc.Vec2) {
        this.polygonPoints.length = 0
        this.polygonPoints.push(cc.v2(-TILE_SIZE_HALF.x, -TILE_SIZE_HALF.y))
        this.polygonPoints.push(cc.v2(TILE_SIZE_HALF.x, -TILE_SIZE_HALF.y))
        this.polygonPoints.push(cc.v2(TILE_SIZE_HALF.x, TILE_SIZE_HALF.y))
        this.polygonPoints.push(cc.v2(-TILE_SIZE_HALF.x, TILE_SIZE_HALF.y))

  
    }

    // 点击
    public click(point: cc.Vec2) {
        this.colliderPolygonPoints = []
        for (let i = 0, l = this.polygonPoints.length; i < l; i++) {
            let pos = this.getPosition().add(this.polygonPoints[i])
            this.colliderPolygonPoints.push(this.node.parent.convertToWorldSpaceAR(pos))
        }
        if (!cc.Intersection.pointInPolygon(point, this.colliderPolygonPoints)) {
            return
        }
        // cc.log(this.point)
        this.change(this.state === 0 ? 1 : 0)
    }

    // 切换状态 0.空位 1.障碍
    public change(val: number) {
        if (this.state === val) {
            return
        }
        this.state = val
        this.g.clear()
        this.drawGround()
        if (val === 1) {
            this.drawWall()
        }
    }

    // 绘制正常情况
    private drawGround() {
        this.g.strokeColor = cc.Color.BLACK
        for (let i = 0, l = this.polygonPoints.length; i < l; i++) {
            const a = this.polygonPoints[i]
            const b = this.polygonPoints[(i + 1) % l]
            this.g.moveTo(a.x, a.y)
            this.g.lineTo(b.x, b.y)
        }
        this.g.stroke()
    }

    // 绘制障碍
    private drawWall() {
        this.g.strokeColor = cc.Color.RED
        let a = this.polygonPoints[0]
        let b = this.polygonPoints[2]
        this.g.moveTo(a.x, a.y)
        this.g.lineTo(b.x, b.y)
        a = this.polygonPoints[1]
        b = this.polygonPoints[3]
        this.g.moveTo(a.x, a.y)
        this.g.lineTo(b.x, b.y)
        this.g.stroke()
    }
}
