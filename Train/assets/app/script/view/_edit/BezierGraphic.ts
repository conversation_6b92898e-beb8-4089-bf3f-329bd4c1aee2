const { ccclass, requireComponent, executionOrder, menu, property, executeInEditMode } = cc._decorator;

@ccclass
@executionOrder(-100)
@menu('自定义组件/BezierGraphic')
@executeInEditMode
export default class BezierGraphic extends cc.Component {

    @property(cc.Node)
    public startNode_: cc.Node = null
    @property(cc.Node)
    public oneNode_: cc.Node = null
    @property(cc.Node)
    public manyNode_: cc.Node = null

    public onEnable() {
        this.draw()
    }

    // 绘制
    private draw() {
        let grap = this.Component(cc.Graphics)
        grap.clear()
        if (this.oneNode_) {
            this.drawOnce(this.oneNode_)
        }
        if (this.manyNode_) {
            this.manyNode_.children.forEach(node => {
                this.drawOnce(node)
            })
        }
    }
    private drawOnce(node: cc.Node) {
        let grap = this.Component(cc.Graphics)
        let xs = this.startNode_.x, ys = this.startNode_.y
        let xt = node.x, yt = node.y
        let x1 = xs, y1 = ys - 150
        let x2 = (xs + xt) * 0.5, y2 = (y1 + yt) * 0.5
        grap.moveTo(xs, ys)
        grap.bezierCurveTo(x1, y1, x2, y2, xt, yt)
        grap.stroke()
    }
}