import BaseMap from "../../model/map/BaseMap";

const { ccclass, executionOrder, menu, property, executeInEditMode } = cc._decorator;

@ccclass
@executionOrder(-100)
@menu('自定义组件/MapGridView')
@executeInEditMode
export default class MapGridView extends cc.Component {

    private map: BaseMap = null

    @property(cc.Graphics)
    public bg: cc.Graphics = null

    @property(cc.Graphics)
    public block: cc.Graphics = null

    public init(map: BaseMap) {
        this.map = map
        this.draw()
    }

    // 绘制
    private draw() {
        let grap = this.bg
        let block = this.block
        grap.clear()
        block.clear()
        let map = this.map
        let { width, height } = map.getSize()
        let gridSize = map.getGridSize()
        grap.strokeColor = cc.Color.BLACK
        for (let i = 0; i < width; i++) {
            for (let j = 0; j < height; j++) {
                let pos = map.getActPixelByPoint(cc.v2(i, j))
                grap.rect(pos.x - gridSize / 2, pos.y - gridSize / 2, gridSize, gridSize)
            }
        }
        grap.stroke()

        for (let i = 0; i < width; i++) {
            for (let j = 0; j < height; j++) {
                if (!map.checkCanPass(i, j)) {
                    let pos = map.getActPixelByPoint(cc.v2(i, j))
                    if (CC_EDITOR) {
                        block.moveTo(pos.x - gridSize / 2, pos.y - gridSize / 2)
                        block.lineTo(pos.x + gridSize / 2, pos.y + gridSize / 2)

                        block.moveTo(pos.x - gridSize / 2, pos.y + gridSize / 2)
                        block.lineTo(pos.x + gridSize / 2, pos.y - gridSize / 2)
                    } else {
                        block.fillRect(pos.x - gridSize / 2, pos.y - gridSize / 2, gridSize, gridSize)
                    }
                }
            }
        }
        block.stroke()
    }
}