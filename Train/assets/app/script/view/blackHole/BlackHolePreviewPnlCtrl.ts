import { LongPress } from "../../common/constant/Enums";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BattleRole from "../../model/battle/BattleRole";

const { ccclass } = cc._decorator;

@ccclass
export default class BlackHolePreviewPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected bgNode_: cc.Node = null // path://bg_n
    protected battleRolesNode_: cc.Node = null // path://battleRoles_n
    //@end

    private roles: BattleRole[] = []

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false })
        let level = gameHelper.blackHole.getLevel()
        let boss = gameHelper.blackHole.getBoss(level)
        if (!boss) {
            let succ = await gameHelper.blackHole.readyStart(level)
            if (!succ) return
            boss = gameHelper.blackHole.getBoss(level)
        }
        let roles = boss.map(b => {
            if (b.lv <= 0) {
                b.lv = gameHelper.resonance.getResonaceLv() || 1
            }
            return gameHelper.toBattleRole(b)
        })
        this.roles = roles
    }

    public onEnter(data: any) {
        this.initBattleRoles()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initBattleRoles() {
        this.battleRolesNode_.Items(this.roles, (it, data, i) => {
            let drag = it.Child('drag')
            this.setUIRole(drag.Child('role'), data, true)
            this.registerRoleLong(it, data)
        })
    }

    private registerRoleLong(it: cc.Node, data: any) {
        let drag = it.Child('drag')
        drag.off(LongPress.LPSTART)
        drag.on(LongPress.LPSTART, () => { this.showSkillInfo(it, data) }, this)
    }

    private setUIRole(role: cc.Node, data: BattleRole, playOnLoad: boolean) {
        let body = role.Child('body')
        let starLv = data.getStarLvWithQuality()
        let level = data.getLevel()
        let quality = data.quality
        let lvs = ut.newArray(starLv, 1)
        role.Child('starLv').Items(lvs, (it, data) => {
            it.active = true
        })
        this.setUILv(role, level)
        role.Child('lv', cc.MultiColor).setColor(quality - 1)
        this.setUISk(body.Child('sp'), data.id, playOnLoad)
        this.setUIAtkHp(role, data.getHp(), data.getAttack())
        return body
    }

    private setUILv(role: cc.Node, val: number) {
        role.Child('lv', cc.Label).setLocaleKey('common_guiText_11', val)
    }
    private setUISk(skNode: cc.Node, id: number, playOnLoad: boolean) {
        let dic = skNode.Data
        if (dic && dic.id == id && dic.bol == playOnLoad) {
            return
        }
        skNode.Data = { id, bol: playOnLoad }
        let sk = skNode.Component(sp.Skeleton)
        resHelper.loadRoleSp(id, sk, this.getTag(), playOnLoad).then(() => {
            if (playOnLoad) {
                uiHelper.setRoleDragSize(skNode, 360, 67)
            } else {
                uiHelper.setRoleDragSize(skNode, 300, 22)
            }
        })
    }

    private setUIAtkHp(role: cc.Node, hp, atk) {
        let ui = role.Child('ui/BattleUIAtkHp')
        ui.Child("hp/count", cc.Label).string = hp
        ui.Child("hp/bar", cc.Sprite).fillRange = 1
        ui.Child("attack/count", cc.Label).string = atk
    }

    private showSkillInfo(it: cc.Node, data: BattleRole) {
        let info = { id: data.id, skills: data.getSkills() }
        viewHelper.showBubble("SkillBubble", it, info)
    }
}
