import MultiMaterial from "../../../core/component/MultiMaterial";
import { LongPress } from "../../common/constant/Enums";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BattleRole from "../../model/battle/BattleRole";
import FBOCmpt from "../cmpt/common/fbo/FBOCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class BlackHoleRolePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected backNode_: cc.Node = null // path://back_be_n
    protected selectRolesSv_: cc.ScrollView = null // path://root/selectRoles_sv
    //@end

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({ isAct: false })
    }

    public onEnter(doata: any) {
        this.initSelectRoles()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initSelectRoles() {
        let roles = gameHelper.blackHole.getPassengers()
        let mask = this.selectRolesSv_.node.Child('mask')
        this.selectRolesSv_.node.Child('mask', MultiMaterial).setMaterial(1, { color: mask.color })
        this.selectRolesSv_.node.Component(cc.Widget).bottom = 70
        let passenegrs = roles
        let deads = gameHelper.blackHole.getDeads()
        passenegrs.sort((a, b) => {
            if (this.hasUid(deads, a) != this.hasUid(deads, b)) {
                if (this.hasUid(deads, a)) {
                    return 1
                }
                return -1
            }
            return gameHelper.battlePassengersCmp(a, b)
        })

        this.selectRolesSv_.List(passenegrs.length, (it, i) => {
            let data = passenegrs[i]
            this.initRole(it.Child('drag/role'), data, !!gameHelper.blackHole.getAid(data.uid))
            let node = it.Child('drag')
            it.Child('dead').active = this.hasUid(deads, data)
            let mask = node.Child('mask')
            mask.active = it.Child('dead').active
            mask.Component(FBOCmpt).source = it.Child('drag/role')
            mask.Component(FBOCmpt).target = mask.Component(cc.Sprite)
            if (it.Child('dead').active) {
                mask.Component(MultiMaterial).setMaterial(1, { color: mask.color, opacity: 0.5 })
            }
            node.Child('role/select').active = false
            this.registerSkillTips(node, data)
        })
    }

    private hasUid(list: BattleRole[], a: BattleRole) {
        return !!list.find(r => r.uid == a.uid)
    }

    private initRole(role: cc.Node, data: BattleRole, isAid: boolean = false) {
        let body = role.Child('body')
        role.Child('lv', cc.MultiColor).setColor(data.quality - 1)
        role.Child('isAid').active = isAid
        this.setUILv(role, data.getLevel())
        this.setUISk(body.Child('sp'), data, true)
        this.setUIAtkHp(role, data.getHp(), data.getAttack())
        this.setStars(data.getStarLvWithQuality(), role.Child('star'))

        let battleTypeNode = role.Child("battle_type")
        resHelper.loadBattleTypeIcon(data.battleType, battleTypeNode.Child('icon'), this.getTag())
        let animalTypeNode = role.Child("animal_type")
        resHelper.loadAnimalTypeIcon(data.animalType, animalTypeNode.Child('icon'), this.getTag())
    }

    private setUILv(role: cc.Node, val: number) {
        role.Child('lv', cc.Label).setLocaleKey('common_guiText_11', val)
    }

    private async setUISk(skNode: cc.Node, data: BattleRole, playOnLoad?: boolean) {
        let sk = skNode.Component(sp.Skeleton)
        if (data.uid != String(data.id)) {
            await resHelper.loadRoleSp(data.id, sk, this.getTag(), playOnLoad)
        }
        else {
            await resHelper.loadOwnRoleSp(data.id, sk, this.getTag(), playOnLoad)
        }
    }

    private setUIAtkHp(role: cc.Node, hp, atk) {
        let ui = role.Child('BattleUIAtkHp')
        ui.Child("hp/count", cc.Label).string = hp
        ui.Child("hp/bar", cc.Sprite).fillRange = 1
        ui.Child("attack/count", cc.Label).string = atk
    }

    private setStars(num: number, node: cc.Node) {
        let list: boolean[] = []
        for (let i = 0; i < num; i++) {
            list.push(true)
        }
        node.Items(list, (it, data) => {
            it.active = data
        })
    }

    private registerSkillTips(node: cc.Node, data: BattleRole) {
        node.off(LongPress.LPSTART)
        node.on(LongPress.LPSTART, () => { this.showSkillInfo(node, data) }, this)
    }

    private showSkillInfo(it: cc.Node, data: BattleRole) {
        let info = { id: data.id, skills: data.getSkills() }
        viewHelper.showBubble("SkillBubble", it, info)
    }

}
