import { game<PERSON>elper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import BlackHoleEquip from "../../model/blackHole/BlackHoleEquip";

const { ccclass } = cc._decorator;

@ccclass
export default class BlackHoleEquipPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected equipsSv_: cc.ScrollView = null // path://root/equips_sv
    protected backNode_: cc.Node = null // path://back_be_n
    //@end

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({isAct: false})
    }

    public onEnter(data: any) {
        this.initView()
    }


    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    
    // ----------------------------------------- custom function ----------------------------------------------------

    private async initView() {
        let equips = gameHelper.blackHole.equips

        // equips = assetsMgr.getJson<any>("BlackHoleEquip").datas.filter(c => c.target).map((cfg: any)=> new BlackHoleEquip().init({id: cfg.id, level: 50, target: 1}))

        this.equipsSv_.List(equips.length, (it, i)=>{
            let equip = equips[i]
            resHelper.loadBlackHoleEquipIcon(equip, it.Child("icon"), this.getTag())
            it.Child("lv").setLocaleKey("common_guiText_11", equip.level)
            it.Child("name").setLocaleKey(equip.name)
            it.Child("desc").setLocaleUpdate(()=>equip.getDescStr())
        })
    }
    
}
