import MultiMaterial from "../../../core/component/MultiMaterial";
import { UNLOCK_TIME_SHORT } from "../../common/constant/Constant";
import { BlackHoleBuffType, BlackHoleNodeType, LongPress } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { anim<PERSON>elper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BattleRole from "../../model/battle/BattleRole";
import BlackHoleEquip from "../../model/blackHole/BlackHoleEquip";
import { BlackHoleBuff, BlackHoleNode } from "../../model/blackHole/BlackHoleModel";
import FBOCmpt from "../cmpt/common/fbo/FBOCmpt";

const { ccclass } = cc._decorator;


const POS = [
    { pos: cc.v2(0, 0), scale: 1 },
    { pos: cc.v2(400, 50), scale: 0.8 },
    { pos: cc.v2(691, 52), scale: 0.8 },
    { pos: cc.v2(830, 100), scale: 0 },
]

const Sensitivity = 0.25 // 滑动灵敏度
const Boundary = 0.35 // 滑动回弹临界值

@ccclass
export default class BlackHoleDetailPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected closeCurrentNode_: cc.Node = null // path://close_current_n
    protected backNode_: cc.Node = null // path://back_be_n
    protected rebirthNode_: cc.Node = null // path://root/rebirth_n
    protected aidsNode_: cc.Node = null // path://root/aids_n
    protected touchMoveNode_: cc.Node = null // path://root/aids_n/touchMove_n
    protected aidSkillAskNode_: cc.Node = null // path://root/aids_n/aidSkillAsk_be_n
    protected preNode_: cc.Node = null // path://root/aids_n/pre_be_n
    protected nextNode_: cc.Node = null // path://root/aids_n/next_be_n
    protected buffsNode_: cc.Node = null // path://root/buffs_n
    protected equipNode_: cc.Node = null // path://root/equip_n
    protected selectRolesSv_: cc.ScrollView = null // path://root/selectRoles_sv
    protected rebirthTipNode_: cc.Node = null // path://root/rebirthTip_n
    protected bottomTipsNode_: cc.Node = null // path://root/rebirthTip_n/bottomTips_n
    protected confirmNode_: cc.Node = null // path://root/confirm_be_n
    protected titleNode_: cc.Node = null // path://root/title_n
    protected tipsNode_: cc.Node = null // path://tips_n
    protected uiNode_: cc.Node = null // path://ui_n
    protected techNode_: cc.Node = null // path://ui_n/btns/tech_be_n
    //@end

    private model: BlackHoleNode = null

    private selectedRole: BattleRole = null

    private selectBuff: BlackHoleBuff = null

    private selectEquip: BlackHoleEquip = null

    private curIndex: number = 1
    private touchMove: number = 0
    private isMove: boolean = false
    private isTouchMove: boolean = false

    public listenEventMaps() {
        return [
        ]
    }

    public async onCreate() {
        this.setParam({ isMask: false, isAct: false })
        let mask = this.node.Child("PNL_MASK")
        mask.SetColor("#070c19")
        mask.opacity = 0.85 * 255

        this.touchMoveNode_.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.touchMoveNode_.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        this.touchMoveNode_.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
    }

    public onEnter(node: BlackHoleNode, isPreview: boolean) {
        this.initNodes()
        if (!node) {
            this.initTitle()
            this.initSelectRoles(false)
        } else {
            this.confirmNode_.Component(cc.Button).interactable = !isPreview
            this.rebirthNode_.Child('confirm', cc.Button).interactable = !isPreview
            this.model = node
            this.initView()
        }
        this.techNode_.active = !gameHelper.blackHole.isSimple()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        if (this.model?.type == BlackHoleNodeType.ATTR && this.selectedRole) {
            this.selectedRole = null
            this.initNodes()
            this.initView()
        }
        else {
            this.close()
        }
    }

    // path://root/confirm_be_n
    onClickConfirm(event: cc.Event.EventTouch, data: string) {
        this.onConfirm()
    }

    // path://root/aids_n/pre_be_n
    onClickPre(event: cc.Event.EventTouch, data: string) {
        if (this.isMove || this.isTouchMove) return
        this.moveItems(-1)
    }

    // path://root/aids_n/next_be_n
    onClickNext(event: cc.Event.EventTouch, data: string) {
        if (this.isMove || this.isTouchMove) return
        this.moveItems(1)
    }

    // path://root/aids_n/aidSkillAsk_be_n
    onClickAidSkillAsk(event: cc.Event.EventTouch, data: string) {
        this.showSkillInfo(event.target, this.selectedRole)
        // if (!this.selectedRole?.getSkills()) {
        //     viewHelper.showAlert('chapterEmbattle_guiText_2')
        // }
        // else {
        //     viewHelper.showPnl('role/RoleTalentPnl', this.selectedRole, this.selectedRole.getSkills()[0], true)
        // }
    }

    // path://ui_n/btns/passengers_be
    onClickPassengers(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("blackHole/BlackHoleRole")
    }

    // path://ui_n/btns/tech_be_n
    onClickTech(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("blackHole/BlackHoleEquip")
    }

    // path://ui_n/btns/boss_be
    onClickBoss(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("blackHole/BlackHolePreview")
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private onTouchMove(event: cc.Event.EventTouch, data: string) {
        if (!this.enabledInHierarchy || this.isMove) return;
        let touch = event.touch;
        let hit = this.touchMoveNode_._hitTest(touch.getLocation());
        if (hit) {
            let x = event.getDeltaX() * Sensitivity * -0.01
            this.touchMove += x
            if ((this.curIndex <= 0 && x < 0) || (this.curIndex >= this.model.aids.length - 1 && x > 0) || x == 0) return

            if (this.curIndex + x < 0) x = 0 - this.curIndex
            else if (this.curIndex + x > this.model.aids.length - 1) x = this.model.aids.length - 1 - this.curIndex
            this.touchMoveItems(x, 0)
        }
        else {
            this.onTouchEnd(event, data)
        }
        event.stopPropagation();
    }

    private onTouchEnd(event: cc.Event.EventTouch, data: string) {
        if (this.isMove) return
        let x = Math.ceil(this.curIndex) - this.curIndex
        if (this.touchMove > 0) {
            if (this.curIndex - Math.floor(this.curIndex) >= Boundary) {
                x = Math.ceil(this.curIndex) - this.curIndex
            }
            else {
                x = Math.floor(this.curIndex) - this.curIndex
            }
        }
        else if (this.touchMove < 0) {
            if (this.curIndex - Math.floor(this.curIndex) >= 1 - Boundary) {
                x = Math.ceil(this.curIndex) - this.curIndex
            }
            else {
                x = Math.floor(this.curIndex) - this.curIndex
            }
        }
        this.touchMove = 0
        if (x == 0 && !this.isTouchMove) return
        this.touchMoveItems(x)
    }
    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        let type = this.model.type

        if (type == BlackHoleNodeType.AID) {
            this.initAid()
        }
        else if (type == BlackHoleNodeType.ATTR) {
            if (this.selectedRole) {
                this.initSelectRoles()
            }
            else {
                if (this.model.buffs.length) {
                    this.initBuffs()
                }
                else {
                    this.initEquips()
                }
            }
        }
        else if (type == BlackHoleNodeType.REBIRTH) {
            this.initRebirth()
        }
    }

    private initNodes() {
        this.selectRolesSv_.node.active = false
        this.aidsNode_.active = false
        this.equipNode_.active = false
        this.buffsNode_.active = false
        this.rebirthNode_.active = false
        this.rebirthTipNode_.active = false
        this.backNode_.active = false
        this.closeCurrentNode_.active = true
    }

    private initSelectRoles(canClick: boolean = true) {
        let roles = gameHelper.blackHole.getPassengers()
        this.backNode_.active = true
        this.closeCurrentNode_.active = false
        this.selectRolesSv_.node.active = true
        let mask = this.selectRolesSv_.node.Child('mask')
        this.selectRolesSv_.node.Child('mask', MultiMaterial).setMaterial(1, { color: mask.color })
        //this.selectRolesSv_.node.height = canClick ? 800 : 1000
        //this.selectRolesSv_.node.Component(cc.Widget).top = canClick ? 180 : 180
        this.selectRolesSv_.node.Component(cc.Widget).bottom = canClick ? 210 : 70
        //this.maskNode_.y = this.selectRolesSv_.node.y - this.selectRolesSv_.node.height
        let passenegrs = roles
        let deads = gameHelper.blackHole.getDeads()
        passenegrs.sort((a, b) => {
            if (this.hasUid(deads, a) != this.hasUid(deads, b)) {
                if (this.hasUid(deads, a)) {
                    return 1
                }
                return -1
            }
            return gameHelper.battlePassengersCmp(a, b)
        })

        if (this.selectedRole == null) {
            this.selectedRole = passenegrs[0]
        }

        this.selectRolesSv_.List(passenegrs.length, (it, i) => {
            let data = passenegrs[i]
            this.initRole(it.Child('drag/role'), data, !!gameHelper.blackHole.getAid(data.uid))
            let node = it.Child('drag')
            it.Child('dead').active = this.hasUid(deads, data)
            let mask = node.Child('mask')
            mask.active = it.Child('dead').active
            mask.Component(FBOCmpt).source = it.Child('drag/role')
            mask.Component(FBOCmpt).target = mask.Component(cc.Sprite)
            if (it.Child('dead').active) {
                mask.Component(MultiMaterial).setMaterial(1, { color: mask.color, opacity: 0.5 })
            }
            if (canClick && !this.hasUid(deads, data)) {
                node.Child('role/select').active = this.selectedRole.uid == data.uid
                node.off(LongPress.CLICK)
                node.on(LongPress.CLICK, () => {
                    this.onSelectRole(data)
                })
            } else {
                node.Child('role/select').active = false
            }
            this.registerSkillTips(node, data)
        })
    }

    private hasUid(list: BattleRole[], a: BattleRole) {
        return !!list.find(r => r.uid == a.uid)
    }

    private onSelectRole(role: BattleRole) {
        this.selectedRole = role
        this.initView()
    }

    private initRole(role: cc.Node, data: BattleRole, isAid: boolean = false) {
        let body = role.Child('body')
        role.Child('lv', cc.MultiColor).setColor(data.quality - 1)
        role.Child('isAid').active = isAid
        this.setUILv(role, data.getLevel())
        this.setUISk(body.Child('sp'), data, true)
        this.setUIAtkHp(role, data.getHp(), data.getAttack())
        this.setStars(data.getStarLvWithQuality(), role.Child('star'))

        let battleTypeNode = role.Child("battle_type")
        resHelper.loadBattleTypeIcon(data.battleType, battleTypeNode.Child('icon'), this.getTag())
        let animalTypeNode = role.Child("animal_type")
        resHelper.loadAnimalTypeIcon(data.animalType, animalTypeNode.Child('icon'), this.getTag())
    }

    private setUILv(role: cc.Node, val: number) {
        role.Child('lv', cc.Label).setLocaleKey('common_guiText_11', val)
    }

    private async setUISk(skNode: cc.Node, data: BattleRole, playOnLoad?: boolean) {
        let sk = skNode.Component(sp.Skeleton)
        if (data.uid != String(data.id)) {
            await resHelper.loadRoleSp(data.id, sk, this.getTag(), playOnLoad)
        }
        else {
            await resHelper.loadOwnRoleSp(data.id, sk, this.getTag(), playOnLoad)
        }
        if (!cc.isValid(this)) return
        uiHelper.setRoleDragSize(skNode, 300, 22)
    }

    private setUIAtkHp(role: cc.Node, hp, atk) {
        let ui = role.Child('BattleUIAtkHp')
        ui.Child("hp/count", cc.Label).string = hp
        ui.Child("hp/bar", cc.Sprite).fillRange = 1
        ui.Child("attack/count", cc.Label).string = atk
    }

    private setStars(num: number, node: cc.Node) {
        let list: boolean[] = []
        for (let i = 0; i < num; i++) {
            list.push(true)
        }
        node.Items(list, (it, data) => {
            it.active = data
        })
    }

    private registerSkillTips(node: cc.Node, data: BattleRole) {
        node.off(LongPress.LPSTART)
        node.on(LongPress.LPSTART, () => { this.showSkillInfo(node, data) }, this)
        node.off(LongPress.LPEND)
        node.on(LongPress.LPEND, this.hideSkillInfo, this)
    }

    private showSkillInfo(it: cc.Node, data: BattleRole) {
        this.tipsNode_.active = true

        let info = {id: data.id, skills: data.getSkills()}
        viewHelper.showBubble("SkillBubble", it, info)
    }

    private hideSkillInfo() {
        this.tipsNode_.active = false
    }

    //---------------------------援助---------------------------------------------
    private initAid() {
        this.confirmNode_.active = true
        this.backNode_.active = true
        this.closeCurrentNode_.active = false
        this.aidsNode_.active = true
        let passenegrs = this.model.aids

        if (this.selectedRole == null) {
            this.selectedRole = passenegrs[0]
        }
        this.initTitle("blackHole_guiText_10", assetsMgr.lang("blackHole_guiText_1"))
        this.aidsNode_.Child('content').Items(passenegrs, (it, data, i) => {
            it.name = String(i)
            this.initRole(it.Child('drag/role'), data, true)
            let node = it.Child('drag')
            this.registerSkillTips(node, data)
        })
        this.moveItems(this.curIndex, 0)
    }

    private updatePagingBtn() {
        this.preNode_.active = this.curIndex > 0
        this.nextNode_.active = this.curIndex < this.model.aids.length - 1
    }

    private async touchMoveItems(index: number, time = 0.2) {
        let nodes = this.aidsNode_.Child('content').children

        this.curIndex += index
        this.updatePagingBtn()
        if (time) { this.isMove = true }
        this.isTouchMove = true
        await ut.promiseMap(nodes, async (node: cc.Node) => {
            let i = Number(node.name)
            let indexDis = Math.min(POS.length - 1, Math.abs(this.curIndex - i))
            let nxtPos = POS[Math.min(POS.length - 1, Math.floor(indexDis) + 1)]
            let pos = POS[Math.floor(indexDis)]
            let { x, y } = pos.pos
            let scale = pos.scale
            let drtX = nxtPos.pos.sub(pos.pos).x
            let drtY = nxtPos.pos.sub(pos.pos).y
            let drtScale = nxtPos.scale - scale
            let progress = (Math.abs(this.curIndex - i) - Math.floor(Math.abs(this.curIndex - i)))
            let targetX = progress * drtX + x
            let targetY = progress * drtY + y
            let targetScale = progress * drtScale + scale

            targetX *= ut.normalizeNumber(i - this.curIndex)
            if (i == this.curIndex) {
                node.setDark(0, true)
            }
            else {
                node.setDark(0.2, true)
            }

            node.zIndex = 100 - indexDis
            if (time) {
                await cc.tween(node).to(time, { x: targetX, y: targetY, scale: targetScale }).promise()
            }
            else {
                node.setPosition(targetX, targetY)
                node.scale = targetScale
            }
        })
        this.updateConfirm()
        this.isTouchMove = false
        this.isMove = false
    }

    private async moveItems(index: number, time = 0.3) {
        let nodes = this.aidsNode_.Child('content').children
        if (time == 0) {
            this.curIndex = index
        } else {
            this.curIndex += index
        }
        this.updatePagingBtn()
        this.confirmNode_.active = false
        this.aidSkillAskNode_.active = false
        this.isMove = true
        await ut.promiseMap(nodes, async (node: cc.Node) => {
            let i = Number(node.name)
            let indexDis = Math.min(POS.length - 1, Math.abs(this.curIndex - i))
            let { x, y } = POS[indexDis].pos
            x *= ut.normalizeNumber(i - this.curIndex)
            let scale = POS[indexDis].scale
            if (i == this.curIndex) {
                node.setDark(0, true)
            } else {
                node.setDark(0.2, true)
            }

            node.zIndex = 100 - indexDis

            if (time) {
                await cc.tween(node).to(time, { x, y, scale }).promise()
            }
            else {
                node.setPosition(x, y)
                node.scale = scale
            }
        })
        this.confirmNode_.active = true
        this.aidSkillAskNode_.active = true
        this.updateConfirm()
        this.isMove = false
    }

    private updateConfirm() {
        this.confirmNode_.active = this.curIndex == Math.ceil(this.curIndex)
        this.aidSkillAskNode_.active = this.confirmNode_.active
        this.selectedRole = this.curIndex == Math.ceil(this.curIndex) ? this.model.aids[this.curIndex] : this.selectedRole
    }

    //-----------------------------成长-------------------------------------------
    private initBuffs() {
        this.buffsNode_.active = true
        this.backNode_.active = true
        this.closeCurrentNode_.active = false
        this.confirmNode_.active = true
        if (this.selectBuff == null) {
            this.selectBuff = this.model.buffs[0]
        }

        this.initTitle("blackHole_guiText_12")
        this.buffsNode_.Child('content').Items(this.model.buffs, (it, buff) => {
            it.Child('bg', cc.MultiFrame).setFrame(this.selectBuff == buff)
            it.Child('buffSp', cc.MultiFrame).setFrame(buff.type - 1)
            it.Child('name', cc.Label).setLocaleKey(buff.name)
            if (buff.type == BlackHoleBuffType.ALL) {
                it.Child('desc', cc.RichText).setLocaleKey(buff.content)
            }
            else {
                it.Child('desc', cc.RichText).setLocaleKey(buff.content, `<color=#7ac23f>${buff.add.count}</c>`)
            }
            it.Child('effect', cc.RichText).string = this.getBuffDesc(buff)
            it.off("click")
            it.on("click", () => {
                this.selectBuff = buff
                this.initView()
            })
        })
    }

    private getBuffDesc(buff) {
        let str = ""
        if (buff.add.hp) {
            str += `<img src='xue'/> +${buff.add.hp}\t`
        }
        if (buff.add.attack) {
            str += `\t<img src='gong'/> +${buff.add.attack}`
        }
        return str
    }

    private initEquips() {
        this.equipNode_.active = true
        this.backNode_.active = true
        this.closeCurrentNode_.active = false
        this.confirmNode_.active = true
        if (this.selectEquip == null) {
            this.selectEquip = this.model.equips[0]
        }

        this.initTitle("blackHole_guiText_23")
        this.equipNode_.Child('content').Items(this.model.equips, (it, equip) => {
            it.Data = equip
            resHelper.loadBlackHoleEquipIcon(equip, it.Child("icon"), this.getTag())
            it.Child("lv").setLocaleKey("common_guiText_11", equip.level)
            it.Child("name").setLocaleKey(equip.name)
            it.Child("desc").setLocaleUpdate(()=>{
                return equip.getDescStr()
            })
            it.Child('select').active = this.selectEquip == equip
            it.off("click")
            it.on("click", () => {
                this.selectEquip = equip
                this.initView()
            })
        })
    }

    //-----------------------------复活-------------------------------------------
    private initRebirth() {
        this.rebirthNode_.active = true
        let buff = this.model.buffs[0]
        this.rebirthNode_.Child('desc', cc.RichText).setLocaleKey("blackHole_guiText_7", `${buff.add.count}`, `${buff.add.hp}`, `${buff.add.attack}`)
        this.rebirthNode_.Child('confirm').off('click')
        this.rebirthNode_.Child('confirm').on('click', () => {
            this.rebirthNode_.active = false
            this.onConfirm()
        })
    }

    private showRebirthTip(uid: string[], isRebirth: boolean) {
        if (!uid || uid.length <= 0 || !uid[0]) return
        this.backNode_.active = false
        this.rebirthTipNode_.active = true
        let passenegrs = gameHelper.blackHole.getPassengers()
        let roles = uid.map(_uid => passenegrs.find(r => r.uid == _uid))
        let str = ""
        this.rebirthTipNode_.Child("content").Items(roles, (it, role, i) => {
            if (i > 0) str += '、'
            str += assetsMgr.lang(cfgHelper.getCharacter(role.id).name)
            this.initRole(it.Child('drag/role'), role, !!gameHelper.blackHole.getAid(role.uid))
            it.Child('drag/role/select').Swih(isRebirth ? "2" : "1")
        })
        this.initTitle(isRebirth ? "blackHole_guiText_8" : "blackHole_guiText_14", str)
        ut.wait(UNLOCK_TIME_SHORT, this).then(() => {
            this.closeCurrentNode_.active = true
            this.bottomTipsNode_.active = true
            animHelper.playContinueBlink(this.bottomTipsNode_)
        })
    }


    private async onConfirm() {
        let type = this.model.type
        let blackHole = gameHelper.blackHole
        let uid = this.selectedRole?.uid

        let data: proto.IC2S_SelectBlackHoleNodeMessage = { nodeId: this.model.id }

        if (type == BlackHoleNodeType.AID) {
            data.aid = uid
        }
        else if (type == BlackHoleNodeType.ATTR) {
            if (this.model.buffs.length) {
                let buffType = this.selectBuff.type
                if (buffType == BlackHoleBuffType.SELECT && !this.selectedRole) {
                    this.buffsNode_.active = false
                    //改title
                    let str = assetsMgr.lang(this.selectBuff.content, `${this.selectBuff.add.count}`) + `<color=89ff5b>${this.getBuffDesc(this.selectBuff)}</c>`
                    this.titleNode_.Child('layout/rt', cc.RichText).string = str
                    this.initSelectRoles()
                    return
                }
                else {
                    data.buff = { type: buffType, index: this.model.buffs.findIndex(b => b == this.selectBuff) }
                    if (uid) {
                        data.buff.targets = [uid]
                    }
                }
            }
            else {
                data.equip = {id: this.selectEquip.id, target: this.selectEquip.target, level: this.selectEquip.level}
            }
     
        }

        let { code, rebirth, buff } = await blackHole.select(data)
        if (code != 0) return
        viewHelper.waitCloseUI('blackHole/BlackHoleDetail').then(() => {
            eventCenter.emit(EventType.BLACK_HOLE_MOVE)
        })

        let passengers = blackHole.getPassengers()
        if (buff) {
            if (buff.type == BlackHoleBuffType.RANDOM || buff.type == BlackHoleBuffType.SELECT) {
                this.buffsNode_.active = false
                this.confirmNode_.active = false
                this.selectRolesSv_.node.active = false
                let roleIds = passengers.filter(r => buff.targets.has(r.uid)).map(r => r.uid)
                this.showRebirthTip(roleIds, false)
                return
            }
        }
        else if (rebirth) {
            this.rebirthNode_.active = false
            let role = passengers.find(p => p.uid == rebirth)
            this.showRebirthTip([role.uid], true)
            return
        }

        this.close()
    }

    private initTitle(lbKey?: string, ...params: any[]) {
        this.titleNode_.active = true
        if (!lbKey) {
            this.titleNode_.Child('layout').active = false
            this.titleNode_.Component(cc.Label).setLocaleKey("blackHole_guiText_4")
            return
        }
        let type = this.model.type
        if (type == BlackHoleNodeType.AID) {
            this.titleNode_.Component(cc.Label).setLocaleKey("blackHole_guiText_9")
        }
        else if (type == BlackHoleNodeType.ATTR) {
            if (this.model.buffs.length) {
                this.titleNode_.Component(cc.Label).setLocaleKey("blackHole_guiText_11")
            }
            else {
                this.titleNode_.Component(cc.Label).setLocaleKey("blackHole_guiText_21")
            }
        }
        else if (type == BlackHoleNodeType.REBIRTH) {
            this.titleNode_.Component(cc.Label).setLocaleKey("blackHole_guiText_6")
        }
        this.titleNode_.Child("layout/rt", cc.RichText).setLocaleKey(lbKey, params)
    }
}
