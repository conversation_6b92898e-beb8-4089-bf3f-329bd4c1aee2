import { StoreId, ConditionType, ItemID } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { timeHelper } from "../../common/helper/TimeHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import StoreModel from "../../model/store/StoreModel";
import FrameIconNum from "../prefab/FrameIconNum";

const { ccclass } = cc._decorator;

@ccclass
export default class BlackHoleStorePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected refreshNode_: cc.Node = null // path://root/refresh_be_n
    protected goodsNode_: cc.Node = null // path://root/goods_n
    protected resetTimeLbl_: cc.Label = null // path://root/resetTime_l
    //@end

    protected model: StoreModel = null

    public listenEventMaps() {
        return [
            { [EventType.STORE_REFRESH]: this.initView },
            { [EventType.DAILY_REFRESH]: this.initView },
            { [EventType.UPDATE_CURRENCY]: this.initView }
        ]
    }

    public async onCreate() {
        this.model = gameHelper.store.getStore(StoreId.BLACK_HOLE)
        await this.model.checkSync()
    }

    public onEnter(data: any) {
        this.initView()
    }


    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/refresh_be_n
    onClickRefresh(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("common/Refresh", { 
            cost: this.model.getRefreshCost(), title: assetsMgr.lang("store_buttonName_1"), desc: assetsMgr.lang("blackHole_guiText_25"), cb: this.refresh.bind(this) 
        })
    }

    // path://back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        let goods = this.model.getGoods()
        this.goodsNode_.Items(goods, (node, data) => {
            node.Child("sold_out").active = data.isSellOut()
            let discount = node.Child("discount")
            discount.active = data.isDiscount()
            discount.Child("count").setLocaleKey("store_guiText_3", data.discount)

            let iconNode = node.Child("FrameIconNum")
            iconNode.Component(FrameIconNum).init(data.item, this.getTag())
            if (gameHelper.isRoleTicket(data.item)) {
                iconNode.Child("icon").scale = 1
            }
            node.Child('num').active = !data.isSellOut()
            node.Child('num', cc.Label).string = "x" + uiHelper.getShowNum(data.item)

            node.off("click")
            node.on("click", () => {
                if (data.isSellOut()) {
                    return viewHelper.showAlert("field_tips_2")
                }
                viewHelper.showPnl("store/GoodsPnl", data)
            })

            node.Child("cost/count", cc.Label).string = uiHelper.getShowNum(data.cost)

            for (let child of node.children) {
                if (child.name == "sold_out") continue
                child.setDark(data.isSellOut() ? 0.5 : 0, true)
            }
        })
    }

    update() {
        this.resetTimeLbl_.setLocaleKey("store_guiText_4", timeHelper.getTimeText(Math.floor(this.model.getRefreshSurplusTime() / ut.Time.Second)))
    }

    private async refresh() {
        if (!this.model.canRefresh()) {
            viewHelper.showAlert('store_tips_1')
            return
        }
        return await this.model.refresh()
    }

}
