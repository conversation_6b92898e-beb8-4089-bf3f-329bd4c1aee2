import { ConditionType } from "../../common/constant/Enums";
import { game<PERSON>elper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import PropObj from "../../model/bag/PropObj";
import ConditionObj from "../../model/common/ConditionObj";
import RoleAvatarCmpt from "../role/RoleAvatarCmpt";
import FragLvUpPnlCtrl from "../train/FragLvUpPnlCtrl";

const { ccclass } = cc._decorator;

@ccclass
export default class CollectionThrowPnlCtrl extends FragLvUpPnlCtrl {

    //@autocode property begin
    protected detailLbl_: cc.Label = null // path://root/info/detail_l
    protected itemLNode_: cc.Node = null // path://root/mid/itemL_n
    protected itemRNode_: cc.Node = null // path://root/mid/itemR_n
    protected progressNode_: cc.Node = null // path://root/progress_n
    protected moveNode_: cc.Node = null // path://root/progress_n/move_n
    protected reduceNode_: cc.Node = null // path://root/reduce_be_n
    protected addNode_: cc.Node = null // path://root/add_be_n
    protected confirmNode_: cc.Node = null // path://root/confirm_be_n
    protected tipNode_: cc.Node = null // path://root/tip_n
    //@end

    private item: PropObj = null

    public listenEventMaps() {
        return []
    }

    public onEnter(data: any) {
        this.changeNum = 1
        this.rate = 1
        this.item = data
        this.cap = this.item.count
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/reduce_be_n
    onClickReduce(event: cc.Event.EventTouch, data: string) {
        super.onClickReduce(event, data)
    }

    // path://root/add_be_n
    onClickAdd(event: cc.Event.EventTouch, data: string) {
        super.onClickAdd(event, data)
    }

    // path://root/confirm_be_n
    onClickConfirm(event: cc.Event.EventTouch, data: string) {
        this.onThrow()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async onThrow() {
        let succ = await gameHelper.bag.collectionThrow(this.item.id, this.changeNum)
        if (succ && cc.isValid(this)) {
            this.close()
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    protected initView(): void {
        this.refreshItem(this.itemLNode_, true)
        super.setChangeNum(this.changeNum, 0)
    }

    protected refreshItem(it: cc.Node, isLeft: boolean = false) {
        let cond = new ConditionObj().init(ConditionType.PROP, this.item.id, this.item.count)
        resHelper.loadIconByCondInfo(cond, it.Child("icon"), this.getTag())
        super.refreshCount(it, isLeft)
    }

}
