import { ConditionType, ItemID } from "../../common/constant/Enums";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../../model/common/ConditionObj";
import FragLvUpPnlCtrl from "../train/FragLvUpPnlCtrl";

const { ccclass } = cc._decorator;

@ccclass
export default class TicketMergePnlCtrl extends FragLvUpPnlCtrl {

    //@autocode property begin
    protected detailLbl_: cc.Label = null // path://root/info/detail_l
    protected itemLNode_: cc.Node = null // path://root/mid/itemL_n
    protected itemRNode_: cc.Node = null // path://root/mid/itemR_n
    protected progressNode_: cc.Node = null // path://root/progress_n
    protected moveNode_: cc.Node = null // path://root/progress_n/move_n
    protected reduceNode_: cc.Node = null // path://root/reduce_be_n
    protected addNode_: cc.Node = null // path://root/add_be_n
    protected confirmNode_: cc.Node = null // path://root/confirm_be_n
    protected tipNode_: cc.Node = null // path://root/tip_n
    //@end

    private item: ConditionObj = null
    private frag: ConditionObj = null

    public onEnter(data: any) {
        this.changeNum = 1
        this.rate = cfgHelper.getMiscData("ticket").mergeCnt

        this.item = new ConditionObj().init(ConditionType.PROP, ItemID.TICKET)
        this.frag = new ConditionObj().init(ConditionType.PROP, ItemID.TICKET_FRAG)
        let num = gameHelper.getNumByCondition(this.frag)
        this.frag.num = num
        this.cap = Math.floor(this.frag.num / this.rate)

        this.detailLbl_.setLocaleUpdate(()=>{
            return assetsMgr.lang("itemBag_guiText_5", `${this.rate}`, resHelper.getDescByCond(this.frag).name, resHelper.getDescByCond(this.item).name)
        })
        this.initView()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/reduce_be_n
    onClickReduce(event: cc.Event.EventTouch, data: string) {
        super.onClickReduce(event, data)
    }

    // path://root/add_be_n
    onClickAdd(event: cc.Event.EventTouch, data: string) {
        super.onClickAdd(event, data)
    }

    // path://root/confirm_be_n
    onClickConfirm(event: cc.Event.EventTouch, data: string) {
        super.onClickConfirm(event, data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    
    // ----------------------------------------- custom function ----------------------------------------------------

    protected async exchange() {
        let cost = this.frag.clone()
        cost.num = this.changeNum * this.rate
        if (!uiHelper.checkBuyCost([cost])) {
            return
        }
        let item = await gameHelper.bag.ticketMerge(this.changeNum)
        if (item && cc.isValid(this)) {
            viewHelper.showGeneralReward([item])
            this.close()
        }
    }

    protected refreshItem(it: cc.Node, isLeft: boolean = false) {
        let cond = isLeft ?  this.frag : this.item
        resHelper.loadIconByCondInfo(cond, it.Child("icon"), this.getTag())
        this.refreshCount(it, isLeft)
    }

    protected refreshCount(it: cc.Node, isLeft: boolean): void {
        let num = isLeft ? this.changeNum * this.rate : this.changeNum
        it.Child('count/lb', cc.Label).string = `${num}`
    }
    
}
