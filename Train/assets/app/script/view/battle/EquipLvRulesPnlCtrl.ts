import { CharacterCfg } from "../../common/constant/DataType";
import { PassengerQuality, RuleType } from "../../common/constant/Enums";

const { ccclass } = cc._decorator;

@ccclass
export default class EquipLvRulesPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected titleLbl_: cc.Label = null // path://title_l
    protected scrollSv_: cc.ScrollView = null // path://scroll_sv
    //@end

    public onEnter(type: RuleType) {
        this.setTitle(type)
        this.setDesc(type)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------
    private setTitle(type: RuleType) {
        this.titleLbl_.setLocaleKey(`help_title_${type}`)
    }
    private setDesc(type: RuleType) {
        if (type == RuleType.LOTTERY) {
            this.scrollSv_.Items(new Array(1), (it, i) => {
                it.setLocaleUpdate(this.getDescLottery)
            })
        } else {
            const ary: string[] = []
            let i = 0
            while (true) {
                let key = `help_content_${type}_${i++}`
                const str = assetsMgr.lang(key)
                if (!str || str == key) break
                ary.push(str)
            }
            this.scrollSv_.Items(ary, (it, i) => {
                it.setLocaleUpdate(() => i)
            })
        }
    }
    private getDescLottery() {
        let strN = ''
        let strR = ''
        let strSR = ''
        let comma = assetsMgr.lang('common_guiText_21')
        let array = assetsMgr.getJson<CharacterCfg>("Character").datas
        array.forEach(cfg => {
            switch (cfg.quality) {
                case PassengerQuality.N:
                    if (strN) strN += comma
                    strN += assetsMgr.lang(cfg.name)
                    break;
                case PassengerQuality.R:
                    if (strR) strR += comma
                    strR += assetsMgr.lang(cfg.name)
                    break;
                case PassengerQuality.SR:
                    if (strSR) strSR += comma
                    strSR += assetsMgr.lang(cfg.name)
                    break;
            }
        })
        return assetsMgr.lang('help_content_7', strSR, strR, strN)
    }
}
