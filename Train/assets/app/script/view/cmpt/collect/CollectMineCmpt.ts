import NodePool from "../../../../core/utils/NodePool";
import { PlanetMineType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { anim<PERSON>elper } from "../../../common/helper/AnimHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import CollectMineModel from "../../../model/collect/CollectMineModel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CollectMineCmpt extends mc.BaseCmptCtrl {

    public model: CollectMineModel = null

    protected body: cc.Sprite = null

    protected hp: cc.Sprite = null

    protected hpBar: cc.Node = null

    protected infoLayout: cc.Layout = null

    protected icon: cc.Sprite = null

    protected info: cc.Label = null

    protected actionMap: any = {}

    protected curHp: number = 0

    protected ui: cc.Node = null

    protected selected: cc.Node = null

    protected collectEffectNode: cc.Node = null

    protected nodePool: NodePool = new NodePool()

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.PLANET_MINE_HP_CHANGE]: this.onChangHp },
            { [EventType.COLLECT_TASK_TARGET_ERROR]: this.onTargetError },
        ]
    }

    public onCreate() {
        this.ui = this.Child('ui')
        this.hpBar = this.ui.Child('hpBar')
        this.hp = this.hpBar.Child('hp', cc.Sprite)
        this.selected = this.ui.Child('selected')
        this.body = this.Child('body', cc.Sprite)
        this.collectEffectNode = this.Child('collectEffect')
    }


    public init(model: CollectMineModel) {
        this.model = model
        this.curHp = this.model.hp
        this.initView()
    }

    protected async initView() {
        this.initScale()
        this.initPos()

        this.updateHpView(1)
        await this.initBody()
        if (!cc.isValid(this)) return

        this.initSize()

        this.initUI()
        this.initCollectEffect()
    }

    protected initScale() {
        if (this.model.scale < 0) {
            this.body.node.scaleX = -1
        }
        let scale = Math.abs(this.model.scale)
        this.node.scale = scale
        this.ui.scale = 1 / scale
    }

    protected initPos() {
        this.node.setPosition(this.model.position)
    }

    protected initSize() {
        let size = this.body.node.getContentSize()
        this.model.size = size
    }

    protected initBody() {
        if (!this.model.isInterference) {
            return this.initIcon()
        }
        else {
            return this.initSpine()
        }
    }

    protected async initIcon() {
        let tag = this.getTag()
        let icon = this.model.icon
        let spf = await assetsMgr.loadTempRes(`planet/collect/mine/${icon}`, cc.SpriteFrame, tag)
        if (!cc.isValid(this)) {
            assetsMgr.releaseTempResByTag(tag)
            return
        }
        this.body.spriteFrame = spf
    }

    protected async initSpine() {
        let tag = this.getTag()
        let icon = this.model.icon
        let skeletonData: sp.SkeletonData = await assetsMgr.loadTempRes(`collect/${icon}`, sp.SkeletonData, tag)
        if (!cc.isValid(this)) {
            assetsMgr.releaseTempResByTag(tag)
            return
        }
        let sk = this.body.Child("spine", sp.Skeleton)
        sk.node.active = true
        sk.skeletonData = skeletonData
        sk.playAnimation("idle", true)
        this.body.node.setContentSize(sk.node.getContentSize())
    }

    protected initUI() {
        this.ui.y = this.model.size.height + (40 / Math.abs(this.model.scale))
        this.ui.x = this.model.centerOffset.x
        this.ui.active = false
    }

    protected initCollectEffect() {
        this.collectEffectNode.x = this.ui.x
        this.collectEffectNode.y = this.model.size.height * 0.5
    }

    update(dt) {
        if (this.ui.active) {
            this.updateHpView(dt)
        }

        if (!this.model) return

        if (this.model.dead) {
            this.onDeath()
        }
    }

    private onChangHp(model: CollectMineModel, hp: number, damageMul: number = 1, isAuto = false) {
        if (this.model != model) return
        let damage = -hp
        if (damage > 0) {
            this.onHit(damage)
        }
        eventCenter.emit(EventType.PLANET_SHOW_HIT_TIPS, damage, damageMul, this.model, this.node, isAuto)
    }

    protected updateHpView(dt) {
        let lerpRatio = 10 * dt
        let targetRatio = 0
        if (this.model) {
            targetRatio = this.model.hp / this.model.maxHp
        }
        let shadow = this.hpBar.Child('shadow').Component(cc.Sprite)
        if (Math.abs(targetRatio - shadow.fillRange) < 0.01) {
            lerpRatio = 1
        }
        shadow.fillRange = cc.misc.lerp(shadow.fillRange, targetRatio, cc.misc.clamp01(lerpRatio))

        this.hp.fillRange = targetRatio
    }

    protected onHit(damge: number) {
        if (!this.model) return

        damge = ut.toRound(damge)

        this.ui.active = true

        let node = this.body.node
        this.doScaleAction(node, (scale) => {
            return cc.tween(node).to(0.1, { scaleY: scale.y * 1.2 }).to(0.1
                , { scaleY: scale.y * 0.8 }).to(0.1, { scaleY: scale.y })
        })

        if (this.collectEffectNode && this.model.collectEffect) {
            let node = this.nodePool.get(this.collectEffectNode, this.node)
            let sk = node.Component(sp.Skeleton)
            sk.setSkin(this.model.collectEffect)
            let name = "animation" + [1, 2, 3].random()
            sk.playAnimation(name).then(() => {
                this.nodePool.put(this.collectEffectNode, node)
            })
        }
    }

    private doScaleAction(node, callback) {
        let key = node.uuid
        let info = this.actionMap[key]
        let scale
        if (!info) {
            info = { node }
            this.actionMap[key] = info
        }
        if (info.orgScale) {
            scale = info.orgScale;
        }
        else {
            scale = cc.v2(node.scaleX, node.scaleY);
        }
        info.orgScale = scale;

        if (info.scaleAction) {
            info.scaleAction.stop();
        }
        info.scaleAction = callback(scale).call(() => {
            info.orgAnimScale = null;
            info.scaleAction = null;
        }).start()
    }

    protected stopScaleActions() {
        for (let key in this.actionMap) {
            let { node } = this.actionMap[key]
            node.stopAllActions()
        }
    }

    public onTarget(model: CollectMineModel) {
        if (this.model != model) {
            this.ui.active = false
        }
        else {
            this.ui.active = true
            this.selected.active = true
            this.selected.y = 100
            this.hpBar.active = this.model == model
        }
    }

    protected onDeath() {
        this.model = null

        this.stopScaleActions()

        let toOpacity = (node) => {
            cc.tween(node).to(0.2, { opacity: 0 }).start()
        }
        toOpacity(this.body.node)
        toOpacity(this.ui)

        this.playDeadAnim().then(() => {
            this.node.parent = null
            this.node.destroy()
        })
    }

    protected playDeadAnim() {
        let deadAni = this.Child('deadAni')
        deadAni.active = true
        deadAni.SetColor("#B2B2B278") //todo区分星球
        return deadAni.Component(sp.Skeleton).playAnimation("animation")
    }

    private onTargetError(model: CollectMineModel) {
        if (this.model != model) return

        if (this.model.isInterference) {
            let sk = this.body.Child("spine", sp.Skeleton)
            sk.unscheduleAllCallbacks()
            sk.playAnimation("enter").then(() => {
                sk.playAnimation("idle", true)
            })
        }
        else {
            animHelper.playClickShake(this.body.node)
        }
    }
}