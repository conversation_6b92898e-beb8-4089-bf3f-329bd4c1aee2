const { ccclass, property } = cc._decorator;

const ROPE_CFG = [
    { name: 'doubleS' },
    { name: 'doubleL' },
    { name: 'oneS' },
    { name: 'oneL' },
]

@ccclass
export default class Skill1017Cmpt extends cc.Component {

    private tmpVec2: cc.Vec2 = cc.v2()

    protected lateUpdate(dt: number): void {
        let node
        this.node.children.forEach(it => {
            if (it.active && ROPE_CFG.find(data => data.name == it.name)) {
                it.Child('connect').height = it.getPosition(this.tmpVec2).len()
                it.angle = it.getPosition(this.tmpVec2).xAngle()
                node = it
            }
        });
        if (!!node) {
            this.node.Child('downs').active = true
            this.node.Child('downs').setPosition(node.getPosition())
            this.node.Child('downs').angle = node.angle
            this.node.Child('downs').Swih(node.name)
        }
    }

}
