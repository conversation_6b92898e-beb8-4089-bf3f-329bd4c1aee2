import { PassengerAnimationType } from "../../../common/constant/Enums"

export const SP_ANIM = {
    2008: {
        1: {
            idle: "aniAttackIdle_first",
            ready: "aniAttackReady_first",
            readyJump: "aniAttackReadyJump_first",
            jump: "aniAttackJump_first",
            attack: "aniAttack_first",
            fall: "aniAttackFall_first",
            hit: "aniHit_first"
        },
        2: {
            idle: "aniAttackIdle",
            ready: "aniAttackReady",
            readyJump: "aniAttackReadyJump",
            jump: "aniAttackJump",
            attack: "aniAttack",
        },
        3: {
            idle: "aniAttackIdle2",
            ready: "aniAttackReady2",
            readyJump: "aniAttackReadyJump2",
            jump: "aniAttackJump2",
            attack: "aniAttack2",
        }
    },

    2015: {
        2: {
            idle: "aniAttackIdle2",
        }
    }
}

for (let key in SP_ANIM) {
    let info = SP_ANIM[key]
    for (let key2 in info) {
        let info2 = info[key2]
        for (let key3 in info2) {
            if (info2[key3]) {
                info2[key3] = PassengerAnimationType.BATTLE + "/" + info2[key3]
            }
        }
    }
}

export const LOAD_RES_CFG = {
    1007: {
        skill: true,
    },

    1011: {
        skill: true,
    },

    1017: {
        skill: true,
    },

    1020: {
        skill: true,
    },

    1021: {
        skill: true,
    },

    1022: {
        skill: true,
    },

    1023: {
        skill: true,
    },

    1024: {
        skill: true,
    },

    1026: {
        skill: true,
    },

    1027: {
        skill: true,
    },

    1028: {
        skill: true,
    },
    1035: {
        skill: true,
    },
    1036: {
        skill: true,
    },
    1037: {
        skill: true,
    },
    1038: {
        skill: true,
    },

    1042: {
        skill: true,
    },

    2002: {
        skill: true,
    },

    2005: {
        skill: true,
    },

    2006: {
        skill: true,
    },

    2007: {
        skill: true,
    },

    2009: {
        skill: true,
    },

    2010: {
        skill: true,
    },
    2011: {
        skill: true,
    },
    2019: {
        skill: true,
    },
    2022: {
        skill: true,
    },
    2023: {
        skill: true,
    },
    2024: {
        skill: true,
    },
    2025: {
        skill: true,
    },
    2026: {
        skill: true,
    },
    2029: {
        skill: true,
    },
    2034: {
        skill: true,
    },
    2035: {
        skill: true,
    },
}

export const SP_SKILL = {
    1003: {
        deathSkillFly: true
    },
    1004: {
        ready: true,
        skill: false,
    },
    1005: {
        ready: true,
    },
    1006: {
        deathSkillFly: true,
    },
    1007: {
        ready: true,
        skill: true,
    },
    1008: {
        deathSkillFly: true,
    },

    1011: {
        ready: true,
        showSkill: true,
        skill: false,
    },

    1012: {
        ready: {
            isGainBySelf: true,
        },
        skill: false,
    },

    1014: {
        ready: {
            isGainBySelf: true,
        },
        skill: false,
    },

    1016: {
        ready: true,
        skill: false,
    },

    1017: {
        ready: true,
        skill: true,
    },

    1019: {
        ready: true,
        skill: false,
    },

    1020: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },

    1021: {
        ready: true,
        skill: {
            func: "playSkillStraightLine"
        },
    },

    1022: {
        ready: true,
        skill: true,
    },

    1023: {
        ready: {
            func: "playSkillReady_1023"
        },
        skill: false,
    },

    1024: {
        skill: false,
        addBuff: {
            func: "playAddBuff_1024"
        },
        removeBuff: {
            delay: true,
            func: "playRemoveBuff_1024"
        },
    },


    1026: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },

    1027: {
        skill: {
            func: "playSkillCurve",
        },
    },

    1028: {
        ready: {
            func: "playSkillReady_1028"
        },
        skill: false,
        addBuff: {
            func: "playAddBuff_2009",
            mountPoint: "zhixin",
        },
        removeBuff: {
            func: "playRemoveBuff_2009",
            mountPoint: "zhixin",
        },
    },

    1029: {
        ready: {
            isGainBySelf: true,
        },
    },

    1033: {
        ready: {
            isGainBySelf: true,
        },
    },

    1036: {
        ready: true,
        skill: {
            func: "playSkillStraightLine",
        },
    },

    1035: {
        skill: {
            func: "playSkillCurve",
        },
    },

    1037: {
        ready: true,
        skill: {
            func: "playSkillStraightLine",
        },
    },

    1038: {
        ready: true,
        skill: {
            func: "playSkillStraightLine",
        },
    },

    1039: {
        ready: {
            isGainBySelf: true,
        },
    },

    1040: {
        ready: true,
    },

    1042: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },

    1043: {
        showSkill: true
    },


    2002: {
        skill: true,
    },

    2005: {
        ready: true,
        skill: {
            func: "playSkillCurve"
        },
    },

    2006: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },

    2007: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },

    2009: {
        skill: false,
        addBuff: true,
        removeBuff: true,
    },

    2010: {
        ready: true,
        skill: true,
    },
    2011: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },
    2012: {
        ready: {
            isGainBySelf: true
        },
        skill: false,
    },
    2014: {
        ready: true,
    },
    2015: {
        ready: {
            isGainBySelf: true,
        },
    },
    2016: {
    },
    2017: {
        ready: {
            isGainBySelf: true,
        },
    },
    2018: {
        ready: true,
        skill: false,
        addBuff: {
            func: "playAddBuff_2018"
        },
        removeBuff: {
            func: "playRemoveBuff_2018"
        },
    },
    2019: {
        showSkill: true
    },
    2021: {
        ready: true,
        skill: false,
    },
    2022: {
        ready: true,
        showSkill: {
            func: "showSkill_1011"
        },
        skill: false,
    },
    2023: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },
    2024: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },
    2025: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },
    2026: {
        ready: true,
        skill: {
            func: "playSkillStraightLine",
        },
    },
    2028: {
        ready: {
            func: "playSkillReady_2012",
            isGainBySelf: true
        },
        skill: false,
    },
    2029: {
        ready: true,
        skill: {
            func: "playSkillCurve",
        },
    },
    2032: {
        ready: {
            isGainBySelf: true,
        },
        skill: false,
    },
    2033: {
        ready: true,
        skill: false,
    },
    2034: {
        ready: true,
        skill: false,
        showSkill: true,
    },
}

export const EQUIP_SKILL = {
    
}