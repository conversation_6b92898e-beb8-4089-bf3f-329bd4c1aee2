import { CURRENY_CFG, PASSENGER_QUALITY_NAME } from "../../../common/constant/Constant";
import { Condition } from "../../../common/constant/DataType";
import { ConditionType, WantedConditionType } from "../../../common/constant/Enums";
import { cfgHelper } from "../../../common/helper/CfgHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import ConditionObj from "../../../model/common/ConditionObj";
import { WantedCondition } from "../../../model/wanted/WantedModel";
import BaseTipsCmpt from "./BaseBubbleCmpt";

const { ccclass } = cc._decorator;
@ccclass
export default class WantedCondBubbleCmpt extends BaseTipsCmpt {

    public init(data: WantedCondition) {
        let lb = this.Child('desc')
   
        lb.setLocaleUpdate(() => {
            switch(data.type) {
                case WantedConditionType.STAR: return assetsMgr.lang("character_starNeed", data.value)
                case WantedConditionType.QUALITY: return assetsMgr.lang('character_qualityNeed', assetsMgr.lang(PASSENGER_QUALITY_NAME[data.value]))
                case WantedConditionType.ANIMAL_TYPE: return assetsMgr.lang(cfgHelper.getAnimalTypeName(data.value))
                case WantedConditionType.BATTLE_TYPE: return assetsMgr.lang(cfgHelper.getBattleTypeName(data.value))
            }
        })
        this.node.parent.Component(cc.Layout)?.updateLayout()
    }
}