import EventType from "../../../common/event/EventType";
import { resHelper } from "../../../common/helper/ResHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import FixedNodeTransCmpt from "../common/FixedNodeTransCmpt";
import SpeechBubbleCmpt from "./SpeechBubbleCmpt";

const { ccclass, property, requireComponent, menu } = cc._decorator;

@ccclass
export default class BubbleRootCmpt extends mc.BaseCmptCtrl {

    public listenEventMaps() {
        return [
            { [EventType.SHOW_SPEECH_BUBBLE]: this.showSpeechBubble },
            { [EventType.HIDE_SPEECH_BUBBLE]: this.hideSpeechBubble },
        ]
    }

    public async showSpeechBubble(worldPos: cc.Vec2, key: string, tag: string, time = 3) {
        let root = this.node
        if (!root.activeInHierarchy) return
        let name = "__speech_bubble"
        let node = root.Child(name)
        if (!node) {
            node = new cc.Node(name)
            node.parent = root
        }
        else {
            node.active = true
        }
        let bubble = await resHelper.loadSpeechBubble(node, tag)
        if (!cc.isValid(this) || !bubble) return

        let pos = node.parent.convertToNodeSpaceAR(worldPos)
        node.setPosition(pos)
        bubble.Component(SpeechBubbleCmpt).init(key, time)
    }

    public hideSpeechBubble() {
        let root = this.node
        let name = "__speech_bubble"
        let node = root.Child(name)
        if (node) {
            node.active = false
        }
    }
}