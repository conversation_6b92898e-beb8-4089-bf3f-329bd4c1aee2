import EventType from "../../../common/event/EventType";
import { viewHelper } from "../../../common/helper/ViewHelper";
import BaseBubbleCmpt from "./BaseBubbleCmpt";

const { ccclass, property, requireComponent, menu } = cc._decorator;

@ccclass
export default class ExplainBubbleCmpt extends BaseBubbleCmpt {

    public init(data?) {
        let descNode = this.node.getChildByName('desc')
        let richText = descNode.Component(cc.RichText)
        richText.setLocaleUpdate(()=>{
            return data.desc
        })
        viewHelper.adaptLabel(richText, 400)
        let layout = this.node.Component(cc.Layout)
        descNode.setAnchorPoint(0.5, 1 + layout.paddingTop / descNode.height)
        layout.updateLayout()
    }

}