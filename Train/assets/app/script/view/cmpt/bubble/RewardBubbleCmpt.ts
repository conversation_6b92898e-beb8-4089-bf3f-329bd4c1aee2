import { CURRENY_CFG } from "../../../common/constant/Constant";
import { Condition } from "../../../common/constant/DataType";
import { ConditionType } from "../../../common/constant/Enums";
import { cfgHelper } from "../../../common/helper/CfgHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import ConditionObj from "../../../model/common/ConditionObj";
import BaseTipsCmpt from "./BaseBubbleCmpt";

const { ccclass } = cc._decorator;
@ccclass
export default class RewardBubbleCmpt extends BaseTipsCmpt {

    public init(data: Condition | ConditionObj) {
        let lb = this.Child('desc')
        lb.setLocaleUpdate(() => {
            if (data.type == ConditionType.PLANET_PROFILE) {
                return assetsMgr.lang("profile_item_desc")
            } else {
                let { name, content } = resHelper.getDescByCond(data)
                let str = name
                if (content) {
                    str += `：${content}`
                }
                return str
            }
        })
        viewHelper.adaptLabel(lb.Component(cc.RichText), 340)
        let layout = this.node.Component(cc.Layout)
        lb.setAnchorPoint(0.5, -layout.paddingBottom / lb.height)
        layout.updateLayout()
    }
}