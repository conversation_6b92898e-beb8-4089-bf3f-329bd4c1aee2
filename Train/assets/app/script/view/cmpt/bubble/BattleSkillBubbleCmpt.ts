import { CharacterCfg, PlanetMonsterCfg } from "../../../common/constant/DataType";
import { SkillType } from "../../../common/constant/Enums";
import { cfgHelper } from "../../../common/helper/CfgHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import BattleSkill from "../../../model/battle/BattleSkill";
import BaseBubbleCmpt from "./BaseBubbleCmpt";

const { ccclass } = cc._decorator;
@ccclass
export default class BattleSkillBubbleCmpt extends BaseBubbleCmpt {

    public init(data) {
        let { skills } = data

        let skillNode = this.node.Swih('skill')[0]

        skillNode.Items(skills, (it: cc.Node, skill: BattleSkill, i)=>{
            let descLb = it.Child('desc', cc.RichText)
            let needTrigger = !skills.slice(0, i).some(s => s.getTriggerDesc() == skill.getTriggerDesc())
            descLb.setLocaleUpdate(()=>{
                return skill.getDescStr(needTrigger)
            })
            it.Child("xian").active = i != skills.length - 1
            it.Component(cc.Layout).updateLayout()
        })

        skillNode.Component(cc.Layout).updateLayout()
    }

}