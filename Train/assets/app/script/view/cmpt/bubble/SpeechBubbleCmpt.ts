import EventType from "../../../common/event/EventType";
import { viewHelper } from "../../../common/helper/ViewHelper";
import FixedNodeTransCmpt from "../common/FixedNodeTransCmpt";
import BaseBubbleCmpt from "./BaseBubbleCmpt";

const { ccclass, property, requireComponent, menu } = cc._decorator;

@ccclass
export default class SpeechBubbleCmpt extends BaseBubbleCmpt {

    public async init(key: string, time: number = 0) {
        let descNode = this.node.getChildByName('lb')
        let lb = descNode.Component(cc.Label)
        lb.setLocaleKey(key)
        viewHelper.adaptLabel(lb, 378)
        let layout = this.node.Component(cc.Layout)
        descNode.setAnchorPoint(0.5, -layout.paddingBottom / descNode.height)
        layout.updateLayout()

        this.Component(FixedNodeTransCmpt)?.updateFlipX()

        let bubble = this.node
        let scaleX = bubble.scaleX
        bubble.scale = 0
        cc.Tween.stopAllByTarget(bubble)
        cc.tween(bubble).to(0.1, { scaleX, scaleY: 1 }).start()
        if (time > 0) {
            await ut.wait(time, this)
            if (cc.isValid(bubble)) {
                bubble.destroy()
            }
        }
    }

}