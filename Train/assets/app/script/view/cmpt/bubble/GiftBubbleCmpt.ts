import { res<PERSON>elper } from "../../../common/helper/ResHelper";
import { uiHelper } from "../../../common/helper/UIHelper";
import ConditionObj from "../../../model/common/ConditionObj";
import BaseTipsCmpt from "./BaseBubbleCmpt";

const { ccclass } = cc._decorator;
@ccclass
export default class GiftBubbleCmpt extends BaseTipsCmpt {

    public init(ary) {
        this.node.Items(ary, (it: cc.Node, data: ConditionObj) => {
            resHelper.loadIconByCondInfo(data, it.Child('icon'), this.uuid)
            it.Child("num", cc.Label).string = ut.simplifyMoney(data.num)
            uiHelper.regClickPropBubble(it, data, this)
        })
    }
}