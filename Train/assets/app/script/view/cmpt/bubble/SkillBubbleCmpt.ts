import { ConditionType, SkillType } from "../../../common/constant/Enums";
import { cfgHelper } from "../../../common/helper/CfgHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import BattleSkill from "../../../model/battle/BattleSkill";
import BaseBubbleCmpt from "./BaseBubbleCmpt";

const { ccclass } = cc._decorator;
@ccclass
export default class SkillBubbleCmpt extends BaseBubbleCmpt {

    private curSkill: BattleSkill = null
    private skills: BattleSkill[] = []

    public init(data) {
        let { id, skills } = data
        let cfg = cfgHelper.getCharacter(id)
        if (skills.length <= 0) {
            this.node.Swih('unSkill')
            let it = this.node.Child('unSkill')
            if (cfg?.battleSkill) {
                it.Child('lb', cc.Label).setLocaleKey('chapterEmbattle_guiText_2')
                it.width = 276
            } else {
                it.Child('lb', cc.Label).setLocaleKey('chapterEmbattle_guiText_1')
                it.width = 316
            }
            this.node.setContentSize(it.getContentSize())
        }
        else {
            let skillNode = this.node.Swih('skill')[0]
    
            this.skills = skills
    
            let maxHeight = 0
            let layout = skillNode.Child("desc")
            let cmpt = layout.Component("LayoutMinSize")
            let minSize = cmpt.minSize
            minSize.height = 60
            //保持统一高度
            for (let skill of skills) {
                this.updateDesc(skill)
                maxHeight = Math.max(maxHeight, layout.height)
            }
            minSize.height = maxHeight
            cmpt.minSize = minSize
    
            this.initSkills()

            this.node.setContentSize(skillNode.getContentSize())
        }
    }

    protected onDisable(): void {
        this.curSkill = null
        this.skills = null
        assetsMgr.releaseTempResByTag(this.uuid)
    }

    private initSkills() {
        let icons = this.node.Child("skill/icons")
        let skills = this.skills
        if (!this.curSkill) {
            this.curSkill = skills[0]
        }
        icons.Items(skills, (it, skill: BattleSkill)=>{
            it.Data = skill
            it.off("click")
            it.on("click", ()=>{
                this.onSelect(skill)
            })
            let icon = it.Child("icon")
            if (skill.getType() == SkillType.BATTLE) {
                icon.scale = 0.7
                resHelper.loadSkillIcon(skill, icon, this.uuid)
            }
            it.Child("select").active = this.curSkill == skill
        })
        this.updateDesc(this.curSkill)
    }

    private onSelect(skill: BattleSkill) {
        this.curSkill = skill
        this.initSkills()
    }

    private updateDesc(skill: BattleSkill) {
        let tips = this.node.Child('skill')
        let layout = tips.Child("desc")
        let descLb = layout.Child('desc', cc.RichText)
        descLb.setLocaleUpdate(()=>{
            return skill.getDescStr()
        })
        layout.Component(cc.Layout).updateLayout()

        tips.Component(cc.Layout).updateLayout()
    }

}