import IgnoreCmpt from "./IgnoreCmpt";

const { ccclass, property } = cc._decorator;

//正片叠底


@ccclass
export default class MulColorCmpt extends mc.BaseCmptCtrl {

    @property(cc.Color)
    private color: cc.Color = null

    protected opacity: number = 255
    protected orgColor: cc.Color = null

    protected isPause: boolean = false

    protected needRender: boolean = false

    public listenEventMaps() {
        return [
        ]
    }

    onCreate() {
        this.checkNeedRender()

        this.node.on(cc.Node.EventType.CHILD_ADDED, (node) => {
            this.addChildCmpt(node)
        })

        if (this.needRender) {
            let orgColor = this.node.color
            if (!orgColor.equals(cc.Color.WHITE)) {
                this.orgColor = orgColor.clone()
            }
            this.node.on(cc.Node.EventType.COLOR_CHANGED, () => {
                if (!orgColor.equals(cc.Color.WHITE)) {
                    this.orgColor = orgColor.clone()
                }
            })
        }
    }

    protected checkNeedRender() {
        if (this.Component(cc.RenderComponent)) this.needRender = true
    }

    public setColor(color) {
        this.color = color
    }

    public show(s) {
        if (s) {
            this.resume()
        }
        else {
            this.pause()
        }
    }

    public resume() {
        this.isPause = false
        let Cmpt = MulColorCmpt
        for (let node of this.node.children) {
            let cmpt = node.getComponent(Cmpt)
            cmpt && cmpt.resume()
        }
    }

    public pause() {
        let color = this.orgColor || cc.Color.WHITE
        this.updateColor(color.r, color.g, color.b)
        this.isPause = true
        let Cmpt = MulColorCmpt
        for (let node of this.node.children) {
            let cmpt = node.getComponent(Cmpt)
            cmpt && cmpt.pause()
        }
    }

    onEnter(): void {
        this.checkNeedRender()
        for (let child of this.node.children) {
            this.addChildCmpt(child)
        }
        this.update(0)
    }

    protected addChildCmpt(node) {
        if (this.getIgnoreCmpt()) return
        let Cmpt = this["__classname__"]
        if (!node.getComponent(Cmpt)) {
            node.addComponent(Cmpt)
        }
    }

    protected getIgnoreCmpt(): cc.Component {
        return this.getComponent(IgnoreCmpt)
    }

    public getColor() {
        if (!this.color) {
            this.color = this.node.parent?.getComponent(MulColorCmpt)?.getColor()
        }
        return this.color
    }

    update(dt) {
        if (!this.needRender || this.isPause) return
        this.opacity = this.getColor().a
        this.updateMulColor()
    }

    /*
    通过改变颜色来达到正片叠底的效果
    对于blend公式，有 srcColor * srcFactor + destColor * destFactor ，其中dest为当前节点，src为叠在上面的遮罩
    对于正片叠底，令srcFactor = destColor, destFactor = (1 - srcAlpha)，则有 srcColor * destColor + destColor * (1 - srcAlpha)
    提公因式，最终的渲染颜色为 destColor * (srcColor * srcAlpha + (1 - srcAlpha))
    即我们只要把当前节点的颜色设成 (srcColor * srcAlpha + (1 - srcAlpha))，节点的默认shader会自动乘上destColor
    */
    protected updateMulColor() {
        let color = this.getColor(), opacity = this.opacity

        let secondPart = (255 - opacity)
        let r = color.r * opacity / 255 + secondPart
        let g = color.g * opacity / 255 + secondPart
        let b = color.b * opacity / 255 + secondPart

        if (this.orgColor) { //如果节点本身有颜色，还有再乘一下
            r *= this.orgColor.r / 255
            g *= this.orgColor.g / 255
            b *= this.orgColor.b / 255
        }

        r = Math.round(r)
        g = Math.round(g)
        b = Math.round(b)

        this.updateColor(r, g, b)
    }

    protected updateColor(r: number, g: number, b: number) {
        let curColor = this.node["_color"]
        if (curColor.r == r && curColor.g == g && curColor.b) return
        cc.Color.set(curColor, r, g, b)
        this.node["_renderFlag"] |= cc["RenderFlow"]["FLAG_COLOR"]
    }
}
