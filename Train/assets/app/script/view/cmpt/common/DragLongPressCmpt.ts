import ButtonEx from "../../../../core/component/ButtonEx"

const { ccclass, property, menu } = cc._decorator

export enum DragLongEvent {
    DL_CLICK = "DL_CLICK",//单击
    LP_START = 'LP_START',// 长按开始
    LP_END = 'LP_END',// 长按结束
    LP_CANCEL = 'LP_CANCEL',// 长按取消
    DRAG_START = 'DRAG_START',
    DRAG_MOVE = 'DRAG_MOVE',
    DRAG_END = 'DRAG_END',
}

enum StateDragLong {
    None,
    Work,
    Long,
    Drag,
}

@ccclass
@menu('自定义组件/拖拽长按按钮')
export default class DragLongPressCmpt extends cc.Component {
    @property({ type: cc.Node, tooltip: CC_DEV && '拖动时挂载的节点' })
    public dragParent: cc.Node = null
    @property()
    public isInScroll: boolean = false
    @property({ type: cc.Node, tooltip: CC_DEV && '需要放大缩小的节点' })
    public zoomNode: cc.Node = null
    @property({ visible() { return this.zoomNode != null } })
    public zoomScale: number = 1.2
    @property({ visible() { return this.zoomNode != null } })
    public zoomDuration: number = 0.1
    @property()
    public longPressTime: number = 300
    private zoomOrgScale: cc.Vec2 = null

    private myState = StateDragLong.None

    //private longPressTime: number = 300
    private longPressStart: number = 0

    public dragOrgInfo: { pos: cc.Vec2, parent: cc.Node, siblingIndex: number, offset: cc.Vec2 } = null

    onLoad() {
        let touchNode = this.node
        touchNode.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this)
        touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        touchNode.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)
        touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
        this.zoomInit()
    }
    update() {
        this.longUpdate()
    }

    private onTouchStart(event: cc.Event.EventTouch) {
        this.myState = StateDragLong.Work
        this.longStart()
        this.dragStart(event.getLocation())
        this.zoomStart()
        this.playSound()
    }
    private onTouchMove(event: cc.Event.EventTouch) {
        this.dragMove(event)
    }
    private onTouchEnd(event: cc.Event.EventTouch) {
        let state = this.myState
        this.myState = StateDragLong.None
        if (state == StateDragLong.Work) {
            this.zoomEnd()
            this.node.emit(DragLongEvent.DL_CLICK, this)
        } else if (state == StateDragLong.Long) {
            this.longEnd()
        } else if (state == StateDragLong.Drag) {
            this.dragEnd(event)
        }
    }
    private onTouchCancel(event: cc.Event.EventTouch) {
        let state = this.myState
        this.myState = StateDragLong.None
        if (state == StateDragLong.Work) {
            this.zoomEnd()
        } else if (state == StateDragLong.Long) {
            this.longEnd(true)
        } else if (state == StateDragLong.Drag) {
            this.dragEnd(event)
        }
    }

    private longStart() {
        this.longPressStart = Date.now()
    }
    private longEnd(isCancel: boolean = false) {
        this.zoomEnd()
        if (this.isInScroll)
            this.dragReset()
        if (isCancel) {
            this.node.emit(DragLongEvent.LP_CANCEL, this)
        } else {
            this.node.emit(DragLongEvent.LP_END, this)
        }
    }
    private longCheckEnd() {
        if (this.myState != StateDragLong.Long) return
        this.node.emit(DragLongEvent.LP_END, this)
    }
    private longUpdate() {
        if (this.myState != StateDragLong.Work) return
        if (Date.now() - this.longPressStart < this.longPressTime) return
        this.myState = StateDragLong.Long
        this.node.emit(DragLongEvent.LP_START, this)
    }

    private dragStart(locPos: cc.Vec2) {
        let touchNode = this.node
        let curPos = touchNode.getPosition()
        this.dragOrgInfo = {
            pos: curPos,
            parent: touchNode.parent,
            siblingIndex: touchNode.getSiblingIndex(),
            offset: touchNode.parent.convertToNodeSpaceAR(locPos).sub(curPos)
        }
    }
    private dragReset() {
        let info = this.dragOrgInfo
        let touchNode = this.node
        touchNode.parent = info.parent
        touchNode.setPosition(info.pos)
        touchNode.setSiblingIndex(info.siblingIndex)
    }
    private dragEnd(event: cc.Event.EventTouch) {
        this.node.emit(DragLongEvent.DRAG_END, this, event.getLocation())
        this.dragReset()
    }
    private dragMove(event: cc.Event.EventTouch) {
        if (this.myState == StateDragLong.None) return
        if (this.myState == StateDragLong.Drag) {
            this.dragSetPos(event.getLocation())
            this.node.emit(DragLongEvent.DRAG_MOVE, this)
        } else if (this.dragCheck(event)) {
            if (this.isInScroll && (this.myState == StateDragLong.Work || this.myState == StateDragLong.Long)) {
                let startPos = event.getStartLocation()
                let position = event.getLocation()
                let vec = position.sub(startPos)
                if (vec.x != 0 && Math.abs(vec.y) / Math.abs(vec.x) < 0.25) {
                    this.onTouchCancel(event)
                    return
                }
            }
            this.longCheckEnd()
            this.myState = StateDragLong.Drag
            this.node.parent = this.dragParent
            this.dragSetPos(event.getLocation())
            this.zoomEnd()
            this.node.emit(DragLongEvent.DRAG_START, this)
        }
    }
    private dragCheck(event: cc.Event.EventTouch) {
        let startPos = event.getStartLocation()
        let position = event.getLocation()
        let vec = position.sub(startPos)
        let dis = vec.magSqr()
        return dis > 2500
    }
    private dragSetPos(worldPos: cc.Vec2) {
        let touchNode = this.node
        let localPos = touchNode.parent.convertToNodeSpaceAR(worldPos).sub(this.dragOrgInfo.offset)
        touchNode.setPosition(localPos)
    }

    private zoomInit() {
        let node = this.zoomNode
        if (!node) return
        this.zoomOrgScale = cc.v2(node.scaleX, node.scaleY)
    }
    private zoomStart() {
        let node = this.zoomNode
        if (!node) return
        let scaleX = this.zoomOrgScale.x * this.zoomScale
        let scaleY = this.zoomOrgScale.y * this.zoomScale
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to(this.zoomDuration, { scaleX, scaleY }).start()
    }
    private zoomEnd() {
        let node = this.zoomNode
        if (!node) return
        let scaleX = this.zoomOrgScale.x
        let scaleY = this.zoomOrgScale.y
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to((this.zoomDuration / 2), { scaleX, scaleY }).start()
    }

    private playSound() {
        audioMgr.playSFX(ButtonEx.DefaultClickPath, { volume: 1 })
    }
}
