/*
把source的视图渲染到renderTexture
*/

const { ccclass, property } = cc._decorator;

export enum UpdateMode {
    ONCE,
    ALWAYS
}

@ccclass
export default class FBOCmpt extends cc.Component {

    @property({ type: cc.Enum(UpdateMode), tooltip: CC_DEV && '更新模式' })
    public updateMode: UpdateMode = UpdateMode.ONCE;

    @property({ type: cc.Node, tooltip: CC_DEV && '观察节点' })
    public source: cc.Node = null;

    @property({ type: cc.Sprite, tooltip: CC_DEV && '渲染节点' })
    public target: cc.Sprite = null;

    protected _isPaused: boolean = false;
    protected _renderTexture: cc.RenderTexture = null;
    protected _fboCamera: cc.Camera = null;
    public group: string = "fbo"
    protected tempVec2: cc.Vec2 = cc.v2()
    protected inited: boolean = false

    public getRenderTexture() {
        return this._fboCamera?.targetTexture
    }

    /**
     * 初始化内部数据
     * @method _initData
     * @private
     */
    protected _initData() {
        this._isPaused = false;

        if (CC_DEV) {
            window["fbo"] = this
        }
    }
    /**
     * 初始化 RenderTexture 对象
     * @method _initFBORenderTexture
     * @private
     */
    protected _initFBORenderTexture() {
        if (!this._renderTexture) {
            this._renderTexture = new cc.RenderTexture();
            let viewSize = this.getViewSize()
            this._renderTexture.initWithSize(viewSize.width, viewSize.height, cc.RenderTexture.DepthStencilFormat.RB_FMT_S8);
        }
    }
    /**
     * 初始化 FBO 最终渲染目标对象 cc.Sprite
     * @method _inittarget
     * @private
     */
    protected _initTarget() {
        this.target = this.target || this.node.getComponent(cc.Sprite);
        if (!this.target) {
            return
        }
        let spf = this.target.spriteFrame
        if (!spf) {
            spf = new cc.SpriteFrame();
        }
        this.target.spriteFrame = spf;
        this.target.spriteFrame.setFlipY(true);
    }
    /**
     * 初始化 FBO 专用 cc.Camera
     * @method _initFBOCamera
     * @private
     */
    protected _initFBOCamera() {
        this._fboCamera = this.node.getComponent(cc.Camera);
        if (!this._fboCamera) {
            this._fboCamera = this.node.addComponent(cc.Camera);
        }
        this._fboCamera.depth = 10;
        this._fboCamera.backgroundColor = new cc.Color(0, 0, 0, 0)
        //debug
        // this._fboCamera.backgroundColor = new cc.Color(255, 0, 0, 255)
        this._fboCamera.clearFlags = cc.Camera.ClearFlags.COLOR;
        this._fboCamera.targetTexture = this._renderTexture;

        this._fboCamera.cullingMask = (1 << this.source.groupIndex);
        this._fboCamera.alignWithScreen = false;
        this.updateCameraOrthoSize()
        this._fboCamera.enabled = false;
    }


    protected updateCameraOrthoSize() {
        this._fboCamera.orthoSize = this.getViewSize().height * 0.5 * this.source.getWorldScale(this.tempVec2).y;
    }

    protected getViewSize() {
        return this.node.getContentSize()
    }

    public setSource(source: cc.Node) {
        this.source = source
        this.init()
    }

    /**
     * start 生命周期函数，初始化一些内部用对象，调用一次更新 FBO 函数 (UpdateMode.ONCE 模式仅此一次调用)
     * @method start
     */
    protected start() {
        if (this.inited) return
        this.setSource(this.source)
    }

    protected init() {
        if (!this.source) return
        this.inited = true
        this._initData();
        this._initFBORenderTexture();
        this._initTarget();
        this._initFBOCamera();
        this.updateFBO();
    }

    /**
     * 更新 RenderTexture 大小
     * @method _updateRenderTextureSize
     * @private
     */
    protected _updateRenderTextureSize(width, height) {
        if (!this._renderTexture) {
            return;
        }

        if (window.jsb) {
            // https://forum.cocos.org/t/cc-rendertexture-updatesize/99634
            this._renderTexture.destroy();
            this._renderTexture = new cc.RenderTexture();
            this._renderTexture.initWithSize(width, height, cc.RenderTexture.DepthStencilFormat.RB_FMT_S8);
        }
        else {
            // @ts-ignore
            this._renderTexture.updateSize(width, height);
        }
    }
    /**
     * update 生命周期函数，每帧调用，更新 FBO (UpdateMode.ALWAYS 模式每帧调用)
     * @method update
     */
    protected lateUpdate() {
        if (CC_EDITOR) {
            return;
        }
        if (this._isPaused) {
            return;
        }
        if (this.updateMode == UpdateMode.ALWAYS) {
            this.updateFBO();
        }
    }
    /**
     * 刷新一次 FBO 并将其渲染到 cc.Sprite 对象中
     * @method updateFBO
     */
    public updateFBO() {
        if (CC_EDITOR) {
            return;
        }
        if (!this.source || !this._fboCamera) return
        this._fboCamera.enabled = true;
        this.updateCameraOrthoSize()
        this._fboCamera.render(this.source);
        if (this.target) {
            this.target.spriteFrame.setTexture(this._fboCamera.targetTexture);
        }
        this._fboCamera.enabled = false;
    }
    /**
     * 暂停刷新
     * @method pause
     */
    public pause() {
        this._isPaused = true;
    }
    /**
     * 继续刷新
     * @method resume
     */
    public resume() {
        this._isPaused = false;
    }

    public capture(imageName?) {
        imageName = imageName || "fbo.png";
        let texture = this._fboCamera.targetTexture
        if (window.jsb) {
            // let width = texture.width;
            // let height = texture.height;
            // let data = texture.readPixels();
            // let picData = new Uint8Array(width * height * 4);
            // let rowBytes = width * 4;
            // for (let row = 0; row < height; row++) {
            //     let srow = height - 1 - row;
            //     let start = srow * width * 4;
            //     let reStart = row * width * 4;
            //     for (let i = 0; i < rowBytes; i++) {
            //         picData[reStart + i] = data[start + i];
            //     }
            // }
            // let filePath = jsb.fileUtils.getWritablePath() + imageName;
            // let success = jsb.saveImageData(picData, width, height, filePath)
            // if (success) {
            //     return filePath;
            // }
            // else {
            //     return null;
            // }
        }
        else {
            let width = texture.width;
            let height = texture.height;
            let canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            let ctx = canvas.getContext('2d');
            let data = texture.readPixels();
            let rowBytes = width * 4;
            for (let row = 0; row < height; row++) {
                let srow = height - 1 - row;
                let imageData = ctx.createImageData(width, 1);
                let start = srow * width * 4;
                for (let i = 0; i < rowBytes; i++) {
                    imageData.data[i] = data[start + i];
                }
                ctx.putImageData(imageData, 0, row);
            }

            this.showImage(canvas)
        }
    }

    showImage(canvas) {
        var dataURL = canvas.toDataURL("image/png");
        canvas.remove();
        var img = document.createElement("img");
        img.src = dataURL;

        // let a = document.createElement('a')
        // a.href = dataURL
        // a.download = "test.png"
        // document.body.appendChild(a)
        // a.click()
        // document.body.removeChild(a)

        let texture = new cc.Texture2D();
        texture.initWithElement(img);

        let spriteFrame = new cc.SpriteFrame();
        spriteFrame.setTexture(texture);

        let node = new cc.Node();
        let sprite = node.addComponent(cc.Sprite);
        sprite.spriteFrame = spriteFrame;

        node.zIndex = cc.macro.MAX_ZINDEX;
        node.parent = cc.director.getScene();
        // set position
        let width = cc.winSize.width;
        let height = cc.winSize.height;
        node.x = width / 2;
        node.y = height / 2;
        node.on(cc.Node.EventType.TOUCH_START, () => {
            node.parent = null;
            node.destroy();
        });
    }
}