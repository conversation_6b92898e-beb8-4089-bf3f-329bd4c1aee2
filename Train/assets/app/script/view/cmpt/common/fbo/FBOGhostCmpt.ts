/*
考虑到性能，绑定到source的子节点，同步source的大小，旋转，缩放，位移
*/

import FBOCmpt, { UpdateMode } from "./FBOCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FBOGhostCmpt extends FBOCmpt {

    public updateMode: UpdateMode = UpdateMode.ALWAYS;

    @property({ tooltip: CC_DEV && '视图中心的锚点' })
    public anchor: cc.Vec2 = cc.v2(0.5, 0.5);

    @property({ tooltip: CC_DEV && '额外需要扩大的大小 (处理类似骨骼动画boundingbox不正确的情况)' })
    public inflateSize: cc.Size = cc.size(0, 0);

    private _viewSize: cc.Size = cc.size(0, 0)

    onEnable() {
        if (!this.source) return
        this._registerNodeEvent();
    }

    _registerNodeEvent() {
        if (!this.source) return
        this.source.on(cc.Node.EventType.SIZE_CHANGED, this._updateSize, this);
        this.source.on(cc.Node.EventType.ANCHOR_CHANGED, this._updatePos, this);
    }

    _unregisterNodeEvent() {
        if (!this.source) return
        this.source.off(cc.Node.EventType.SIZE_CHANGED, this._updateSize, this);
        this.source.off(cc.Node.EventType.ANCHOR_CHANGED, this._updatePos, this);
    }

    _updateSize() {
        this.node.setContentSize(this.source.getContentSize())
        let viewSize = this.getViewSize()
        this._updateRenderTextureSize(viewSize.width, viewSize.height);
        if (this._fboCamera) {
            this._fboCamera.targetTexture = this._renderTexture;
            this.updateCameraOrthoSize()
        }
        this._updatePos()
    }

    protected getViewSize() {
        this._viewSize.width = this.node.width + this.inflateSize.width
        this._viewSize.height = this.node.height + this.inflateSize.height
        return this._viewSize
    }

    _updatePos() {
        let worldScale = this.source.getWorldScale(this.tempVec2)
        let viewSize = this.getViewSize()
        let offsetX = (this.anchor.x - this.node.anchorX) * viewSize.width * Math.abs(worldScale.x)
        let offsetY = (this.anchor.y - this.node.anchorY) * viewSize.height * worldScale.y
        this.node.setPosition(offsetX, offsetY)
    }

    public setSource(source: cc.Node): void {
        if (!source) {
            return;
        }
        this.onDisable()
        this.source = source
       
        this.onEnable()
        this._updateSize()
        this.init()
    }

    onDisable() {
        if (!this.source) return
        this._unregisterNodeEvent();
    }
}

