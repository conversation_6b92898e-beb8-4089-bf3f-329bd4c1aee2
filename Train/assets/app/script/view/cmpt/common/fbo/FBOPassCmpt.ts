/*
用途：
在所绑定的节点做完一次渲染后，以最终的渲染结果再做一次自定义渲染
用于处理需要与坐标强关联，但又不好拿到的shader，如骨骼动画，合图

使用:
绑定一个二次渲染的材质
代码中开启关闭使用 cmpt.enable = true/false

实现原理:
在所绑定节点下生成一个显示用的子节点，当前节点的渲染会被隐藏，由子节点代替
*/

import FBOGhostCmpt from "./FBOGhostCmpt";

const { ccclass, property, menu } = cc._decorator;

const GHOST_NAME = "FBO_GHOST"

function _getActualGroupIndex(node) {
    let groupIndex = node.groupIndex;
    if (groupIndex === 0 && node.parent) {
        groupIndex = _getActualGroupIndex(node.parent);
    }
    return groupIndex;
}

@ccclass
@menu("自定义组件/FBOPassCmpt")
export default class FBOPassCmpt extends cc.Component {

    @property({ type: cc.Material, tooltip: CC_DEV && '渲染用的材质' })
    protected material: cc.Material = null;

    @property({ tooltip: CC_DEV && '视图中心的锚点' })
    protected anchor: cc.Vec2 = cc.v2(0.5, 0.5);

    @property({ tooltip: CC_DEV && '额外需要扩大的大小 (处理类似骨骼动画boundingbox不正确的情况)' })
    protected inflateSize: cc.Size = cc.size(0, 0);

    private sourceGroup: string = null

    private prop: any = {}

    protected start() {
        let ghostNode = new cc.Node(GHOST_NAME)
        ghostNode.parent = this.node
        let sp = ghostNode.addComponent(cc.Sprite)
        let cmpt = ghostNode.addComponent(FBOGhostCmpt)
        cmpt.anchor = this.anchor
        cmpt.inflateSize = this.inflateSize

        if (this.node.scaleX < 0) {
            ghostNode.scaleX = -ghostNode.scaleX
        }

        this.sourceGroup = this.node.group
        ghostNode.groupIndex = _getActualGroupIndex(this.node) || cc.game["groupList"].indexOf("default2");
        this.node.group = cmpt.group;

        cmpt.setSource(this.node)

        this.setMaterial(this.material)
    }

    public setMaterial(material: cc.Material) {
        if (material) {
            let node = this.Child(GHOST_NAME)
            material = cc.MaterialVariant.create(material, node.Component(FBOGhostCmpt).target.Component(cc.Sprite))
            try {
                material.setProperty("isFlipY", 1)
            } catch (error) {
            }
            if (this.prop) {
                this.setProperty(this.prop)
            }
            node.Component(cc.Sprite).setMaterial(0, material)
            this.material = material
        }
    }

    public setProperty(prop) {
        this.prop = prop
        if (!this.material) return
        for (let key in prop) {
            this.material.setProperty(key, prop[key])
        }
    }

    protected onEnable() {
        let node = this.Child(GHOST_NAME)
        if (node) {
            node.active = true
            this.node.group = node.Component(FBOGhostCmpt).group
        }
    }

    protected onDisable() {
        let node = this.Child(GHOST_NAME)
        if (node) {
            node.active = false
            this.node.group = this.sourceGroup
        }
    }

}