/*
用于保持节点的位置，旋转，缩放，不受父节点影响
*/

const { ccclass, property } = cc._decorator;

export enum UpdateMode {
    ONCE,
    ALWAYS
}

@ccclass
export default class FixedNodeTransCmpt extends cc.Component {

    private orgScale: cc.Vec2 = null

    onLoad() {
        this.orgScale = cc.v2(this.node.scaleX, this.node.scaleY)
    }

    public updateFlipX() {
        if (!this.orgScale) return
        let node = this.node
        let worldScale = node.parent.getWorldScale(cc.v2())
        node.scaleX = this.orgScale.x * ut.normalizeNumber(worldScale.x)
    }

}