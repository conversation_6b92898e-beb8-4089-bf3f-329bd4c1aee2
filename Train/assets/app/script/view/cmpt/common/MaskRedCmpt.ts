import { res<PERSON><PERSON><PERSON> } from "../../../common/helper/ResHelper";
import { RedFunType, RedObject } from "./MaskRedObject";

const { ccclass, property, menu } = cc._decorator;

/**
 * 红点标签
 * 支持多个(有互斥关系的)红点放在一起
 * 如多个红点没有冲突关系，则绑多个组件即可
 */
@ccclass
@menu('自定义组件/MaskRedCmpt')
export default class MaskRedCmpt extends cc.Component {
    @property([RedObject])
    private redAry: RedObject[] = []
    private redParam: any = null
    private interval: number = 0
    private defaultReddot: cc.Node = null
    private redOpposite: (bol: boolean) => void = null

    init(param: any, cb?: (bol: boolean) => void) {
        if (!cc.isValid(this)) return
        this.redOpposite = cb
        this.redParam = param
        this.interval = 0
        this.initNodes()
        this.update()
    }
    onLoad() {
        this.initNodes()
        if (this.redAry.length == 0) {
            this.enabled = false
        }
    }
    update() {
        this.interval--
        if (this.interval <= 0) {
            this.interval = 5
            this.setOnce()
        }
    }
    private initNodes() {
        this.redAry.forEach(obj => {
            if (!obj.node) {
                obj.node = this.createRed()
            }
            obj.node.active = false
        })
    }
    private setOnce() {
        let show: cc.Node = null
        for (const obj of this.redAry) {
            let node = obj.node
            if (show) {
                if (show != node) {
                    node.active = false
                }
                continue
            }
            let call = RedFunType[obj.type]
            if (call) {
                let bol = call(this.redParam)
                node.active = bol
                this.redOpposite && this.redOpposite(!bol)
                if (bol) show = node
            } else {
                node.active = false
            }
        }
    }
    // 默认创建在右上角
    private createRed() {
        if (this.defaultReddot) return this.defaultReddot
        let parent = this.node
        let node = new cc.Node()
        let sp = node.addComponent(cc.Sprite)
        node.x = parent.width * (1 - parent.anchorX)
        node.y = parent.height * (1 - parent.anchorY)
        node.anchorX = 1
        node.anchorY = 1
        node.parent = parent
        resHelper.loadImageUrl(sp, 'hongdian')
        this.defaultReddot = node
        return node
    }
}
