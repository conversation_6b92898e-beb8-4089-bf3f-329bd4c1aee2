import ButtonEx from "../../../../core/component/ButtonEx";

const { ccclass, property } = cc._decorator;


export enum DragEvent {
    CLICK = "click",
    DRAG_START = 'drag-start',
    ON_DRAG = "on-drag",
    DRAG_END = 'drag-end',
}
@ccclass
export default class DragCmpt extends cc.Component {

    @property({ type: cc.Node, tooltip: CC_DEV && '拖动时挂载的节点，用来解决层级问题；默认当前节点的父节点' })
    public dragParent: cc.Node = null;

    public touchNode: cc.Node = null

    private startPos: cc.Vec2 = null
    private moveStarted: boolean = false
    private orgPos: cc.Vec2 = null
    private orgParent: cc.Node = null
    private orgSiblingIndex: number = 0
    private zoomOrgScale: cc.Vec2 = null
    private zoomScale: number = 1.2
    private zoomDuration: number = 0.1

    protected onLoad(): void {
        this.touchNode = this.node
    }

    protected onEnable(): void {
        this.touchNode.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.touchNode.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
        this.zoomInit()
    }

    protected onDisable(): void {
        this.off()
    }

    private off() {
        this.touchNode.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.touchNode.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.touchNode.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.touchNode.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
    }

    private onTouchStart(event: cc.Event.EventTouch) {
        let touchNode = this.touchNode
        this.orgPos = touchNode.getPosition()
        this.orgParent = touchNode.parent
        this.orgSiblingIndex = touchNode.getSiblingIndex()

        let position = event.getLocation()
        this.startPos = touchNode.convertToNodeSpaceAR(position) 
        this.zoomStart()
        this.playSound()
    }

    private onMoveStart(position: cc.Vec2) {
        let touchNode = this.touchNode
        let worldPos = position
        let dragParent = this.dragParent || this.node.parent
        touchNode.parent = dragParent
        // let worldPos = cc.find('Canvas/Main Camera').getComponent(cc.Camera).getScreenToWorldPoint(position);
        // touchNode.parent = cc.find("Canvas")
        // touchNode.zIndex = 10000
        let localPos = touchNode.parent.convertToNodeSpaceAR(worldPos)
        touchNode.setPosition(localPos)

        this.touchNode.emit(DragEvent.DRAG_START)
    }

    private onMove(position: cc.Vec2) {
        let touchNode = this.touchNode
        let worldPos = position
        let localPos = touchNode.parent.convertToNodeSpaceAR(worldPos)
        touchNode.setPosition(localPos)
        this.touchNode.emit(DragEvent.ON_DRAG)
    }

    private onTouchMove(event: cc.Event.EventTouch) {
        let position = event.getLocation()
        if (!this.moveStarted) {
            if (this.checkStartMove(position)) {
                this.moveStarted = true
                this.onMoveStart(position)
            }
        }
        else {
            this.onMove(position)
        }
    }

    private checkStartMove(position: cc.Vec2) {
        let touchNode = this.touchNode
        let localPos = touchNode.convertToNodeSpaceAR(position)
        let vec = localPos.sub(this.startPos)
        let dis = vec.magSqr()
        return dis > 2500
    }

    private onTouchEnd(event: cc.Event.EventTouch) {
        let moveStarted = this.moveStarted
        this.reset()
        if (!moveStarted) {
            this.touchNode.emit(DragEvent.CLICK)
        }
        else {
            this.touchNode.emit(DragEvent.DRAG_END, event.getLocation())
        }
        event.stopPropagation()
    }

    private onTouchCancel() {
        this.reset()
    }

    public reset(){
        this.moveStarted = false
        let touchNode = this.touchNode
        touchNode.parent = this.orgParent
        touchNode.setPosition(this.orgPos)
        touchNode.setSiblingIndex(this.orgSiblingIndex)
        this.zoomEnd()
    }

    public zoomInit() {
        let node = this.touchNode
        if (!node) return
        this.zoomOrgScale = cc.v2(node.scaleX, node.scaleY)
    }
    private zoomStart() {
        let node = this.touchNode
        if (!node) return
        let scaleX = this.zoomOrgScale.x * this.zoomScale
        let scaleY = this.zoomOrgScale.y * this.zoomScale
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to(this.zoomDuration, { scaleX, scaleY }).start()
    }
    private zoomEnd() {
        let node = this.touchNode
        if (!node) return
        let scaleX = this.zoomOrgScale.x
        let scaleY = this.zoomOrgScale.y
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to((this.zoomDuration / 2), { scaleX, scaleY }).start()
    }

    private playSound() {
        audioMgr.playSFX(ButtonEx.DefaultClickPath, { volume: 1 })
    }
}
