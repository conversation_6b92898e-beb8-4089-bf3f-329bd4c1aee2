import { LongPress } from '../../../common/constant/Enums';

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu('自定义组件/长按按钮')
export default class LongPressButton extends cc.Component {
    @property({ type: cc.Node, tooltip: CC_DEV && '需要放大缩小的节点(默认自己)' })
    public zoomNode: cc.Node = null
    @property()
    public zoomScale: number = 1.1;
    @property()
    public duration: number = 0.1;

    @property({
        type: cc.Integer,
        tooltip: CC_DEV && '长按触发时间'
    })
    public longPressTime: number = 300;

    @property({
        type: cc.Component.EventHandler,
        tooltip: CC_DEV && '按住状态下事件',
    })
    public pressOnEvents: cc.Component.EventHandler[] = [];

    @property({
        type: cc.Component.EventHandler,
        tooltip: CC_DEV && '长按开始事件',
    })
    public startEvents: cc.Component.EventHandler[] = [];

    @property({
        type: cc.Component.EventHandler,
        tooltip: CC_DEV && '长按结束事件',
    })
    public endEvents: cc.Component.EventHandler[] = [];

    @property({
        type: cc.Component.EventHandler,
        tooltip: CC_DEV && '长按取消事件',
    })
    public cancelEvents: cc.Component.EventHandler[] = [];

    private _pressed: boolean = false;
    private _pressTime: number = Date.now();
    private lpStar: boolean = false;

    private stopPropagation: boolean = false;
    private zoomOrgScale: cc.Vec2 = null


    protected onLoad(): void {
        this.node.on(cc.Node.EventType.TOUCH_START, this._TouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this._TouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this._TouchCancel, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this._TouchMove, this);
        let node = this.zoomNode || this.node
        this.zoomOrgScale = cc.v2(node.scaleX, node.scaleY)
    }

    public _TouchStart(event: cc.Event.EventTouch) {
        this._pressed = true;
        this._pressTime = Date.now();
        this.zoomStart()
    }


    public _TouchEnd(event: cc.Event.EventTouch) {
        this._pressed = false;
        let clickCustom = !!event;
        if (this.stopPropagation) clickCustom = false;
        if (this.lpStar) this.node.emit(LongPress.LPEND, this);
        else this.node.emit(LongPress.CLICK, this, clickCustom);
        if (this.stopPropagation) this.stopPropagation = false;
        this.lpStar = false;
        this.zoomEnd()
        if (!event) this.stopPropagation = true;
    }

    public _TouchCancel(event?: cc.Event.EventTouch) {
        if (!this._pressed) return;
        this._pressed = false;
        if (this.lpStar) this.node.emit(LongPress.LPEND, this);
        this.lpStar = false;
        this.zoomEnd()
    }

    public _TouchMove(event: cc.Event.EventTouch) {
        let hit = this.node._hitTest(event.touch.getLocation());
        if (this._pressed && !hit) this._TouchCancel();
    }

    private zoomStart() {
        let node = this.zoomNode || this.node
        let scaleX = this.zoomOrgScale.x * this.zoomScale
        let scaleY = this.zoomOrgScale.y * this.zoomScale
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to(this.duration, { scaleX, scaleY }).start()
    }

    private zoomEnd() {
        let node = this.zoomNode || this.node
        let scaleX = this.zoomOrgScale.x
        let scaleY = this.zoomOrgScale.y
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to((this.duration / 2), { scaleX, scaleY }).start()
    }

    protected update(dt: number): void {
        if (!this._pressed) return;
        if (Date.now() - this._pressTime >= this.longPressTime && !this.lpStar) {
            this.lpStar = true;
            this.node.emit(LongPress.LPSTART, this);
        }
    }
}