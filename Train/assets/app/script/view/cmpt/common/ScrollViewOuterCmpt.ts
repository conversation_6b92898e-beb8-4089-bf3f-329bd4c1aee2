import { CLICK_SPACE } from "../../../common/constant/Constant";
import ScrollViewInnerCmpt from "./ScrollViewInnerCmpt";

const { ccclass, property } = cc._decorator;

// 代替外部的ScrollView
@ccclass
export default class ScrollViewOuterCmpt extends cc.ScrollView {

    private planDir: number = -2
    private scrollingInnerSv: ScrollViewInnerCmpt = null

    private tempVec: cc.Vec2 = cc.v2()

    //是否为子物体
    //注意，这里递归, 如果child藏的太深, 可能影响效率。其实也还好，只是开始滑动时执行一次。
    private _findScrollingInnerSv(node: cc.Node) {
        let scrollViewInnerCmpt: ScrollViewInnerCmpt = null;
        let flag = false;
        while(node != null && !flag) {
            if(node == this.node) flag = true;
            if(node.getComponent(ScrollViewInnerCmpt)) 
            scrollViewInnerCmpt = node.getComponent(ScrollViewInnerCmpt);
            node = node.parent
        }
        return scrollViewInnerCmpt;
    }

    //判断Target是否是InnerScrollView的子物体, 如果是，就返回这个InnerScrollView。
    //注意，这里遍历所有InnerScrollView, 如果InnerScrollView数量太多，可能影响效率。其实也还好，只是开始滑动时执行一次。
    // private _findScrollingInnerSv(target: cc.Node) {
    //     // console.log("target: ", target);
    //     const node = this.Find(m => this._isHisChild(target));
    //     // console.log("node: ", node);
    //     return node && node.Child('list', ScrollViewInnerCmpt)
    // }

    //检查实际与计划方向的一致性
    private isDifferentBetweenSettingAndPlan(sv: cc.ScrollView) {
        if (this.planDir === 0) {
            return false
        }
        if (this.planDir === 1 && sv.horizontal) {
            return false
        }
        if (this.planDir === -1 && sv.vertical) {
            return false
        }
        return true
    }

    //#region 重写cc.ScrollView的方法
    hasNestedViewGroup(event: cc.Event.EventTouch, captureListeners: cc.Node[]) {
        if (event.eventPhase !== cc.Event.CAPTURING_PHASE) {
            return false
        }
        return false
    }

    _onTouchBegan(event: cc.Event.EventTouch, captureListeners: cc.Node[]) {
        if (!this.enabledInHierarchy || this.hasNestedViewGroup(event, captureListeners)) {
            return
        }
        // 重置计划方向
        this.planDir = -2
        this.scrollingInnerSv = null

        if (this.content) {
            // @ts-ignore
            this._handlePressLogic(event.touch)
        }
        // @ts-ignore
        this._touchMoved = false
        // @ts-ignore
        this._stopPropagationIfTargetIsMe(event)
    }

    _onTouchMoved(event: cc.Event.EventTouch, captureListeners: cc.Node[]) {
        if (!this.enabledInHierarchy || this.hasNestedViewGroup(event, captureListeners)) {
            return
        }
        // console.log("event: ", event);
        //在滑动时, 设置开始时滑动的方向为计划方向
        //为什么在Outer中做这件事？
        //因为Outer的_onTouchMoved比Inner的早执行, 如果在Inner里做, Outer中就得忽略一帧，体验可能会不好。
        const deltaMove = event.getLocation().sub(event.getStartLocation(), this.tempVec)
        if (this.planDir === -2 && deltaMove.mag() > CLICK_SPACE) {
            this.scrollingInnerSv = this._findScrollingInnerSv(event.target)
            if (this.scrollingInnerSv) {
                const contentSize = this.scrollingInnerSv.content.getContentSize()
                const scrollViewSize = this.scrollingInnerSv.node.getContentSize()
                if ((this.scrollingInnerSv.vertical && (contentSize.height > scrollViewSize.height)) || (this.scrollingInnerSv.horizontal && (contentSize.width > scrollViewSize.width))) {
                    this.planDir = Math.abs(deltaMove.x) > Math.abs(deltaMove.y) ? 1 : -1
                } else {
                    this.planDir = 0
                }
            } else {
                this.planDir = 0
            }
        }
        if (this.content && !this.isDifferentBetweenSettingAndPlan(this)) {
            // @ts-ignore
            this._handleMoveLogic(event.touch)
        }
        if (!this.cancelInnerEvents) {
            return
        }
        //只取消会捕获事件的直接子物体(如Button)上的事件
        if (!this.scrollingInnerSv) {
            if (deltaMove.mag() > CLICK_SPACE) {
                // @ts-ignore
                if (!this._touchMoved && event.target !== this.node) {
                    const cancelEvent = new cc.Event.EventTouch(event.getTouches(), event.bubbles)
                    cancelEvent.type = cc.Node.EventType.TOUCH_CANCEL
                    cancelEvent.touch = event.touch
                    // @ts-ignore
                    cancelEvent.simulate = true
                    event.target.dispatchEvent(cancelEvent)
                    // @ts-ignore
                    this._touchMoved = true
                }
            }
            // @ts-ignore
            this._stopPropagationIfTargetIsMe(event)
        }
    }
}