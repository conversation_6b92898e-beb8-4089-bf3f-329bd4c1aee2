import { CLICK_SPACE } from "../../../common/constant/Constant";
import { gameHelper } from "../../../common/helper/GameHelper";

const { ccclass, property } = cc._decorator;

/**
 * 用于点击地图上面的
 */
@ccclass
export default class ClickTouchCmpt extends cc.Component {

    private isPlayAction: boolean = false //是否播放动画
    private isDown: boolean = false// 是否按下
    private _target: cc.Node = null
    private _node: cc.Node = null
    private _originalScale: number = undefined
    private _interactable: boolean = true

    private downCallback: Function = null
    private clickCallback: Function = null
    private clickTarget: any = null
    private data: any = null

    private tempVec: cc.Vec2 = cc.v2()

    onDestroy() {
        this.clean()
    }

    public on(callback: Function, target?: any) {
        this.off()
        this.clickCallback = callback
        this.clickTarget = target
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
        this.node.SetSwallowTouches(false)
        return this
    }

    public off() {
        this.clean()
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this)
        this.node.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this)
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this)
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this)
    }

    private clean() {
        this.downCallback = null
        this.clickCallback = null
        this.clickTarget = null
        this._node = null
        this._target = null
        this.data = null
        if (this.isDown) {
            this.isDown = false
            gameHelper.clickTouchId = -1
        }
    }

    public setPlayAction(val: boolean) {
        this.isPlayAction = val
        return this
    }

    public setTarget(target: cc.Node) {
        this._target = target
        return this
    }

    public setNode(node: cc.Node) {
        this._node = node
        return this
    }

    public setData(data: any) {
        this.data = data
        return this
    }

    public setDownCallback(val: Function) {
        this.downCallback = val
        return this
    }

    private get target() {
        if (!this._target) {
            this._target = this.node
        }
        return this._target
    }

    private getNode() {
        if (!this._node) {
            this._node = this.node
        }
        return this._node
    }

    private get originalScale() {
        if (this._originalScale === undefined) {
            this._originalScale = this.node.scale
        }
        return this._originalScale
    }

    public get interactable() {
        return this._interactable
    }
    public set interactable(val: boolean) {
        this._interactable = val
    }

    // 触摸开始
    private onTouchStart(event: cc.Event.EventTouch) {
        if (!this.interactable || gameHelper.clickTouchId !== -1) {
            return
        }
        gameHelper.clickTouchId = event.getID()
        this.down()
    }

    // 触摸移动
    private onTouchMove(event: cc.Event.EventTouch) {
        if (!this.interactable || gameHelper.clickTouchId !== event.getID()) {
            return
        }
        const hit = this.node._hitTest(event.getLocation())
        if (hit) {
            if (!this.isDown) {
                this.down()
            }
        } else if (this.isDown) {
            this.restore()
        }
    }

    // 触摸结束
    private onTouchEnd(event: cc.Event.EventTouch) {
        if (gameHelper.clickTouchId !== event.getID()) {
            return
        }
        gameHelper.clickTouchId = -1
        this.restore()
        if (this.interactable) {
            const location = event.getLocation()
            const startPos = this.screenToWorld(event.getStartLocation())
            const nowPos = this.screenToWorld(location)

            if (startPos.sub(nowPos).magSqr() <= CLICK_SPACE * CLICK_SPACE) {
                this.emit(location)
            }
        }
    }

    // 触摸取消
    private onTouchCancel(event: cc.Event.EventTouch) {
        if (gameHelper.clickTouchId !== event.getID()) {
            return
        }
        gameHelper.clickTouchId = -1
        this.restore()
    }

    // 按下
    private down() {
        this.isDown = true
        if (this.downCallback) {
            this.downCallback(true)
        } else if (this.isPlayAction) {
            cc.tween(this.target).to(0.1, { scale: this.originalScale * 1.05 }).start()
        }
    }

    // 还原
    private restore() {
        this.isDown = false
        if (this.downCallback) {
            this.downCallback(false)
        } else if (this.isPlayAction) {
            cc.tween(this.target).to(0.1, { scale: this.originalScale }).start()
        }
    }

    private emit(location: cc.Vec2) {
        const pos = this.screenToWorld(location)
        if (this.clickTarget) {
            this.clickCallback.call(this.clickTarget, this.getNode(), this.data, pos, location)
        } else {
            this.clickCallback(this.getNode(), this.data, pos, location)
        }
    }

    private screenToWorld(location) {
        return cc.Camera.findCamera(this.node).getScreenToWorldPoint(location)
    }
}
