const { ccclass, property, requireComponent, menu } = cc._decorator;

@ccclass
@requireComponent(cc.Button)
@menu('自定义组件/PolygonButton')
export default class PolygonButton extends cc.Component {
    @property(cc.String)
    private cameraName: string = ''

    onLoad() {
        this.rewriteHit()
        this.initPoints()
    }

    private rewriteHit() {
        let node = this.node
        node["_oldHit"] = node._hitTest
        node._hitTest = (point, listener) => {
            let hit = node["_oldHit"](point, listener)
            if (hit) {
                hit = this.isHitPolygon(point)
            }
            return hit
        }
    }

    private isHitPolygon(point) {
        let world = this.screenToWorld(point)
        let touch = this.node.convertToNodeSpaceAR(world)
        let nodePos = cc.v2(touch.x, touch.y)
        let children = this.node.children
        for (const item of children) {
            if (item.active && cc.Intersection.pointInPolygon(nodePos, item.getComponent(cc.PolygonCollider).points)) {
                return true
            }
        }
        return false
    }

    private screenToWorld(location) {
        if (!this.cameraName) return location
        return cc.find(`Canvas/${this.cameraName}`).Component(cc.Camera).getScreenToWorldPoint(location)
    }

    private initPoints() {
        for (const item of this.node.children) {
            this.initOnePoints(item)
        }
    }

    private initOnePoints(item: cc.Node) {
        let x = item.x, y = item.y
        if (x == 0 && y == 0) return
        let ary = item.getComponent(cc.PolygonCollider).points
        ary.forEach(pos => {
            pos.x += x
            pos.y += y
        });
    }
}