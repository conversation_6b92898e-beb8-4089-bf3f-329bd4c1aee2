const { ccclass, property } = cc._decorator;

@ccclass
export default class RotateAroundCmpt extends cc.Component {

    @property(cc.Node)
    public target: cc.Node = null

    @property
    public speed: number = 100

    @property
    public radis: number = 100

    @property
    public rotate: boolean = true

    @property
    public offsetRotate: number = 0

    @property
    public angle: number = 0

    private _centerPos: cc.Vec2 = null
    private get centerPos(): cc.Vec2 {
        if (this._centerPos) return this._centerPos
        if (!this.target) return this.zero
        this._centerPos = ut.convertToNodeAR(this.target, this.node.parent)
        return this._centerPos
    }

    private zero: cc.Vec2 = cc.v2(0, 0)
    private tempPos1: cc.Vec2 = cc.v2()

    update(dt) {
        this.angle += dt * this.speed
        this.updatePos()
        if (this.rotate) {
            this.updateRotate()
        }
    }

    private updatePos() {
        let targetPos = this.centerPos
        let x = targetPos.x + this.radis * ut.cos(this.angle)  //x = a + rcos(theat)
        let y = targetPos.y + this.radis * ut.sin(this.angle)  //y = b + rsin(theat)
        this.node.setPosition(x, y)
    }

    private updateRotate() {
        this.node.angle = this.getRotateByPos(this.getPosition(this.tempPos1))
    }

    public getAngleByPos(pos: cc.Vec2) {
        let targetPos = this.centerPos
        let angle = ut.getAngle(targetPos, pos)
        return angle
    }

    public getRotateByPos(pos: cc.Vec2) {
        let targetPos = this.centerPos
        let vec = pos.sub(targetPos, this.tempPos1)
        vec.rotateSelf(Math.PI * 0.5 * ut.normalizeNumber(this.speed)) //逆时针+，顺时针-
        let angle = vec.xAngle() + this.offsetRotate
        return angle
    }

    public resetCenter() {
        this._centerPos = null      
    }
}