import { CLICK_SPACE } from "../../../common/constant/Constant";
import ScrollViewOuterCmpt from "./ScrollViewOuterCmpt";

const { ccclass, property } = cc._decorator;

// 代替内部的ScrollView
@ccclass
export default class ScrollViewInnerCmpt extends cc.ScrollView {

    @property(ScrollViewOuterCmpt)
    public outer: ScrollViewOuterCmpt = null

    private tempVec: cc.Vec2 = cc.v2()

    _onTouchMoved(event: cc.Event.EventTouch, captureListeners: cc.Node[]) {
        // @ts-ignore
        if (!this.enabledInHierarchy || this.hasNestedViewGroup(event, captureListeners)) {
            return
        }
        // @ts-ignore
        if (this.content && !this.outer.isDifferentBetweenSettingAndPlan(this)) {
            // @ts-ignore
            this._handleMoveLogic(event.touch)
        }
        if (!this.cancelInnerEvents) {
            return
        }
        if (event.getLocation().sub(event.getStartLocation(), this.tempVec).mag() > CLICK_SPACE) {
            // @ts-ignore
            if (!this._touchMoved && event.target !== this.node) {
                const cancelEvent = new cc.Event.EventTouch(event.getTouches(), event.bubbles)
                cancelEvent.type = cc.Node.EventType.TOUCH_CANCEL
                cancelEvent.touch = event.touch
                // @ts-ignore
                cancelEvent.simulate = true
                event.target.dispatchEvent(cancelEvent)
                // @ts-ignore
                this._touchMoved = true
            }
        }
        // @ts-ignore
        this._stopPropagationIfTargetIsMe(event)
    }
}