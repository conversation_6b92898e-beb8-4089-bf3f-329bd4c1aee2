const { ccclass, property, requireComponent, executeInEditMode } = cc._decorator;

@ccclass
@requireComponent(cc.Label)
@executeInEditMode
export default class LabelOverlay extends cc.Component {

    @property
    private _count: number = 0

    @property
    get count() {
        return this._count
    }
    set count(val) {
        this._count = val
        this.updateItems()
    }

    protected start(): void {
        this.updateItems()
    }

    public updateItems() {
        this.node.removeAndDestroyAllChildren()
        let tmp = cc.instantiate(this.node)
        tmp.name = "overlay"
        tmp.setPosition(0, 0)
        for (let i = 0; i < this.count; i++) {
            let node = cc.instantiate(tmp)
            node.removeComponent(LabelOverlay)
            node.parent = this.node
        }
        tmp.destroy()
    }
}
