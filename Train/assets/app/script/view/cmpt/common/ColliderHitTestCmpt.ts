const { ccclass, property } = cc._decorator;

// _hitTest temp var
let _htVec3a = cc.v3();
let _htVec3b = cc.v3();
let _mat4_temp = cc.mat4();

@ccclass
export default class ColliderHitTestCmpt extends cc.Component {

    @property(cc.Node)
    private target: cc.Node = null

    @property(cc.PolygonCollider)
    private collider: cc.PolygonCollider = null

    onLoad() {
        this.collider = this.collider || this.Component(cc.PolygonCollider)
        this.target = this.target || this.node

        let hitTest = this.target._hitTest
        let self = this
        if (this.collider) {
            this.target._hitTest = function (point, listener) {
                let succ = hitTest.call(this, point, listener)
                if (succ) {
                    let cameraPt = _htVec3a, testPt = _htVec3b;
    
                    let camera = cc.Camera.findCamera(this)
                    if (camera) {
                        camera.getScreenToWorldPoint(point, cameraPt);
                    }
                    else {
                        cameraPt.set(point as cc.Vec3);
                    }
    
                    this._updateWorldMatrix();
                    // If scale is 0, it can't be hit.
                    if (!cc.Mat4.invert(_mat4_temp, this._worldMatrix)) {
                        return false;
                    }
                    cc.Vec2.transformMat4(testPt, cameraPt, _mat4_temp);
                    succ = self.hitTest(testPt)
                }
                return succ
            }
        }
    }

    private hitTest(pos) {
        let points = this.collider.points.map(p => p.add(this.collider.offset))
        return cc.Intersection.pointInPolygon(pos, points)
    }

}