const { ccclass, property } = cc._decorator;
/**
 * 节点的scale随摄像机的zoomRatio变化
 */
@ccclass
export default class ScaleByCamera extends cc.Component {
    private orgScale: number = 1
    private curRatio: number = 100
    private myCamera: cc.Camera = null

    protected onLoad(): void {
        this.orgScale = this.node.scale
        this.setScale()
    }
    protected update(dt: number): void {
        this.setScale()
    }
    private setScale() {
        let ratio = this.getRatio()
        if (ratio == 0) return
        if (this.fuzzyEquals(ratio)) return
        if (ratio >= 1) {
            this.node.scale = this.orgScale
        } else {
            this.node.scale = this.orgScale / ratio
        }
    }
    private getRatio() {
        if (!this.myCamera) {
            let camera = cc.Camera.findCamera(this.node)
            if (!camera) return 0
            this.myCamera = camera
        }
        return this.myCamera.zoomRatio
    }
    private fuzzyEquals(ratio: number) {
        let a = Math.floor(ratio * 100)
        if (a == this.curRatio) return true
        this.curRatio = a
        return false
    }
}
