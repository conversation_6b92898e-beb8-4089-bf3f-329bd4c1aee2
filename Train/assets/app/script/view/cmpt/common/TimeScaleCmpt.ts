import IgnoreCmpt from "./IgnoreCmpt";

const { ccclass, property, executionOrder } = cc._decorator;

@ccclass
export default class TimeScaleCmpt extends cc.Component {

    public name = "TimeScaleCmpt"

    private data: { scale: number } = null

    private cmptCount: number = 0
    private animCount: number = 0
    private animCmpt: cc.Animation = null

    private hasWarp: WeakSet<any> = new WeakSet()

    onLoad() {
        this.node.on(cc.Node.EventType.CHILD_ADDED, (node) => {
            if (!cc.isValid(this)) return
            this.addChildCmpt(node)
        })
        this.node._onAddAction = (action) => {
            if (!cc.isValid(this)) return
            this.warpUpdate(action)
        }
        this.update(0)
    }

    public setScale(scale: number, recur = false) {
        if (!this.data) {
            this.data = { scale: scale }
        }
        this.data.scale = scale

        if (recur) {
            this.setData(this.data, recur)
        }

        this.updateJsb(true)
    }

    public setData(data, recur) {
        this.data = data
        if (recur) {
            for (let child of this.node.children) {
                let cmpt = child.getComponent(TimeScaleCmpt)
                if (cmpt) {
                    cmpt.setData(data, recur)
                }
            }
        }
    }

    public updateJsb(recur) {
        if (!CC_JSB) return
        let curCmpts = this.getCmpts()
        for (let cmpt of curCmpts) {
            if (cmpt instanceof sp.Skeleton) {
                //@ts-ignore
                cmpt.setAccTimeScale(this.getData().scale)
                break
            }
        }
        if (recur) {
            for (let child of this.node.children) {
                let cmpt = child.getComponent(TimeScaleCmpt)
                if (cmpt) {
                    cmpt.updateJsb(recur)
                }
            }
        }
    }

    public getData() {
        if (!this.data) {
            this.data = this.node.parent?.getComponent(TimeScaleCmpt)?.getData()
        }
        return this.data
    }

    private getCmpts() {
        return this.node["_components"]
    }

    private getAnimStates() {
        if (!this.animCmpt) return
        //@ts-ignore
        let anims = this.animCmpt?._animator?._anims?.array
        return anims
    }

    protected onEnable(): void {
        for (let child of this.node.children) {
            this.addChildCmpt(child)
        }
    }

    protected addChildCmpt(node) {
        if (node.getComponent(IgnoreCmpt)?.isIgnore(this)) return
        if (!node.getComponent(TimeScaleCmpt)) {
            node.addComponent(TimeScaleCmpt)
        }
    }

    private warpUpdate(obj) {
        if (this.hasWarp.has(obj)) return
        if (obj === this) return

        let names = []
        if (cc["CallbackTimer"] && obj instanceof cc["CallbackTimer"]) {
            names = ["update"]
        }
        else {
            this.hasWarp.add(obj)
            if (obj instanceof cc.Component) {
                names = ["update", "lateUpdate"]
                obj._onAddAction = (action) => {
                    if (!cc.isValid(this)) return
                    this.warpUpdate(action)
                }
                obj._onSchedule = (timer) => {
                    if (!cc.isValid(this)) return
                    this.warpUpdate(timer)
                }
            }
            else if (obj instanceof cc.ActionInterval) {
                names = ["step"]
            }
            else if (obj instanceof cc.AnimationState) {
                names = ["update"]
            }
        }

        for (let name of names) {
            let orgFunc = obj[name]
            if (!orgFunc) {
                continue
            }
            obj[name] = (dt) => {
                if (cc.isValid(this)) {
                    let data = this.getData()
                    if (data) {
                        dt *= data.scale
                    }
                }
                orgFunc.call(obj, dt)
            }
        }
    }

    private checkCmpts() {
        let curCmpts = this.getCmpts()
        if (this.cmptCount != curCmpts.length) {
            for (let cmpt of curCmpts) {
                if (!this.animCmpt) {
                    if (cmpt instanceof cc.Animation) {
                        this.animCmpt = cmpt
                    }
                }
                this.warpUpdate(cmpt)
            }
            this.cmptCount = curCmpts.length
        }
    }

    private checkAnimation() {
        let curAnimStates = this.getAnimStates()
        if (!curAnimStates) return
        if (this.animCount != curAnimStates.length) {
            for (let animState of curAnimStates) {
                this.warpUpdate(animState)
            }
            this.animCount = curAnimStates.length
        }
    }

    update(dt) {
        this.checkCmpts()
        this.checkAnimation()
    }

    protected onDestroy() {
        if (this.node?.children) {
            for (let child of this.node.children) {
                if (cc.isValid(child, true)) {
                    child.removeComponent(TimeScaleCmpt)
                }
            }
        }
    }
}
