import { loader } from "../../../../../core/utils/ResLoader";

const {ccclass, property} = cc._decorator;

@ccclass
export default class BaseResLodCmpt extends mc.BaseCmptCtrl {

    @property({
        tooltip: "最大尺寸等级",
    })
    maxLevel: number = 3;

    path: string = '';
    orgData: any = null

    lastPath: string = ''
    orgPath: string = ''
    isFirstData: boolean = true

    initData (orgData: any) {
        let assInfo = loader.getAssetInfo(orgData._uuid)

        let path = assInfo.path
        this.path = path.substring(0, assInfo.path.length - 4)
        this.lastPath = path
        this.orgPath = path
        this.orgData = orgData
    }

    public initPath (path: string) {
        this.path = path
        this.lastPath = this.path
        this.orgPath = this.path
    }

    protected onDestroy(): void {
        if (this.lastPath ) {
            this.onRelease(this.lastPath)
            this.lastPath = null
        }
    }

    @ut.addLock
    async checkZoom() {
        let mainCamera = cc.find("Canvas/Main Camera")
        if (mainCamera) {
            let camera = mainCamera.getComponent(cc.Camera)
            if (camera) {
                let finalPath = this.path
                const zoomRatio = camera.zoomRatio
                const canvasSize = cc.view.getCanvasSize()
                const width = canvasSize.width * zoomRatio
                const rsWidth = cc.view.getDesignResolutionSize().width
                if (width > 0.9 * rsWidth && this.maxLevel >= 4) {
                    //保持原名
                }
                else if (width > 0.6 * rsWidth && this.maxLevel >= 3) {
                    finalPath += "_s70"
                }
                else if (width > 0.3 * rsWidth && this.maxLevel >= 2) {
                    finalPath += "_s40"
                }
                else if (width > 0.15 * rsWidth && this.maxLevel >= 1) {
                    finalPath += "_s20"
                }
                else {
                    finalPath += "_s10"
                }
                if (this.lastPath === finalPath) return
                await this.onChangePath(finalPath)
            }
        } 
    }

    protected async onChangePath(finalPath) {
        let releasePath = this.lastPath
        if (this.isFirstData) {
            releasePath = null
            this.isFirstData = false
        }

        this.lastPath = finalPath

        let data = this.orgData
 
        if (finalPath !== this.orgPath) {
            this.onUpdateRes(this.orgData) //暂时用初始资源代替
            this.onRelease(releasePath)   //先释放当前的资源
            releasePath = null

            data = await this.loadRes(finalPath)
        }

        if (data) {
            this.onRelease(releasePath)

            if (this.isValid && this.lastPath === finalPath) {
                this.onUpdateRes(data)
            }
            else {
                this.onRelease(finalPath)
            }
        }
        else {
            console.warn("res not find", finalPath)

            if (this.maxLevel > 0) {
                this.lastPath = ''
                if (this.orgData) {
                    this.onUpdateRes(this.orgData)
                }
                this.maxLevel--
            }
        }
    }

    protected onRelease(path: string) {
        if (!path) return
        if (path == this.orgPath) return //不释放静态依赖的资源
        assetsMgr.releaseTempRes(path, this.getTag())
        twlog.info("onRelease", path)
    }

    protected async loadRes(finalPath:string) : Promise<cc.Asset> {
        return null
    }

    protected onUpdateRes(data: any) {
        
    }

    update (dt) {
        this.checkZoom()
    }
}
