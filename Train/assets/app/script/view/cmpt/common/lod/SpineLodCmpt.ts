
import BaseResLodCmpt from "./BaseResLodCmpt";
const { ccclass, property, requireComponent } = cc._decorator;

@ccclass
@requireComponent(sp.Skeleton)
export default class SpineLodCmpt extends BaseResLodCmpt {

    sk: sp.Skeleton = null

    private curUid: string = ''

    onLoad() {
        this.sk = this.getComponent(sp.Skeleton)
        let data = this.sk.skeletonData
        if (data) {
            this.initData(data)
            this.curUid = data["_uuid"]
        }
    }

    protected async loadRes(finalPath: string) {
        if (finalPath == this.orgPath) return this.orgData
        return await assetsMgr.loadTempRes(finalPath, sp.SkeletonData, this.getTag())
    }

    protected onUpdateRes(data: sp.SkeletonData) {
        if (this.curUid === data["_uuid"]) {
            return
        }

        //@ts-ignore
        let atlas = data._getAtlas();
        if (!atlas) {
            return
        }
    
        //todo? 现在是直接替换skeleton._skeleton.data上的数据，没有改skeletonData（不能部分修改，因为被多个skeleton引用；修改引用会触发状态重置，得改源码）
        //可能的隐患：重新触发skeletonData初始化的话，贴图数据会被重置回去；获取skeletonData上的属性会和当前显示的不符

        //替换skletonData数据
        // curData.atlasText = data.atlasText
        // let oldTextures = curData.textures
        // let newTextures = data.textures
        // curData.textures = newTextures
        // for (let texture of newTextures) {
        //     texture.addRef()
        // }
        // for (let texture of oldTextures) {
        //     texture.decRef()
        // }
        //@ts-ignore
        // curData.textureNames = data.textureNames
        // console.log("load", data["_uuid"])

        //替换attachment数据
        let attachments = this.getChangeAttachments(this.sk)
        for (let attachment of attachments) {
            let region = atlas.findRegion(attachment.region["name"])
            region.renderObject = region
            attachment["setRegion"](region)
        }
        this.curUid = data["_uuid"]
    }

    private getChangeAttachments(sk: sp.Skeleton) {
        let results = []
        let skins = sk["_skeleton"].data.skins
        for (let skin of skins) {
            let attachments = skin.attachments
            for (let i = 0; i < attachments.length; i++) {
                let slotAttachments = attachments[i];
                if (slotAttachments) {
                    for (let name_4 in slotAttachments) {
                        let attachment = slotAttachments[name_4];
                        if (!attachment) continue
                        if (attachment instanceof sp.spine.RegionAttachment || attachment instanceof sp.spine.MeshAttachment) {
                            results.push(attachment)
                        }
                    }
                }
            }
        }
        return results
    }
}
