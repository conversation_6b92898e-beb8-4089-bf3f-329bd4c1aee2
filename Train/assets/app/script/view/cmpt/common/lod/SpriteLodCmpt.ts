import BaseResLodCmpt from "./BaseResLodCmpt";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SpriteLodCmpt extends BaseResLodCmpt {
    editor: {
        requireComponent: cc.Sprite
    }

    sp: cc.Sprite = null

    onLoad () {
        this.sp = this.getComponent(cc.Sprite)
        if (this.sp.spriteFrame) {
            this.initData(this.sp.spriteFrame)
        }
    }

    protected async loadRes(finalPath:string) {
        if (finalPath == this.orgPath) return this.orgData
        return await assetsMgr.loadTempRes(finalPath, cc.SpriteFrame, this.getTag())
    }

    protected onUpdateRes(data: any) {
        this.sp.spriteFrame = data
    }
}
