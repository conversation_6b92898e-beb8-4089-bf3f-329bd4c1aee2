import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetMineModel from "../../../../model/planet/PlanetMineModel";
import EternalGardenWindCtrl from "../../../eternalGarden/EternalGardenWindCtrl";
import HeroCmpt from "../../hero/HeroCmpt";
import PlanetMineCmpt from "../PlanetMineCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

const MAX_HP = 100
const MINE_COUNT = 5
class Grass extends PlanetMineModel {

    public hp: number = MINE_COUNT
    public maxHp: number = MINE_COUNT

    public isLast() { return false }

    public checkShow() { return true}
    public isPassContorl() {return true}

    public hit(damage, damageMul: number = 1) {
        super.hit(1)
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        return true
    }
}

class GrassCmpt extends PlanetMineCmpt  {

    public init(model: PlanetMineModel) {
        this.model = model
        this.curHp = this.model.hp
        this.initView()
    }

    protected async initView() {
        this.ui.active = false
        this.info.string = "???"
    }

    protected updateHpView(dt) {
        let lerpRatio = 10 * dt
        let targetRatio = 0
        if (this.model) {
            let hp = this.model.hp + (MAX_HP - MINE_COUNT)
            let maxHp = MAX_HP
            targetRatio = hp / maxHp
        }
        let shadow = this.hpBar.Child('shadow').Component(cc.Sprite)
        if (Math.abs(targetRatio - shadow.fillRange) < 0.01) {
            lerpRatio = 1
        }
        shadow.fillRange = cc.misc.lerp(shadow.fillRange, targetRatio, cc.misc.clamp01(lerpRatio))
   
        this.hp.fillRange = targetRatio
    }

    protected onDeath() {
        this.ui.active = false
        this.model = null
    }
}
@ccclass
export default class AwakeGardenBossCmpt extends PlanetNodeCmpt  {

    @property(sp.Skeleton)
    private anim: sp.Skeleton = null

    @property(cc.Node)
    private reachNode: cc.Node = null

    @property(cc.Node)
    private mineNode: cc.Node = null

    public model: PlanetEmptyNode = null
    private actionTree: ActionTree = null
    private mine: Grass = null

    @property([cc.Node])
    private pathPoints: cc.Node[] = []

    @property([cc.Node])
    private cameraPathPoints: cc.Node[] = []

    private planetCtrl: EternalGardenWindCtrl = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    init(model, planetCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.actionTree = new ActionTree().init(this)
        this.mine = new Grass()

        let cmpt = this.mineNode.addComponent(GrassCmpt)
        cmpt.init(this.mine)
        this.anim.node.active = false

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return
        let hero = gameHelper.hero
        let mapNode = this.node.parent
        let heroNode = this.planetCtrl.getHeroNode()
        let heroCmpt = heroNode.Component(HeroCmpt)
        let camera = this.planetCtrl.getCamera()
        this.mineNode.Component(GrassCmpt).onTarget(this.mine)

        this.planetCtrl.setCameraTarget(null)
        await this.actionTree.start(async(action: ActionNode)=>{

            let p4 = ut.convertToNodeAR(this.pathPoints[3], mapNode)
            let time4 = heroNode.getPosition().sub(p4).mag() / hero.getMoveSpeed()

            //镜头5
            let camePos5 = ut.convertToNodeAR(this.cameraPathPoints[4], camera.node.parent)
            cc.tween(camera.node).to(time4, { x: camePos5.x}).start()

            await action.run(hero.moveToPos, p4, hero)
           

            let p1 = ut.convertToNodeAR(this.pathPoints[0], mapNode)
            let time = heroNode.getPosition().sub(p1).mag() / hero.getMoveSpeed()

            //镜头1
            let camePos1 = ut.convertToNodeAR(this.cameraPathPoints[0], camera.node.parent)
            cc.tween(camera.node).to(time, { y: camePos1.y}).start()

            await action.run(hero.moveToPos, p1, hero)

            let p2 = ut.convertToNodeAR(this.pathPoints[1], mapNode)

            //镜头2
            let camePos2 = ut.convertToNodeAR(this.cameraPathPoints[1], camera.node.parent)
            cc.tween(camera.node).delay(0.1).to(0.9, { y: camePos2.y}).start()

            await action.waitFunc(async()=>{
                heroCmpt.playAnimation(HeroAnimation.JUMP2)
                await ut.wait(0.15, this)
                heroCmpt.playAnimation(HeroAnimation.JUMP3)
                await cc.tween(heroNode).then(cc.jumpTo(0.5, p2, 200, 1)).promise()
                await heroCmpt.playAnimation(HeroAnimation.JUMP4)
            })
            hero.setPosition(p2)

            let p3 = ut.convertToNodeAR(this.pathPoints[2], mapNode)

            //镜头3
            let camePos3 = ut.convertToNodeAR(this.cameraPathPoints[2], camera.node.parent)
            cc.tween(camera.node).to(0.83 * 0.8, { y: camePos3.y}).start()

            heroCmpt.playAnimation(PassengerLifeAnimation.WALK)
            await action.run(hero.moveToPos, p3, hero)

            await action.run(hero.collectAction, this.mine, hero)

            this.anim.node.active = true
            this.mineNode.active = false

            let awakeAnim = "animation"
            let p = this.anim.playAnimation(awakeAnim)

            this.anim.setEventListener(({ animation }, { data })=>{
                if (animation?.name == awakeAnim && data?.name == "effect") {
                    this.shake()    
                }
            })

            await action.waitFunc(async()=>{
                await ut.wait(0.1, this)
                await heroCmpt.playAnimation(HeroAnimation.JUMP2)
            })
           
            let nextNode = gameHelper.planet.getCurPlanet().getNextNode()
            hero.anim = HeroAnimation.RUN
            let orgSpeed = hero.getMoveSpeed()
            hero.setMoveSpeed(orgSpeed * 2)
            action.onTerminate = ()=>{
                hero.setMoveSpeed(orgSpeed)
            }

            //镜头4
            let camePos4 = ut.convertToNodeAR(this.cameraPathPoints[3], camera.node.parent)
            cc.tween(camera.node).to(0.9, { y: camePos4.y}).start()

            action.run(hero.moveToPos, nextNode.reachPosition, hero).then(()=>{
                hero.anim = null
                hero.setAction(HeroAction.IDLE)
                hero.setFlip(false)
                hero.setMoveSpeed(orgSpeed)
            })

            await action.waitFunc(()=>{
                return p
            })

            action.ok()
        })

        await this.model.die()
        if (!cc.isValid(this)) return
        this.model.end()
    }

    private async shake() {
        let offsetY = 20
        let camera = this.planetCtrl.getCamera()
        let cameraTarget = this.planetCtrl.getCameraTaget()
        await cc.tween(camera.node).by(0.1, {y: offsetY}).by(0.1, {y: -offsetY}).promise()
    }

    protected onDeath(): void {
        this.model = null
        this.node.parent = null
        this.node.destroy()
    }

    onRemove() {
        super.onRemove()
        if (this.planetCtrl) {
            let camera = this.planetCtrl.getCamera()
            cc.Tween.stopAllByTarget(camera.node)
            cc.Tween.stopAllByTarget(camera)
        }
        this.actionTree && this.actionTree.terminate()
    }
}