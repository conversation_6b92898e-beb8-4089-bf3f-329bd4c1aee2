import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EnterGardenTimeStoneCmpt extends PlanetNodeCmpt {
    @property(cc.Node)
    private holeNode_: cc.Node = null
    @property(cc.Node)
    private posNode_: cc.Node = null

    public model: PlanetEmptyNode = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.TIME_STONE_APPEAR]: this.onAppear },
        ]
    }

    init(model: PlanetEmptyNode, planetCtrl) {
        super.init(model)
        model.noShowReward = true
        this.holeNode_.active = false
    }
    update(dt) {
        super.update(dt)
    }
    protected onDeath(): void {
        //do nothing
    }
    private async onTarget(model) {
        if (this.model != model) return
        this.holeNode_.active = true
        this.dropStone()
    }

    // ----------------------------------------- custom function ----------------------------------------------------
    private dropStone() {
        let parent = mc.getViewNode()
        let pos = ut.convertToNodeAR(this.posNode_, parent, null, null, true)
        viewHelper.showPnl("timeStone/TimeStoneDropPnl", pos)
    }
    private async onAppear() {
        await this.waitPlot()
        await this.waitClick()
        this.stoneDie()
        await viewHelper.waitCloseUI("timeStone/TimeStoneDropPnl")
        this.stoneEnd()
    }
    private stoneDie() {
        this.model.planet.immediateDone = false
        this.model.die()
    }
    private async stoneEnd() {
        this.model.end()
        await ut.wait(0.5, this)
        viewHelper.gotoMainFromPlanet()
    }
    private waitPlot() {
        const key = "guidePlot_key_100_1"
        return gameHelper.guide.logic.showPlotKey(key)
    }
    private waitClick() {
        const stepInfo = {
            mask: false,
            delayTime: 0.3,
            advancedAllow: true,
            logic: { call: "showClick" },
            guide: { location: "TIME_STONE", type: "HAND" },
        }
        return gameHelper.guide.logic.showGuidePnl(stepInfo)
    }
}
