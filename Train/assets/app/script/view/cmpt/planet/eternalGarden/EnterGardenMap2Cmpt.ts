import { CCClass } from "../../../../../../../engine/cocos2d/core/platform/deserialize-compiled";
import CoreEventType from "../../../../../core/event/CoreEventType";
import { ConditionType, HeroAction, PassengerAction } from "../../../../common/constant/Enums";
import CurvePath from "../../../../common/curve/CurvePath";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetNodeModel from "../../../../model/planet/PlanetNodeModel";
import EternalGardenWindCtrl from "../../../eternalGarden/EternalGardenWindCtrl";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

enum State {
    START,
    ON_BRIDGE,
    END,
}

@ccclass
export default class EnterGardenMap2Cmpt extends PlanetNodeCmpt  {

    public model: PlanetEmptyNode = null
    private actionTree: ActionTree = null

    @property(cc.AnimationClip)
    private pathAnim: cc.AnimationClip = null

    private path: CurvePath = null

    private state: State = State.START

    private planetCtrl: EternalGardenWindCtrl = null

    private cameraTargetPos: cc.Vec2 = null
    private cameraOrgPos: cc.Vec2 = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    init(model: PlanetEmptyNode, planetCtrl) {
        super.init(model)

        this.planetCtrl = planetCtrl
        this.actionTree = new ActionTree().init(this)
        this.initPath()
        let map = model.getMap()
        model.endOffset = cc.v2(map.getWidth() - model.position.x, 0)

        this.model.reachOffset = this.path.getPosition(0)

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    private initPath() {
        this.path = new CurvePath().init(this.pathAnim)
    }


    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
        this.updateHero()
    }

    private async onTarget(model) {
        if (this.model != model) return
        let hero = gameHelper.hero
        await ut.wait(0.5, this)
        this.planetCtrl.focusHero()
 
        let targetPos = cc.v2()
        let moveAgent = hero.moveAgent
        let basePos = this.node.getPosition()
        let convertFunc = (pos: cc.Vec2)=>{
            return basePos.add(pos, targetPos)
        }
        let pos = convertFunc(this.path.getPosition(0))

        await this.actionTree.start(async(action: ActionNode)=>{
            await action.run(hero.moveToPos, pos, hero)

            moveAgent.init(this.path, convertFunc)
            let orgSpeed = moveAgent.getSpeed()
            action.onTerminate = ()=>{
                moveAgent.setSpeed(orgSpeed)
            }
            this.state = State.ON_BRIDGE

            this.planetCtrl.setCameraTarget(null)
            let camera = this.planetCtrl.getCamera()
            this.cameraOrgPos = camera.node.getPosition()
            this.cameraTargetPos = ut.convertToNodeAR(this.node, camera.node.parent, cc.v2(1100, 500))

            await action.run(hero.onMove, null, hero)
            hero.setAction(HeroAction.IDLE)
            moveAgent.setSpeed(orgSpeed)
            action.ok()
        })
        targetPos = null
        this.enterMap()
    }

    private updateHero() {
        if (this.state == State.ON_BRIDGE) {
            let ratio = gameHelper.hero.moveAgent.getRatio()
            let hero = gameHelper.hero
            let heroNode = this.planetCtrl.getHeroNode()
            heroNode.scale = cc.misc.lerp(1, 0.4, ratio)
            hero.moveAgent.setSpeed(heroNode.scale * hero.orgMoveSpeed)

            let offset = 0.65 //角色走到这个进度的时候，镜头移动停止，开始放大
            let camera = this.planetCtrl.getCamera()
            camera.node.setPosition(this.cameraOrgPos.lerp(this.cameraTargetPos, Math.min(offset, ratio) / offset))
            camera.zoomRatio = cc.misc.lerp(1, 2.5, Math.max(ratio - offset, 0) / (1 - offset))
        }
    }

    private async enterMap() {
        this.state = State.END
        let model = this.model
        let p = model.die()
        await p
        model.end()
        gameHelper.planet.getCurPlanet().updateMap()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}