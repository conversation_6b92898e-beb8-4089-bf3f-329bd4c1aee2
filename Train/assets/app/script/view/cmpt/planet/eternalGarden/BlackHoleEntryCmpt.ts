import { util } from "../../../../../core/utils/Utils";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import SchoolPlanetWindCtrl from "../../../schoolPlanet/SchoolPlanetWindCtrl";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BlackHoleEntryCmpt extends PlanetNodeCmpt {

    public model: PlanetEmptyNode = null

    private planetCtrl: any = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    init(model, planetCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl

        if (gameHelper.hero.isTarget(this.model)) {
            this.onTarget(this.model)
        }
    }

    @util.addLock
    protected async onTarget(model: PlanetEmptyNode) {
        if (this.model != model) return
        this.showEntry()

        model.planet.immediateDone = false
        await model.die()

        const key = "guidePlot_key_101_1"
        await gameHelper.guide.logic.showPlotKey(key)

        this.onEnd()
    }

    protected onDeath(): void {
        //do nothing
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    protected showEntry() {
        let bg: cc.Node = this.planetCtrl.bgSwitchNode_.Child("bg-2")
        let entry = bg.Child("entry")
        entry.active = true
        entry.Component(sp.Skeleton).playAnimation("daiji")
    }

    protected onEnd() {
        this.model.end()
        viewHelper.gotoMainFromPlanet()
    }
}
