import EventType from "../../../../common/event/EventType";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EnterGardenCmpt extends mc.BaseCmptCtrl {
    @property(sp.Skeleton)
    private pig: sp.Skeleton = null

    @property(sp.Skeleton)
    private rabbit: sp.Skeleton = null

    @property(cc.Node)
    private runEffect: cc.Node = null

    onEnter() {
        this.node.zIndex = 10000
    }

    public async play() {
        let time = 2.8
        let tidyAnim = "sp/aniTidy"
        let tidyTime = this.pig.getAnimationDuration(tidyAnim)
        this.pig.playAnimation(tidyAnim, true, tidyTime - time % tidyTime)
        await ut.wait(time, this)
        this.runPig()
        await ut.wait(1.3, this)
        await this.runRabbit()
        this.node.destroy()
        eventCenter.emit(EventType.GARDEN_FIRST_LAND_OVER)
    }

    private async runPig() {
        let pig = this.pig
        await pig.playAnimation("sp/aniScare")
        this.runEffect.active = true
        return this.runAnim(pig)
    }

    private async runRabbit() {
        let rabbit = this.rabbit
        await rabbit.playAnimation("sp/aniScare")
        this.rabbit.node.scaleX = -this.rabbit.node.scaleX
        return this.runAnim(rabbit)
    }

    private runAnim(sk) {
        sk.playAnimation("sp/aniRun", true)
        let dis = cc.winSize.width * 0.5
        let time = dis / 600 / 5
        return cc.tween(sk.node).by(time, { x: dis }).promise()
    }
}