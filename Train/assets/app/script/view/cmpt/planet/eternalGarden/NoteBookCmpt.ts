import { util } from "../../../../../core/utils/Utils";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import GetItemCmpt from "../GetItemCmpt";

const { ccclass, property } = cc._decorator;


@ccclass
export default class NoteBookCmpt extends GetItemCmpt  {

    public listenEventMaps() {
        let list = super.listenEventMaps()
        list.pushArr(
            [
                { [NodeType.GUIDE_NOTE_BOOK]: ()=>this.node },
            ]
        )
        return list
    }

    public async onClick() {
        let succ = await super.onClick()
        if (!succ) return

        let model = this.model
        await model.die()
        viewHelper.showPnl('train/NotebookPnl', 0, false, false, async()=>{
            model.end()
            eventCenter.emit(EventType.CLAIM_MINE_REWARD_START, model)
        })
        return true
    }
}