import EventType from "../../../../common/event/EventType";
import { HeroAction, PassengerSpAnimation } from "../../../../common/constant/Enums";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import EternalGardenWindCtrl from "../../../eternalGarden/EternalGardenWindCtrl";
import PlanetNodeCmpt from "../PlanetNodeCmpt";
import PlanetCheckPointModel from "../../../../model/planet/PlanetCheckPointModel";
import PlanetCheckPointCmpt from "../PlanetCheckPointCmpt";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";

const { ccclass, property } = cc._decorator;

interface Animal { run: boolean, speed: number, add: number, curX: number, endX: number, y: number, endFun?: Function }
let RoleSpeed = 900  //赏金猎犬初速度
let PigBackStart = 3.5
enum TypeCamera {
    None,
    Jump,
    Start,
    Chase,
    Pig,
}

@ccclass
export default class CurveChaseCmpt extends PlanetNodeCmpt {
    public model: PlanetEmptyNode = null
    private actionTree: ActionTree = null
    @property(cc.Node)
    protected runEndNode: cc.Node = null
    @property(cc.Node)
    protected pigEndNode: cc.Node = null
    @property(cc.Node)
    protected rabbitEndNode: cc.Node = null
    @property(sp.Skeleton)
    private pig: sp.Skeleton = null
    @property(sp.Skeleton)
    private rabbit: sp.Skeleton = null

    private planetCtrl: EternalGardenWindCtrl = null
    private posChase: Function = null
    private posAnimal: Function = null
    private isInitAnimal: boolean = false
    private dicPig: Animal = { run: false, speed: 720, add: 0, curX: 0, endX: 0, y: 0 }  //猪初速度
    private dicRab: Animal = { run: false, speed: 900, add: 0, curX: 0, endX: 0, y: 0 }  //兔子初速度
    private dicCamera = { type: TypeCamera.None, x: 0, addX: 0, offX: 0 }
    private isHeroRunning: boolean = false
    private heroAdd: number = 0
    private cameraLerp: { time: number, target: cc.Vec2 }

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.PLAENT_CAN_JUMP_TO_END]: this.onFound },
        ]
    }
    init(model, planetCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.actionTree = new ActionTree().init(this)
        this.hideAll()
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }
    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
        this.updateCamera(dt)
        this.updateAnimal(dt)
    }
    private hideAll() {
        this.pig.node.active = false
        this.rabbit.node.active = false
        this.showYanwu(this.pig)
        this.showYanwu(this.rabbit)
        this.showHanshui(this.pig)
    }
    private async onFound() {
        if (gameHelper.hero.getTargetModel().index + 1 == this.model.index) {
            this.initPigRabbit()
            this.initCamera(TypeCamera.Jump, 600)//跳一跳的最后一跳 镜头交给这边
            eventCenter.emit(EventType.PLAENT_CHANGE_JUMP_END, this.node.x)
        }
    }
    private async onTarget(model) {
        if (this.model != model) return
        viewHelper.showUI(false)
        await viewHelper.waitClosePnl()
        this.initPigRabbit()
        this.initCamera(TypeCamera.Start, 600)
        await this.cameraMoveOver()
        let hero = gameHelper.hero
        hero.setAction(HeroAction.CHASE_1)//大喊站住
        let timeTalk = cfgHelper.getRoleAnimTime(hero.getRole().getID(), PassengerSpAnimation.TALK_SHOUT)
        let timeElap = timeTalk * 0.3
        await ut.wait(timeElap, this)
        this.emit(EventType.CHANGE_PLOT, { npcId: 1005 })
        this.scarePig()
        this.scareRabbit()
        await ut.wait(timeTalk - timeElap, this)
        this.emit(EventType.END_PLOT)
        this.startRun()
        let pos = ut.convertToNodeAR(this.runEndNode, this.node.parent)
        hero.setAction(HeroAction.CHASE_2)//追逐
        await this.actionTree.start(async (action: ActionNode) => {
            action.onTerminate = () => {
                hero.setMoveSpeed(hero.orgMoveSpeed)
            }
            hero.setMoveSpeed(RoleSpeed)
            hero.setPathByPos(pos)
            await action.run(hero.onMove, null, hero)
            action.ok()
            cc.log("hero.getMoveSpeed()", hero.getMoveSpeed())
            hero.setMoveSpeed(hero.orgMoveSpeed)
        })
        cc.log("this.dicCamera", this.dicCamera)
        this.isHeroRunning = false
        // this.dicCamera.addX = 0
        hero.setAction(HeroAction.IDLE)
    }
    private async chaseEnd() {
        let hero = gameHelper.hero
        hero.setMoveSpeed(hero.orgMoveSpeed)
        this.dicCamera.type = TypeCamera.None
        this.planetCtrl.clearCurveChase()
        this.planetCtrl.focusHero()
        await this.model.die()
        this.model.end()
        this.convertToCheckPoint()
        viewHelper.showUI(true)
    }
    private convertToCheckPoint() {
        let target = gameHelper.planet.getCurPlanet().getCurNode()
        if (target instanceof PlanetCheckPointModel) {
            let ary = this.node.parent.children
            for (let i = 0; i < ary.length; i++) {
                const element = ary[i];
                let cmpt = element.Component(PlanetCheckPointCmpt)
                if (cmpt?.model?.getId() == target.getId()) {
                    ut.convertParent(this.pig.node, element)
                    ut.convertParent(this.rabbit.node, element)
                    break
                }
            }
        } else {
            twlog.error('convertToCheckPoint demand has changed')
        }
    }
    private initPigRabbit() {
        if (this.isInitAnimal) return
        this.isInitAnimal = true
        this.pig.node.active = true
        this.rabbit.node.active = true
        this.dicPig.endFun = this.endPig.bind(this)
        this.dicRab.endFun = this.endRabbit.bind(this)
        this.dicPig.endX = ut.convertToNodeAR(this.pigEndNode, this.node.parent).x
        this.dicRab.endX = ut.convertToNodeAR(this.rabbitEndNode, this.node.parent).x
        this.pig.playAnimation('sp/aniTidy', true)
        this.rabbit.playAnimation('aniTaunt', true)
        ut.convertParent(this.pig.node, this.node.parent)
        ut.convertParent(this.rabbit.node, this.node.parent)
        this.pig.node.zIndex = -1
        this.dicPig.curX = this.pig.node.x
        this.dicRab.curX = this.rabbit.node.x
        this.dicPig.y = this.pig.node.y
        this.dicRab.y = this.rabbit.node.y
    }
    private initCamera(type: TypeCamera, off: number) {
        let target = this.planetCtrl.getCameraTaget()
        this.posChase = target.posChase
        this.posAnimal = target.posAnimal
        this.dicCamera.type = type
        this.dicCamera.offX = off
        this.planetCtrl.setCameraTarget(null)
        this.planetCtrl.setCurveChase(this.getCameraX.bind(this))
        this.setCameraPos(0)
    }
    private async cameraMoveOver() {
        return new Promise(resolve => {
            let check = () => {
                if (this.dicCamera.type == TypeCamera.None) {
                    this.unschedule(check)
                    resolve(true)
                }
            }
            this.scheduleUpdate(check)
        })
    }
    private getCameraX() {
        return this.dicCamera.x
    }
    private addCameraPos(dt) {
        let dic = this.dicCamera
        if (dic.addX == 0) return
        dic.x += dic.addX * dt
    }
    private setCameraPos(dt) {
        let dic = this.dicCamera
        let type = dic.type
        if (type == TypeCamera.Jump || type == TypeCamera.Start) {
            dic.x = this.planetCtrl.getHeroNode().x + dic.offX
        } else {
            this.addCameraPos(dt)
        }
    }
    private updateCamera(dt) {
        if (!this.dicCamera.type) return
        this.setCameraPos(dt)
        let camera = this.planetCtrl.getCamera()
        let targetPos: cc.Vec2 = this.posChase()
        let camPosition = cc.v2()
        if (this.dicCamera.type == TypeCamera.Jump) {
            this.setCameraLerp(targetPos, dt * 10)
            camera.node.getPosition(camPosition)
            camPosition.set(camPosition.lerp(targetPos, cc.misc.clamp01(this.cameraLerp.time)))
        } else if (this.dicCamera.type == TypeCamera.Start) {
            this.setCameraLerp(targetPos, dt)
            camera.node.getPosition(camPosition)
            camPosition.set(camPosition.lerp(targetPos, cc.misc.clamp01(this.cameraLerp.time)))
        } else {
            camPosition = targetPos
        }
        camera.node.x = camPosition.x
        camera.node.y = camPosition.y
    }
    private setCameraLerp(targetPos: cc.Vec2, speed: number) {
        let dic = this.cameraLerp
        if (dic) {
            if (dic.target.equals(targetPos)) {
                dic.time += speed
                if (dic.time >= 1) {
                    this.dicCamera.type = TypeCamera.None
                }
            } else {
                dic.time = speed
                dic.target = cc.v2(targetPos)
            }
        } else {
            this.cameraLerp = dic = {
                time: speed,
                target: cc.v2(targetPos),
            }
        }
    }
    private startRun() {
        let timeCamera = 0
        this.isHeroRunning = true
        this.dicCamera.type = TypeCamera.Chase
        this.setCameraOff()
        this.changeHeroSpeed()
        this.schedule((dt) => {
            if (!this.isHeroRunning) return
            timeCamera += dt
            // let pigPos = cc.v2(this.pig.node.x, this.pig.node.y)
            // let heroPos = this.planetCtrl.getHeroNode().getPosition()
            // cc.log("timeCamera", timeCamera, "distance", pigPos.edistance(heroPos))
            // eventCenter.emit('CHASE_SHOW_TIME', timeCamera)  //UI展示时间
            this.addHeroSpeed(dt)
            this.addAnimalSpeed(dt, this.dicPig)
            this.addAnimalSpeed(dt, this.dicRab)
        }, 1 / 60, cc.macro.REPEAT_FOREVER)
    }
    private async setCameraOff() {
        this.dicCamera.addX = 300
        await ut.wait(2, this)
        this.dicCamera.addX = 0
        await ut.wait(0.2, this)
        this.dicCamera.addX = RoleSpeed + 300
        await ut.wait(3, this)
        // this.dicCamera.addX = RoleSpeed
        // await ut.wait(1.8, this)
        // this.dicCamera.addX = RoleSpeed + 100
        // await ut.wait(1, this)
        this.dicCamera.addX = RoleSpeed
    }
    private async changeHeroSpeed() {
        await ut.wait(8.8, this)
        this.heroAdd = -900
        await ut.wait(0.5, this)
        this.heroAdd = 0
    }
    private addHeroSpeed(dt) {
        if (this.heroAdd == 0) return
        let hero = gameHelper.hero
        hero.setMoveSpeed(hero.getMoveSpeed() + this.heroAdd * dt)
        // cc.log("hero speed", hero.getMoveSpeed())
    }
    private addAnimalSpeed(dt, dic: Animal) {
        if (dic.add == 0) return
        dic.speed += dic.add * dt
        // cc.log("animal speed", dic.speed)
    }
    private updateAnimal(dt) {
        if (!this.posAnimal) return
        this.setAnimalPos(dt, this.dicPig, this.pig.node)
        this.setAnimalPos(dt, this.dicRab, this.rabbit.node)
    }
    private setAnimalPos(dt: number, dic: Animal, node: cc.Node) {
        if (!dic.run) return
        dic.curX += dic.speed * dt
        this.posAnimal(dic.curX, dic.y, node)
        if (dic.curX >= dic.endX) {
            dic.run = false
            this.showYanwu(node.Component(sp.Skeleton))
            dic.endFun()
        }
    }
    private async scarePig() {
        let ani = 'sp/aniScare'
        let dur = this.pig.getAnimationDuration(ani)
        let elapse = dur * 0.52
        this.pig.playAnimation('sp/aniScare', false, elapse)
        await ut.wait(dur - elapse, this)
        this.runPig()
    }
    private async scareRabbit() {
        let ani = 'sp/aniScare'
        let dur = this.rabbit.getAnimationDuration(ani)
        this.rabbit.playAnimation(ani)
        await ut.wait(dur * 0.71, this)
        this.runRabbit()
    }
    private async runPig() {
        let sk = this.pig
        this.runAnim(sk, this.dicPig)
        this.showYanwu(sk, true)
        await ut.wait(PigBackStart, this)
        this.pigHeadBack(sk, 1.2)  //猪保持回头动作的时长
        await ut.wait(0.2, this)
        this.pigSpeedup()
        this.showHanshui(sk, true)
        let lizi = this.getYanwu(sk)
        lizi.emissionRate *= 3
    }
    private async pigSpeedup() {
        this.dicPig.add = 720  //每一秒速度提升的像素
        await ut.wait(0.4, this)
        this.dicPig.add = 0
        await ut.wait(0.5, this)
        this.dicPig.add = -360
        await ut.wait(0.3, this)
        this.dicPig.add = 0
        await ut.wait(2, this)
        this.dicPig.add = -50
        await ut.wait(2, this)
        this.dicPig.add = 0
    }
    private async pigHeadBack(sk: sp.Skeleton, time: number = 1) {
        sk.playAnimation("sp/aniRun2", true)
        await ut.wait(time, this)
        sk.playAnimation("sp/aniRun", true)
    }
    private async endPig() {
        let sk = this.pig
        let len = 500
        let time = len / this.dicPig.speed
        this.dicCamera.type = TypeCamera.Pig
        this.calcuEndCamera1(time, len)
        this.endHanshui(sk)
        this.pigHeadBack(sk, time)
        await cc.tween(sk.node).by(time, { x: len }).promise()
        let speed = 400
        let lenBack = len - 300
        let timeBack = lenBack / speed
        sk.node.scaleX *= -1
        this.calcuEndCamera2(timeBack, lenBack, -speed)
        this.setYanwuFlipX(sk)
        await cc.tween(sk.node).by(timeBack, { x: -lenBack }).promise()
        sk.playAnimation('aniTaunt', true)
        await ut.wait(0.1, this)//等待镜头到位
        this.chaseEnd()
    }
    private calcuEndCamera1(time: number, len: number) {
        this.dicCamera.addX = this.dicPig.speed
        // let cur = this.dicCamera.x
        // let toX = this.pig.node.x + len - 500
        // if (cur >= toX) {
        //     this.dicCamera.addX = 0
        // } else {
        //     this.dicCamera.addX = (toX - cur) / time
        // }
    }
    private calcuEndCamera2(time: number, len: number, speed:number) {
        this.dicCamera.addX = speed
        // let cur = this.dicCamera.x
        // let toX = this.pig.node.x - len - 500
        // this.dicCamera.addX = (toX - cur) / time
    }
    private runRabbit() {
        let sk = this.rabbit
        sk.node.scaleX *= -1
        this.runAnim(sk, this.dicRab)
        this.rabbitLizi()
        sk.setEventListener(({ animation }, { data }) => {
            if (data.name == "effect") {
                this.rabbitLizi()
            }
        })
    }
    private rabbitLizi() {
        let sk = this.rabbit
        let all = this.getAllYanwu(sk)
        let item = null
        for (let i = 0; i < all.children.length; i++) {
            const element = all.children[i]
            if (element['canUse_']) {
                item = element
                break
            }
        }
        if (!item) {
            item = cc.instantiate2(all.children[0], all)
        }
        item.Component(cc.ParticleSystem).resetSystem()
        item['canUse_'] = false
        cc.tween(item).delay(1).call(() => { item['canUse_'] = true }).start()
    }
    private endRabbit() {
        let sk = this.rabbit
        sk.node.scaleX *= -1
        sk.playAnimation('aniTaunt2', true)
        this.setYanwuFlipX(sk)
    }
    private runAnim(sk: sp.Skeleton, dic: Animal) {
        sk.playAnimation("sp/aniRun", true)
        dic.run = true
    }
    private showYanwu(sk: sp.Skeleton, bol: boolean = false) {
        let lizi = this.getYanwu(sk)
        if (bol) {
            lizi.resetSystem()
        } else {
            lizi.stopSystem()
        }
    }
    private getYanwu(sk: sp.Skeleton): cc.ParticleSystem {
        let node = this.getAllYanwu(sk)
        return node.Child('lizi', cc.ParticleSystem)
    }
    private getAllYanwu(sk: sp.Skeleton): cc.Node {
        return sk.getAttachedNode('all').Child('all')
    }
    private setYanwuFlipX(sk: sp.Skeleton) {
        let all = this.getAllYanwu(sk)
        all.scaleX *= -1
    }
    private showHanshui(sk: sp.Skeleton, bol: boolean = false) {
        let node = this.getHanshui(sk)
        node.active = bol
        if (bol) {
            node.Component(sp.Skeleton).playAnimation('animation', true)
        }
    }
    private endHanshui(sk: sp.Skeleton) {
        let node = this.getHanshui(sk)
        node.Component(sp.Skeleton).setCompleteListener(() => {
            node.active = false
        })
    }
    private getHanshui(sk: sp.Skeleton): cc.Node {
        return sk.getAttachedNode('guadian_hanshui').Child('biaohanshui')
    }
}