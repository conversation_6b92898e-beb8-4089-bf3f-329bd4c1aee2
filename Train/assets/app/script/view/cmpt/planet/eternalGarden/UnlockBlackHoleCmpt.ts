import { util } from "../../../../../core/utils/Utils";
import { HeroAnimation, PassengerLifeAnimation, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class UnlockBlackHoleCmpt extends mc.BaseCmptCtrl {

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onUnlockFunction },
            { [NodeType.GUIDE_BLACK_HOLE_ENTRY]: this.getEntry },
        ]
    }

    private model: PlanetModel = null

    public async init() {
        this.model = gameHelper.planet.getPlanet(1005)
        let root = this.Child("root")
        let entry = this.getEntry()
        entry.active = true

        let role = root.Child("role")
        let roleSk = role.Component(sp.Skeleton)
        roleSk.playAnimation(PassengerLifeAnimation.IDLE, true)
        this.updateName()

        let sk = entry.Component(sp.Skeleton)
        await sk.playAnimation("jiaohu")
        sk.playAnimation("daiji2", true)
        eventCenter.emit(EventType.GUIDE_START_UNLOCK_BLACK_HOLE)

        entry.addComponent(cc.Button)
        entry.addComponent(cc.ButtonEx)
        entry.once("click", ()=>{
            this.onEntry()
        })
    }

    private updateName() {
        let name = this.Child("root/play_name")
        name.Component(PlanetPlayNameCmpt).init(this.model, UIFunctionType.PLAY_BLACKHOLE)
    }

    private onUnlockFunction(type: UIFunctionType) {
        if (type == UIFunctionType.PLAY_BLACKHOLE) {
            this.updateName()
        }
    }

    private async moveToEntry() {
        let role = this.getRole()
        let roleSk = role.Component(sp.Skeleton)
        let path = this.getPath()
        let posNode = path.children[0]
        let pos = ut.convertToNodeAR(posNode, role.parent)
        roleSk.playAnimation(HeroAnimation.JUMP2, true)
        await ut.wait(0.3, this)
        roleSk.playAnimation(HeroAnimation.JUMP3)
        await cc.tween(role).then(cc.jumpTo(0.5, pos, 200, 1)).promise()
    }

    @util.addLock
    private async onEntry() {
        await this.moveToEntry()
        await this.jumpToEntry()
        this.onEnd()
    }

    private async jumpToEntry() {
        let role = this.getRole()
        let roleSk = role.Component(sp.Skeleton)
        let path = this.getPath()
        let last = path.children.last()
        roleSk.playAnimation(HeroAnimation.JUMP2, true)
        await ut.wait(0.3, this)
        let pos = ut.convertToNodeAR(last, role.parent)
        roleSk.playAnimation(HeroAnimation.JUMP3)
        await cc.tween(role).then(cc.jumpTo(0.5, pos, 200, 1)).promise()
    }

    private onEnd() {
        eventCenter.emit(EventType.GUIDE_END_UNLOCK_BLACK_HOLE)
    }

    private getRole() {
        return this.Child("root/role")
    }

    private getPath() {
        return this.Child("root/path")
    }

    public getEntry() {
        let root = this.Child("root")
        let entry = root.Child("bg-2/entry")
        return entry
    }
}