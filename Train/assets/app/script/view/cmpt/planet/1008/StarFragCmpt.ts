import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import GetItemCmpt from "../GetItemCmpt";

const { ccclass, property } = cc._decorator;


@ccclass
export default class StarFragCmpt extends GetItemCmpt  {

    public async onClick() {
        let succ = await super.onClick()
        if (!succ) return

        let model = this.model
        await model.die()

        let p
        if (gameHelper.openGuide) {
            p = eventCenter.wait(EventType.END_PLOT_UI)
        }
        else {
            p = eventCenter.wait(EventType.ADD_PLANET_ITEM)
        }
        p.then(async ()=>{
            model.end()
        })
   
        viewHelper.showPnl("planet/ItemUnlockPnl", {scale: 1.6, icon: "fb_open_shitou_an", name: "name_planetItem_star_frag"})
        
        return true
    }
}