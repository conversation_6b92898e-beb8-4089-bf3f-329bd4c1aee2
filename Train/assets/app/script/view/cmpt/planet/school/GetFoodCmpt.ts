import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import GetItemCmpt from "../GetItemCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

enum State {
    START,
    ON_TARGET,
    ON_MOVE,
}

@ccclass
export default class GetFoodCmpt extends GetItemCmpt  {
    public async onClick() {
        let succ = await super.onClick()
        if (!succ) return

        let model = this.model
        await model.die()
        viewHelper.showPnl("planet/FoodUnlockPnl", ()=>{
            model.end()
        })
        return true
    }
}