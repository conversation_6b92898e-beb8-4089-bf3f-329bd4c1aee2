import { Condition } from "../../../../common/constant/DataType";
import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import ConditionObj from "../../../../model/common/ConditionObj";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetMineModel from "../../../../model/planet/PlanetMineModel";
import EternalGardenWindCtrl from "../../../eternalGarden/EternalGardenWindCtrl";
import HeroCmpt from "../../hero/HeroCmpt";
import PlanetMineCmpt from "../PlanetMineCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

class Mine extends PlanetMineModel {

    public init(json) {
        let { hp, mineId } = json
        this.json = json
        this.mineId = mineId
        this.map = gameHelper.planet.getCurPlanet().getCurMap()
        this.initBaseJson()
        this.maxHp = hp
        this.hp = this.maxHp
        return this
    }

    public isLast() { return false }

    public checkShow() { return true }
    public isPassContorl() { return true }

    public async die() {
        if (this.dead) return false
        this.dead = true
        return true
    }
}

class MineCmpt extends PlanetMineCmpt {

    protected body: any = null

    public deadCallback: Function = null

    public onCreate(): void {
        super.onCreate()
        this.body = this.Child('body', sp.Skeleton)
    }

    protected initPos(): void {
        //do nothing
    }

    protected initSize(): void {
        let size = cc.size(239, 411)
        this.model.setSize(size)
    }

    protected async initIcon() {
        //do nothing
    }

    protected async onDeath() {
        if (!this.model) return
        this.model = null
        this.stopScaleActions()
        let toOpacity = (node) => {
            cc.tween(node).to(0.2, { opacity: 0 }).start()
        }
        toOpacity(this.ui)
        toOpacity(this.shadow)
        this.playDeadAnim()
        this.deadCallback && this.deadCallback(1)

        await this.body.playAnimation("animation")
        this.body.playAnimation("animation2", true)

        this.deadCallback && this.deadCallback(2)
    }
}

@ccclass
export default class ClockworkCmpt extends PlanetNodeCmpt {

    @property(cc.Node)
    private clockworkGuideNode: cc.Node = null

    @property(cc.Node)
    private mineNode: cc.Node = null

    public model: PlanetEmptyNode = null

    private actionTree: ActionTree = null

    private mine: Mine = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.ADD_PLANET_ITEM]: this.onEnd },

            { [NodeType.GUIDE_CLOCKWORK]: () => this.clockworkGuideNode },
        ]
    }

    async init(model) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        this.mine = new Mine().init(this.model["json"])
        this.mine.setPosition(this.model.position)
        this.model.reachOffset = this.mine.reachOffset
        this.mine.setProgress(this.model.progress)
    
        let cmpt = this.mineNode.addComponent(MineCmpt)
        cmpt.init(this.mine)
        let p = null
        cmpt.deadCallback = (state) => {
            if (state == 1) {
                p = this.model.die()
            }
            else if (state == 2) {
                p.then(()=>{
                    if (!cc.isValid(this)) return
                    this.onMineDieEnd()
                })
            }
        }

        if (gameHelper.hero.getTargetModel() == this.model) {
            await this.onTarget(this.model)
        }
    }

    public async onClick() {
        if (!this.model) return

        this.mineNode.active = false
        this.clockworkGuideNode.active = false
        viewHelper.showPnl("planet/ItemUnlockPnl", {icon: "ty_hd_05", offsetX: -11.5, name: "name_planetItem_clockwork"})
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero
        this.mineNode.Component(MineCmpt).onTarget(this.mine)

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.mine.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            await action.run(hero.collectAction, this.mine, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })
    }

    private async onMineDieEnd() {
        if (!cc.isValid(this)) return
        let model = this.model
        ut.wait(0.4).then(()=>{
            eventCenter.emit(EventType.CLAIM_MINE_REWARD_START, model)
        })
        ut.wait(1, this).then(() => {
            this.clockworkGuideNode.active = true
            eventCenter.emit(EventType.GUIDE_CLOCK_DROP)
        })
    }

    private onEnd() {
        this.model.end()
    }

    protected onDeath() {
        //do nothing
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}