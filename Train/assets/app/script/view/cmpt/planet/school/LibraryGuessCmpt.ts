import { HeroAction, HeroAnimation, PassengerSpAnimation, TipsNotType } from "../../../../common/constant/Enums";
import { anim<PERSON>elper } from "../../../../common/helper/AnimHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import CurvePath from "../../../../common/curve/CurvePath";
import EventType from "../../../../common/event/EventType";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetPuzzleModel from "../../../../model/planet/PlanetPuzzleModel";
import PlanetWindCtrl from "../../../planet/PlanetWindCtrl";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;


enum LGState {
    WAIT_START,
    ENTER_OVER,//从右侧入场完毕
    START,
    WAIT_END,
    END,
}

enum Tips {
    TIPS1 = 1,
    TIPS2,
    TIPS3,
}

let KeyPlot = {
    LookAt: "guidePlot_key_11_1",//看到博士喵
    FoundG: "guidePlot_key_11_2",//找到眼镜后
    Search: "guidePlot_key_11_3",//翻找资料后
    ThinkT: "guidePlot_key_11_4",//自言自语
}

@ccclass
export default class LibraryGuessCmpt extends PlanetNodeCmpt {

    public model: PlanetPuzzleModel = null

    @property(sp.Skeleton)
    private body: sp.Skeleton = null

    @property(sp.Skeleton)
    private pig: sp.Skeleton = null

    @property(sp.Skeleton)
    private tableSp: sp.Skeleton = null

    @property(sp.Skeleton)
    private owlSp: sp.Skeleton = null

    @property(sp.Skeleton)
    private hatSp: sp.Skeleton = null

    @property(cc.Node)
    private book: cc.Node = null

    @property(cc.Node)
    private glass: cc.Node = null

    @property(cc.Node)
    private ui: cc.Node = null

    @property(cc.Node)
    private screenTouch: cc.Node = null

    @property(cc.Node)
    private taskNode: cc.Node = null

    @property(cc.Animation)
    private anim: cc.Animation = null

    @property(cc.AnimationClip)
    private animClip: cc.AnimationClip = null

    private actionTree: ActionTree = null

    @property(cc.Node)
    private glassFlyPos: cc.Node = null

    @property([cc.Node])
    private normalNodes: cc.Node[] = []

    private passTime: number = 0
    private tips: Tips = null

    private bookLightTime: number = 0

    private heroNode: cc.Node = null

    get hideY() {
        return cc.winSize.height * 0.5 + 82
    }

    get showY() {
        return cc.winSize.height * 0.5 - 82
    }

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.GUIDE_LIBRARY_GUESS_START_FINISH]: this.guessFinish },
        ]
    }

    onCreate() {
        for (let node of this.normalNodes) {
            let waitMap = {}
            node.on("click", async () => {
                if (waitMap[node.uuid]) return
                waitMap[node.uuid] = true
                await animHelper.playClickShake(node)
                waitMap[node.uuid] = false
            })
        }
    }

    onClean() {
        viewHelper.hidePlanetTips()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

    protected onDeath(): void {
        //do nothing
    }

    init(model, planetCtrl: PlanetWindCtrl) {
        super.init(model)
        this.heroNode = planetCtrl.getHeroNode()
        this.node.setPosition(model.position)

        this.actionTree = new ActionTree().init(this)

        this.initState()
        this.initUI()
        this.initMix()
        this.initPig()
        viewHelper.showUI(false)
        this.recoverCase()
        this.recoverBook()

        if (gameHelper.hero.isTarget(this.model)) {
            this.onTarget(this.model)
        }
    }

    private initState() {
        if (this.model.state == LGState.WAIT_START) {
            // 入场需要与map的showLandAnim同步
            let bol = gameHelper.planet.getCurPlanet()?.getCurMap()?.showLandAnim
            if (bol) this.setState(LGState.ENTER_OVER)
        }
    }

    private initUI() {
        this.ui.active = false
        this.ui.setContentSize(cc.winSize)
        let worlScale = this.ui.getWorldScale(cc.v2())
        this.ui.scale /= worlScale.x
        let camera = cc.Camera.findCamera(this.ui)
        this.ui.setPosition(ut.convertToNodeAR(camera.node, this.ui.parent))
        this.screenTouch.setContentSize(cc.winSize)
        this.screenTouch.active = true
        this.screenTouch.setPosition(this.ui.getPosition())
    }

    private initMix() {
        let sk = this.body
        sk.mix2(PassengerSpAnimation.IDLE, PassengerSpAnimation.WALK)
        sk.mix2(PassengerSpAnimation.IDLE, PassengerSpAnimation.THINK)
    }

    @ut.addLock
    protected async onClickOwl() {
        let worldPos = this.owlSp.Child('bubble').convertToWorldSpaceAR(cc.v2())
        viewHelper.showSpeechBubble(worldPos, "explore_guiText_12", 1.5)
    }

    @ut.addLock
    protected async onClickHat() {
        let sk = this.hatSp
        sk.setEventListener(({ animation }, { data }) => {
            if (animation.name == "animation" && data.name == "effect") {
                viewHelper.showAlert("explore_tips_4")
            }
        })
        await sk.playAnimation("animation")
        sk.playAnimation('jingzhi')
    }

    @ut.addLock
    protected async onClickBook() {
        let bookSk = this.book.Component(sp.Skeleton)
        this.book.Component(cc.Button).interactable = false
        await bookSk.playAnimation("animation")
        bookSk.playAnimation("jingzhi2")
        this.book.SetSwallowTouches(false)
        this.model.setPuzzleKeyValue('book', true)
    }

    private recoverBook() {
        if (!this.model.getPuzzleKeyValue('book')) return
        let bookSk = this.book.Component(sp.Skeleton)
        this.book.Component(cc.Button).interactable = false
        bookSk.playAnimation("jingzhi2")
        this.book.SetSwallowTouches(false)
    }

    private async showGlass() {
        await this.glass.Child('sp', sp.Skeleton).playAnimation("animation")
        this.blinkGlass()
    }

    private blinkGlass() {
        this.glass.Child('sp', sp.Skeleton).playAnimation("animation2", true)
    }

    @ut.addLock
    protected async onClickCase() {
        let sk = this.tableSp
        if (sk.animation != "jingzhi2") {
            await sk.playAnimation("animation")
            sk.playAnimation("jingzhi2")
            this.model.setPuzzleKeyValue('case', true)
        }
        viewHelper.showAlert("explore_tips_5")
    }

    private recoverCase() {
        if (!this.model.getPuzzleKeyValue('case')) return
        let sk = this.tableSp
        sk.playAnimation("jingzhi2")
    }

    protected onClickBack() {
        if (this.model.state >= LGState.WAIT_END) return
        viewHelper.gotoMainFromPlanet()
    }

    protected onClickGlass() {
        if (this.model.state >= LGState.WAIT_END) return
        this.setState(LGState.WAIT_END)
        this.hideTips()
        this.glass.active = false
        viewHelper.showPnl("planet/GlassUnlockPnl", { targetNode: this.glassFlyPos })
    }

    private async glassTaskFinish() {
        let finished = this.taskNode.Child('finished')
        // finished.scale = 0
        finished.active = true
        await finished.Component(sp.Skeleton).playAnimation('animation')
        // await cc.tween(finished).to(0.15, { scale: 1.3 }).delay(0.04).to(0.1, { scale: 0.85 }).to(0.05, { scale: 1 }).delay(0.5).promise()
        // return cc.tween(this.taskNode).to(0.5, { opacity: 0 }).promise()
        await cc.tween(this.taskNode).delay(0.5).to(0.5, { x: this.getTaskNodeX() }, { easing: cc.easing.sineIn }).promise()
        this.ui.active = false
    }

    private async playRoleAnimStand() {
        let sk = this.body
        await sk.playAnimation(PassengerSpAnimation.GLASSES)
        sk.playAnimation(PassengerSpAnimation.IDLE, true)
        await ut.wait(0.5, this) //起身之后待x秒
    }

    private async playRoleAnimWalk() {
        let sk = this.body
        sk.playAnimation(PassengerSpAnimation.WALK, true)
        sk.node.scaleX = -sk.node.scaleX
        await this.anim.playAsync()
        sk.node.scaleX = -sk.node.scaleX

        await sk.playAnimation(PassengerSpAnimation.TIDY)
        let lizi = sk.getAttachedNode("guadian_yanwu").Child('lizi')
        lizi.active = true
        await sk.playAnimation(PassengerSpAnimation.TIDY2)
        lizi.Component(cc.ParticleSystem).stopSystem()
        await sk.playAnimation(PassengerSpAnimation.TIDY3)
        sk.playAnimation(PassengerSpAnimation.IDLE, true)
    }

    private playRoleAnimThink() {
        let sk = this.body
        sk.playAnimation(PassengerSpAnimation.THINK, true)
    }

    private initPig() {
        let sk = this.pig
        let state = this.model.state
        if (state == LGState.WAIT_START) {
            this.pigRun()
        } else {
            sk.node.active = false
        }
    }

    private async pigRun() {
        let sk = this.pig
        let bol = gameHelper.planet.getCurPlanet()?.getCurMap()?.needLandAnim()
        if (bol) {
            await sk.playAnimation("sp/aniTidy")
        }
        await sk.playAnimation("sp/aniScare")
        sk.setSkin("beibao")
        sk.playAnimation("sp/aniRun", true)
        await cc.tween(sk.node).to(2, { x: -cc.winSize.width * 0.5 - 150 }).promise()
        sk.node.active = false
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)

        if (this.model.state == LGState.START) {
            this.passTime += dt
            this.checkTips()
        }

        if (this.tips == Tips.TIPS3) {
            this.bookLightTime += dt
            if (this.bookLightTime >= 1.5) { //选择提示之后x秒书发光
                let sk = this.book.Component(sp.Skeleton)
                if (sk.animation == "jingzhi1") {
                    sk.playAnimation("animation2", true)
                }
            }
        }
    }

    private async onTarget(model) {
        if (this.model != model) return

        let reachNode = this.Child('reachNode')
        let pos = ut.convertToNodeAR(reachNode, this.node.parent)
        if (this.model.state == LGState.WAIT_START) {
            await this.enterStart(pos)
        } else {
            this.recoverEnterOver(pos)
        }
        eventCenter.emit(EventType.REACH_PLANET_NODE, this.model)
        if (this.model.state == LGState.ENTER_OVER) await gameHelper.guide.logic.showPlotKey(KeyPlot.LookAt)
        this.guessStart()
    }

    private async enterStart(pos: cc.Vec2) {
        let hero = gameHelper.hero
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, pos, hero)
            this.setState(LGState.ENTER_OVER)
            gameHelper.hero.setAction(HeroAction.IDLE)
            action.ok()
        })
    }
    private async leaveAction() {
        this.walkByPath()
        await ut.wait(2, this)
    }
    private walkByPath() {
        let path = new CurvePath().init(this.animClip)
        let hero = gameHelper.hero
        let moveAgent = hero.moveAgent
        this.heroNode.parent = this.node
        moveAgent.init(path)
        hero.anim = HeroAnimation.RUN
        hero.setAction(HeroAction.MOVE)
        this.actionTree.start(async (action: ActionNode) => {
            let orgSpeed = moveAgent.getSpeed()
            action.onTerminate = () => { moveAgent.setSpeed(orgSpeed) }
            moveAgent.setSpeed(orgSpeed * 3)
            await action.run(hero.onMove, null, hero)
            hero.anim = null
            hero.setAction(HeroAction.IDLE)
            action.onTerminate()
            action.ok()
        })
    }

    private recoverEnterOver(pos: cc.Vec2) {
        let hero = gameHelper.hero
        hero.setPosition(pos)
        hero.setFlip(true)
        gameHelper.hero.setAction(HeroAction.IDLE)
    }

    private guessStart() {
        this.screenTouch.active = false
        this.ui.active = true
        let x = -cc.winSize.width * 0.5 + 108
        if (this.model.state == LGState.ENTER_OVER) {
            this.setState(LGState.START)
            this.actUI(x)
        } else if (this.model.state == LGState.START) {
            this.recoverStart(x)
        } else if (this.model.state == LGState.WAIT_END) {
            this.setState(LGState.START)
            this.recoverStart(x)
        } else if (this.model.state == LGState.END) {
            this.recoverStand(x)
        }
    }

    private recoverStart(x: number) {
        this.taskNode.x = x
        if (this.model.getPuzzleKeyValue('tipOver')) {
            this.recoverTips3()
        } else {
            this.showTips1(false)
        }
    }

    private recoverStand(x: number) {
        this.taskNode.x = x
        this.glass.active = false
        this.guessFinish()
    }

    private actUI(x: number) {
        this.taskNode.x = this.getTaskNodeX()
        cc.tween(this.taskNode).delay(1).to(0.5, { x }, { easing: cc.easing.sineOut }).start()
        ut.wait(1.75, this).then(() => { 
            if (this.model.state >= LGState.WAIT_END) return
            this.showTips1(true) 
        })
    }

    private getTaskNodeX() {
        return -cc.winSize.width * 0.5 - this.taskNode.width - 12
    }

    private async guessFinish() {
        this.screenTouch.active = true
        this.setState(LGState.END)
        this.glassTaskFinish()
        await this.playRoleAnimStand()
        await gameHelper.guide.logic.showPlotKey(KeyPlot.FoundG)
        await this.playRoleAnimWalk()
        await gameHelper.guide.logic.showPlotKey(KeyPlot.Search)
        this.guessDie()
        await this.leaveAction()
        this.playRoleAnimThink()
        await gameHelper.guide.logic.showPlotKey(KeyPlot.ThinkT)
        this.guessEnd()
    }

    private guessDie() {
        this.model.planet.immediateDone = false
        this.model.die()
    }

    private guessEnd() {
        this.model.end()
        viewHelper.gotoMainFromPlanet()
    }

    private setState(st: LGState) {
        this.model.state = st
    }

    private checkTips() {
        if (this.tips == Tips.TIPS1 && this.passTime >= 15) { //提示2出现
            this.hideTips(false)
            this.showTips2()
        }
    }

    private showTips1(showAnim) {
        this.tips = Tips.TIPS1
        viewHelper.showPlanetTips("explore_guiText_13", TipsNotType.NORMAL, { showAnim })
    }

    private showTips2() {
        this.tips = Tips.TIPS2
        viewHelper.showPlanetTips("explore_guiText_14", TipsNotType.OPT, {
            ok: () => {
                this.hideTips(false)

                //延迟x秒出tips3
                ut.wait(0.25, this).then(() => {
                    if (this.model.state == LGState.START) {
                        this.showTips3()
                    }
                })
            },
            cancel: () => {
                this.hideTips(false)
                this.showTips1(false)
            }
        })
    }

    private showTips3() {
        this.tips = Tips.TIPS3
        this.model.setPuzzleKeyValue('tipOver', true)
        viewHelper.showPlanetTips("explore_guiText_15")
        this.showGlass()
    }

    private recoverTips3() {
        this.tips = Tips.TIPS3
        viewHelper.showPlanetTips("explore_guiText_15", TipsNotType.NORMAL, { showAnim: false })
        this.blinkGlass()
    }

    private hideTips(showAnim: boolean = true) {
        this.tips = null
        this.passTime = 0
        viewHelper.hidePlanetTips(showAnim)
    }
}
