import CoreEventType from "../../../../../core/event/CoreEventType";
import { util } from "../../../../../core/utils/Utils";
import { HeroAnimation, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, PLANET_ITEM_ID, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { resHelper } from "../../../../common/helper/ResHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import UnlockBlackHoleCmpt from "../eternalGarden/UnlockBlackHoleCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

const PLOT_KEY = "guidePlot_key_4002_1"

@ccclass
export default class PlanetEntry1005Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.END_PLOT]: this.onPlotEnd },
            { [CoreEventType.CLOSE_PNL]: this.onClosePnl, tag: "create" },
            { [EventType.USE_PLANET_ITEM]: this.onUsePlanetItem },
            { [EventType.CHECK_USE_PLANET_ITEM]: this.checkUsePlanetItem },
        ]
    }

    private shopWaitAppear: boolean = false

    protected initView() {
        this.initBlackHole()
        this.initShop()
    }

    private initBlackHole() {
        let type = UIFunctionType.PLAY_BLACKHOLE
        let blackHole = this.Component(MountPointCmpt).getPoint("blackHole")
        blackHole.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        let unlock = unlockHelper.isUnlockFunction(type)
        blackHole.Child("touch").off("click")
        blackHole.Child("touch").on("click", () => {
            if (!unlock) {
                viewHelper.showAlert("unlockFunc_tips_7")
                return
            }
            if (gameHelper.blackHole.isStart()) {
                if (gameHelper.blackHole.isRoundEnd()) {
                    viewHelper.showAlert("blackHole_tips_3")
                }
                else {
                    viewHelper.showPnl("blackHole/BlackHolePnl")
                }
            }
            else {
                viewHelper.showPnl("blackHole/BlackHoleReadyPnl")
            }
        })
        blackHole.Child("lock").active = !unlock
        blackHole.Child("unlock").active = unlock
    }

    private initShop() {
        let shop = this.Component(MountPointCmpt).getPoint(NPC_ID.BLACK_HOLE_SHOP)
        let unlock = unlockHelper.isUnlockFunction(UIFunctionType.PLAY_BLACKHOLE)
        shop.active = unlock && !this.shopWaitAppear
        let name = shop.Child("play_name")
        name.Component(PlanetPlayNameCmpt).init(this.model, UIFunctionType.BLACK_HOLE_SHOP)

        let reddot = shop.Child("reddot")
        reddot.active = gameHelper.new.isNew(MarkNewType.NPC_DIALOG, [NPC_ID.BLACK_HOLE_SHOP])
        name.active = !reddot.active

        shop.off("click")
        shop.on("click", () => {
            if (gameHelper.new.isNew(MarkNewType.NPC_DIALOG, [NPC_ID.BLACK_HOLE_SHOP])) {
                gameHelper.plot.start(PLOT_KEY)
                gameHelper.new.removeNew(MarkNewType.NPC_DIALOG, [NPC_ID.BLACK_HOLE_SHOP])
                reddot.active = false
                name.active = true
            }
            else {
                if (this.checkClickTaskDialogNpc(shop)) return
                this.showStore()
            }
        })
    }

    private async showUnlockBlackHole() {
        viewHelper.showUI(false)
        let pnl = "blackHole/BlackHoleReadyPnl"
        let node = await resHelper.loadPrefabByUrl("planet/1005/unlock_black_hole", this.node, this.getTag())
        if (!cc.isValid(this)) return
        let p = viewHelper.preloadPnl(pnl)

        node.Component(UnlockBlackHoleCmpt).init()

        await eventCenter.wait(EventType.GUIDE_END_UNLOCK_BLACK_HOLE)

        await p
        viewHelper.showPnl(pnl)
        viewHelper.showUI(true)
        this.shopWaitAppear = true
        this.initView()
        node.destroy()
    }

    private onPlotEnd(plotkey: string) {
        if (plotkey != PLOT_KEY) return
        this.showStore()
    }

    private showStore() {
        viewHelper.showPnl("blackHole/BlackHoleStore")
    }

    public getNpc() {
        return this.getShop().getChildByName("sk")
    }

    private onClosePnl(pnl: mc.BasePnlCtrl) {
        if (!this.shopWaitAppear) return
        let key = pnl.key
        let key1 = "blackHole/BlackHolePnl"
        let key2 = "blackHole/BlackHoleReadyPnl"
        if (key != key1 && key != key2) return
        if (!viewHelper.checkPnlClose(key1) || !viewHelper.checkPnlClose(key2)) return
        this.showNpc()
        this.shopWaitAppear = false
    }

    private async showNpc() {
        viewHelper.showUI(false)
        mc.lockTouch(true)
        let shop = this.getShop()
        shop.active = true
        let name = shop.getChildByName("play_name")
        name.active = false
        let reddot = shop.Child("reddot")
        reddot.active = false
        let npc = this.getNpc()
        let sk = npc.Component(sp.Skeleton)
        sk.playAnimation("chuxian").then(() => {
            sk.playAnimation("daiji", true)
        })
        await ut.wait(2.5, this)
        reddot.active = true
        mc.lockTouch(false)
        viewHelper.showUI(true)
    }

    private checkUsePlanetItem(id: string, screenPos: cc.Vec2) {
        if (id != PLANET_ITEM_ID.BLACK_HOLE_KEY) return
        let blackHole = this.Component(MountPointCmpt).getPoint("blackHole")
        let node = blackHole.getChildByName("touch")
        return node._hitTest(screenPos)
    }

    private async onUsePlanetItem(id: string, screenPos: cc.Vec2) {
        if (!this.checkUsePlanetItem(id, screenPos)) return
        gameHelper.blackHole.isUnlock = true
        eventCenter.emit(EventType.REMOVE_PLANET_ITEM, id)

        let succ = await gameHelper.blackHole.unlock()
        if (succ) {
            this.showUnlockBlackHole()
        }
        else {
            gameHelper.blackHole.isUnlock = false
            eventCenter.emit(EventType.ADD_PLANET_ITEM, id)
        }
    }

    private getShop() {
        return this.Component(MountPointCmpt).getPoint(NPC_ID.BLACK_HOLE_SHOP)
    }
}