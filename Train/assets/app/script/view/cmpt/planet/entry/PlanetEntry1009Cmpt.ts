import CoreEventType from "../../../../../core/event/CoreEventType";
import { util } from "../../../../../core/utils/Utils";
import { EquipMakeCfg } from "../../../../common/constant/DataType";
import { ConditionType, GuideStepMark, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, PLANET_ITEM_ID, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetEntry1009Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_FIRST_ENTER_PLANET_1009_COME]: this.showFirstEnterCome },
            { [EventType.GUIDE_FIRST_ENTER_PLANET_1009_LEAVE]: this.showFirstEnterLeave },
            { [EventType.USE_PLANET_ITEM]: this.onUsePlanetItem },
            { [EventType.CHECK_USE_PLANET_ITEM]: this.checkUsePlanetItem },
            { [CoreEventType.CLOSE_PNL]: this.onClosePnl, tag: "create" },
            { [EventType.GUDIE_MOVE_TO_ORE]: this.moveToOre },
            { [EventType.GUIDE_SHOW_TOUCH_MOVE]: this.onGuideTouchMove },
        ]
    }

    protected initView() {
        this.initOre()
        this.initEquipMake()
        this.initEquipBuy()
        this.initMakeNpc()
        this.initBuyNpc()
        this.initRole()
        this.initCamera()

        let guide = this.Child("guide")
        guide.active = false
    }

    private initOre() {
        let type = UIFunctionType.PLAY_ORE
        let ore = this.Component(MountPointCmpt).getPoint("ore")
        ore.Child("play_name", PlanetPlayNameCmpt).init(this.model, type, true)
        let sk = ore.Child("sk", sp.Skeleton)
        if (!unlockHelper.isUnlockFunction(UIFunctionType.PLAY_ORE)) {
            sk.playAnimation("jingzhi", true)
        }
        else {
            sk.playAnimation("daiji", true)
        }
        for (let i = 1; i <= 4; i++) {
            this.updateOrePuzzleKey(i)
        }

        let touch = ore.Child("touch")
        touch.off("click")
        touch.on("click", () => {
            if (!unlockHelper.isUnlockFunction(UIFunctionType.PLAY_ORE)) {
                viewHelper.showAlert("unlockFunc_tips_8")
            }
            else {
                viewHelper.showPnl('ore/OreSelectPnl')
            }
        })
    }

    private updateOrePuzzleKey(index: number) {
        let ore = this.Component(MountPointCmpt).getPoint("ore")
        let skNode = ore.Child("sk")
        let node = skNode.getChildByName(String(index))
        node.active = gameHelper.ore.getPuzzleKey(index) || unlockHelper.isUnlockFunction(UIFunctionType.PLAY_ORE)
        if (node.active) {
            node.Component(sp.Skeleton).playAnimation(`jiemi_${index}`, true)
        }
    }

    private initEquipMake() {
        let type = UIFunctionType.EQUIP_MAKE
        let make = this.Component(MountPointCmpt).getPoint("make")
        make.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        make.off("click")
        make.on("click", () => {
            this.onClickMake()
        })
    }

    private initMakeNpc() {
        let role = this.getMakeNpc()
        let speakCmpt = role.Child("body", RoleSpeakCmpt)
        speakCmpt.init(NPC_ID.MAKE_EQUIP_SHOP)

        this.setReddot(role, false)
        let canMake = this.canMake()
        if (canMake && gameHelper.new.isNew(MarkNewType.NPC_DIALOG, [NPC_ID.MAKE_EQUIP_SHOP])) {
            this.setReddot(role, true)
        }

        if (gameHelper.equip.getMadeMaxQuality() > 0) {
            role.setPosition(this.getPos("make_normal"))
        }
        else {
            if (canMake) {
                role.setPosition(this.getPos("make_normal"))
            }
            else {
                role.setPosition(this.getPos("make_door"))
            }
        }

        role.off("click")
        role.on("click", () => {
            if (this.checkClickTaskDialogNpc(role)) return
            this.onClickMake()
        })
    }

    private onClickMake() {
        let role = this.getMakeNpc()
        let speakCmpt = role.Child("body", RoleSpeakCmpt)
        let ores = gameHelper.ore.getOreItemList()
        let talkNode = speakCmpt.getTalkNode()

        if (gameHelper.equip.getMadeMaxQuality() > 0) {
            viewHelper.showPnl("ore/OreMakeDialogPnl")
        }
        else {
            //没矿石
            if (ores.length == 0) {
                let key = cfgHelper.getPlotId("guide_bubble_4004_1")
                if (talkNode) {
                    viewHelper.showSpeechBubble(talkNode.convertToWorldSpaceAR(cc.v2(0, 0)), key)
                }
            }
            else {
                if (this.canMake()) {
                    viewHelper.showPnl("ore/OreMakeDialogPnl")
                    this.setReddot(role, false)
                }
                else {
                    let makeData = assetsMgr.getJson<EquipMakeCfg>('EquipMake').datas.find(d => d.level == 1)
                    let succ = gameHelper.checkConditions(makeData.cost.filter(c => c.type == ConditionType.ORE_ITEM))
                    let key
                    if (!succ) {
                        //矿石不够
                        key = cfgHelper.getPlotId("guide_bubble_4004_2")
                    }
                    else {
                        //星尘不够
                        key = cfgHelper.getPlotId("guide_bubble_4004_3")
                    }

                    if (talkNode) {
                        viewHelper.showSpeechBubble(talkNode.convertToWorldSpaceAR(cc.v2(0, 0)), key)
                    }
                }
            }
        }
    }

    private canMake() {
        let makeData = assetsMgr.getJson<EquipMakeCfg>('EquipMake').datas.find(d => d.level == 1)
        return gameHelper.checkConditions(makeData.cost)
    }

    private initEquipBuy() {
        let type = UIFunctionType.EQUIP_BUY
        let shop = this.getShop()
        shop.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        shop.off("click")
        shop.on("click", () => {
            viewHelper.showPnl('ore/OreEquipBuyPnl')
        })
    }

    private initBuyNpc() {
        let role = this.getBuyNpc()
        let speakCmpt = role.Component(RoleSpeakCmpt)
        speakCmpt.init(NPC_ID.BUY_EQUIP_SHOP)
        let sk = role.Component(sp.Skeleton)

        if (!gameHelper.guide.isStepEnd(GuideStepMark.FIRST_ENTER_PLANET_1009)) {
            role.parent = this.node
            role.setPosition(this.getPos("buy_first"))
            sk.playAnimation("daiji", true)
        }
        else {
            let shop = this.getShop()
            role.parent = shop.Child("role")
            role.setPosition(cc.v2())
            sk.playAnimation("daiji2", true)
        }
    }

    private initRole() {
        let role = this.getRole()
        if (unlockHelper.isUnlockFunction(UIFunctionType.PLAY_ORE)) {
            return
        }
        if (gameHelper.guide.isStepEnd(GuideStepMark.START_ORE_PUZZLE)) {
            role.setPosition(this.getPos("ore"))
        }
    }

    private initCamera() {
        let sv = this.getScrollView()
        if (!gameHelper.guide.isStepEnd(GuideStepMark.START_ORE_PUZZLE)) {
            sv.Component(cc.ScrollView).scrollToLeft(0, false)
        }
    }

    private async showFirstEnterCome() {
        viewHelper.showUI(false)
        let buyNpc = this.getBuyNpc()
        let makeNpc = this.getMakeNpc()
        buyNpc.parent = this.node
        buyNpc.setPosition(this.getPos("buy_first"))
        makeNpc.setPosition(this.getPos("make_door"))

        let buySk = buyNpc.Component(sp.Skeleton)
        let makeSk = makeNpc.Child("body", sp.Skeleton)

        buySk.playAnimation("jingya").then(() => {
            buySk.playAnimation("daiji", true)
        })
        await makeSk.playAnimation("jingya")

        await this.roleMoveTo(makeNpc, this.getPos("make_first"))
        eventCenter.emit(EventType.GUIDE_FIRST_ENTER_PLANET_1009_COME_END)
    }

    private async showFirstEnterLeave() {
        let buyNpc = this.getBuyNpc()
        let makeNpc = this.getMakeNpc()
        let shop = this.getShop()
        let showBuy = async () => {
            await this.roleMoveTo(buyNpc, this.getPos("buy_first_2"))
            ut.convertParent(buyNpc, shop.Child("role"))
            await this.roleMoveTo(buyNpc, cc.v2())
            buyNpc.Component(sp.Skeleton).playAnimation("daiji2", true)
        }
        let showMake = async () => {
            await this.roleMoveTo(makeNpc, this.getPos("make_door"))
        }
        await Promise.all([
            showBuy(),
            showMake(),
        ])
        viewHelper.showUI(true)
        eventCenter.emit(EventType.GUIDE_FIRST_ENTER_PLANET_1009_LEAVE_END)
    }

    private async openDoor() {
        mc.lockTouch(true)
        let ore = this.getOre()
        let sk = ore.Child("sk", sp.Skeleton)
        await sk.playAnimation("jiaohu")
        ore.Child("play_name", PlanetPlayNameCmpt).init(this.model, UIFunctionType.PLAY_ORE)
        sk.playAnimation("daiji", true)
        mc.lockTouch(false)

        viewHelper.showPnl("planet/PlanetPlayUnlockPnl", UIFunctionType.PLAY_ORE)
    }

    private getBuyNpc() {
        return this.Component(MountPointCmpt).getPoint(NPC_ID.BUY_EQUIP_SHOP)
    }

    private getMakeNpc() {
        return this.Component(MountPointCmpt).getPoint(NPC_ID.MAKE_EQUIP_SHOP)
    }

    private getShop() {
        return this.Component(MountPointCmpt).getPoint("shop")
    }

    private getOre() {
        return this.Component(MountPointCmpt).getPoint("ore")
    }


    private getPuzzleIndexById(id: string) {
        let index = 0
        if (id == PLANET_ITEM_ID.ORE_KEY_1) {
            index = 1
        }
        else if (id == PLANET_ITEM_ID.ORE_KEY_2) {
            index = 2
        }
        else if (id == PLANET_ITEM_ID.ORE_KEY_3) {
            index = 3
        }
        else if (id == PLANET_ITEM_ID.ORE_KEY_4) {
            index = 4
        }
        return index
    }

    private checkUsePlanetItem(id: string, screenPos: cc.Vec2) {
        let ore = this.getOre()
        let area = ore.Child("puzzleArea")
        let index = this.getPuzzleIndexById(id)
        if (!index) return false
        let node = area.getChildByName(String(index))
        return node._hitTest(screenPos)
    }

    private async onUsePlanetItem(id: string, screenPos: cc.Vec2) {
        if (!this.checkUsePlanetItem(id, screenPos)) return
        let index = this.getPuzzleIndexById(id)
        gameHelper.ore.addPuzzleKey(index)
        eventCenter.emit(EventType.REMOVE_PLANET_ITEM, id)
        this.updateOrePuzzleKey(index)

        if (gameHelper.ore.canUnlcok()) {
            let succ = await gameHelper.ore.unlock()
            if (succ) {
                this.openDoor()
            }
            else {
                gameHelper.ore.removePuzzleKey(index)
                eventCenter.emit(EventType.ADD_PLANET_ITEM, id)
            }
        }
    }

    private onClosePnl(pnl: mc.BasePnlCtrl) {
        let key = pnl.key
        if (key == "ore/OrePnl") {
            this.initMakeNpc()
        }
    }

    private async moveToOre() {
        viewHelper.showUI(false)
        let role = this.getRole()
        let p1 = ut.wait(1, this)
        let p2 = this.roleMoveTo(role, this.getPos("ore"), 1)
        await Promise.race([p1, p2])
        viewHelper.showUI(true)
        eventCenter.emit(EventType.GUDIE_MOVE_TO_ORE_END)
    }

    private onGuideTouchMove() {
        viewHelper.showUI(false)
        let guide = this.Child("guide")
        guide.active = true

        let content = this.getContent()
        let sv = this.getScrollView()
        let checkEnd = () => {
            let x = content.x
            let maxX = -(content.width - cc.winSize.width) / 2
            if (x <= maxX + 50) {
                guide.active = false
                sv.off("scrolling", checkEnd)
                eventCenter.emit(EventType.GUIDE_TOUCH_MOVE_END)
                viewHelper.showUI(true)
            }
        }
        sv.on("scrolling", checkEnd)
        checkEnd()
    }

    private getScrollView() {
        let content = this.getContent()
        let sv = content.parent.parent
        return sv
    }

    private getContent() {
        let content = this.node.parent
        return content
    }

}