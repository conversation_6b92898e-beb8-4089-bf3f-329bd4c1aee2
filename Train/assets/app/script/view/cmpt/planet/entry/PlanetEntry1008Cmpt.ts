import { Hero<PERSON>ni<PERSON>, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, PLANET_ITEM_ID, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import MountPointCmpt from "../../common/MountPointCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetEntry1008Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.USE_PLANET_ITEM]: this.onUsePlanetItem },
            { [EventType.INSTANCE_COMPLETE_PUZZLE]: this.openDoor },
            { [EventType.CHECK_USE_PLANET_ITEM]: this.checkUsePlanetItem },
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onUnlockFunction },
        ]
    }

    protected initView() {
        this.initInstance()
        this.initBoss()
        this.initLightTable()
    }

    private initInstance() {
        let type = UIFunctionType.PLAY_INSTANCE
        let game = this.getGame()
        game.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        let sk = game.Child("sk", sp.Skeleton)
        if (unlockHelper.isUnlockFunction(type)) {
            sk.playAnimation("daiji", true)
        }
        else {
            sk.playAnimation("jingzhi2", true)
        }
        game.off("click")
        game.on("click", () => {
            if (!unlockHelper.isUnlockFunction(type)) {
                gameHelper.plot.start("guidePlot_key_39_1")
                return
            }
            viewHelper.showPnl("instance/InstanceLevelPnl")
        })
    }

    private initBoss() {
        let boss = this.Component(MountPointCmpt).getPoint("boss")
        boss.off("click")
        boss.on("click", () => {

        })
        boss.active = false
    }

    private initLightTable() {
        let lightTable = this.getLightTable()
        let sk = lightTable.Component(sp.Skeleton)
        sk.playAnimation("daiji1", true)

        let touch = lightTable.Child("touch")
        touch.off("click")
        touch.on("click", () => {
            viewHelper.showAlert("unlockFunc_tips_10")
        })
    }

    private getLightTable() {
        return this.Component(MountPointCmpt).getPoint("light_table")
    }

    private getGame() {
        return this.Component(MountPointCmpt).getPoint("game")
    }

    private checkUsePlanetItem(id: string, screenPos: cc.Vec2) {
        if (id == PLANET_ITEM_ID.INSTANCE_KEY_1) {
            let lightTable = this.getLightTable()
            let node = lightTable.Child("touch")
            return node._hitTest(screenPos)
        }
    }

    private async onUsePlanetItem(id: string, screenPos: cc.Vec2) {
        if (!this.checkUsePlanetItem(id, screenPos)) return
        if (id == PLANET_ITEM_ID.INSTANCE_KEY_1) {
            viewHelper.showPnl("planet/LightTablePuzzlePnl")
        }
    }

    private async openDoor() {
        mc.lockTouch(true)
        let type = UIFunctionType.PLAY_INSTANCE
        let game = this.getGame()
        let sk = game.Child("sk", sp.Skeleton)
        await ut.wait(0.5, this)
        await sk.playAnimation("kaiqi")
        sk.playAnimation("daiji", true)
        await ut.wait(1, this)
        viewHelper.showPnl("planet/PlanetPlayUnlockPnl", UIFunctionType.PLAY_INSTANCE)
        mc.lockTouch(false)
    }

    private onUnlockFunction(type: UIFunctionType) {
        if (type == UIFunctionType.PLAY_INSTANCE) {
            let game = this.getGame()
            game.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        }
    }
}