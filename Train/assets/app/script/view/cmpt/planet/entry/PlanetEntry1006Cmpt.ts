import { util } from "../../../../../core/utils/Utils";
import { LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import FixedNodeTransCmpt from "../../common/FixedNodeTransCmpt";
import MountPointCmpt from "../../common/MountPointCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;


const PLOT_KEY = "guidePlot_key_4001_1"

@ccclass
export default class PlanetEntry1006Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_FIRST_ENTER_PLANET_1006]: this.showFirstEnter },
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onUnlockFunction },
            { [NodeType.GUIDE_BUTTON_TOWER]: () => this.getTower() },
        ]
    }

    protected initView() {
        this.initTower()
        this.initMasterShu()
    }

    private initTower() {
        let type = UIFunctionType.PLAY_TOWER
        let tower = this.getTower()
        tower.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        let touch = tower.Child("touch")

        let normal = tower.getChildByName("normal")
        let broken = tower.getChildByName("broken")
        broken.active = !unlockHelper.isUnlockFunction(UIFunctionType.PLAY_TOWER)
        normal.active = !broken.active

        touch.off("click")
        touch.on("click", () => {
            this.onClickTower()
        })
    }

    @util.addLock
    private async showTower() {
        let tower = this.Component(MountPointCmpt).getPoint("tower")
        let normal = tower.getChildByName("normal")
        let door = normal.Child("door", sp.Skeleton)
        let pnl = 'tower/TowerPnl'
        let p = viewHelper.preloadPnl(pnl)
        let p2 = door.playAnimation("jiaohu")
        await Promise.all([p, p2])
        if (!cc.isValid(this)) return
        viewHelper.showPnl(pnl)
        door.playAnimation("jingzhi", true)
    }

    private getTower() {
        return this.Component(MountPointCmpt).getPoint("tower")
    }

    private initMasterShu() {
        let role = this.getMasterShu()
        let speakCmpt = role.Child("body", RoleSpeakCmpt)
        speakCmpt.init(NPC_ID.MASTER_SHU)
        let sk = role.Child("body", sp.Skeleton)

        this.setReddot(role, false)

        if (!unlockHelper.isUnlockFunction(UIFunctionType.PLAY_TOWER)) {
            role.setPosition(this.getPos("door"))
            if (unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_TOWER)) {
                this.setReddot(role, true)
            }
        }
        else {
            role.setPosition(this.getPos("normal"))
        }

        role.off("click")
        role.on("click", () => {
            if (this.checkClickTaskDialogNpc(role)) return
            this.onClickTower()
        })
    }

    private onClickTower() {
        let role = this.getMasterShu()
        let speakCmpt = role.Child("body", RoleSpeakCmpt)
        let talkNode = speakCmpt.getTalkNode()

        if (!unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_TOWER)) {
            let key = cfgHelper.getPlotId("guide_bubble_4001_2")
            if (talkNode) {
                viewHelper.showSpeechBubble(talkNode.convertToWorldSpaceAR(cc.v2(0, 0)), key)
            }
        }
        else if (!unlockHelper.isUnlockFunction(UIFunctionType.PLAY_TOWER)) {
            eventCenter.emit(EventType.GUIDE_UNLOCK_TOWER)
            let reddot = role.Child("reddot")
            reddot.active = false
        }
        else {
            this.showTower()
        }
    }

    private getMasterShu() {
        return this.Component(MountPointCmpt).getPoint(NPC_ID.MASTER_SHU)
    }

    private async showFirstEnter() {
        let role = this.Component(MountPointCmpt).getPoint("role")
        role.active = true
        let sk = role.getComponent(sp.Skeleton)
        await sk.playAnimation("sp/aniScare")
        sk.playAnimation(PassengerLifeAnimation.IDLE, true)
        eventCenter.emit(EventType.GUIDE_FIRST_ENTER_PLANET_1006_END)
    }

    private async openDoor() {
        mc.lockTouch(true)
        let role = this.getMasterShu()
        await this.roleMoveTo(role, this.getPos("normal"))
        mc.lockTouch(false)
    }

    private onUnlockFunction(type: UIFunctionType) {
        if (type == UIFunctionType.PLAY_TOWER) {
            this.initTower()
            this.openDoor()
        }
    }
}