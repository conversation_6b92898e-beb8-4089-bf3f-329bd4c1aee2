import { Hero<PERSON>ni<PERSON>, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetEntry1001Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
        ]
    }

    private showPnls: string[] = []

    public async preload(model: PlanetModel) {
        super.preload(model)
        if (mc.preWindName == "planetQuestion") { 
            this.showPnls.push("planetQuestion/PlanetQuestionSelectPnl")
        }
        if (this.showPnls.length > 0) {
            await ut.promiseMap(this.showPnls, async (pnl) => {
                await viewHelper.preloadPnl(pnl)
            })
        }
    }

    protected initView() {
        this.initShowPnls()
        this.initProfileBranch()
        this.initCollect()
    }

    private initShowPnls() {
        for (let pnlName of this.showPnls) {
            viewHelper.showPnl(pnlName)
        }
    }

    private initProfileBranch() {
        let node = this.Component(MountPointCmpt).getPoint("profileBranch")
        let type = UIFunctionType.PLAY_ARCHIVES

        let role = this.getDoctor()
        let speakCmpt = role.Component(RoleSpeakCmpt)
        let npcId = 1001
        speakCmpt.init(npcId)

        node.Child("scale/play_name", PlanetPlayNameCmpt).init(this.model, type)
        let touch = node.getChildByName("touch")
        touch.on("click", () => {
            // let key = cfgHelper.getPlotId("guide_bubble_1001_1")
            // let node = speakCmpt.getTalkNode()
            // if (node) {
            //     viewHelper.showSpeechBubble(node.convertToWorldSpaceAR(cc.v2(0, 0)), key)
            // }
            viewHelper.showPnl("planetQuestion/PlanetQuestionSelectPnl")
        })
    }

    private initCollect() {
        let type = UIFunctionType.PLAY_COLLECT
        let collect = this.Component(MountPointCmpt).getPoint("collect")
        collect.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        collect.active = unlockHelper.isUnlockFunction(UIFunctionType.PLAY_DAILY_TASK)
        collect.off("click")
        collect.on("click", () => {
            viewHelper.gotoWind("collect")
        })
    }

    private getProfileBranch() {
        return this.Component(MountPointCmpt).getPoint("profileBranch")
    }

    private getDoctor() {
        return this.getProfileBranch().getChildByName("sk")
    }

    private onFunctionUnlock(type: UIFunctionType) {
    }
}