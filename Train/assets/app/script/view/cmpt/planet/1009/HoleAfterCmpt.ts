import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation, PlanetMineType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { animHelper } from "../../../../common/helper/AnimHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import { localConfig } from "../../../../common/LocalConfig";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetNodeCmpt from "../PlanetNodeCmpt";
import { Hole } from "./HoleBeforeCmpt";

const { ccclass, property } = cc._decorator;

const PLOT_KEY = "guidePlot_key_38_3"

@ccclass
export default class HoleAfterCmpt extends PlanetNodeCmpt  {

    public model: PlanetEmptyNode = null
    private actionTree: ActionTree = null
    private mine: Hole = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.END_PLOT_UI]: this.onEndPlot },
        ]
    }

    init(model, planetCtrl) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        this.mine = new Hole()
        this.mine.setPosition(this.model.position)
        this.node.zIndex = -1

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.mine.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            await action.run(hero.collectAction, this.mine, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await this.model.die()
        await this.playEnd()

        if (!localConfig.openGuide) {
            this.onEnd()
        }
        else {
            await ut.wait(0.5, this)
            gameHelper.plot.start(PLOT_KEY)
        }
    }

    private async playEnd() {
        let rewardsNode = this.node.Child("rewards")
        rewardsNode.active = true
        let recordPosAry = []
        let pList = []
        for (let child of rewardsNode.children) {
            let p = animHelper.playPlanetRewardJump(child, rewardsNode.getPosition(), recordPosAry)
            pList.push(p)
        }
        await Promise.all(pList)

        let playFly = async (node, index)=>{
            let target = eventCenter.get(NodeType.PLANET_ITEM_ICON, index)
            let root = eventCenter.get(NodeType.PLANET_UI_TOPLAYER)
            ut.convertParent(node, root, true)
            let targetPos = ut.convertToNodeAR(target, node.parent, null, null, true)
            return cc.tween(node).to(0.5, {scale: 1, x: targetPos.x, y: targetPos.y }).promise()
        }

        eventCenter.emit(EventType.READY_ADD_PLANET_ITEM, rewardsNode.children.length)

        pList = []
        let nodes = rewardsNode.children.slice()
        for (let i = 0; i < nodes.length; i++) {
            pList.push(playFly(nodes[i], i))
        }
        await Promise.all(pList)
        eventCenter.emit(EventType.ADD_PLANET_ITEM)
    }

    private onEndPlot(id: string) {
        if (id != PLOT_KEY) return
        this.onEnd()
    }

    private async onEnd() {
        this.model.end()
    }

    protected onDeath() {
        //do nothing
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}