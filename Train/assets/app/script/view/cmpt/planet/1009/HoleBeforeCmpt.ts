import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation, PlanetMineType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import { localConfig } from "../../../../common/LocalConfig";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetMineModel from "../../../../model/planet/PlanetMineModel";
import EternalGardenWindCtrl from "../../../eternalGarden/EternalGardenWindCtrl";
import HeroCmpt from "../../hero/HeroCmpt";
import PlanetMineCmpt from "../PlanetMineCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

const MINE_COUNT = 5

const PLOT_KEY = "guidePlot_key_38_1"

export class Hole extends PlanetMineModel {

    public hp: number = MINE_COUNT
    public maxHp: number = MINE_COUNT
    public type: PlanetMineType = PlanetMineType.ORE
    public reachOffset: cc.Vec2 = cc.v2(-220, -25)

    public isLast() { return false }

    public checkShow() { return true }
    public isPassContorl() {return true}

    public hit(damage, damageMul: number = 1) {
        super.hit(1)
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        return true
    }
}

@ccclass
export default class HoleBeforeCmpt extends PlanetNodeCmpt  {

    public model: PlanetEmptyNode = null
    private actionTree: ActionTree = null
    private mine: Hole = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.END_PLOT]: this.onEndPlot },
        ]
    }

    init(model, planetCtrl) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        this.mine = new Hole()
        this.mine.setPosition(this.model.position)

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.mine.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            await action.run(hero.collectAction, this.mine, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await this.model.die()

        if (!localConfig.openGuide) {
            this.onEnd()
        }
        else {
            gameHelper.plot.start(PLOT_KEY)
        }
    }

    protected onDeath(): void {
        this.Child("anim").active = false
    }

    private onEndPlot(id: string) {
        if (id != PLOT_KEY) return
        this.onEnd()
    }

    private onEnd() {
        this.model.end()
        super.onDeath()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}