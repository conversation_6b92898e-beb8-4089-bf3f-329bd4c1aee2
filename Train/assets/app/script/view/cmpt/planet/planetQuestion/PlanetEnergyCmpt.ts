import BaseCmptCtrl from "../../../../../core/base/BaseCmptCtrl";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("星球问答/羽毛")
export default class PlanetEnergyCmpt extends BaseCmptCtrl {

    @property(cc.Label)
    public lb: cc.Label = null

    @property(cc.Node)
    public time: cc.Node = null

    onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_END, () => viewHelper.showPnl("planetQuestion/EnergyInfoPnl"))
    }

    update(dt: number) {
        this.time.active = gameHelper.profileBranch.getEnergy() < gameHelper.profileBranch.getMaxEnergy()
        this.time.Child('lb', cc.Label).string = ut.millisecondFormat(gameHelper.profileBranch.getRecoverTime())
        this.lb.string = `${gameHelper.profileBranch.getEnergy()}/${gameHelper.profileBranch.getMaxEnergy()}`
    }
}