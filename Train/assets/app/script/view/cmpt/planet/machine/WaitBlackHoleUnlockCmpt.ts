import { util } from "../../../../../core/utils/Utils";
import { LangCfgName, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import ActionTree from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

enum State {
    START,
    ON_TARGET,
    ON_MOVE,
}

@ccclass
export default class GetItemCmpt extends PlanetNodeCmpt  {

    public model: PlanetEmptyNode = null

    private actionTree: ActionTree = null

    private state: State = State.START

    private planetCtrl

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        this.planetCtrl = planetCtrl

        if (gameHelper.hero.isTarget(this.model)) {
            this.onTarget(this.model)
        }
    }

    @util.addLock
    private async onTarget(model) {
        if (this.model != model) return
        this.state = State.ON_TARGET

        if (unlockHelper.isUnlockFunction(UIFunctionType.PLAY_BLACKHOLE)) {
            await model.die()
            model.end()
        }
        else {
            let key = cfgHelper.getPlotId("guide_bubble_1005_2")
            viewHelper.showSpeechBubbleByRole(1005, key, -1)
        }
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}