import { LIFT_HEIGHT } from "../../../../common/constant/Constant";
import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import MachinePlanetWindCtrl from "../../../machinePlanet/MachinePlanetWindCtrl";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LiftCmpt extends mc.BaseCmptCtrl  {

    @property(cc.Node)
    private lift: cc.Node = null

    private planetCtrl: MachinePlanetWindCtrl = null

    private orgParent: cc.Node = null

    private actionTree: ActionTree = null

    public listenEventMaps() {
        return [
        ]
    }

    public init(planetCtrl: MachinePlanetWindCtrl) {
        this.planetCtrl = planetCtrl
        let curMap = gameHelper.planet.getCurPlanet().getCurMap()
        this.actionTree = new ActionTree().init(this)
        if (curMap.getId() == 2) {
            this.setDown()
        }
    }

    public async startDown() {
        await this.down()
        await this.exit()
    }

    public async enter() {
        let hero = gameHelper.hero
        let heroNode = this.planetCtrl.getHeroNode()
        this.orgParent = heroNode.parent
        ut.convertParent(heroNode, this.lift.Child("role"))

        hero.setPosition(heroNode.getPosition())

        this.planetCtrl.setCameraTarget(null)
        let cameraCmpt = this.planetCtrl.getCamera()
        let targetPos = cc.v2()
        let dis = targetPos.sub(hero.getPosition()).mag()
        let time = dis / gameHelper.hero.getMoveSpeed()
        cc.tween(cameraCmpt.node).to(time, {x: ut.convertToNodeAR(heroNode.parent, cameraCmpt.node.parent).x}).start()

        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, cc.v2(), hero)
            hero.setAction(null)
            action.ok()
        })
    }

    private async exit() {
        let curMap = gameHelper.planet.getCurPlanet().getCurMap()
        let targetPos = curMap.getStartPos()
        let heroNode = this.planetCtrl.getHeroNode()
        targetPos = ut.convertToNodeAR(this.orgParent, heroNode.parent, targetPos)
        let hero = gameHelper.hero
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, targetPos, hero)
            action.ok()
        })

        ut.convertParent(heroNode, this.orgParent)
        hero.setPosition(heroNode.getPosition())

        hero.startAction()
        this.planetCtrl.focusHero()
    }

    public async down() {
        let time = 3
        await this.playDown(time)
    }

    private setDown() {
        this.lift.y -= LIFT_HEIGHT
    }

    private playDown(time) {
        this.planetCtrl.setCameraTarget(null)
        let cameraCmpt = this.planetCtrl.getCamera()
        let p1 = cc.tween(this.lift).by(time, {y: -LIFT_HEIGHT}, {easing: cc.easing.sineInOut}).promise()
        let p2 = cc.tween(cameraCmpt.node).to(time, {y: 0}, {easing: cc.easing.sineInOut}).promise()
        return Promise.all([p1, p2])
    }

    update(dt) {
        this.actionTree && this.actionTree.update(dt)
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

}