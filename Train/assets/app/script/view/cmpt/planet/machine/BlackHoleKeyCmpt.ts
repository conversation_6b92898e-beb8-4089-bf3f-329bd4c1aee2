import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import GetItemCmpt from "../GetItemCmpt";

const { ccclass, property } = cc._decorator;


@ccclass
export default class BlackHoleKeyCmpt extends GetItemCmpt  {

    public async onClick() {
        let succ = await super.onClick()
        if (!succ) return

        let model = this.model
        await model.die()
   
        let p
        if (gameHelper.openGuide) {
            p = eventCenter.wait(EventType.END_PLOT)
        }
        else {
            p = eventCenter.wait(EventType.ADD_PLANET_ITEM)
        }
        p.then(()=>{
            model.end()
        })
   
        viewHelper.showPnl("planet/ItemUnlockPnl", {scale: 2, icon: "yd_daoju_2", offsetX: -14, name: "name_planetItem_black_hole_key"})
        return true
    }
}