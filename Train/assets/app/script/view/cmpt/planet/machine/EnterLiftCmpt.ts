import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import MachinePlanetWindCtrl from "../../../machinePlanet/MachinePlanetWindCtrl";
import PlanetNodeCmpt from "../PlanetNodeCmpt";
import LiftCmpt from "./LiftCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EnterLiftCmpt extends PlanetNodeCmpt  {

    public model: PlanetEmptyNode = null

    private actionTree: ActionTree = null

    private planetCtrl: MachinePlanetWindCtrl = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.actionTree = new ActionTree().init(this)
    }

    private async onTarget(model) {
        if (this.model != model) return
        this.enter()
    }

    private async enter() {
        let hero = gameHelper.hero
        let transNode = this.planetCtrl.getTransNode()
        let cmpt = transNode.Component(LiftCmpt)
        await cmpt.enter()
        if (!cc.isValid(this)) return
        await this.model.die()
        if (!cc.isValid(this)) return
        this.model.end()

        cmpt.startDown()

        gameHelper.planet.getCurPlanet().updateMap()
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}