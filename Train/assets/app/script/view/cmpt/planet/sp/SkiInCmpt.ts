import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetSkiIn from "../../../../model/planet/sp/PlanetSkiIn";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class SkiInCmpt extends PlanetNodeCmpt {

    public model: PlanetSkiIn = null
    private actionTree: ActionTree = null
    private endCb: Function = null
    private heroNode: cc.Node = null
    private mapNode: cc.Node = null
    private camera: cc.Camera = null
    private planetCtrl: any = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        this.planetCtrl = planetCtrl
        this.heroNode = planetCtrl.heroNode_
        this.mapNode = planetCtrl.mapNode_
        this.camera = planetCtrl.camera
        this.actionTree = new ActionTree().init(this)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt: number) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model: PlanetSkiIn) {
        if (this.model != model) return

        let hero = gameHelper.hero
        const waitEnd = new Promise((r) => this.endCb = r)

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.model.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await waitEnd

        // 移动到洞口
        model.reachOffset = model.hmOffset
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, model.reachPosition, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        // 透明
        cc.Tween.stopAllByTarget(this.heroNode)
        await cc.tween(this.heroNode).to(.2, { opacity: 0 }).start().promise()

        // 层级
        const role = this.node.Child("role")
        this.heroNode.parent = role
        this.heroNode.setPosition(new cc.Vec2(150, 0))
        this.heroNode.opacity = 255

        cc.Tween.stopAllByTarget(this.heroNode)
        await this.performBezierJump()
        const target = ut.convertToNodeAR(this.heroNode, this.mapNode)
        this.heroNode.parent = this.mapNode
        gameHelper.hero.setPosition(target)

        await this.model.die()
        this.model.end()
    }

    /**
     * 贝塞尔曲线跳跃动画
     */
    private async performBezierJump() {
        const startPos = this.heroNode.getPosition()
        // 跳跃高度
        const jumpHeight = 250
        // 跳跃距离
        const jumpDistance = 1150
        // 跳跃持续时间
        const jumpDuration = 1
        // 终点位置
        const endPos = cc.v2(startPos.x + jumpDistance, startPos.y - 300)
        // 控制点
        const controlPoint1 = cc.v2(startPos.x + jumpDistance * 0.25, startPos.y + jumpHeight)
        const controlPoint2 = cc.v2(startPos.x + jumpDistance * 0.75, startPos.y + jumpHeight * 0.6)
        gameHelper.hero.setAction(HeroAction.SKI_ING)
        this.planetCtrl.setCameraTarget({
            posFunc: () => {
                let heroWorldPos = this.heroNode.parent.convertToWorldSpaceAR(this.heroNode.getPosition())
                let cameraParentPos = this.camera.node.parent.convertToNodeSpaceAR(heroWorldPos)
                return cc.v2(cameraParentPos.x, this.camera.node.y)
            },
            lerp: false
        })

        await cc.tween(this.heroNode)
            .bezierTo(jumpDuration, controlPoint1, controlPoint2, endPos)
            .start()
            .promise()
        gameHelper.hero.setPosition(this.heroNode.getPosition())
        this.planetCtrl.focusHero()
    }

    public async onClick() {
        cc.Tween.stopAllByTarget(this.touchNode)
        const orgSpeed = gameHelper.hero.getMoveSpeed()
        gameHelper.hero.setMoveSpeed(orgSpeed * 2)
        await cc.tween(this.touchNode).to(.2, { opacity: 0 }).start().promise()
        this.endCb?.()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

}