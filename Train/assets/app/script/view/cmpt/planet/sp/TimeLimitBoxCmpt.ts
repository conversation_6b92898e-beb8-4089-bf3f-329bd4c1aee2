import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation, PlanetMineType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { anim<PERSON>elper } from "../../../../common/helper/AnimHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ConditionObj from "../../../../model/common/ConditionObj";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetMineModel from "../../../../model/planet/PlanetMineModel";
import PlanetTimeLimitBox, { PlanetTimeLimitBoxState } from "../../../../model/planet/sp/PlanetTimeLimitBox";
import PlanetWindCtrl from "../../../planet/PlanetWindCtrl";
import PlanetMineCmpt from "../PlanetMineCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";
import PlanetNodeRewardCmpt from "../PlanetNodeRewardCmpt";
import PlanetNodeRewardGroupCmpt from "../PlanetNodeRewardGroupCmpt";

const { ccclass, property } = cc._decorator;


const HP = 2
const MAX_REWARD_NUM = 20

class Mine extends PlanetMineModel {

    public hp: number = HP
    public maxHp: number = HP
    public type: PlanetMineType = PlanetMineType.ORE
    public hitCb: Function = null

    public isLast() { return false }

    public checkShow() { return true }
    public isPassContorl() { return true }

    public hit(damage, damageMul: number = 1) {
        this.hitCb && this.hitCb()
        eventCenter.emit(EventType.PLANET_MINE_HP_CHANGE, this)
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        return true
    }
}

class MineCmpt extends PlanetMineCmpt {

    protected body: any = null
    private isOpen: boolean = false

    public cb: Function = null

    public onCreate(): void {
        super.onCreate()
        this.body = this.Child('body', sp.Skeleton)
    }

    public init(model: PlanetMineModel) {
        super.init(model)

        if (model.hp < model.maxHp) {
            this.playOpen(false)
        }
    }

    protected onEnterCollect(model) {
        if (this.model != model) return
        this.selected.active = false
    }

    protected initPos() {
        //do nothing
    }

    protected async initIcon() {
        //do nothing
    }

    protected onChangHp(model: PlanetMineModel, hp: number, damageMul: number = 1, isAuto = false) {
        if (this.model != model) return
        this.onHit()
        if (!this.isOpen) {
            this.playOpen(true)
        }
    }

    public async playOpen(showAnim) {
        this.isOpen = true
        this.ui.active = false
        if (showAnim) {
            await this.body.playAnimation("on")
        }
        this.body.playAnimation("on_idle", true)
    }
}

@ccclass
export default class TimeLimitBoxCmpt extends PlanetNodeCmpt {

    public model: PlanetTimeLimitBox = null
    private actionTree: ActionTree = null
    private mine: Mine = null
    private rewardNodes: cc.Node[] = []
    private timerNode: cc.Node = null
    private planetCtrl: PlanetWindCtrl = null
    private recordPosAry: cc.Vec2[] = []

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    init(model: PlanetTimeLimitBox, planetCtrl) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        this.planetCtrl = planetCtrl
        this.timerNode = this.Child("timer")
        this.timerNode.active = this.model.getState() != PlanetTimeLimitBoxState.END
        this.initRewards()
        this.initMine()
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    private initMine() {
        let mine = new Mine()
        this.mine = mine
        mine.reachOffset = this.model.reachOffset
        let state = this.model.getState()
        if (state == PlanetTimeLimitBoxState.START) {
            mine.hp = 1
        }
        else if (state == PlanetTimeLimitBoxState.END) {
            mine.die()
        }

        mine.setPosition(this.model.position)
        mine.hitCb = ()=>{
            this.onHit()
        }

        let mineNode = this.Child("mine")
        let cmpt = mineNode.addComponent(MineCmpt)
        cmpt.init(this.mine)
    }

    private onHit() {
        if (this.model.getState() == PlanetTimeLimitBoxState.READY) {
            this.model.start()
            return
        }
        let cfg = this.model.json.diyReward[0]
        if (cfg) {
            let reward = gameHelper.toCondition(cfg)
            this.showReward(reward)
        }
        this.model.clicks++
    }

    private initRewards() {
        let cond = this.model.diyRewards[0]
        if (cond) {
            let num = Math.min(MAX_REWARD_NUM, cond.num)
            for (let i = 0; i < num; i++) {
                this.showReward(cond)
            }
        }
    }

    private showReward(reward: ConditionObj) {
        let planetCtrl = this.planetCtrl
        let center = this.model.position
        let gen = (reward) => {
            //@ts-ignore
            let node = cc.instantiate2(planetCtrl.nodeRewardNode_, planetCtrl.mapNode_)
            let cmpt = node.Component(PlanetNodeRewardCmpt)
            cmpt.init(reward)
            animHelper.playPlanetRewardJump(node, center, this.recordPosAry, { maxa: 200 }).then(()=>{
                cmpt.onDropEnd()
            })
            return node
        }
        let cond = new ConditionObj().init(reward)
        let node = gen(cond)
        this.rewardNodes.push(node)
    }

    private async fly() {
        let items: cc.Node[] = this.rewardNodes
        let go = (items: cc.Node[]) => {
            if (items.length <= 0) return
            let node = items[0]
            let cmpt = node.addComponent(PlanetNodeRewardGroupCmpt)
            return new Promise((r) => {
                cmpt.init(items, r, this.model)
            })
        }

        go(items)
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)

        if (!this.model) return

        if (this.model.getState() == PlanetTimeLimitBoxState.START) {
            this.updateTimer()
        }
        if (this.model.getState() == PlanetTimeLimitBoxState.END) {
            this.mine.die()
            gameHelper.hero.collectEnd()
        }
    }

    private updateTimer() {
        let surplusTime = this.model.getSurplusTime()
        let timerNode = this.timerNode
        let bgNode = timerNode.Child("bg")
        let root = bgNode.Component(sp.Skeleton).getAttachedNode("guadian")
        let pointer = root.Child("pointer")
        let progress = root.Child("progress", cc.Sprite)
        let totTime = this.model.json.time * ut.Time.Second
        let percent = Math.min(1, 1 - (surplusTime / totTime))
        pointer.angle = -360 * percent
        progress.fillRange = 1 - percent
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero

        if (this.timerNode.active) {
            let sk = this.timerNode.Child("bg", sp.Skeleton)
            let p = new Promise(r => r(true))
            if (this.model.getState() == PlanetTimeLimitBoxState.READY) {
                p = sk.playAnimation("enter")
            }
            p.then(()=>{
                sk.playAnimation("idle", true)
            })
        }
 

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.mine.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            await action.run(hero.collectAction, this.mine, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        this.onEnd()
    }

    private async onEnd() {
        cc.tween(this.timerNode).to(0.2, { opacity: 0 }).start()
        let model = this.model
        await Promise.all([
            model.die(),
            ut.wait(1),
        ])
        if (cc.isValid(this)) {
            this.fly()
            eventCenter.emit(EventType.CLAIM_MINE_REWARD_START, model)
            super.onDeath()
        }
        model.end()
    }

    protected onDeath(): void {
        //do nothing
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}