import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation, PlanetMineType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetRandomBox from "../../../../model/planet/sp/PlanetRandomBox";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;


enum State {
    CLICK,
    END,
}

@ccclass
export default class ToolBlessCmpt extends PlanetNodeCmpt {

    public model: PlanetRandomBox = null
    private actionTree: ActionTree = null

    private state: State = State.CLICK

    private endCb: Function = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    init(model, planetCtrl) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero
        
        let waitEnd = new Promise((r)=>{
            this.endCb = r
        })

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.model.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await waitEnd

        this.onEnd()
    }

    public async onClick() {
        if (!this.endCb) return
        if (this.state != State.CLICK) return
        this.state = State.END

        let succ = await this.model.syncDie()
        if (!succ) return
        if (!cc.isValid(this)) return

        await this.playAnim()
        this.endCb()
    }

    private async playAnim() {
        let body = this.Child("body")
        body.Component(sp.Skeleton).playAnimation("idle_2", true)

        let curNode = this.Child("icon")
        curNode.active = true
        let targetNode = eventCenter.get(NodeType.PLANET_UI_BLESS_ICON)
        let root = eventCenter.get(NodeType.PLANET_UI_TOPLAYER)
        ut.convertParent(curNode, root, true)
        let targetPos = ut.convertToNodeAR(targetNode, curNode.parent)
        let targetScale = 0.5
        await cc.tween(curNode).to(0.5, { scaleX: -targetScale, scaleY: targetScale, x: targetPos.x, y: targetPos.y }).promise()
        curNode.destroy()
    }

    private async onEnd() {
        let model = this.model
        await model.die()
        model.end()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}