import BuildCmpt from "./BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class FloorCmpt extends BuildCmpt {

    @property(cc.ParticleSystem)
    private particle: cc.ParticleSystem = null

    protected onBuildAnimStart() {
        super.onBuildAnimStart()
        this.particle.node.active = true
        this.particle.resetSystem()
    }

    protected onBuildAnimEnd() {
        super.onBuildAnimEnd()
        this.particle.node.active = false
        this.particle.stopSystem()
    }
}
