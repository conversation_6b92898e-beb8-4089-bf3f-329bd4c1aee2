import Dorm3<PERSON>hakeBaseCmpt from "./Dorm3ShakeBaseCmpt";
import Dorm3ShakeBearCmpt from "./Dorm3ShakeBearCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm3ShakeBedCmpt extends Dorm3ShakeBaseCmpt {

    @property(cc.Node)
    private sleep: cc.Node = null

    protected checkBoolShake() {
        this.needShake = this.sleep.childrenCount > 0
    }
    protected checkRunShake() {
        this.runShake(this.checkBear())
    }
    private checkBear() {
        let ary = this.carriageCmpt.builds
        let node = ary.find(b => b.getComponent(Dorm3ShakeBearCmpt)?.isShake)
        return node != null
    }
}
