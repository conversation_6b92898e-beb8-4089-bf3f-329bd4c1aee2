import { BuildAnimation } from "../../../../common/constant/Enums";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm3ShakeBaseCmpt extends BuildCmpt {

    @property(sp.Skeleton)
    protected body: sp.Skeleton = null
    public isShake: boolean = false
    public needShake: boolean = false

    protected getIdleAnim() {
        return BuildAnimation.JINGZHI
    }
    protected updateBState(sk: sp.Skeleton) {
        this.checkBoolShake()
        this.checkRunShake()
    }
    protected checkBoolShake() {
    }
    protected checkRunShake() {
    }
    public runShake(bol: boolean) {
        if (this.isShake == bol) return
        this.isShake = bol
        if (bol) {
            this.body.playAnimation(BuildAnimation.IDLE, true)
        } else {
            this.body.playAnimation(BuildAnimation.JINGZHI)
        }
    }
}
