import Dorm3ShakeBaseCmpt from "./Dorm3ShakeBaseCmpt";
import Dorm3ShakeBedCmpt from "./Dorm3ShakeBedCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm3ShakeBearCmpt extends Dorm3ShakeBaseCmpt {

    protected checkBoolShake() {
        this.needShake = this.checkBed()
    }
    protected checkRunShake() {
        this.runShake(this.needShake)
    }
    private checkBed() {
        let ary = this.carriageCmpt.builds
        let node = ary.find(b => b.getComponent(Dorm3ShakeBedCmpt)?.needShake)
        return node != null
    }
}
