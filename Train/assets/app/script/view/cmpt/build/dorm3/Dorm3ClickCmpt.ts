import { BuildAnimation } from "../../../../common/constant/Enums";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm3ClickCmpt extends BuildCmpt {
    @property(sp.Skeleton)
    private body: sp.Skeleton = null
    @property(cc.Button)
    private btn: cc.Button = null
    @property([cc.Node])
    public pointCollider: cc.Node[] = []//挂点跟碰撞体的映射

    private async onClickBody() {
        if (this.body.animation == BuildAnimation.BUILD) return
        this.btn.interactable = false
        await this.body.playAnimation("dianji")
        this.btn.interactable = true
    }

    update() {
        let ary = this.pointCollider
        let max = ary.length
        for (let i = 0; i < max; i += 2) {
            let point = ary[i]
            let collider = ary[i + 1]
            collider.active = point.childrenCount > 0
        }
    }
}
