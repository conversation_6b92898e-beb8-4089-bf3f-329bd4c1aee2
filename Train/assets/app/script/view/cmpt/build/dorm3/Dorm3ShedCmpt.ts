/**
 * 棚(分为两前后部分)
 */
import StateObj from "../../../../model/passenger/StateObj";
import Dorm3ShedObj, { Dorm3ShedObjState } from "../../../../model/train/dorm3/Dorm3ShedObj";
import BuildAfterFrontCmpt from "../BuildAfterFrontCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm3ShedCmpt extends BuildAfterFrontCmpt {
    public model: Dorm3ShedObj = null
    private preState: StateObj<Dorm3ShedObjState> = null
    protected buildAfter = 'aniBuild1'
    private jingzhiAfter = 'jingzhi3'
    private jingzhiFront = 'jingzhi2'

    protected play2Quiet(): boolean {
        this.playNodeAnimation(this.after, this.jingzhiAfter)
        this.playNodeAnimation(this.front, this.jingzhiFront)
        return false
    }

    update() {
        if (!this.model) return
        let sk = this.front.Component(sp.Skeleton)
        if (sk.animation == this.buildFront) return

        let state = this.model.state
        if (this.preState == state) return
        this.preState = state
        this.setState(sk, state)
    }
    private setState(sk: sp.Skeleton, state: StateObj<Dorm3ShedObjState>) {
        let type = state?.type
        let data = state?.data
        if (type == Dorm3ShedObjState.KAI) {
            sk.playAnimation('kai', false, data.timeData.elapsed)
        } else if (type == Dorm3ShedObjState.GUAN) {
            sk.playAnimation('guan', false, data.timeData.elapsed)
        } else {
            sk.playAnimation(this.jingzhiFront)
        }
    }
}
