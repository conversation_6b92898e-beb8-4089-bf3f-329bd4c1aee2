import { gameHelper } from "../../../../common/helper/GameHelper";
import BuildCmpt from "../BuildCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class Dorm4MagicMirrorCmpt extends BuildCmpt {
    protected playQuiet(sk: sp.Skeleton) {
        this.randomIdle(sk)
    }
    private async randomIdle(sk: sp.Skeleton) {
        let name = this.randomName()
        if (sk.findAnimation(name)) {
            await sk.playAnimation(name)
        }
        this.randomIdle(sk)
    }
    private randomName() {
        let cfgs = [
            { name: "aniIdle", weight: 25 },
            { name: "aniIdle2", weight: 75 },
        ]
        let index = gameHelper.randomByWeight(cfgs)
        return cfgs[index].name
    }
}
