import { MAX_ZINDEX } from "../../../common/constant/Constant";
import BuildCmpt from "./BuildCmpt";
import CarriageLightCmpt from "../train/CarriageLightCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BuildAfterFrontCmpt extends BuildCmpt {
    @property(cc.Node)
    protected after: cc.Node = null
    @property(cc.Node)
    protected front: cc.Node = null
    protected buildAfter = 'aniBuild'
    protected buildFront = 'aniBuild2'

    start() {
        this.setFront()
    }
    onDestroy() {
        this.front.destroy()
    }
    protected initEditView() {
        if (!this.play2Quiet()) return
        this.skPaused(this.after.Component(sp.Skeleton))
        this.skPaused(this.front.Component(sp.Skeleton))
    }
    protected pauseLight() {
        super.pauseLight()
        this.front.Component(CarriageLightCmpt)?.pause()
    }
    protected resumeLight() {
        super.resumeLight()
        this.front.Component(CarriageLightCmpt)?.resume()
    }
    public playFlash(fboPrefab: cc.Node) {
        this.addFlash(fboPrefab, this.node)
        this.addFlash(fboPrefab, this.front)
    }
    protected setNodeActive(bol: boolean) {
        this.node.active = bol
        this.front.active = bol
    }
    protected async playBuildAnimation(sk: sp.Skeleton) {
        let p1 = this.playNodeAnimation(this.after, this.buildAfter)
        let p2 = this.playNodeAnimation(this.front, this.buildFront)
        await Promise.all([p1, p2])
    }
    protected async playRemoveAnimation(sk: sp.Skeleton) {
        this.hidePoints()
        this.setWhenPlayRemove()
        let p1 = this.playNodeBackAnimation(this.after, this.buildAfter)
        let p2 = this.playNodeBackAnimation(this.front, this.buildFront)
        await Promise.all([p1, p2])
    }
    protected playQuiet(sk: sp.Skeleton) {
        this.play2Quiet()
    }
    protected play2Quiet(): boolean {
        this.playNodeAnimation(this.after, 'aniIdle', true)
        this.playNodeAnimation(this.front, 'aniIdle2', true)
        return true
    }
    protected play2Jingzhi() {
        this.playNodeAnimation(this.after, 'jingzhi')
        this.playNodeAnimation(this.front, 'jingzhi2')
    }
    protected setFront() {
        let node = this.node
        let front = this.front
        front.x = node.x
        front.y = node.y
        front.name = node.name + '_front'
        front.parent = node.parent
        front.zIndex = this.calculateZIndex()
    }
    private calculateZIndex() {
        let node = this.node.Child('zIndex')
        return MAX_ZINDEX - this.node.y - node.y
    }
}
