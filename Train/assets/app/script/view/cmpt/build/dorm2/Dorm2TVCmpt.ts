import StateObj from "../../../../model/passenger/StateObj";
import Dorm2TVObj, { Dorm2TVObjState } from "../../../../model/train/dorm2/Dorm2TVObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm2TVCmpt extends BuildCmpt {

    public model: Dorm2TVObj = null
    private preState: StateObj<Dorm2TVObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        if (type == Dorm2TVObjState.ON) {
            sk.playAnimation("animation", true)
        } else {
            sk.playAnimation("jingzhi", true)
        }
    }
}
