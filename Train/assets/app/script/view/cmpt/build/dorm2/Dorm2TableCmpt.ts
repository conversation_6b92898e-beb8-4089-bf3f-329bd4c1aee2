import { resHelper } from "../../../../common/helper/ResHelper";
import StateObj from "../../../../model/passenger/StateObj";
import Dorm2TableObj, { Dorm2TableObjState } from "../../../../model/train/dorm2/Dorm2TableObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm2TableCmpt extends BuildCmpt {

    public model: Dorm2TableObj = null
    private preState: StateObj<Dorm2TableObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        if (type == Dorm2TableObjState.INTO) {
            sk.playAnimation("jingzhi")
        }
        else if (type == Dorm2TableObjState.OUT) {
            sk.playAnimation("jingzhi2")
        }
        else {
            sk.playAnimation("jingzhi", true)
        }
        this.updateFood()
    }

    private async updateFood() {
        let foods = this.model.foods
        let tag = this.getTag()
        for (let i = 0; i < foods.length; i++) {
            let url = foods[i]
            let sp = this.Child(`food${i + 1}`, cc.Sprite)
            if (!url) {
                resHelper.releaseSpriteSpf(sp)
            }
            else {
                await resHelper.loadTmpIcon(url, sp, tag)
                if (!foods[i]) {
                    return resHelper.releaseSpriteSpf(sp)
                }
            }
        }
    }
}
