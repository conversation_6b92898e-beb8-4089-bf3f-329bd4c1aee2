import StateObj from "../../../../model/passenger/StateObj";
import Dorm2HearthObj, { Dorm2HearthState } from "../../../../model/train/dorm2/Dorm2HearthObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Dorm2HearthCmpt extends BuildCmpt {

    public model: Dorm2HearthObj = null
    private preState: StateObj<Dorm2HearthState> = null

    private food: sp.Skeleton = null

    onLoad() {
        this.food = this.Child("food", sp.Skeleton)
    }

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let data = state?.data
        let timeData = data?.timeData
        let type = state?.type
        if (!state) {
            this.food.node.active = false
            sk.playAnimation("jingzhi")
        }
        else if (type == Dorm2HearthState.READY_FOOD) {
            let elapsed = timeData.elapsed
            this.playAnimation(this.food, "animation", elapsed)
            this.playAnimation(sk, "animation", elapsed)
        }
        else if (type == Dorm2HearthState.CUT_FOOD) {
            this.playCutFood(data)
        }
        else if (type == Dorm2HearthState.WAIT_COOK) {
            this.food.playAnimation("jingzhi3")
            sk.playAnimation("jingzhi3")
        }
        else if (type == Dorm2HearthState.COOK) {
            this.playCook(data)
        }
        else if (type == Dorm2HearthState.DONE) {
            this.food.playAnimation("animation6", true)
            sk.playAnimation("animation6", true)
        }

        if (state && this.model.foodIndex) {
            this.food.setSkin(`food${this.model.foodIndex}`)
        }
    }

    private async playCutFood(data) {
        let sk = this.node.Child('body', sp.Skeleton)
        let elapsed = data.timeData.elapsed
        this.playAnimation(this.food, "animation2", elapsed)
        elapsed = await this.playAnimation(sk, "animation2", elapsed)

        this.playAnimation(this.food, "animation3", elapsed)
        elapsed = await this.playAnimation(sk, "animation3", elapsed)
    }

    private async playCook(data) {
        let sk = this.node.Child('body', sp.Skeleton)
        let elapsed = data.timeData.elapsed
        this.playAnimation(this.food, "animation4", elapsed)
        elapsed = await this.playAnimation(sk, "animation4", elapsed)

        this.playAnimation(this.food, "animation5", elapsed, true)
        elapsed = await this.playAnimation(sk, "animation5", elapsed, true)
    }
}
