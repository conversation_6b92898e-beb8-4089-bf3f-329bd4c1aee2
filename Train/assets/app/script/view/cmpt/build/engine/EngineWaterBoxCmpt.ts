import StateObj from "../../../../model/passenger/StateObj";
import EngineWaterBoxObj, { EngineWaterBoxObjState } from "../../../../model/train/engine/EngineWaterBoxObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EngineWaterBoxCmpt extends BuildCmpt {
    public model: EngineWaterBoxObj = null
    private preState: StateObj<EngineWaterBoxObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == EngineWaterBoxObjState.OUT) {
            this.playOut(data)
        }
        else if (type == EngineWaterBoxObjState.INTO) {
            this.playInto(data)
        }
        else {
            sk.playAnimation("aniIdle", true)
        }
    }

    private async playInto(data) {
        let elapsed = (Date.now() - data.time) / ut.Time.Second
        let sk = this.node.Child('body', sp.Skeleton)
        elapsed = await this.playAnimation(sk, "into", elapsed, false)
        if (this.model.state?.type != EngineWaterBoxObjState.INTO) return
        this.playAnimation(sk, "aniIdle2", elapsed, true)
    }

    private async playOut(data) {
        let elapsed = (Date.now() - data.time) / ut.Time.Second
        let sk = this.node.Child('body', sp.Skeleton)
        elapsed = await this.playAnimation(sk, "out", elapsed, false)
        if (this.model.state?.type != EngineWaterBoxObjState.OUT) return
        this.playAnimation(sk, "aniIdle", elapsed, true)
    }
}
