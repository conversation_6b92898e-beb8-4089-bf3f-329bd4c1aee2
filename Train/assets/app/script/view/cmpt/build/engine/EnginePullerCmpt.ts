import StateObj from "../../../../model/passenger/StateObj";
import EnginePullerObj, { EnginePullerObjState } from "../../../../model/train/engine/EnginePullerObj";
import BuildQuietCmpt from "../BuildQuietCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EnginePullerCmpt extends BuildQuietCmpt {
    @property(sp.Skeleton)
    private mask: sp.Skeleton = null

    public model: EnginePullerObj = null
    private preState: StateObj<EnginePullerObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        if (type == EnginePullerObjState.ON) {
            sk.playAnimation("aniIdle", true)
            this.mask.node.active = true
            this.mask.playAnimation('aniIdle2', true)
        } else if (type == EnginePullerObjState.OFF) {
            sk.playAnimation("jingzhi")
            this.mask.node.active = false
        } else {
            sk.playAnimation("jingzhi", true)
            this.mask.node.active = false
        }
    }
}
