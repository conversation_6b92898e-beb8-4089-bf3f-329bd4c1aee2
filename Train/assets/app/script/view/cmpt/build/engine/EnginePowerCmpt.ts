import { BuildAnimation } from "../../../../common/constant/Enums";
import StateObj from "../../../../model/passenger/StateObj";
import EnginePowerObj, { EnginePowerObjState } from "../../../../model/train/engine/EnginePowerObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EnginePowerCmpt extends BuildCmpt {
    @property(sp.Skeleton)
    private body: sp.Skeleton = null
    @property(cc.Node)
    private lizi: cc.Node = null

    public model: EnginePowerObj = null
    private preState: StateObj<EnginePowerObjState> = null

    protected initEditView() {
        this.body.playAnimation(BuildAnimation.JINGZHI)
    }

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        this.lizi.active = false
        if (type == EnginePowerObjState.IDLE) {
            this.playIdle(data)
        } else if (type == EnginePowerObjState.START) {
            this.playStart(data)
        } else if (type == EnginePowerObjState.WORK) {
            this.playWork(data)
        } else if (type == EnginePowerObjState.OUTPUT) {
            this.playOutput(data)
        } else {
            this.body.playAnimation(BuildAnimation.IDLE)
        }
    }

    private async playIdle(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed, true)
    }

    private async playStart(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed)
    }

    private async playWork(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed, true)

        let surplusTime = timeData.getSurplusTime() - 1
        if (surplusTime > 0) {
            this.lizi.active = true
            this.lizi.Component(cc.ParticleSystem).resetSystem()
            await ut.wait(surplusTime, this)
            let state = this.model.state
            if (state?.type == EnginePowerObjState.WORK) {
                this.lizi.Component(cc.ParticleSystem).stopSystem()
            }
        }
    }

    private async playOutput(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.playAnimation(this.body, data.anim, elapsed)
    }
}
