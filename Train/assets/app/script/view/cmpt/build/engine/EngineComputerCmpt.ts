import StateObj from "../../../../model/passenger/StateObj";
import EngineComputerObj, { EngineComputerObjState } from "../../../../model/train/engine/EngineComputerObj";
import BuildQuietCmpt from "../BuildQuietCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EngineComputerCmpt extends BuildQuietCmpt {
    public model: EngineComputerObj = null
    private preState: StateObj<EngineComputerObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        if (type == EngineComputerObjState.ON) {
            sk.playAnimation("aniIdle", true)
        } else if (type == EngineComputerObjState.OFF) {
            sk.playAnimation("jingzhi")
        } else {
            sk.playAnimation("jingzhi", true)
        }
    }
}
