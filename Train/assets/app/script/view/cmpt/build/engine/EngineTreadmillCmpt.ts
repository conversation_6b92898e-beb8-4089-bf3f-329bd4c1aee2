import StateObj from "../../../../model/passenger/StateObj";
import EngineTreadmillObj, { EngineTreadmillObjState } from "../../../../model/train/engine/EngineTreadmillObj";
import BuildQuietCmpt from "../BuildQuietCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class EngineTreadmillCmpt extends BuildQuietCmpt {

    public model: EngineTreadmillObj = null
    private preState: StateObj<EngineTreadmillObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        if (type == EngineTreadmillObjState.ON) {
            sk.playAnimation("aniIdle", true)
        } else {
            sk.playAnimation("jingzhi", true)
        }
    }
}
