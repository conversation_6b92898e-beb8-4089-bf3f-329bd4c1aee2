import { BuildAnimation } from "../../../../common/constant/Enums";
import StateObj from "../../../../model/passenger/StateObj";
import EngineEnergyObj, { EngineEnergyState } from "../../../../model/train/engine/EngineEnergyObj";
import BuildQuietCmpt from "../BuildQuietCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class EngineEnergyCmpt extends BuildQuietCmpt {

    public model: EngineEnergyObj = null
    private preState: StateObj<EngineEnergyState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == EngineEnergyState.ON) {
            this.playStar(data)
        } else if (type == EngineEnergyState.OFF) {
            this.playEnd(data)
        } else {
            sk.playAnimation("jingzhi", true)
        }
    }
    public async playStar(data) {
        let elapsed = (Date.now() - data.time) / ut.Time.Second
        let sk = this.node.Child('body', sp.Skeleton)
        elapsed = await this.playAnimation(sk, "star", elapsed, false)
        let count = 1000
        let state = this.model.state
        while (count--) {
            if (this.model.state != state) return
            let rand = ut.random(2, 4)
            let time = sk.getAnimationDuration(BuildAnimation.IDLE) + sk.getAnimationDuration(BuildAnimation.IDLE + rand)
            if (time <= 0) return
            elapsed %= time
            await this.playAnimation(sk, BuildAnimation.IDLE, elapsed, false)
            if (this.model.state != state) return
            await this.playAnimation(sk, BuildAnimation.IDLE + rand, elapsed, false)
        }
    }
    public async playEnd(data) {
        let elapsed = (Date.now() - data.time) / ut.Time.Second
        let sk = this.node.Child('body', sp.Skeleton)
        elapsed = await this.playAnimation(sk, "end", elapsed, false)
        if (this.model.state?.type != EngineEnergyState.OFF) return
        sk.playAnimation("jingzhi", true)
    }
}
