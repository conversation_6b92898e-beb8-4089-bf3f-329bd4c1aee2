import { BuildAnimation, CarriageID } from "../../../../common/constant/Enums";
import { gameHelper } from "../../../../common/helper/GameHelper";
import DanceHallModel from "../../../../model/train/danceHall/DanceHallModel";
import BuildQuietCmpt from "../BuildQuietCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class DanceHallDancingCmpt extends BuildQuietCmpt {
    private preState: boolean = null

    protected updateBState(sk: sp.Skeleton) {
        let carriage = gameHelper.train.getCarriageById(CarriageID.DANCEHALL) as DanceHallModel
        let state = carriage.haveDancer()
        if (this.preState == state) return
        this.preState = state
        this.setState(sk, state)
    }
    private setState(sk: sp.Skeleton, state: boolean) {
        if (state) {
            sk.playAnimation(BuildAnimation.IDLE, true)
        } else {
            sk.playAnimation(BuildAnimation.JINGZHI)
        }
    }
}
