import { BuildAnimation, CarriageID } from "../../../../common/constant/Enums";
import { gameHelper } from "../../../../common/helper/GameHelper";
import DanceHallModel from "../../../../model/train/danceHall/DanceHallModel";
import BuildAfterFrontCmpt from "../BuildAfterFrontCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DanceHallStageCmpt extends BuildAfterFrontCmpt {
    @property(cc.Node)
    protected light: cc.Node = null
    private preState: boolean = null

    public onCreate() {
        this.light.active = false
    }
    public playFlash(fboPrefab: cc.Node) {
        this.addFlash(fboPrefab, this.node)
    }
    protected play2Quiet(): boolean {
        this.playNodeAnimation(this.after, BuildAnimation.JINGZHI)
        return false
    }

    update() {
        if (!this.model) return
        let sk = this.node.Child('body', sp.Skeleton)
        if (sk.animation == BuildAnimation.BUILD) return

        let carriage = gameHelper.train.getCarriageById(CarriageID.DANCEHALL) as DanceHallModel
        let state = carriage.haveDancer()
        if (this.preState == state) return
        this.preState = state
        this.light.active = state
    }
}
