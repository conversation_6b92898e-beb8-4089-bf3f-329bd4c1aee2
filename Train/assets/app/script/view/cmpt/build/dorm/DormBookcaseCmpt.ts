/**
 * 柜子分为柜体部分和栏杆部分
 * 栏杆部分需要分身
 */
import { MAX_ZINDEX } from "../../../../common/constant/Constant";
import BuildCmpt from "../BuildCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class DormBookcaseCmpt extends BuildCmpt {
    private myCopy: cc.Node = null

    onLoad() {
        let node = cc.instantiate2(this.node.Child('body'), this.node.parent)
        node.name = this.node.name + '_copy'
        node.active = false
        this.myCopy = node
        node.Component(sp.Skeleton).playAnimation('jingzhi2', true)
    }
    onDestroy() {
        cc.isValid(this.myCopy) && this.myCopy.destroy()
    }
    protected setWhenPlayRemove() {
        if (this.myCopy) this.myCopy.active = false
    }
    protected playQuiet(sk: sp.Skeleton) {
        super.playQuiet(sk)
        if (!this.myCopy) return
        this.myCopy.x = this.node.x
        this.myCopy.y = this.node.y
        this.myCopy.zIndex = this.calculateZIndex()
        this.myCopy.active = true
    }
    private calculateZIndex() {
        let node = this.node.Child('zIndex')
        return MAX_ZINDEX - this.node.y - node.y
    }
}
