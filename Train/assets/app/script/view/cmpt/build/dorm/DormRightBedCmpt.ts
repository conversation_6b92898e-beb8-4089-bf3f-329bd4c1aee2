import { BUILD_MOUNT_POINT } from "../../../../common/constant/Enums";
import StateObj from "../../../../model/passenger/StateObj";
import DormRightBedObj, { DormRightBedObjState } from "../../../../model/train/dorm/DormRightBedObj";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DormRightBedCmpt extends BuildCmpt {

    public model: DormRightBedObj = null
    private preState: StateObj<DormRightBedObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        const ladderNode = this.node.parent.Child("trainItem_1013_1_6_copy")
        const elapsedTime = data?.timeData?.elapsed || 0
        switch (type) {
            case DormRightBedObjState.UP:
                sk.playAnimation("aniUp", false, elapsedTime)
                break;
            case DormRightBedObjState.DOWN:
                sk.playAnimation("aniDown", false, elapsedTime)
                break;
            case DormRightBedObjState.SWING:
                sk.playAnimation("ani_swing", false, elapsedTime)
                if (ladderNode) {
                    ladderNode.opacity = 0
                }
                break;
            case DormRightBedObjState.BIRD_EGG:
                sk.playAnimation("ani_bird", false, elapsedTime)
                break;
            default:
                sk.playAnimation("jingzhi")
                if (ladderNode) {
                    ladderNode.opacity = 255
                }
                break;
        }
    }

    public playOnJumpEnter() {
        let state = this.model.state
        let sk = this.Child("body", sp.Skeleton)
        let anim = "aniSit"
        if (state) {
            let type = state.type
            if (type == DormRightBedObjState.DOWN) {
                anim = "aniSitDown"
            }
        }
        sk.playAnimation(anim)
    }

    public playOnSit() {
        let body2Node = this.Component(MountPointCmpt).getPoint(BUILD_MOUNT_POINT.BODY2)
        if (body2Node) {
            body2Node.active = false
        }
    }
}
