import { BUILD_MOUNT_POINT, RoleDir } from "../../../../common/constant/Enums";
import { StateType } from "../../../../model/passenger/StateEnum";
import StateObj from "../../../../model/passenger/StateObj";
import BuildObj from "../../../../model/train/common/BuildObj";
import DormBirdModel from "../../../../model/train/dorm/DormBirdModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

enum BirdAnim {
    BIAO_YAN_1 = "biaoyan_1",
    BIAO_YAN_2 = "biaoyan_2",
    DAI_JI_1 = "daiji_1",
    DAI_JI_2 = "daiji_2",
    FEI_1 = "fei_1",
    FEI_2 = "fei_2",
    FEI_3 = "fei_3",
}

@ccclass
export default class DormBirdView extends mc.BaseCmptCtrl {

    @property(cc.Node)
    body: cc.Node = null

    private model: DormBirdModel = null
    private prePos: cc.Vec2 = null
    private state: StateObj<StateType> = null

    get sk() { return this.body.Component(sp.Skeleton) }

    public init(model: DormBirdModel) {
        this.model = model
        this.initView()
    }

    private initView() {
        const sk = this.sk
        sk.setSkin(this.model.skin)
        this.updatePosition()
    }

    private updatePosition() {
        let pos = this.getModelPos()
        this.node.setPosition(pos)
        this.updateDir()
        if (this.prePos) {
            this.prePos.set(this.node.getPosition())
        }
    }

    private getModelPos() { return this.model.getPosition() }

    private updateDir() {
        if (!this.prePos) return;
        let _dir = this.model.dir
        if (this.model.isMoving) {
            this.setDir(_dir)
            return
        }
        let vec = this.getModelPos().sub(this.prePos)
        if (vec.x != 0) {
            let dir = vec.x < 0 ? RoleDir.LEFT : RoleDir.RIGHT
            this.setDir(dir)
        }
    }

    public setDir(dir: RoleDir) {
        let curScale = this.body.scale
        let scale = dir == RoleDir.LEFT ? -Math.abs(curScale) : Math.abs(curScale)
        if (curScale != scale) {
            this.body.scaleX = scale
        }
    }


    update(dt: number) {
        if (!this.model) return
        this.model.update(dt)

        if (!this.prePos) {
            this.prePos = this.getModelPos().clone();
        }
        if (this.model.isMoving) {
            this.updatePosition()
        }
        this.updateState()
    }

    private updateState() {
        let state = this.model.state
        if (this.state == state) return
        this.state = state
        this.updatePosition()
        let type = state?.type
        let data = state?.data

        switch (true) {
            case !state:
                this.sk.playAnimation(BirdAnim.DAI_JI_1, false)
                break
            case type == StateType.BIRD_SLEEP:
                console.warn("sleep state")
                break
            case type == StateType.BIRD_BIAO_YAN_1:
                console.warn("biaoyan 1 state")
                break
            case type == StateType.BIRD_BIAO_YAN_2:
                console.warn("biaoyan 2 state")
                break
            case type == StateType.BIRD_FLY:
                this.playFly2(data)
                break
            case type == StateType.BIRD_IDLE:
                this.playIdle()
                break
        }
    }

    private async playFly2(data: { build: BuildObj, resolve: Function }) {
        const build = data.build
        const buildNode = this.getBuildNode(build.id)
        const pointNode = buildNode.Component(MountPointCmpt).getPoint(BUILD_MOUNT_POINT.BIRD)
        const targetPos = ut.convertToNodeAR(pointNode, this.node.parent)

        const distance = this.node.getPosition().sub(targetPos).mag()
        const time = distance / 200
        this.model.isMoving = true
        const sk = this.sk
        sk.playAnimation(BirdAnim.FEI_1, false)
        await ut.wait(sk.getEvent(BirdAnim.FEI_1).time, this)
        sk.playAnimation(BirdAnim.FEI_2, true)
        await cc.tween(this.node).to(time, { x: targetPos.x, y: targetPos.y }).promise()
        sk.playAnimation(BirdAnim.FEI_3, false)
        await ut.wait(sk.getEvent(BirdAnim.FEI_3).time, this)
        this.model.isMoving = false
        data.resolve()
    }

    protected async playIdle() {

    }


    public getBuildNode(id: string): cc.Node {
        const root = this.node.parent
        for (let child of root.children) {
            const cmpt = child.getComponent(BuildCmpt)
            if (cmpt && cmpt.model.id == id && cmpt.isActive()) {
                return child
            }
        }
    }
}
