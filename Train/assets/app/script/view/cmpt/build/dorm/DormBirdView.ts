import { RoleDir } from "../../../../common/constant/Enums";
import { StateType } from "../../../../model/passenger/StateEnum";
import StateObj from "../../../../model/passenger/StateObj";
import DormBirdModel from "../../../../model/train/dorm/DormBirdModel";

const { ccclass, property } = cc._decorator;

enum BirdAnim {
    BIAO_YAN_1 = "biaoyan_1",
    BIAO_YAN_2 = "biaoyan_2",
    DAI_JI_1 = "daiji_1",
    DAI_JI_2 = "daiji_2",
    FEI_1 = "fei_1",
    FEI_2 = "fei_2",
    FEI_3 = "fei_3",
}

@ccclass
export default class DormBirdView extends mc.BaseCmptCtrl {

    @property(cc.Node)
    body: cc.Node = null

    private model: DormBirdModel = null
    private prePos: cc.Vec2 = null
    private state: StateObj<StateType> = null

    get sk() { return this.body.Component(sp.Skeleton) }

    public init(model: DormBirdModel) {
        this.model = model
        this.initView()
    }

    private initView() {
        const sk = this.sk
        sk.setSkin(this.model.skin)
        this.playAnimation(BirdAnim.DAI_JI_1)

        this.updatePosition()
    }

    private playAnimation(ani: BirdAnim, loop: boolean = true) { this.sk.playAnimation(ani, loop) }


    private updatePosition() {
        let pos = this.getModelPos()
        this.node.setPosition(pos)
        this.updateDir()
        if (this.prePos) {
            this.prePos.set(this.node.getPosition())
        }
    }

    private getModelPos() { return this.model.getPosition() }

    private updateDir() {
        if (!this.prePos) return;
        let _dir = this.model.dir
        if (this.model.isMoving) {
            this.setDir(_dir)
            return
        }
        let vec = this.getModelPos().sub(this.prePos)
        if (vec.x != 0) {
            let dir = vec.x < 0 ? RoleDir.LEFT : RoleDir.RIGHT
            this.setDir(dir)
        }
    }

    public setDir(dir: RoleDir) {
        let curScale = this.body.scale
        let scale = dir == RoleDir.LEFT ? -Math.abs(curScale) : Math.abs(curScale)
        if (curScale != scale) {
            this.body.scaleX = scale
        }
    }


    update(dt: number) {
        if (!this.model) return
        this.model.update(dt)
        
        if (!this.prePos) {
            this.prePos = this.getModelPos().clone();
        }
        if (this.model.isMoving) {
            this.updatePosition()
        }
        this.updateState()
    }

    private updateState() {
        let state = this.model.state
        if (this.state == state) return
        this.state = state
        this.updatePosition()
        let type = state?.type
        let data = state?.data

        switch (true) {
            case !state:
                this.playAnimation(BirdAnim.DAI_JI_1)
                break
            case type == StateType.BIRD_SLEEP:
                console.warn("sleep state")
                break
            case type == StateType.BIRD_BIAO_YAN_1:
                console.warn("biaoyan 1 state")
                break
            case type == StateType.BIRD_BIAO_YAN_2:
                console.warn("biaoyan 2 state")
                break
            case type == StateType.BIRD_FLY:
                console.warn("fly state")
                break
        }
    }
}
