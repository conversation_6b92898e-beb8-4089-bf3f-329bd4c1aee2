import { BuildAnimation } from "../../../../common/constant/Enums";
import { resHelper } from "../../../../common/helper/ResHelper";
import StateObj from "../../../../model/passenger/StateObj";
import DiningOrderDrinkObj, { DiningOrderDrinkObjState } from "../../../../model/train/dining/DiningOrderDrinkObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class DiningOrderDrinkCmpt extends BuildCmpt {
    public model: DiningOrderDrinkObj = null
    private preState: StateObj<DiningOrderDrinkObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state
        this.setState(sk, state)
    }

    private setState(sk: sp.Skeleton, state: StateObj<DiningOrderDrinkObjState>) {
        let type = state?.type
        let data = state?.data
        if (type == DiningOrderDrinkObjState.ON) {
            sk.playAnimation(this.model.drink.drinkAnim, false, data.timeData.elapsed)
        } else {
            sk.playAnimation(BuildAnimation.JINGZHI)
        }

        this.updateDropDrink()
        this.updateOutDrink()
    }

    private async updateDropDrink() {
        let type = DiningOrderDrinkObjState.ON
        let sk = this.node.Child('body', sp.Skeleton)
        let slots = ["shuiguo", "shuiguo2"]
        for (let slot of slots) {
            resHelper.updateSlotAttachment(sk, slot, `food/drop/${this.model.drink?.drop}`, this.getTag(), () => {
                return this.model.state?.type == type && this.model.drink?.drop
            })
        }
    }

    private async updateOutDrink() {
        let type = DiningOrderDrinkObjState.ON
        let sk = this.node.Child('body', sp.Skeleton)
        let slot = "guadian_beizi"
        resHelper.updateSlotAttachment(sk, slot, `food/icon/${this.model.drink?.icon}`, this.getTag(), () => {
            return this.model.state?.type == type
        }, (spf: cc.SpriteFrame) => {
            sk.setSlotAttachment(slot, spf, { anchorY: 0 })
        })
    }
}

