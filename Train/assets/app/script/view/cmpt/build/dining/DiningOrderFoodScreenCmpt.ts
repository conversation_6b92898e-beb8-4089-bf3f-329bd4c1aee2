import { GoodsType } from "../../../../common/constant/Enums";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { resHelper } from "../../../../common/helper/ResHelper";
import GoodsObj from "../../../../model/train/common/GoodsObj";
import DiningOrderFoodObj from "../../../../model/train/dining/DiningOrderFoodObj";

const { ccclass } = cc._decorator;
const StartSpeed = 50
const MaxSpeed = 1000
const RunNum = [50, 65, 80] //三台机器各需要跑过多少个物品
const FoodHeight = 65 // item.height+content.spacingY
const Direction = -1
const ITEM_COUNT = 5 //一列的数量

class OneFoodScreen {
    private speed: number = 0 //老虎机速度
    private acc: number = 0 //老虎机加速度
    private list: string[] = [] //当前老虎机各个位置上的食物
    private runMax: number = 0//需要跑过多少个物品
    private runTime: number = 0
    private runCount: number = 0//已经随过的食物
    private runDistance: number = 0 //已经走过的路程
    private targetFood: string = null //最终需要随机出来的食物
    private isStop: boolean = true
    private content: cc.Node = null
    private tag: string = null
    public items: string[] = []

    public init(content: cc.Node, runMax: number, items: string[], tag: string) {
        this.tag = tag
        this.updateRandomItems(items)
        for (let i = 0; i < ITEM_COUNT; i++) {
            this.list.push(this.randomItem())
        }
        content.Items(this.list, (item: cc.Node, data: any) => {
            ut.resetLightItem(item)
            this.loadIcon(item.Child("icon"), data)
        })
        this.content = content
        this.runMax = runMax
    }
    public setIdle() {
        this.acc = 0
        this.speed = StartSpeed
        this.isStop = false
    }
    public setStop() {
        this.isStop = true
    }
    public setStart(food: string, elapsed: number) {
        this.acc = 500
        this.speed = StartSpeed
        this.isStop = false
        this.runTime = 0
        this.targetFood = food
        this.setRun()
        this.caculateElapsed(elapsed)
    }

    public setResult(food: string) {
        this.targetFood = food
        this.setTargetItem()
    }
    private setRun() {
        let key = this.content.y + FoodHeight * 2
        this.runCount = Math.floor(key / FoodHeight)
        this.runDistance = -(key % FoodHeight)
    }
    private stopRuning() {
        this.acc = 0
        this.speed = 0
        this.isStop = true
    }
    public update(dt: number) {
        if (this.isStop) return
        this.setContentY(dt, false)
        this.updateItems()
    }
    private caculateElapsed(dt: number) {
        if (dt <= 0.01) return
        this.setContentY(dt, true)
        this.updateItems()
    }
    private setContentY(dt: number, checkMany: boolean) {
        let add = 0
        let isRuning = this.acc > 0
        if (isRuning) {
            add = this.updateAccDistance(dt)
        } else {
            add = this.speed * dt
        }
        if (checkMany) {
            let one = FoodHeight * 2
            if (add >= one) {
                let num = Math.floor(add / one)
                add -= one * num
                this.runCount += 2 * num
            }
        }
        this.content.y += add * Direction
    }
    private updateAccDistance(dt: number) {
        let addDistance = this.caculateDistance(this.runTime, dt)
        this.speed = Math.min(MaxSpeed, this.speed + this.acc * dt)
        this.runTime += dt
        this.runDistance += addDistance
        if (this.runDistance >= this.runMax * FoodHeight) {
            addDistance -= (this.runDistance - this.runMax * FoodHeight)
            this.stopRuning()
        }
        return addDistance
    }
    // 从时间cur开始,经过时间dt,求走过的路程
    private caculateDistance(cur: number, dt: number) {
        let accMax = (MaxSpeed - StartSpeed) / this.acc
        if (cur < accMax) {
            if (cur + dt <= accMax) {
                // 匀加速运动
                let curS = this.speed
                let maxS = this.speed + this.acc * dt
                return (curS + maxS) * 0.5 * dt
            } else {
                // 匀加速运动+匀速运动
                let curS = this.speed
                let maxS = MaxSpeed
                let accDt = accMax - cur
                return (curS + maxS) * 0.5 * accDt + MaxSpeed * (dt - accDt)
            }
        }
        return MaxSpeed * dt//匀速运动
    }
    private updateItems() {
        let content = this.content
        if ((Direction > 0 && content.y > Direction * FoodHeight) || (Direction < 0 && content.y < Direction * FoodHeight)) {
            this.setItems()
            content.y -= Direction * FoodHeight * 2
        }
    }
    private setItems() {
        let isRuning = this.acc > 0
        if (isRuning) this.runCount += 2
        if (Direction > 0) {
            for (let i = 0; i < 3; i++) {
                this.list[i] = this.list[i + 2]
            }
            for (let i = 3; i < ITEM_COUNT; i++) {
                this.list[i] = this.randomItem()
            }

            if (isRuning && this.runCount >= this.runMax) {
                if (this.runCount > this.runMax) {
                    this.list[3] = this.targetFood
                } else {
                    this.list[4] = this.targetFood
                }
            }
        }
        else if (Direction < 0) {
            for (let i = 4; i > 1; i--) {
                this.list[i] = this.list[i - 2]
            }
            for (let i = 1; i >= 0; i--) {
                this.list[i] = this.randomItem()
            }

            if (isRuning && this.runCount >= this.runMax) {
                if (this.runCount > this.runMax) {
                    this.list[1] = this.targetFood
                } else {
                    this.list[0] = this.targetFood
                }
            }
        }
        for (let i = 0; i < ITEM_COUNT; i++) {
            let iconNode = this.content.children[i].Child("icon")
            this.loadIcon(iconNode, this.list[i])
        }
    }
    private setTargetItem() {
        let i = 2
        this.list[i] = this.targetFood
        let iconNode = this.content.children[i].Child("icon")
        this.loadIcon(iconNode, this.list[i])
    }

    private async loadIcon(node: cc.Node, icon: string) {
        let url = `food/material/${icon}`
        let tag = this.tag
        let spr = node.Component(cc.Sprite)
        await resHelper.loadSpriteFrame(spr, { url, tag }, false, false, true)
    }

    private randomItem() {
        return this.items.random()
    }

    public updateRandomItems(items: string[]) {
        this.items = ut.randomArray(items.slice()).slice(0, ITEM_COUNT)
    }
}
@ccclass
export default class DiningOrderFoodScreenCmpt extends mc.BaseCmptCtrl {
    private aryColumn: OneFoodScreen[] = []
    private model: DiningOrderFoodObj = null

    public init(model: DiningOrderFoodObj) {
        this.model = model

        let foodItems = this.model.carriage.getFoodItems()
        for (let i = 0; i < RunNum.length; i++) {
            this.aryColumn.push(new OneFoodScreen())
        }
        this.node.Child('views').Items(this.aryColumn, (content: cc.Node, data: OneFoodScreen, i: number) => {
            data.init(content, RunNum[i], foodItems, this.getTag())
        })
    }

    public initEdit() {
        let ary = []
        let list = []
        for (let i = 0; i < RunNum.length; i++) {
            ary.push(i)
        }
        for (let i = 0; i < 5; i++) {
            list.push(0)
        }
        this.node.Child('views').Items(ary, (content: cc.Node) => {
            content.Items(list, (item: cc.Node) => {
                item.Child("icon").active = false
            })
        })
    }

    public onIdle() {
        this.onStop()
        // this.aryColumn.forEach(obj => obj.setIdle())
    }

    public onStop() {
        this.aryColumn.forEach(obj => obj.setStop())
    }

    public onStart(food: GoodsObj, elapsed: number) {
        let foodItems = this.model.carriage.getFoodItems()
        let ary = this.getTargetFoods(food)
        if (ary) {
            this.aryColumn.forEach((obj, i) => {
                obj.updateRandomItems(foodItems)
                obj.setStart(ary[i], elapsed)
            })
        }
    }

    public showResult(food: GoodsObj) {
        let ary = this.getTargetFoods(food)
        if (ary) {
            this.aryColumn.forEach((obj, i) => obj.setResult(ary[i]))
        }
    }

    update(dt) {
        this.aryColumn.forEach((obj) => obj.update(dt))
    }

    private getTargetFoods(food: GoodsObj) {
        return food.foodItem
    }
}
