import { resHelper } from "../../../../common/helper/ResHelper";
import DiningTableObj from "../../../../model/train/dining/DiningTableObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class DiningTableCmpt extends BuildCmpt {
    public model: DiningTableObj = null
    private preCount: number = 0

    protected updateBState(sk: sp.Skeleton) {
        let count = this.model.getFoodCount()
        if (this.preCount == count) return
        this.preCount = count
        this.updateFood()
    }

    private async updateFood() {
        let foods = this.model.foods
        for (let i = 0; i < foods.length; i++) {
            let url = foods[i]
            if (url) {
                resHelper.loadTmpIcon(url, this.getFoodSprite(i), this.getTag())
            }
        }
        for (let i = 0; i < foods.length; i++) {
            let url = foods[i]
            if (!url) {
                resHelper.releaseSpriteSpf(this.getFoodSprite(i))
            }
        }
    }

    private getFoodSprite(i: number) {
        return this.Child(`food${i + 1}`, cc.Sprite)
    }
}
