import { BuildAnimation } from "../../../../common/constant/Enums";
import { resHelper } from "../../../../common/helper/ResHelper";
import StateObj from "../../../../model/passenger/StateObj";
import DiningOrderFoodBakingObj, { DiningOrderFoodBakingObjState } from "../../../../model/train/dining/DiningOrderFoodBakingObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class DiningOrderFoodBakingCmpt extends BuildCmpt {
    public model: DiningOrderFoodBakingObj = null
    private preState: StateObj<DiningOrderFoodBakingObjState> = null

    protected getIdleAnim(): any {
        return BuildAnimation.JINGZHI
    }

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state
        this.setState(sk, state)
    }

    private setState(sk: sp.Skeleton, state: StateObj<DiningOrderFoodBakingObjState>) {
        let type = state?.type
        let data = state?.data
        if (type == DiningOrderFoodBakingObjState.SCREEN_START) {
            sk.playAnimation(BuildAnimation.IDLE, true)
        } else if (type == DiningOrderFoodBakingObjState.SCREEN_END) {
            sk.playAnimation('end', false, data.timeData.elapsed)
        } else if (type == DiningOrderFoodBakingObjState.MAKE) {
            sk.playAnimation('foodDown', false, data.timeData.elapsed)
        } else {
            sk.playAnimation(BuildAnimation.JINGZHI, true)
        }

        this.updateFood()
    }

    private updateFood() {
        let type = DiningOrderFoodBakingObjState.MAKE
        let sk = this.node.Child('body', sp.Skeleton)
        let slot = "guadian_food"
        resHelper.updateSlotAttachment(sk, slot, `food/icon/${this.model.food?.icon}`, this.getTag(), () => {
            return this.model.state?.type == type
        }, (spf: cc.SpriteFrame) => {
            sk.setSlotAttachment(slot, spf, { anchorY: 0 })
        })
    }
}

