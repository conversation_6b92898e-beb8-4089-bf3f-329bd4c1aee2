import { BuildAnimation, BuildFromType } from "../../../../common/constant/Enums";
import { resHelper } from "../../../../common/helper/ResHelper";
import StateObj from "../../../../model/passenger/StateObj";
import DiningOrderFoodObj, { DiningOrderFoodObjState } from "../../../../model/train/dining/DiningOrderFoodObj";
import TrainCarriage from "../../../train/TrainCarriage";
import BuildCmpt from "../BuildCmpt";
import DiningOrderFoodScreenCmpt from "./DiningOrderFoodScreenCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DiningOrderFoodCmpt extends BuildCmpt {
    public model: DiningOrderFoodObj = null
    private preState: StateObj<DiningOrderFoodObjState> = null
    @property(DiningOrderFoodScreenCmpt)
    private myScreen: DiningOrderFoodScreenCmpt = null

    protected getIdleAnim(): any {
        return BuildAnimation.JINGZHI
    }

    public init(model: DiningOrderFoodObj, carriageCmpt: TrainCarriage, from?: BuildFromType): void {
        super.init(model, carriageCmpt, from)
        this.setSkin(model)
        this.initScreen()
    }

    public initEdit(build: DiningOrderFoodObj) {
        super.initEdit(build)
        this.setSkin(build)
        this.initEditScreen()
    }

    private setSkin(model: DiningOrderFoodObj) {
        let sk = this.node.Child('body', sp.Skeleton)
        sk.setSkin(model.getSkin())
    }

    private initScreen() {
        this.myScreen.init(this.model)
        this.setScreenByState()
    }

    private initEditScreen() {
        this.myScreen.initEdit()
    }

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state
        this.setState(sk, state)
    }

    private setScreenByState() {
        let state = this.model.state
        if (state != null) {
            let type = state?.type
            if (type == DiningOrderFoodObjState.SCREEN_START) {
                return
            } else if (type == DiningOrderFoodObjState.SCREEN_END) {
                return this.myScreen.showResult(this.model.food)
            } else if (type == DiningOrderFoodObjState.MAKE) {
                return this.myScreen.showResult(this.model.food)
            } else if (type == DiningOrderFoodObjState.BUILD_BAKING) {
                return
            }
        }
        this.myScreen.onIdle()
    }

    private setState(sk: sp.Skeleton, state: StateObj<DiningOrderFoodObjState>) {
        let type = state?.type
        let data = state?.data
        if (type == DiningOrderFoodObjState.START) {
            sk.playAnimation('start', false, data.timeData.elapsed)
        } else if (type == DiningOrderFoodObjState.SCREEN_START) {
            sk.playAnimation(BuildAnimation.IDLE, true)
            this.myScreen.onStart(this.model.food, data.timeData.elapsed)
        } else if (type == DiningOrderFoodObjState.SCREEN_END) {
            sk.playAnimation('end', false, data.timeData.elapsed)
        } else if (type == DiningOrderFoodObjState.MAKE) {
            sk.playAnimation('foodDown', false, data.timeData.elapsed)
        } else if (type == DiningOrderFoodObjState.BUILD_BAKING) {
            this.aniXiaoshi(sk)
        } else {
            sk.playAnimation(BuildAnimation.JINGZHI, true)
            this.myScreen.onIdle()
        }

        this.updateDropFood()
        this.updateOutFood()
    }

    private async aniXiaoshi(sk: sp.Skeleton) {
        this.myScreen.onStop()
        await sk.playAnimation('chucankou_xiaoshi', false)
        this.setSkin(this.model)
    }

    private async updateDropFood() {
        let type = DiningOrderFoodObjState.MAKE
        let sk = this.node.Child('body', sp.Skeleton)
        resHelper.updateSlotAttachment(sk, "guadian_downfood", `food/drop/${this.model.food?.drop}`, this.getTag(), () => {
            return this.model.state?.type == type
        })
    }

    private async updateOutFood() {
        let type = DiningOrderFoodObjState.MAKE
        let sk = this.node.Child('body', sp.Skeleton)
        let food = this.model.food
        let slots = ["guadian_panzifood", "guadian_handsfood"]
        let slot = slots[0]
        if (food?.handsFood) {
            slot = slots[1]
        }
        resHelper.switchSlotAttachment(sk, slot, `food/icon/${this.model.food?.icon}`, this.getTag(), () => {
            return this.model.state?.type == type && !this.model.getBaking()
        }, slots, (spf: cc.SpriteFrame) => {
            if (!cc.isValid(spf)) return
            sk.setSlotAttachment(slot, spf, { anchorY: 0 })
        })
    }

}

