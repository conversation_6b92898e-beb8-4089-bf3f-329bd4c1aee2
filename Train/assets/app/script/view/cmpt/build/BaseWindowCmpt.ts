import { res<PERSON><PERSON><PERSON> } from "../../../common/helper/ResHelper";
import MainWindCtrl from "../../main/MainWindCtrl";
import FBOCmpt from "../common/fbo/FBOCmpt";

const { ccclass, property } = cc._decorator;

enum WindowType {
    Null,
    Dorm2,
    Dorm4Left,
    Dorm4Right,
}

@ccclass
export default class BaseWindowCmpt extends mc.BaseCmptCtrl {
    @property({ type: cc.Enum(WindowType) })
    private type: WindowType = WindowType.Null

    @property({ type: cc.SpriteFrame })
    private bg: cc.SpriteFrame = null

    update() {
        // let window = this.getWindow()
        // let body = this.node.Child('body')
        // let pos = ut.convertToNodeAR(body, window.parent, body.getPosition(), null, true)
        // window.setPosition(pos)
    }
    start() {
        let sk = this.node.Child('body', sp.Skeleton)
        sk.setSlotAttachment("bg", this.bg)
        // let map = {
        //     [WindowType.Dorm2]: "chuangwai",
        //     [WindowType.Dorm4Left]: "chuangwai_l",
        //     [WindowType.Dorm4Right]: "chuangwai_r",
        // }
        // let url = `windows_bg/${map[this.type]}`
        // if (!url) return
        // let tag = this.getTag()
        // assetsMgr.loadTempRes(url, cc.SpriteFrame, tag).then(spf => {
        //     if (!cc.isValid(this)) {
        //         return
        //     }
        //     let sk = this.node.Child('body', sp.Skeleton)
        //     sk.setSlotAttachment("bg", spf)
        // })

        // let window = this.getWindow()
        // let sk = this.node.Child('body', sp.Skeleton)
        // let tex = window.Component(FBOCmpt).getRenderTexture()
        // if (tex) {
        //     sk.setSlotAttachment("bg", tex, { flipY: true })
        // }
    }
    private getWindow() {
        let key = WindowType[this.type]
        let cmpt = mc.currWind as MainWindCtrl
        return cmpt.getBgWindow(key)
    }
}
