import StateObj from "../../../../model/passenger/StateObj";
import WaterGameObj, { WaterGameObjState } from "../../../../model/train/water/WaterGameObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterGameCmpt extends BuildCmpt {
    public model: WaterGameObj = null
    private preState: StateObj<WaterGameObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == WaterGameObjState.USE) {
            this.playAction(data)
        }
        else {
            this.playQuiet(sk)
        }
    }

}
