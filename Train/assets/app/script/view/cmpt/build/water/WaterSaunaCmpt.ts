import StateObj from "../../../../model/passenger/StateObj";
import WaterSaunaObj, { WaterSaunaObjState } from "../../../../model/train/water/WaterSaunaObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterSaunaCmpt extends BuildCmpt {
    public model: WaterSaunaObj = null
    private preState: StateObj<WaterSaunaObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == WaterSaunaObjState.ADD) {
            this.playAction(data)
        }
        else if (type == WaterSaunaObjState.COAL) {
            this.playAction(data)
        }
        else if (type == WaterSaunaObjState.USE) {
            this.playAction(data)
        }
        else {
            this.playQuiet(sk)
        }
    }

}
