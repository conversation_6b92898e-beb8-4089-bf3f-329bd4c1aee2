import { BuildAnimation } from "../../../../common/constant/Enums";
import StateObj from "../../../../model/passenger/StateObj";
import WaterTableObj, { WaterTableObjState } from "../../../../model/train/water/WaterTableObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterTableCmpt extends BuildCmpt {
    public model: WaterTableObj = null
    private preState: StateObj<WaterTableObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == WaterTableObjState.USE) {
            this.playUse(data)
        }
        else if (type == WaterTableObjState.MAKE) {
        }
        else {
            let body2 = this.node.Child('body2')
            body2.active = false
            sk.playAnimation(BuildAnimation.JINGZHI, true)
        }
    }

    private playUse(data) {
        let sk = this.node.Child('body', sp.Skeleton)
        let elapsed = data.timeData.elapsed
        this.playAnimation(sk, "aniUse", elapsed)

        let sk2 = this.node.Child('body2', sp.Skeleton)
        sk2.node.active = true
        this.playAnimation(sk2, "aniUse2", elapsed)
    }
}
