import StateObj from "../../../../model/passenger/StateObj";
import WaterAirObj, { WaterAirObjState } from "../../../../model/train/water/WaterAirObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterAirCmpt extends BuildCmpt {
    public model: WaterAirObj = null
    private preState: StateObj<WaterAirObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == WaterAirObjState.USE) {
            this.playAction(data)
        }
        else {
            this.playQuiet(sk)
        }
    }

}
