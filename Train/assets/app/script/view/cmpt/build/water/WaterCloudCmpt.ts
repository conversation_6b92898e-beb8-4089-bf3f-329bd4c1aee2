import { MAX_ZINDEX } from "../../../../common/constant/Constant";
import { RoleDir } from "../../../../common/constant/Enums";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import StateObj from "../../../../model/passenger/StateObj";
import EngineComputerObj, { EngineComputerObjState } from "../../../../model/train/engine/EngineComputerObj";
import WaterCloudObj, { WaterCloudObjState } from "../../../../model/train/water/WaterCloudObj";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterCloudCmpt extends mc.BaseCmptCtrl {
    public model: WaterCloudObj = null

    private prePos: cc.Vec2 = null

    private state: StateObj<WaterCloudObjState> = null

    private orgParent: cc.Node = null

    private body: sp.Skeleton = null

    onCreate() {
        this.orgParent = this.node.parent
        this.body = this.Child('body', sp.Skeleton)
    }

    init(model: WaterCloudObj) {
        this.model = model
    }

    update(dt: number) {
        if (!this.model) return

        if (!this.prePos) {
            this.prePos = this.getModelPos().clone();
        }
        if (this.model.isMoving()) {
            this.updatePosition()
        }

        this.updateState();
    }

    private resetParent() {
        this.node.setParent(this.orgParent)
        this.prePos = null
        this.updatePosition()
    }

    private updateState() {
        let model = this.model

        if (!model) return

        let state = model.states.last()
        if (this.state == state) return
        this.state = state

        cc.Tween.stopAllByTarget(this.node)
        if (this.node.parent !== this.orgParent) {
            this.resetParent()
        }
        this.updatePosition()

        let type = state?.type
        let data = state?.data

        if (!state) {
            this.playCommon({loop: true})
        }
        else if (type == WaterCloudObjState.TABLE_BIRTH) {
            this.playTableBirthAnim(data, type)
        }
        else if (type == WaterCloudObjState.ENTER_SAUNA) {
            this.playEnterSaunaAnim(data, type)
        }
        else if (type == WaterCloudObjState.EXIT_HIDE) {
            this.playExitHideAnim(data)
        }
        else {
            this.playCommon(data, type)
        }
    }

    private async playCommon(data?, type?) {
        let anim = data?.anim || "aniIdle"
        let sk = this.body
        let build = data?.build
        let mountPoint = data?.mountPoint
        let mountPos = data?.mountPos

        if (sk.animation != anim) {
            let elapsed = data?.timeData?.elapsed || 0
            let loop = data?.loop
            if (anim == "aniIdle") loop = true
            viewHelper.playAnimation(sk, anim, elapsed, loop)
        }

        if (build && mountPoint) {
            let buildNode = this.getBuildNode(build.id)
            if (!buildNode) return
            this.setMountPoint(buildNode, mountPoint, mountPos)
        }
    }

    private async playTableBirthAnim(data, type) {
        let elapsed = data?.timeData?.elapsed || 0
        this.playCommon(data, type)

        this.node.scale = 0.5
        let targetPos = ut.convertToNodeAR(this.orgParent, this.node.parent, this.model.getPosition()) 
        let tween = cc.tween(this.node).to(data.timeData.time, {scale: 1, y: targetPos.y}, {easing: cc.easing.backOut}).start()
        tween.update(elapsed)
    }

    private async playEnterSaunaAnim(data, type) {
        let elapsed = data.timeData.elapsed
        let carriage = this.model.carriage
        let enterPos = carriage.getSaunaShell().getUseById("enter").pos
        data.mountPos = enterPos
        this.playCommon(data, type)

        let endPos = carriage.getSauna().getUseById("use").pos
        let targetPos = ut.convertToNodeAR(this.orgParent, this.node.parent, endPos) 
        let tween = cc.tween(this.node).to(data.timeData.time, {y: targetPos.y}).start()
        tween.update(elapsed)
    }

    private async playExitHideAnim(data) {
        this.playCommon(data)
        let elapsed = data.timeData.elapsed
        let tween = cc.tween(this.node).to(data.timeData.time, {opacity: 0}).start()
        tween.update(elapsed)
    }

    private updatePosition() {
        let pos = this.getModelPos()
        this.node.setPosition(pos)
        this.updateZIndex()
        this.updateScale()
        this.updateDir()

        if (this.prePos) {
            this.prePos.set(this.node.getPosition())
        }
    }

    private updateScale() {
        return 1
        let scale = this.model.getScale()
        this.node.scale = scale
    }

    private updateZIndex() {
        // this.node.zIndex = MAX_ZINDEX - this.getModelPos().y
        this.node.zIndex = MAX_ZINDEX - 10
    }

    private getModelPos() {
        let pos = this.model.getPosition()
        return pos
    }

    private updateDir() {
        if (!this.prePos) return;
        let _dir = this.model.getDir()
        if (_dir != RoleDir.NONE) {
            this.setFlip(_dir == RoleDir.LEFT)
            return
        }

        let dir = this.getModelPos().sub(this.prePos)
        if (dir.x != 0) { //防止垂直移动的时候转向
            this.setFlip(dir.x < 0)
        }
    }

    public setFlip(left?: boolean) {
        let curScale = this.body.node.scale //面朝左边scale > 0
        let scale = left ? Math.abs(curScale) : -Math.abs(curScale)
        if (curScale != scale) {
            this.body.node.scaleX = scale
        }
    }

    private getBuildNode(id): cc.Node {
        let root = this.orgParent
        for (let child of root.children) {
            let cmpt = child.getComponent(BuildCmpt)
            if (cmpt && cmpt['model'].id == id && cmpt.isActive()) {
                return child
            }
        }
    }

    private async getBuildNodeAysnc(id: string, type: WaterCloudObjState) {
        while (true) {
            let build = this.getBuildNode(id)
            if (build) return build
            await ut.wait(0.1, this)
            if (!cc.isValid(this)) return
            if (!this.model.getState(type)) return
        }
    }

    private setMountPoint(node: cc.Node, pointName: string = "guadian", pos?) {
        let point = this.getMountPoint(node, pointName)
        if (!point) {
            console.warn("mount_point not found", node.getPath(), pointName)
        }
        this.node.setParent(point)
        this.node.zIndex = 0
        if (pos) {
            this.node.setPosition(ut.convertToNodeAR(this.orgParent, point, pos))
        }
        else {
            this.node.setPosition(0, 0)
        }
    }

    private getMountPoint(node: cc.Node, pointName: string = "guadian") {
        let cmpt = node.Component(MountPointCmpt)
        if (!cmpt) return
        return cmpt.getPoint(pointName)
    }

}
