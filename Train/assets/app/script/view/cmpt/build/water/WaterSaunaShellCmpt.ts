import { BuildAnimation } from "../../../../common/constant/Enums";
import StateObj from "../../../../model/passenger/StateObj";
import WaterSaunaShellObj, { WaterSaunaShellObjState } from "../../../../model/train/water/WaterSaunaShellObj";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterSaunaShellCmpt extends BuildCmpt {
    public model: WaterSaunaShellObj = null
    private preState: StateObj<WaterSaunaShellObjState> = null

    update() {
        if (!this.model) return

        let sk = this.node.Child('body', sp.Skeleton)
        let body2 = this.node.Component(MountPointCmpt).getPoint('body2')
        body2.active = sk.animation != BuildAnimation.BUILD

        if (sk.animation == BuildAnimation.BUILD) return

        let state = this.model.state
        if (this.preState == state) return

        let type = state?.type
        let data = state?.data
        if (type == WaterSaunaShellObjState.USE_START) {
            this.playAction(data)
        }
        else if (type == WaterSaunaShellObjState.USE) {
            this.playAction(data)
        }
        else if (type == WaterSaunaShellObjState.USE_END) {
            this.playAction(data)
        }
        else {
            this.playQuiet(sk)
        }
        this.preState = state
    }

}
