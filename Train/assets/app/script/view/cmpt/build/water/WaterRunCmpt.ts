import StateObj from "../../../../model/passenger/StateObj";
import WaterRunObj, { WaterRunObjState } from "../../../../model/train/water/WaterRunObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WaterRunCmpt extends BuildCmpt {
    public model: WaterRunObj = null
    private preState: StateObj<WaterRunObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state

        let type = state?.type
        let data = state?.data
        if (type == WaterRunObjState.USE) {
            this.playUse(data)
        }
        else {
            this.node.Child('body2').active = false
            this.playQuiet(sk)
        }
    }

    private playUse(data) {
        let sk = this.node.Child('body', sp.Skeleton)
        this.playAnimation(sk, "aniUse", 0, true)

        let sk2 = this.node.Child('body2', sp.Skeleton)
        sk2.node.active = true
        this.playAnimation(sk2, "aniUse2", 0, true)
    }

}
