import { BuildAnimation, BuildAttr, BuildFromType, ConditionType } from "../../../common/constant/Enums";
import { viewHelper } from "../../../common/helper/ViewHelper";
import FBOCmpt from "../common/fbo/FBOCmpt";
import BuildObj from "../../../model/train/common/BuildObj";
import EventType from "../../../common/event/EventType";
import TrainCarriage from "../../train/TrainCarriage";
import MountPointCmpt from "../common/MountPointCmpt";
import CarriageLightCmpt from "../train/CarriageLightCmpt";
import FlashSpriteShaderCtrl from "../../../common/shader/FlashSpriteShaderCtrl";

const { ccclass } = cc._decorator;

@ccclass
export default class BuildCmpt extends mc.BaseCmptCtrl {
    public carriageCmpt: TrainCarriage = null
    public model: BuildObj = null
    protected slotMap = {}
    private skin: number = 0

    public init(model: BuildObj, carriageCmpt: TrainCarriage, from: BuildFromType = BuildFromType.NONE) {
        this.model = model
        this.skin = model.skin
        this.carriageCmpt = carriageCmpt
        this.initView(from)
    }

    public initEdit(build: BuildObj) {
        this.initEditView()
    }

    public update() {
        if (!this.model) return
        let sk = this.getBodySkeleton()
        if (sk && sk.animation == BuildAnimation.BUILD) return
        this.updateBState(sk)
    }

    protected updateBState(sk: sp.Skeleton) {
    }

    protected initEditView() {
        let sk = this.getBodySkeleton()
        if (sk) {
            let idleAnim = this.getIdleAnim()
            if (!sk.findAnimation(idleAnim)) {
                idleAnim = BuildAnimation.JINGZHI
            }
            if (sk.findAnimation(idleAnim)) {
                sk.defaultAnimation = idleAnim
                sk.playAnimation(idleAnim);
            }
            this.skPaused(sk)
        }
    }

    protected skPaused(sk: sp.Skeleton) {
        ut.waitNextFrame(1, this).then(() => {
            if (!cc.isValid(sk)) return
            sk.paused = true
        })
    }

    protected async initView(from: BuildFromType = BuildFromType.NONE) {
        let sk = this.getBodySkeleton()
        if (sk) {
            if (from == BuildFromType.BUY) {
                eventCenter.emit(EventType.HIDE_BUILDS, this.model)

                this.onBuildAnimStart()
                eventCenter.emit(EventType.SHOW_BUILDCOST_TIPS, this.model)
                eventCenter.emit(EventType.SET_FOCUS, {carriage: this.model.carriage, id: this.model.id})
                await this.playBuildAct(sk)
                this.onBuildAnimEnd()
                this.model.buildingEnd(1.5)
                eventCenter.emit(EventType.SHOW_BUILDUP_TIPS, this.model)
                eventCenter.emit(EventType.SHOW_BUILDS)
                await ut.wait(1.2, this)

                //建造完成
                if (this.model?.id != "1015-1-1") {   //车头建筑走新手引导特殊处理，没有再次打开建造界面
                    eventCenter.emit(EventType.BACK_CAMERA_FROM_EDIT, this.model.carriage, this.model.id)
                }
            } else if (from == BuildFromType.CHANGE) {
                this.carriageCmpt.onBuildChangeSkin(this.model)
            }
            if (!cc.isValid(this)) return
            this.playQuiet(sk)
        }
    }

    protected onBuildAnimStart() {
        this.pauseLight()
        let cmpt2 = this.getSameBuildType()
        if (cmpt2) {
            cmpt2.pauseLight()
        }
    }

    protected onBuildAnimEnd() {
        ut.wait(0.5, this).then(() => { //延迟恢复
            this.resumeLight()
            let cmpt2 = this.getSameBuildType()
            if (cc.isValid(cmpt2)) {
                cmpt2.resumeLight()
            }
        })
    }

    protected pauseLight() {
        this.Component(CarriageLightCmpt)?.pause()
    }

    protected resumeLight() {
        this.Component(CarriageLightCmpt)?.resume()
    }

    protected async playBuildAct(sk: sp.Skeleton) {
        this.model.buildingStart()
        await this.playBuildRemoveAct()
        if (!cc.isValid(this)) return
        await this.playBuildAnimation(sk)
        if (!cc.isValid(this)) return
        this.carriageCmpt.onBuildChangeSkin(this.model)
        this.node.zIndex = this.model.getZIndex()
    }

    // 非地板和墙纸:程序先倒着播前一级的设施的建设动画（视觉上给人感觉是把原来的拆了，退场）
    protected async playBuildRemoveAct() {
        let cmpt = this.getPreBuild()
        if (!cmpt || !cmpt.model) return
        let model = cmpt.model
        if (model.buildType) { // 墙纸地板 要注意新的要在旧的之上
            let old = cmpt.node.zIndex
            if (this.node.zIndex < old) {
                this.node.zIndex = old
            }
        } else {
            let sk = cmpt.getBodySkeleton()
            if (sk) {
                this.setNodeActive(false)
                await cmpt.playRemoveAnimation(sk)
                this.carriageCmpt.onBuildChangeSkin(this.model)
                await ut.wait(0.2, this)
                this.setNodeActive(true)
            }
        }
    }

    protected setNodeActive(bol: boolean) {
        this.node.active = bol
    }

    protected async playRemoveAnimation(sk: sp.Skeleton) {
        this.hidePoints()
        this.setWhenPlayRemove()
        await this.playNodeBackAnimation(sk.node, BuildAnimation.BUILD)
    }

    protected async playBuildAnimation(sk: sp.Skeleton) {
        await sk.playAnimation(BuildAnimation.BUILD, false)
    }

    protected hidePoints() {
        this.Component(MountPointCmpt)?.hideAllPoint()
    }

    protected setWhenPlayRemove() {
    }

    protected getBodySkeleton() {
        let body = this.node.Child('body')
        return body?.Component(sp.Skeleton)
    }

    private getSameBuildType() {
        let buildType = this.model.buildType
        if (!buildType) return
        for (let node of this.node.parent.children) {
            if (node == this.node) continue
            let cmpt = node.getComponent(BuildCmpt)
            if (cmpt && cmpt.model?.buildType == buildType) {
                return cmpt
            }
        }
    }

    private getPreBuild() {
        for (let node of this.node.parent.children) {
            if (node == this.node) continue
            let cmpt = node.getComponent(BuildCmpt)
            if (cmpt && cmpt.model == this.model && !cmpt.isActive()) {
                return cmpt
            }
        }
    }

    protected getIdleAnim(): any {
        return BuildAnimation.IDLE
    }

    protected playQuiet(sk: sp.Skeleton) {
        let idleAnim = this.getIdleAnim()
        if (sk.findAnimation(idleAnim)) {
            sk.playAnimation(idleAnim, true);
        } else if (sk.findAnimation(BuildAnimation.JINGZHI)) {
            sk.playAnimation(BuildAnimation.JINGZHI);
        }
    }

    protected playNodeAnimation(node: cc.Node, act: string, loop: boolean = false) {
        let sk = node.Component(sp.Skeleton)
        if (sk && sk.findAnimation(act))
            return sk.playAnimation(act, loop)
    }

    protected playNodeBackAnimation(node: cc.Node, act: string) {
        let sk = node.Component(sp.Skeleton)
        if (sk && sk.findAnimation(act))
            return sk.playBackAnimation(act, 1.5)
    }

    protected async playAnimation(sk, animName, elapsed = 0, loop = false) {
        return viewHelper.playAnimation(sk, animName, elapsed, loop)
    }

    protected playAction(data, sk?) {
        sk = sk || this.node.Child('body', sp.Skeleton)
        let elapsed = data.timeData?.elapsed || 0
        let anim = data.anim
        this.playAnimation(sk, anim, elapsed, data.loop)
    }

    public isActive() {
        return this.model?.skin === this.skin
    }

    protected addFlash(fboPrefab: cc.Node, buildNode: cc.Node) {
        let fboNode = buildNode.Child(fboPrefab.name)
        if (!fboNode) {
            fboNode = cc.instantiate2(fboPrefab, buildNode)
        }
        fboNode.active = true
        //todo 这里取不到部分的宽高，所以用的最大宽高，后面应该换成build的宽高 by.PETER_Z
        fboNode.width = 2642
        fboNode.height = 1688
        fboNode.Component(FBOCmpt).source = buildNode
        fboNode.Child('sp', FlashSpriteShaderCtrl).play()
    }
    public playFlash(fboPrefab: cc.Node) {
        this.addFlash(fboPrefab, this.node)
    }

}
