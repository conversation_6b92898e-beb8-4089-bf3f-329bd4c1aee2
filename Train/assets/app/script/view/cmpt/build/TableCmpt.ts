import { BuildFromType, PlanetEvent } from "../../../common/constant/Enums";
import { gameHelper } from "../../../common/helper/GameHelper";
import BuildCmpt from "./BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TableCmpt extends BuildCmpt {

    @property(sp.Skeleton)
    private food: sp.Skeleton = null

    protected async initView(from: BuildFromType = BuildFromType.NONE) {
        await super.initView(from)

        let node = gameHelper.planet.getSchoolPlanet().getNodes().find(node => node.eventName == PlanetEvent.FOOD)
        let food = this.food
        food.node.active = !node || node.isPass()
        if (from) {
            await food.playAnimation("animation3")
        }
        if (food.node.active) {
            food.playAnimation("jingzhi2")
        }
    }
}
