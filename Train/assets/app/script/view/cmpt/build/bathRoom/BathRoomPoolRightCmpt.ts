import { BuildAnimation } from "../../../../common/constant/Enums";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BathRoomPoolRightCmpt extends BuildCmpt {
    @property(cc.Node)
    protected mask: cc.Node = null

    public onCreate() {
        this.setWhenPlayRemove()
    }
    protected initEditView() {
        this.playNodeAnimation(this.node.Child('body'), BuildAnimation.JINGZHI)
    }
    protected setWhenPlayRemove() {
        this.mask.opacity = 0
    }
    protected playQuiet(sk: sp.Skeleton) {
        this.mask.opacity = 255
        this.playNodeAnimation(sk.node, BuildAnimation.IDLE, true)
    }
}
