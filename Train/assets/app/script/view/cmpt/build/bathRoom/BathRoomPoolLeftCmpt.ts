import { BuildAnimation } from "../../../../common/constant/Enums";
import BuildAfterFrontCmpt from "../BuildAfterFrontCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BathRoomPoolLeftCmpt extends BuildAfterFrontCmpt {
    @property(cc.Node)
    protected mask: cc.Node = null

    public onCreate() {
        this.setWhenPlayRemove()
    }
    public playFlash(fboPrefab: cc.Node) {
        this.addFlash(fboPrefab, this.node)
        this.addFlash(fboPrefab, this.mask)
    }
    protected initEditView() {
        this.playNodeAnimation(this.after, BuildAnimation.JINGZHI)
    }
    protected setWhenPlayRemove() {
        this.mask.opacity = 0
    }
    protected playQuiet(sk: sp.Skeleton) {
        this.mask.opacity = 255
        this.playNodeAnimation(this.after, BuildAnimation.IDLE, true)
    }

}
