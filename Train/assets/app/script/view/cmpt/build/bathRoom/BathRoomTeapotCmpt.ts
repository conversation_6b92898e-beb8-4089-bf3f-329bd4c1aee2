import BuildAfterFrontCmpt from "../BuildAfterFrontCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BathRoomTeapotCmpt extends BuildAfterFrontCmpt {
    @property(cc.ParticleSystem)
    private lizi: cc.ParticleSystem = null

    public onCreate() {
        this.lizi.stopSystem()
    }
    protected initEditView() {
        this.play2Jingzhi()
    }
    protected async playBuildAnimation(sk: sp.Skeleton) {
        this.front.active = false
        await this.after.Component(sp.Skeleton).playAnimation(this.buildAfter)
        this.front.active = true
        await this.front.Component(sp.Skeleton).playAnimation(this.buildFront)
    }
    protected playQuiet(sk: sp.Skeleton) {
        this.lizi.resetSystem()
        super.playQuiet(sk)
    }
}
