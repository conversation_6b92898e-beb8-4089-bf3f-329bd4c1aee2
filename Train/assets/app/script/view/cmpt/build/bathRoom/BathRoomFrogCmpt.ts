import { resHelper } from "../../../../common/helper/ResHelper";
import StateObj from "../../../../model/passenger/StateObj";
import BathRoomFrogObj, { BathRoomFrogObjState } from "../../../../model/train/bathRoom/BathRoomFrogObj";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BathRoomFrogCmpt extends BuildCmpt {
    public model: BathRoomFrogObj = null
    private preState: StateObj<BathRoomFrogObjState> = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state
        this.setState(sk, state)
    }
    private setState(sk: sp.Skeleton, state: StateObj<BathRoomFrogObjState>) {
        let type = state?.type
        let data = state?.data
        if (type == BathRoomFrogObjState.TAKE) {
            sk.playAnimation('aniTake', false, data.timeData.elapsed)
        } else if (type == BathRoomFrogObjState.WAIT) {
            let show = this.model.getAnimEventTime('aniTake')
            sk.playAnimation('aniTake', false, data.timeData.elapsed + show)
        } else {
            sk.playAnimation("jingzhi")
        }
        this.updateTeaIcon()
    }
    private async updateTeaIcon() {
        let type = BathRoomFrogObjState.WAIT
        let sk = this.node.Child('body', sp.Skeleton)
        let slot = "guadian_tangyao"
        resHelper.updateSlotAttachment(sk, slot, `food/${this.model.getTeaUrl()}`, this.getTag(), () => {
            return this.model.state?.type == type
        })
    }
}
