import BuildAfterFrontCmpt from "../BuildAfterFrontCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class BathRoomBoardCmpt extends BuildAfterFrontCmpt {

    protected async playBuildAnimation(sk: sp.Skeleton) {
        this.front.active = false
        await this.after.Component(sp.Skeleton).playAnimation(this.buildAfter)
        this.front.active = true
    }
    protected setWhenPlayRemove() {
        this.front.active = false
    }
    protected play2Quiet(): boolean {
        this.playNodeAnimation(this.after, 'jingzhi1')
        this.playNodeAnimation(this.front, 'jingzhi2')
        return false
    }
}
