import { CARRIAGE_CONTENT_SIZE, CARRIAGE_LEN, CARRIAGE_SPACING, DAWN_TRANS_TIME, DUSK_TRANS_TIME } from "../../../common/constant/Constant";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { resHelper } from "../../../common/helper/ResHelper";

const { ccclass, property } = cc._decorator;

const PLANET_RATE = 1.06 //星球相对于远景的移动倍率，包括行驶和拖动车厢
const PLANET_BG_RATE = 1.5 //
const MOVE_SPEED = 400 * PLANET_RATE //最大行驶速度，每秒移动像素长度
const FAST_SPEED = 7000 //出发喷气速度
const SPEED_UP_RATE = 2 //加速时候的倍率
const TWEEN_TIME = 2 //加速，减速时间，单位秒
const START_TIME = 1.5 //慢速抖动出发时间
const CAMERA_MOVE_PLANET_RATE = 0.05 //拖动车厢星球移动倍率
const PLANET_WIDTH = 2000

enum MOVE_STATE {
    MOVE,
    END,
    START,
}

@ccclass
export default class StarryCmpt extends mc.BaseCmptCtrl {

    @property(cc.Node)
    private day: cc.Node = null

    @property(cc.Node)
    private night: cc.Node = null

    @property(cc.Node)
    private star: cc.Node = null

    @property(cc.Node)
    private planetBg: cc.Node = null

    @property(cc.Node)
    private ufoNode: cc.Node = null

    private mainCamera: cc.Node = null

    private preSt = 0
    private ufo = { speed: 0, lastTime: 0 }

    private state: number = 0
    private st: number = 0
    private dis: number = 0
    private planetRatio: number = 0
    private preState: number = 0

    private _pause: boolean = false

    public listenEventMaps() {
        return [
            { [EventType.TIME_ACCELERATE_START]: this.speedUpStart },
            { [EventType.TIME_ACCELERATE_END]: this.speedUpEnd },
            { [EventType.CAMERA_MOVE]: this.onCameraMove }
        ]
    }

    public preload() {
        this.updateInfo(0)
        let p = this.changeReachBg()
        this.preState = this.state
        return p
    }

    public onEnter() {
        if (gameHelper.world.isSpeedUp()) {
            this.speedUpStart()
        }
    }

    public resume(){
        this._pause = false
    }

    public pause(){
        this._pause = true
    }

    //v1缓慢出发 ->v2突然喷气加速(速度慢慢降) -> v3匀速 ->v4(速度缓慢降到0)
    update(dt) {
        if (this._pause) return
        let world = gameHelper.world

        let ratio = world.getDayRatio()
        this.day.opacity = cc.misc.lerp(0, 255, ratio)
        this.night.opacity = cc.misc.lerp(0, 255, 1 - ratio)

        this.updateInfo(dt)

        if (this.preState != this.state) {
            this.changeReachBg()
            this.preState = this.state
        }

        this.preSt = this.st

        let dis = this.dis
        this.updateLoopByDis(this.day, dis)
        this.updateLoopByDis(this.night, dis)
        this.updateLoopByDis(this.star, dis)
        this.updateLoopByDis(this.planetBg, dis * PLANET_BG_RATE)

        this.updatePlanet(this.planetRatio, this.state)

        if (this.ufoNode.active) {
            let ufoDis = this.ufo.speed * dt / PLANET_RATE
            this.ufoNode.x += (ufoDis - dis)
            if (this.ufoNode.x > cc.winSize.width * 0.5 + this.ufoNode.width || this.ufoNode.x < -cc.winSize.width * 0.5) {
                this.ufoNode.active = false
                this.ufo.lastTime = gameHelper.now()
            }
        }
        this.checkGenUFO()
    }

    private updateInfo(dt) {
        let dis = 0
        let speed = MOVE_SPEED
        let startTime = START_TIME
        let totTime = TWEEN_TIME
        let s = totTime * speed / 2 //加速区间的路程
        let a = speed / totTime
        let st = 0, state = 0
        let planetRatio = 1
        let totS = Math.max(s, PLANET_WIDTH) //加速到星球消失&&进入匀速状态的路程
        let ss = totS - s //匀速区间的路程
        if (gameHelper.planet.isMoving()) {
            let surplusTime = gameHelper.planet.getMoveSurplusTime() / ut.Time.Second
            let durTime = gameHelper.planet.getCurPlanetMoveTime() / ut.Time.Second - surplusTime
            if (surplusTime < totTime) { //减速
                let time = (totTime - surplusTime)
                st = speed * time + 0.5 * -a * time * time
                dis = (st - this.preSt) / PLANET_RATE
                // console.log("aa", st, dis, time, speed)
                planetRatio = (ss / totS) + (st / s) * (s / totS)
                state = MOVE_STATE.END
            }
            else if (durTime < startTime) { //加速

                // st = 0.5 * a * durTime * durTime
                st = speed * durTime
                dis = (st - this.preSt) / PLANET_RATE
                // console.log("bb", st, dis, durTime)
                planetRatio = st / totS
                state = MOVE_STATE.START
            }
            else if (durTime < startTime + totTime) {
                let preS = speed * startTime
                let time = durTime - startTime
                let fastA = (FAST_SPEED - speed) / totTime
                st = FAST_SPEED * time + 0.5 * -fastA * time * time + preS
                dis = (st - this.preSt) / PLANET_RATE
                planetRatio = Math.min(1, st / totS)
                state = MOVE_STATE.START
            }
            else {
                if (ss > 0) {
                    let st = (durTime - totTime) * speed
                    if (st < ss) { //加速后的匀速
                        state = 2
                    }
                    st = (surplusTime - totTime) * speed
                    if (st < ss) { //匀速后接减速
                        planetRatio = ((ss - st) / ss) * (ss / totS)
                        state = MOVE_STATE.END
                    }
                }

                if (gameHelper.world.isSpeedUp()) {
                    speed *= SPEED_UP_RATE
                }
                dis = dt * speed / PLANET_RATE
            }
        }
        else {
            state = MOVE_STATE.END
        }
        this.state = state
        this.st = st
        this.dis = dis
        this.planetRatio = planetRatio
    }

    public changeReachBg() {
        let tag = this.getTag()
        let planetNode = this.node.Child("planet")
        if (this.state == MOVE_STATE.END) {
            let planet = gameHelper.planet.moveTarget || gameHelper.planet.getCurPlanet()
            return resHelper.loadPlanetReachBg(planet.getId(), planetNode, tag)
        }
        else if (this.state == MOVE_STATE.START) {
            return resHelper.loadPlanetReachBg(gameHelper.planet.getCurPlanet().getId(), planetNode, tag)
        }
        else {
            return resHelper.loadPlanetReachBg(gameHelper.planet.moveTargetId, planetNode, tag)
        }
    }


    private updateLoopByDis(node, dis) {
        let loop = node.Child("loop")
        loop.x -= dis
        let width = cc.winSize.width
        if (loop.x < -width * 0.5) {
            loop.x += width
        }
        else if (loop.x > width * 0.5) {
            loop.x -= width
        }
    }

    private onCameraMove(dis) {
        if (gameHelper.planet.isMoving()) return
        dis *= CAMERA_MOVE_PLANET_RATE / PLANET_RATE
        this.updateLoopByDis(this.day, dis)
        this.updateLoopByDis(this.night, dis)
        this.updateLoopByDis(this.star, dis)
        this.updateLoopByDis(this.planetBg, dis * PLANET_BG_RATE)
    }

    private speedUpStart() {
        let node = this.node.Child('speedUp')
        node.stopAllActions()
        node.active = true
        cc.tween(node).to(0.7, { opacity: 255 }, { easing: cc.easing.sineIn }).start()
        node.Child('lizi', cc.ParticleSystem).resetSystem()
    }

    private speedUpEnd() {
        let node = this.node.Child('speedUp')
        node.stopAllActions()
        node.Child('lizi', cc.ParticleSystem).stopSystem()
        cc.tween(node).to(1.4, { opacity: 0 }, { easing: cc.easing.sineIn })
            .call(() => {
                node.active = false
            }).start()
    }

    private updatePlanet(ratio, state) {
        if (!this.mainCamera) {
            this.mainCamera = cc.find("Canvas/Main Camera")
        }

        let planet = this.node.Child("planet")
        let ratio2 = (-this.mainCamera.x - 436.5) * CAMERA_MOVE_PLANET_RATE / PLANET_WIDTH
        if (state == MOVE_STATE.END) { //到达
            planet.active = true
            planet.scale = 1
            //这里考虑两部分影响，ratio为列车到达过程中，星球从开始出现到完全出现的比例，距离为星球的宽度；ratio2为拖动车厢从车头到车尾对于星球位置的影响比例
            let finalRatio = Math.max(0, (ratio - ratio2))
            planet.x = cc.misc.lerp(cc.winSize.width * 0.5 + PLANET_WIDTH, cc.winSize.width * 0.5, finalRatio)
        }
        else if (state == MOVE_STATE.START) { //出发
            planet.active = true
            planet.scaleX = -1
            planet.x = cc.misc.lerp(-cc.winSize.width * 0.5, -cc.winSize.width * 0.5 - PLANET_WIDTH, ratio)
        }
        else { //航行中
            planet.active = false
        }
    }

    private checkGenUFO() {
        if (this.ufoNode.active) return
        let passTime = gameHelper.now() - this.ufo.lastTime
        if (passTime < 5 * ut.Time.Second) return
        let surplusTime = gameHelper.planet.getMoveSurplusTime() / ut.Time.Second
        let durTime = gameHelper.planet.getCurPlanetMoveTime() / ut.Time.Second - surplusTime
        if (gameHelper.planet.isMoving() && (durTime < 10 || surplusTime < 10)) {
            return
        }
        this.genUFO()
    }

    private genUFO() {
        this.ufoNode.active = true
        this.ufo.speed = MOVE_SPEED + ut.random(50, 100) * (ut.chance(50) ? 1 : -1)
        this.ufoNode.y = ut.random(470, 520)
        this.ufoNode.Component(cc.MultiFrame).setFrame(ut.random(0, 2))
        if (this.ufo.speed > MOVE_SPEED || !gameHelper.planet.isMoving()) {
            this.ufoNode.x = -cc.winSize.width * 0.5
        }
        else {
            this.ufoNode.x = cc.winSize.width * 0.5 + this.ufoNode.width
        }
    }
}
