import { SpeedUpType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import TimeScaleCmpt from "../common/TimeScaleCmpt";

const { ccclass, property, executionOrder, requireComponent } = cc._decorator;

@ccclass
@requireComponent(TimeScaleCmpt)
export default class SpeedUpTimeScaleCmpt extends mc.BaseCmptCtrl {

    public listenEventMaps() {
        return [
            { [EventType.TIME_ACCELERATE_START]: this.updateScale },
            { [EventType.TIME_ACCELERATE_END]: this.updateScale },
        ]
    }

    public onEnter(): void {
        this.updateScale()
    }

    private updateScale() {
        let scale = gameHelper.world.transDT(1, SpeedUpType.S5)
        this.getComponent(TimeScaleCmpt)?.setScale(scale)
    }
}
