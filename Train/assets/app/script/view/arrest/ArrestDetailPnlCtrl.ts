import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { Arrest } from "../../model/arrest/ArrestModel";

const { ccclass } = cc._decorator;

@ccclass
export default class ArrestDetailPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected criminalLbl_: cc.Label = null // path://root/criminal_l
    protected clueNode_: cc.Node = null // path://root/clue_n
    protected getNode_: cc.Node = null // path://root/get_be_n
    protected isDoingNode_: cc.Node = null // path://root/isDoing_n
    //@end
    private data: Arrest = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(arrestId: string) {
        this.data = gameHelper.arrest.getArrestById(arrestId)
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/get_be_n
    onClickGet(event: cc.Event.EventTouch, data: string) {
        this.getTask()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------


    private async getTask() {
        let succ = await gameHelper.arrest.acceptArrests(this.data.id)
        if (succ) {
            //this.initView()
            this.close()
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        let data = this.data
        let root = this.node.Child('root')
        let isDoing = data.getState() == proto.ArrestState.OnGoing
        let color0 = this.criminalLbl_.node.Component(cc.MultiColor).getColor(0).toHEX()
        let color1 = this.criminalLbl_.node.Component(cc.MultiColor).getColor(1).toHEX()
        resHelper.setSpf(`arrest/bag/${data.getBg()}`, root.Child('planetBg'), this.getTag())
        if (!!data.getIconBig()) {
            root.Child('icon').active = true
            resHelper.loadIcon(root.Child('icon'), 'monsterIcon', data.getIconBig(), this.getTag())
        } else {
            root.Child('icon').active = false
        }
        root.Child('lv', cc.Label).string = `${data.getStar()}`//Items(ut.newArray(data.getStar()))
        this.getNode_.active = data.getState() == proto.ArrestState.NotCollected
        this.isDoingNode_.active = isDoing
        this.criminalLbl_.setLocaleKey(data.getCriminal())
        this.clueNode_.Items(data.getClues(), (it, clue) => {
            if (clue.type == proto.ArrestClueType.PLACE) {
                if (clue.placeType == proto.ArrestPlaceType.PIC) {
                    it.Component(cc.RichText).setLocaleKey('arrest_clue_3')
                } else if (clue.placeType == proto.ArrestPlaceType.TEXT) {
                    let params: string = ''//`<color=${color1}>`
                    let planetNames = clue.planets.map(t => assetsMgr.lang(`name_planet_${t}`))
                    planetNames.forEach((n, index) => {
                        if (!!index) {
                            params += assetsMgr.lang('common_guiText_21') + n
                        } else {
                            params += n
                        }
                    })
                    //params += '</c>'
                    it.Component(cc.RichText).setLocaleKey('arrest_clue_1', params)
                }
            }
            else if (clue.type == proto.ArrestClueType.TIME) {
                //全天没有显示
                it.Component(cc.RichText).setLocaleKey('arrest_clue_2', assetsMgr.lang(clue.timeType == proto.ArrestTimeType.DAY ? 'arrest_time_1' : 'arrest_time_2'))
            }
        })

    }

}
