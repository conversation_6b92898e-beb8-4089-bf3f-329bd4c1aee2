import { RuleType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { animHelper } from "../../common/helper/AnimHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { mapHelper } from "../../common/helper/MapHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { timeHelper } from "../../common/helper/TimeHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { Arrest } from "../../model/arrest/ArrestModel";
import PlanetTitleCmpt from "../cmpt/planet/PlanetTitleCmpt";
import ArrestPaperCmpt from "./ArrestPaperCmpt";

const { ccclass } = cc._decorator;
const FlyTime = 0.7            //钻石飞向钻石栏最大时间
const FlySpeed = 1300          //钻石飞向钻石栏速度
const EndScale = 0.5           //钻石飞到钻石栏最终缩放

@ccclass
export default class ArrestPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected listNode_: cc.Node = null // path://root/list_n
    protected listCfgNode_: cc.Node = null // path://root/listCfg_n
    protected isDoingNode_: cc.Node = null // path://root/isDoing_n
    protected refreshTipLbl_: cc.Label = null // path://root/refreshTip_l
    protected getNode_: cc.Node = null // path://root/get_be_n
    protected planetTitleNode_: cc.Node = null // path://planetTitle_n
    protected flyNode_: cc.Node = null // path://fly_n
    protected papersNode_: cc.Node = null // path://papers_n
    protected bagNode_: cc.Node = null // path://bag_n
    protected backNode_: cc.Node = null // path://back_be_n
    //@end

    private curArrest: Arrest = null

    public listenEventMaps() {
        return [
            { [EventType.ARREST_GET]: this.initView },
            { [EventType.ARREST_GET]: this.showFly },
        ]
    }

    public async onCreate() {
        if (gameHelper.arrest.getNeedShowResult()) {
            this.showResult()
        }
    }

    public onEnter(data: any) {
        this.resetCurSelect()
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    protected update(dt: number): void {
        this.refreshTipLbl_.setLocaleKey("arrest_guiTest_21", timeHelper.getTimeText(Math.floor(gameHelper.arrest.getNextSurplusTime() / ut.Time.Second)))
        this.planetTitleNode_.Component(PlanetTitleCmpt).init('arrest_guiTest_17', `${gameHelper.arrest.getScore()}`, RuleType.ARREST)
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://root/get_be_n
    onClickGet(event: cc.Event.EventTouch, data: string) {
        this.showDetail()
    }

    // path://root/refresh_be
    onClickRefresh(event: cc.Event.EventTouch, data: string) {
        this.onRefresh()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------


    private async getArrestReward(data: Arrest) {
        if (data.getState() != proto.ArrestState.DoneNoReward) {
            return
        }
        let succ = await gameHelper.arrest.getReward(data.id)
        if (succ) {
            this.initView()
        }
    }

    private async onRefresh() {
        //todo 这里刷新应该有花费
        let succ = await gameHelper.arrest.refreshArrest()
        if (succ) {
            this.resetCurSelect()
            this.initView()
        }
    }

    private async showResult() {
        let result = gameHelper.arrest.getResult()
        let succ = await gameHelper.arrest.showArrestResult()
        if (succ) {
            viewHelper.showPnl('arrest/ArrestSettlePnl', result)
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.updateList()
        //this.isDoingNode_.active = gameHelper.arrest.getIsDoing()
        this.getNode_.active = false//!gameHelper.arrest.getIsDoing()
    }

    private updateList() {
        let list = gameHelper.arrest.getArrestListInView()
        this.listNode_.Items(list, (it, data, index) => {
            this.setOneItem(it, data, index)
        })
    }

    private setOneItem(it: cc.Node, data: Arrest, index: number) {
        let cfgNode = this.listCfgNode_.Child(`${index}`)
        it.y = cfgNode.y
        it.angle = cfgNode.angle
        resHelper.setSpf(`arrest/bg/${data.getBg()}`, it.Child('planetBg'), this.getTag())
        if (!!data.getIconBig()) {
            it.Child('icon').active = true
            resHelper.loadIcon(it.Child('icon'), 'monsterIcon', data.getIconBig(), this.getTag())
        } else {
            it.Child('icon').active = false
        }
        it.Child('lv', cc.Label).string = `${data.getStar()}`//Items(ut.newArray(data.getStar()))
        it.Child('isDone').active = data.getState() == proto.ArrestState.DoneNoReward
        it.Child('isOver').active = data.getState() == proto.ArrestState.Finished
        it.Child('isDoing').active = data.getState() == proto.ArrestState.OnGoing
        it.Child('isSelect').active = data.id == this.curArrest?.id
        it.Child('rewards').Items(data.getRewards(), (it, cond) => {
            resHelper.loadIconByCondInfo(cond, it.Child('icon'), this.getTag())
            it.Child('num', cc.Label).string = `${cond.num}`
            uiHelper.regClickPropBubble(it, cond)
        })
        it.off('click')
        it.on('click', () => {
            this.onSelectItem(data)
            if (data.getState() != proto.ArrestState.DoneNoReward) {
                this.showDetail()
            }
            else {
                this.getArrestReward(data)
            }
        })
    }

    private onSelectItem(data: Arrest) {
        this.curArrest = data
        this.initView()
    }

    private showDetail() {
        viewHelper.showPnl('arrest/ArrestDetailPnl', this.curArrest.id)
    }

    private resetCurSelect() {
        this.curArrest = null //gameHelper.arrest.getArrestList().find(t => t.getState() != proto.ArrestState.Expired)
    }

    private async showFly() {
        let node = cc.instantiate2(this.flyNode_, this.papersNode_)
        node.active = true
        node.Child('ArrestPaper', ArrestPaperCmpt).init(this.curArrest)
        this.fly(node, this.bagNode_)
    }

    private async fly(it: cc.Node, target: cc.Node, timeWait: number = 0) {
        target.active = true
        let targetPos = target.getPosition()
        let vec = targetPos.sub(it.getPosition())
        let time = Math.min(vec.len() / FlySpeed, FlyTime)
        await cc.tween(it).delay(timeWait).to(time, { x: targetPos.x, y: targetPos.y, scale: EndScale, angle: 0 }, { easing: cc.easing.sineOut }).promise()
        it.destroy()
        await animHelper.scaleBag(target)
        target.active = false
    }


}
