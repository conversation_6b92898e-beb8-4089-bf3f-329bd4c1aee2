import { StoreId } from "../../common/constant/Enums";
import { gameHelper } from "../../common/helper/GameHelper";
import BlackHoleStorePnlCtrl from "../blackHole/BlackHoleStorePnlCtrl";

const { ccclass } = cc._decorator;

@ccclass
export default class ArrestStorePnlCtrl extends BlackHoleStorePnlCtrl {

    //@autocode property begin
    protected refreshNode_: cc.Node = null // path://root/refresh_be_n
    protected currenyLbl_: cc.Label = null // path://root/curreny/curreny_l
    protected goodsNode_: cc.Node = null // path://root/goods_n
    protected resetTimeLbl_: cc.Label = null // path://root/resetTime_l
    //@end

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.model = gameHelper.store.getStore(StoreId.ARREST)
        await this.model.checkSync()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/refresh_be_n
    onClickRefresh(event: cc.Event.EventTouch, data: string) {
        super.onClickRefresh(event, data)
    }

    // path://back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        super.onClickBack(event, data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    
    // ----------------------------------------- custom function ----------------------------------------------------

    protected initCurreny() {
        this.currenyLbl_.string = ut.simplifyMoney(gameHelper.arrest.getCurrency())
    }
}
