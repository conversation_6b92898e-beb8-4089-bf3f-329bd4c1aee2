import { UNLOCK_FADEIN_TIME, UNLOCK_TIME_SHORT } from "../../common/constant/Constant";
import { anim<PERSON>elper } from "../../common/helper/AnimHelper";
import { gameHelper } from "../../common/helper/GameHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class ArrestSettlePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected backNode_: cc.Node = null // path://back_be_n
    protected rootNode_: cc.Node = null // path://root_n
    protected doneNode_: cc.Node = null // path://root_n/done_n
    protected unDoneNode_: cc.Node = null // path://root_n/unDone_n
    protected descNode_: cc.Node = null // path://root_n/all/desc_n
    protected bgNode_: cc.Node = null // path://bg_n
    protected upNode_: cc.Node = null // path://up_n
    protected bottomTipsNode_: cc.Node = null // path://bottomTips_n
    //@end

    private result: proto.IArrestResult = {}

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(data) {
        if (!data) return
        this.result = data
        this.hideContinue()
        this.node.opacity = 0
        let act = cc.fadeIn(UNLOCK_FADEIN_TIME)
        cc.tween(this.node).then(act).start()
        this.initView()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        let result = this.result
        ut.wait(UNLOCK_TIME_SHORT, this).then(() => {
            this.showContinue()
        })
        let score = result.score
        let wins = result.wins
        let fails = result.fails
        let num = 0
        wins.forEach(t => num += t.count)
        fails.forEach(t => num += t.count)
        let isUp = score > 0
        let isEqual = score == 0
        this.descNode_.Child('rt', cc.RichText).string = `${(isUp || isEqual) ? '+' : ''}${score}`
        this.descNode_.Child('up', cc.MultiFrame).setFrame(isUp)
        this.upNode_.active = isUp
        this.descNode_.Child('up').active = !isEqual
        this.rootNode_.Child('info/rt', cc.RichText).setLocaleKey('arrest_guiTest_23', num)
        this.doneNode_.active = wins.length > 0 && !!wins[0]
        this.unDoneNode_.active = fails.length > 0 && !!fails[0]
        if (this.doneNode_.active) {
            this.doneNode_.Child('content').Items(wins, (it, data) => {
                it.Child('lb', cc.Label).setLocaleKey('arrest_guiTest_26', data.star)
                it.Child('num', cc.Label).string = `x${data.count}`
            })
        }
        if (this.unDoneNode_.active) {
            this.unDoneNode_.Child('content').Items(fails, (it, data) => {
                it.Child('lb', cc.Label).setLocaleKey('arrest_guiTest_26', data.star)
                it.Child('num', cc.Label).string = `x${data.count}`
            })
        }
        ut.waitNextFrame().then(() => {
            this.bgNode_.y = this.rootNode_.y + this.rootNode_.height / 2 + 5
        })
    }

    private hideContinue() {
        this.backNode_.active = false
        this.bottomTipsNode_.active = false

    }

    private showContinue() {
        this.backNode_.active = true
        this.bottomTipsNode_.active = true
        animHelper.playContinueBlink(this.bottomTipsNode_)
    }

}
