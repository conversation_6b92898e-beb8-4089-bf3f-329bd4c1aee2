import BaseCmptCtrl from "../../../core/base/BaseCmptCtrl";
import { resHelper } from "../../common/helper/ResHelper";
import { Arrest } from "../../model/arrest/ArrestModel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ArrestPaperCmpt extends BaseCmptCtrl {
    public init(cfg: Arrest) {
        this.setIcon(cfg)
        this.setState(cfg)
    }

    private setIcon(cfg: Arrest) {
        if (!!cfg.getIconBag()) {
            this.node.Child('icon').active = true
            resHelper.setSpf(`arrest/head/${cfg.getIconBag()}`, this.node.Child('icon'), this.getTag())
        }
        else {
            this.node.Child('icon').active = false
        }
    }

    private setState(cfg: Arrest) {
        let state = cfg.getState()
        let stateNode = this.node.Child('state')
        if (state == proto.ArrestState.DoneNoReward || state == proto.ArrestState.OnGoing) {
            stateNode.active = true
            stateNode.Component(cc.MultiFrame).setFrame(state != proto.ArrestState.DoneNoReward)
        }
        else {
            stateNode.active = false
        }
        if (state == proto.ArrestState.Expired) {
            this.node.opacity = 255 * 0.4
        }
        else {
            this.node.opacity = 255 * 1
        }
    }
}

