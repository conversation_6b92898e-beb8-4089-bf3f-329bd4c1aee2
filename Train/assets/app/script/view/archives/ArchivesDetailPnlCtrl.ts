import { CharacterProfileCfg } from "../../common/constant/DataType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import PassengerModel from "../../model/passenger/PassengerModel";
import DragLongPressCmpt, { DragLongEvent } from "../cmpt/common/DragLongPressCmpt";
import ArchivesItemCmpt from "./ArchivesItemCmpt";

const { ccclass } = cc._decorator;

const FlySpeed = 1300          //钻石飞向钻石栏速度
const FlyTime = 0.7            //钻石飞向钻石栏最大时间
const RootY = 80

enum PartType {
    INFO = 1,
    STORY,
    PARTNER,
}

const PartCfg = [
    { id: PartType.INFO, types: [1, 2, 3, 4, 5] },
    { id: PartType.STORY, types: [6, 7, 8, 10] },
    { id: PartType.PARTNER, types: [9, 11] }
]

const DOUBLE_CONNECT_POS = {
    [9]: [639, -294],
    [11]: [312, -294],
}

const SINGLE_CONNECT_POS = {
    [9]: [470, -294],
    [11]: [470, -294],
}

const SINGLE_POS = {
    [0]: [314, -297],
    [9]: [656, -297],
    [11]: [656, -297],
}

const DOUBLE_POS = {
    [0]: [503, -297],
    [9]: [809, -297],
    [11]: [145, -297],
}

const CanDragNode = [1, 2, 6, 7, 8]
const DragGroup = {
    1: [1, 2],
    2: [6, 7, 8],
}

type dragItData = {
    group: number,
    lock: boolean
}

@ccclass
export default class ArchivesDetailPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected rootNode_: cc.Node = null // path://root_n
    protected connectNode_: cc.Node = null // path://root_n/connect_n
    protected clickNode_: cc.Node = null // path://root_n/click_be_n
    protected progressNode_: cc.Node = null // path://root_n/progress_n
    protected profilesNode_: cc.Node = null // path://root_n/tmp_n/profiles_nbe_n
    protected type3Node_: cc.Node = null // path://root_n/type3_n
    protected headNode_: cc.Node = null // path://root_n/head_n
    protected linkNode_: cc.Node = null // path://root_n/link_be_n
    protected linkRolesNode_: cc.Node = null // path://root_n/link_roles_n
    protected bottomNode_: cc.Node = null // path://bottom_n
    protected itemsSv_: cc.ScrollView = null // path://bottom_n/items_sv
    protected backNode_: cc.Node = null // path://back_be_n
    //@end

    private character: PassengerModel
    private curItem: CharacterProfileCfg = null //只在拖拽过程中使用
    private selectPart: PartType = null
    private dataList: CharacterProfileCfg[] = []
    // 羁绊全部激活
    private _linkActive: boolean = false
    // 拖拽开始时的初始pos
    private _dragInitPosData: { [key: number]: { x: number, y: number } } = null
    private _dragSort: { [key: number]: number } = null
    private _lastAnimSort: number[] = []
    private _sendChangeNet: boolean = false

    private _orgSize: { [key: number]: { width: number, height: number } } = null
    private _orgPos: { [key: number]: { x: number, y: number } } = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({ isAct: false })
        this.node.opacity = 0
    }

    public onEnter(characterId: number) {
        this.bottomNode_.opacity = 0
        this.linkNode_.active = false
        this.linkRolesNode_.opacity = 0
        this.character = gameHelper.passenger.getPassenger(characterId)
        this.syncList()
        this._orgSize = {}
        this._orgPos = {}
        for (const it of this.profilesNode_.children) {
            this._orgSize[it.name] = {
                width: it.width,
                height: it.height
            }
            this._orgPos[it.name] = {
                x: it.x,
                y: it.y
            }
        }
        this.initView()
        cc.tween(this.node)
            .to(0.15, { opacity: 255 })
            .start()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root_n/tmp_n/profiles_nbe_n
    onClickProfiles(event: cc.Event.EventTouch, data: string) {
        const it = event.target as cc.Node
        let name = it.name
        let type = Number(name)
        if (it["isStick"]) return
        this.onSelectPart(type)
    }

    // path://root_n/click_be_n
    onClickClick(event: cc.Event.EventTouch, data: string) {
        this.onClick()
    }

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        if (this.bottomNode_.opacity > 0) {
            return void this.hideBottom()
        }
        this.close()
    }

    // path://root_n/link_be_n
    onClickLink(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("archives/ArchivesWatchPnl", { data: this.character.getID() })
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async onStick(data: CharacterProfileCfg, hitNode: cc.Node) {
        mc.lockTouch(true)
        let pos = +hitNode.name
        let group = -1
        if (DragGroup[1].includes(pos)) {
            group = 1
        }
        else if (DragGroup[2].includes(pos)) {
            group = 2
        }

        if (group != -1) {
            const its = this.getSortNodes(group)
            for (let i = 0; i < its.length; i++) {
                const it = its[i]
                if (it == hitNode) {
                    pos = i
                    if (group == 1) {
                        pos += 1
                    }
                    else if (group == 2) {
                        pos += 6
                    }
                    break
                }
            }
        }

        let succ = await gameHelper.archives.unlock(data.id, this.character.id, pos)
        if (succ) {
            this.curItem = null
            this.syncList()
            this.updateItemList()
            await this.updateProfileNodes()
            if (this._linkActive && (data.type == 9 || data.type == 11)) {
                mc.lockTouch(false)
                await new Promise(resolve => viewHelper.showPnl("archives/ArchivesWatchPnl", { data: this.character.id, resolve }))
                this.switchLinkView()
                cc.Tween.stopAllByTarget(this.linkRolesNode_)
                await cc.tween(this.linkRolesNode_).to(0.15, { opacity: 255 }).start().promise()
            }
            this.updateProgress()
            await this.showLvUp()
            mc.lockTouch(false)
        } else {
            mc.lockTouch(false)
        }
        return succ
    }

    private onSelectPart(type: number) {
        this.selectPart = PartCfg.find(t => t.types.has(+type)).id
        this.showBottom()
    }

    private onClick() {
        if (this.bottomNode_.active) {
            this.hideBottom()
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.setInfo()
        this.updateProfileNodes()
        this.updateProgress()
        this.hideBottom(false)
    }

    private setInfo() {
        this.rootNode_.Child('name', cc.Label).setLocaleKey(this.character.name)
        resHelper.loadIcon(this.rootNode_.Child('character'), 'archives/character', `tujian_icon_${this.character.id}`, this.getTag())
        resHelper.loadIcon(this.headNode_.Child('icon'), 'passenger/icon_circle', `character_${this.character.id}_icon_circle`, this.getTag())
    }
    private updateProfilesNodesSelect() {
        for (const it of this.profilesNode_.children) {
            let type = it.name
            const mf = it.Component(cc.MultiFrame)
            let cfg = PartCfg.find(t => t.id == this.selectPart)
            if (cc.isValid(mf)) {
                mf.setFrame(!!cfg && cfg.types.has(+type))
            }
            const stickType = this.character.getProfileByPosition(+type)
            if (cc.isValid(mf)) {
                mf.enabled = !stickType
            }
        }
    }

    private activeDragEvent(bol: boolean) {
        for (const it of this.profilesNode_.children) {
            if (!it.active) return
            const drag = it.Child("drag")
            if (!drag) continue
            drag.Component(DragLongPressCmpt).enabled = bol
        }
    }

    private async updateProfileNodes() {
        const link: cc.Node[] = []
        this.updateConnect()

        for (const it of this.profilesNode_.children) {
            let type = it.name
            it.setContentSize(this._orgSize[type].width, this._orgSize[type].height)
            it.setPosition(this._orgPos[type].x, this._orgPos[type].y)

            let cfg = PartCfg.find(t => t.id == this.selectPart)
            // 实际激活的类型
            let stickType = this.character.getProfileByPosition(+type)
            const mf = it.Component(cc.MultiFrame)
            if (cc.isValid(mf)) {
                mf.setFrame(!!cfg && cfg.types.has(+type))
            }
            const stickNode = CanDragNode.includes(+type) ? it.Child("drag/body/stick") : it.Child("stick")

            stickNode.active = false
            let data = cfgHelper.getCharacterProfileByCharacterIdAndType(this.character.id, +type)
            if (stickType) {
                stickNode.active = true
                data = cfgHelper.getCharacterProfileByCharacterIdAndType(this.character.id, +stickType)
            }
            it["isStick"] = !!stickType

            if (cc.isValid(mf)) {
                mf.enabled = !stickType
            }
            if (cc.isValid(it.Component(cc.Sprite))) {
                it.Component(cc.Sprite).enabled = !stickType
            }
            if (cc.isValid(it.Component(cc.ButtonEx))) {
                it.Component(cc.ButtonEx).enabled = !stickType
            }
            if (cc.isValid(it.Component(cc.Button))) {
                it.Component(cc.Button).enabled = !stickType
            }

            const act = CanDragNode.includes(+type) ? it.Child("drag/body/act") : it.Child("act")
            if (act) act.active = data && data.story && assetsMgr.lang(data.story) != data.story
            if (act && act.active) act.active = !!stickType

            if (!data) {
                it.active = false
                continue
            }
            it.Data = data
            if (+type == 9 || +type == 11) {
                link.push(it)
            }
            else if (+stickType == 9 || +stickType == 11) {
                link.push(it)
            }

            const drag = it.Child("drag")
            drag && (drag.active = !!stickType)
            if (!stickType) continue
            await stickNode.Child('ArchivesItem', ArchivesItemCmpt).init(data)
            if (CanDragNode.includes(+type)) {
                const size = stickNode.getChildByName('ArchivesItem').getContentSize()
                stickNode.setContentSize(size)
                stickNode.parent.setContentSize(size)
                stickNode.parent.parent.setContentSize(size)
                it.setContentSize(size)
                this.regDragEvent(it, +stickType, data, DragGroup[2].includes(+type))
            }

            if (act && act.active) {
                act.off("click")
                act.on("click", () => {
                    viewHelper.showPnl("archives/ArchivesWatchPnl", { data })
                })
            }

        }

        const len = this.character.cfg.profile.friends?.length || 0

        let cnt = 0
        link.forEach(node => this.character.getProfile(node.name) && cnt++)
        this._linkActive = cnt == len

        switch (true) {
            case len == 2:
                link.forEach(l => {
                    l.setPosition(cc.v2(DOUBLE_POS[l.name][0], DOUBLE_POS[l.name][1]))
                    this.connectNode_.Child(l.name).setPosition(cc.v2(DOUBLE_CONNECT_POS[l.name][0], DOUBLE_CONNECT_POS[l.name][1]))
                })
                this.headNode_.setPosition(cc.v2(DOUBLE_POS[0][0], DOUBLE_POS[0][1]))
                break
            case len == 1:
                link.forEach(l => {
                    l.setPosition(cc.v2(SINGLE_POS[l.name][0], SINGLE_POS[l.name][1]))
                    this.connectNode_.Child(l.name).setPosition(cc.v2(SINGLE_CONNECT_POS[l.name][0], SINGLE_CONNECT_POS[l.name][1]))
                })
                this.headNode_.setPosition(cc.v2(SINGLE_POS[0][0], SINGLE_POS[0][1]))
                break
        }

        this.linkNode_.active = this._linkActive
        this.switchLinkView(true)
    }

    private switchLinkView(recoverOpacity: boolean = false) {
        if (!this._linkActive) return
        this.profilesNode_.Child("9").active = false
        this.profilesNode_.Child("11").active = false
        this.connectNode_.active = false
        this.headNode_.active = false
        if (recoverOpacity) this.linkRolesNode_.opacity = 255
        this.character.cfg.profile.friends?.forEach((friend, index) => {
            const it = this.linkRolesNode_.Child(`${index}`)
            resHelper.loadTmpIcon(`passenger/profile/tiezhi_character_icon_${friend}`, it.Component(cc.Sprite), this.getTag())
        })
        resHelper.loadTmpIcon(`passenger/profile/tiezhi_character_icon_${this.character.id}`, this.linkRolesNode_.Child("2").Component(cc.Sprite), this.getTag())
    }

    private updateProgress() {
        let num = this.character.getProfilesNum()
        this.progressNode_.children.forEach(it => {
            let id = it.name
            let cfg = cfgHelper.getCharacterProfileAttrById(this.character.id, id)
            let numMax = cfg.unlockCount
            it.Child('num', cc.Label).string = `${Math.min(num, numMax)}/${numMax}`
            it.Child('bg', cc.MultiFrame).setFrame(num >= numMax)
            it.Child('num', cc.MultiColor).setColor(num < numMax)
            it.Child('lb', cc.MultiColor).setColor(num < numMax)
            it.off("click")
            it.on("click", () => {
                if (num < numMax) return void viewHelper.showAlert("archives_tips_3")
                viewHelper.showPnl('archives/ArchivesLvUpPnl', { id: this.character.id, resolve: null, lv: +id })
            })
        })
    }

    private showBottom() {
        cc.Tween.stopAllByTarget(this.bottomNode_)
        cc.Tween.stopAllByTarget(this.rootNode_)
        this.bottomNode_.active = true
        cc.tween(this.bottomNode_).to(0.1, { opacity: 255 }).start()
        cc.tween(this.rootNode_).to(0.1, { y: RootY }).start()
        this.updateProfilesNodesSelect()
        this.updateItemList()
        this.activeDragEvent(false)
    }

    private hideBottom(updateProfileNodes: boolean = true) {
        this.selectPart = null
        cc.Tween.stopAllByTarget(this.bottomNode_)
        cc.Tween.stopAllByTarget(this.rootNode_)
        cc.tween(this.bottomNode_).to(0.1, { opacity: 0 }).call(() => this.bottomNode_.active = false).start()
        cc.tween(this.rootNode_).to(0.1, { y: 0 }).start()
        this.activeDragEvent(true)
        updateProfileNodes && this.updateProfilesNodesSelect()
    }

    private updateItemList() {
        let types = PartCfg.find(t => t.id == this.selectPart).types
        let list: CharacterProfileCfg[] = this.getListByTypes(types)
        if (this.selectPart == PartType.PARTNER) {
            list = list.filter(l => {
                if (l.type == 9) {
                    return !!cfgHelper.getCharacter(l.characterId).profile.friends[0]
                }
                return !!cfgHelper.getCharacter(l.characterId).profile.friends[1]
            })
        }
        this.itemsSv_.content.Items(list, (it, data) => {
            it.Child('drag/ArchivesItem', ArchivesItemCmpt).init(data).then(() => {
                let size = it.Child('drag/ArchivesItem').getContentSize()
                it.Child('drag').setContentSize(size)
                it.setContentSize(size)
                this.registerDrag(it)
            })
        })
    }

    private registerDrag(it: cc.Node) {
        let drag = it.Child('drag')
        let data = it.Data
        drag.off(DragLongEvent.LP_START)
        drag.on(DragLongEvent.LP_START, () => {
            this.onSelect(data)
        }, this)
        drag.off(DragLongEvent.LP_END)
        drag.on(DragLongEvent.LP_END, () => {
        }, this)
        drag.off(DragLongEvent.DL_CLICK)
        drag.on(DragLongEvent.DL_CLICK, () => {
            viewHelper.showAlert("archives_tips_5")
        }, this)
        drag.off(DragLongEvent.DRAG_START)
        drag.on(DragLongEvent.DRAG_START, () => {
            drag.opacity = 180
        }, this)
        drag.off(DragLongEvent.DRAG_MOVE)
        drag.on(DragLongEvent.DRAG_MOVE, () => {
            this.onSelect(data)
        }, this)
        drag.off(DragLongEvent.DRAG_END)
        drag.on(DragLongEvent.DRAG_END, (cmpt: DragLongPressCmpt, cursor: cc.Vec2) => {
            drag.opacity = 255
            this.dragEnd(drag, data, cursor)
        }, this)
    }

    private onSelect(data: CharacterProfileCfg) {
        if (this.curItem == data) return
        this.curItem = data
        //this.updateItemList()
    }

    private async dragEnd(drag: cc.Node, data: CharacterProfileCfg, cursor: cc.Vec2) {
        const r = this.checkHit(drag, data, cursor)
        if (!r) {
            return void viewHelper.showAlert('archives_tips_1')
        }
        const can = gameHelper.archives.checkItemCouldUse(data, this.character.id)
        if (can.yes) {
            await this.onStick(can.data, r)
        }
        else {
            this.curItem = null
            this.updateItemList()
            viewHelper.showAlert('archives_tips_2', { params: [assetsMgr.lang(this.character.name)] })
        }
    }


    private checkHit(drag: cc.Node, data: CharacterProfileCfg, cursor: cc.Vec2) {
        const pos = this.profilesNode_.convertToNodeSpaceAR(cursor)
        for (const it of this.profilesNode_.children) {
            if (!it.active) continue
            if (it.getBoundingBox().contains(pos)) {
                const itData = it.Data as CharacterProfileCfg
                if (itData == data) return it
                let valid = true
                switch (data.type) {
                    case 1:
                    case 2:
                        valid = itData.type == 1 || itData.type == 2
                        break
                    case 3:
                    case 4:
                    case 5:
                    case 10:
                        valid = itData.type == data.type
                        break
                    case 6:
                    case 7:
                    case 8:
                        valid = itData.type == 6 || itData.type == 7 || itData.type == 8
                        break
                    case 9:
                    case 11:
                        valid = itData.type == 9 || itData.type == 11
                        break
                }
                if (valid) return it
            }
        }
        return null
    }

    private getListByTypes(types: number[]) {
        return this.dataList.filter(t => t && types.has(t.type))
    }

    private syncList() {
        this.dataList = gameHelper.archives.getList().slice()
    }

    private async showLvUp() {
        let num = this.character.getProfilesNum()
        let lv = cfgHelper.getCharacterProfileLvByUnlockCount(this.character.id, num)
        let cfg = cfgHelper.getCharacterProfileAttrById(this.character.id, lv)
        if (!!cfg && cfg.unlockCount == num) {
            let node = this.progressNode_.Child(`${cfg.id}`)
            await cc.tween(node.Child('shine'))
                .to(0.3, { opacity: 255 }, { easing: cc.easing.sineOut })
                .delay(0.4)
                .to(0.3, { opacity: 0 }, { easing: cc.easing.sineIn }).promise()
            await new Promise(resolve => {
                viewHelper.showPnl('archives/ArchivesLvUpPnl', { id: this.character.id, resolve })
            })
        }
    }

    private updateConnect() {
        let mailData = cfgHelper.getCharacterProfileByCharacterIdAndType(this.character.id, 10)
        let friend2Data = cfgHelper.getCharacterProfileByCharacterIdAndType(this.character.id, 11)
        let data11 = cfgHelper.getCharacterProfileByCharacterIdAndType(this.character.id, 11)
        this.connectNode_.Child('11').active = !!data11
        this.connectNode_.Child('9', cc.MultiFrame).setFrame(this.character.getProfile('9'))
        this.connectNode_.Child('11', cc.MultiFrame).setFrame(this.character.getProfile('11'))
        this.type3Node_.active = !!this.character.getProfile('3')
        this.profilesNode_.Child('10').active = !!mailData
        this.profilesNode_.Child('8').active = !mailData
    }

    private regDragEvent(it: cc.Node, stickType: number, data: CharacterProfileCfg, click: boolean) {
        const drag = it.Child("drag")
        if (!drag) return
        const group = Object.keys(DragGroup).find(key => DragGroup[key].includes(stickType))
        if (!group) return
        drag.Data = { group, lock: false }


        drag.off(DragLongEvent.LP_START)
        drag.off(DragLongEvent.LP_END)
        drag.off(DragLongEvent.DL_CLICK)
        drag.off(DragLongEvent.DRAG_START)
        drag.off(DragLongEvent.DRAG_MOVE)
        drag.off(DragLongEvent.DRAG_END)

        drag.on(DragLongEvent.LP_START, () => {
        }, this)
        drag.on(DragLongEvent.LP_END, () => {
        }, this)
        drag.on(DragLongEvent.DL_CLICK, () => {
            viewHelper.showAlert("archives_tips_4")
        }, this)
        drag.on(DragLongEvent.DRAG_START, () => {
            const types = DragGroup[drag.Data.group]
            this._dragInitPosData = {}
            this._dragSort = {}
            let i = 0
            for (const type of types) {
                const it = this.profilesNode_.Child(type)
                this._dragInitPosData[type] = {
                    x: it.x,
                    y: it.y
                }
                this._dragSort[type] = i++
            }
            this._lastAnimSort = Object.values(this._dragSort)
            it.opacity = 180
        }, this)
        drag.on(DragLongEvent.DRAG_MOVE, () => {
            const hit = this.checkDragHit(drag, it)
            if (!hit) return
            this.handleDragMoveOnHit(drag, it, hit)
        }, this)
        drag.on(DragLongEvent.DRAG_END, () => {
            it.opacity = 255
            this.handleDragEnd(drag, it)
        }, this)
    }

    private checkDragHit(drag: cc.Node, baseNode: cc.Node) {
        const group = drag.Data.group as string
        const types = DragGroup[group]
        const pos = ut.convertToNodeAR(drag, this.profilesNode_)
        for (const type of types) {
            const it = this.profilesNode_.Child(`${type}`)
            if (!it.active) continue
            if (it == baseNode) continue
            const targetDrag = it.Child("drag")
            if (!targetDrag) continue
            const targetData = targetDrag.Data as dragItData
            if (targetData?.lock) continue
            if (it.getBoundingBox().contains(pos)) {
                return it
            }
        }
        return null
    }

    private handleDragMoveOnHit(drag: cc.Node, baseNode: cc.Node, hitNode: cc.Node) {
        const hitDrag = hitNode.Child("drag")
        if (!hitDrag) return
        let hitData = hitDrag.Data as dragItData
        if (!hitData) {
            const group = Object.keys(DragGroup).find(key => DragGroup[key].includes(hitNode.name))
            hitData = { group: +group, lock: false }
            hitDrag.Data = hitData
        }
        if (hitData.lock) return void viewHelper.showAlert('archives_tips_7')

        const hitType = +hitNode.name
        const baseType = +baseNode.name
        const tmp = this._dragSort[baseType]
        this._dragSort[baseType] = this._dragSort[hitType]
        this._dragSort[hitType] = tmp

        this.playSortAnimation(drag, baseNode)
    }

    private playSortAnimation(drag: cc.Node, baseNode: cc.Node) {
        const newSort = Object.values(this._dragSort)
        if (newSort.join(",") == this._lastAnimSort.join(",")) return
        const keys = Object.keys(this._dragSort)
        const initPos = Object.values(this._dragInitPosData)
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i]
            const sort = this._dragSort[key]
            const lastSort = this._lastAnimSort[i]
            if (sort == lastSort) continue
            const it = this.profilesNode_.Child(key)
            if (it == baseNode) continue
            const pos = initPos[sort]
            cc.Tween.stopAllByTarget(it)
            const data = it.Child("drag").Data as dragItData
            if (data) {
                data.lock = true
            }
            cc.tween(it)
                .to(0.1, { x: pos.x, y: pos.y, opacity: 180 }, { easing: cc.easing.sineOut })
                .call(() => {
                    if (data) {
                        data.lock = false
                    }
                    it.opacity = 255
                })
                .start()
        }
        this._lastAnimSort = newSort
        this._sendChangeNet = true
    }

    private handleDragEnd(drag: cc.Node, baseNode: cc.Node) {
        const sort = this._dragSort[+baseNode.name]
        const data = drag.Data as dragItData
        const initPos = Object.values(this._dragInitPosData)
        const pos = initPos[sort]
        data.lock = true
        cc.Tween.stopAllByTarget(baseNode)
        baseNode.x = pos.x
        baseNode.y = pos.y
        data.lock = false

        if (!this._sendChangeNet) return
        this._sendChangeNet = false

        const its = this.getSortNodes(data.group)

        const sortMap = {}
        let i = -1
        for (const node of its) {
            i++
            let type = node.name
            if (node.Data && node.Data.type) {
                type = node.Data.type
                if (!node["isStick"]) {
                    continue
                }
            }
            const pos = this.character.getProfile(type)
            if (pos == null) continue
            sortMap[type] = DragGroup[data.group][i]
        }

        gameHelper.archives.changeSort(sortMap, this.character.getID())
    }

    private getSortNodes(group: number) {
        const its: cc.Node[] = DragGroup[group]
            .map(name => this.profilesNode_.Child(name))
            .sort((a, b) => {
                if (group == 1) {
                    return a.x - b.x
                }
                return b.y - a.y
            })
        return its
    }

}
