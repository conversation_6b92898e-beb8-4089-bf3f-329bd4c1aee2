import MultiColor from "../../../core/component/MultiColor";
import MultiMaterial from "../../../core/component/MultiMaterial";
import { CharacterCfg } from "../../common/constant/DataType";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

const { ccclass } = cc._decorator;
const MaxProgress = 9

@ccclass
export default class ArchivesPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected listSv_: cc.ScrollView = null // path://root/list_sv
    protected backNode_: cc.Node = null // path://back_be_n
    //@end

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(data: any) {
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.setList()
    }

    private setList() {
        let list = assetsMgr.getJson<CharacterCfg>("Character").datas
        this.listSv_.List(list.length, (it, i) => {
            let data = list[i]
            this.setOneItem(it, data, i)
        })
    }

    private setOneItem(it: cc.Node, data: CharacterCfg, index: number) {
        let character = gameHelper.passenger.getPassenger(data.id)
        let quality = data.quality
        let num = character?.getProfilesNum() || 0
        let progressNode = it.Child('progress')
        progressNode.angle = !(index % 5 % 2) ? 6 : 0
        progressNode.Child('bar').width = progressNode.Child('bar/sp').width * num / MaxProgress
        progressNode.Component(cc.MultiFrame).setFrame(quality - 1)
        progressNode.Child('bar/sp', cc.MultiFrame).setFrame(quality - 1)
        //progressNode.Child('name', cc.Label).setLocaleKey(data.name)
        it.Child('icon').active = !!character
        it.Child('mask').active = !character
        if (!character) {
            progressNode.Child('name', cc.Label).setLocaleKey('common_guiText_22')
            resHelper.loadRoleSp(data.id, it.Child('mask/sk', sp.Skeleton), this.getTag(), false)
        } else {
            progressNode.Child('name', cc.Label).setLocaleKey(data.name)
            resHelper.loadIcon(it.Child('icon'), 'archives/character', `tujian_icon_${data.id}`, this.getTag())
        }
        it.off('click')
        it.on('click', () => {
            if (!!character) {
                viewHelper.showPnl('archives/ArchivesDetailPnl', data.id)
                return
            }
            viewHelper.showAlert('乘客还没解锁')
        })
    }


}
