import { CharacterProfileAttrCfg } from "../../common/constant/DataType";
import { PassengerAttr, SkillType } from "../../common/constant/Enums";
import { animHelper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import BattleSkill from "../../model/battle/BattleSkill";
import PassengerModel from "../../model/passenger/PassengerModel";
import Skill from "../../model/passenger/Skill";

const { ccclass } = cc._decorator;

@ccclass
export default class ArchivesLvUpPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected titleLbl_: cc.Label = null // path://title_l
    protected lvNode_: cc.Node = null // path://center/lv_n
    protected attrsNode_: cc.Node = null // path://center/attrs_n
    protected skillsNode_: cc.Node = null // path://center/skills_n
    protected skillTitleNode_: cc.Node = null // path://center/skillTitle_n
    protected bottomNode_: cc.Node = null // path://bottom_n
    //@end

    private cfg: CharacterProfileAttrCfg = null
    private model: PassengerModel = null
    private diffLv: number = null
    private showAnim: boolean = true

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
    }

    public onEnter(data: any) {
        const characterId = data.id
        this.model = gameHelper.passenger.getPassenger(characterId)
        let num = this.model.getProfilesNum()
        let lv = cfgHelper.getCharacterProfileLvByUnlockCount(data.id, num)
        this.diffLv = 0
        if (data.lv) {
            this.diffLv = (lv - data.lv) * -1
            lv = data.lv
            this.showAnim = false
        }

        this.cfg = cfgHelper.getCharacterProfileAttrById(characterId, lv)

        this.initView()
        const r = data.resolve as () => void
        r?.()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private async initView() {
        this.titleLbl_.setLocaleKey(`archives_attrText${this.cfg.id}`)

        this.node.active = true
        this.lvNode_.active = false
        this.attrsNode_.active = false
        this.bottomNode_.active = false
        this.skillTitleNode_.active = false
        this.skillsNode_.active = false

        this.playActBottom()
        //这里防止后面显示的时候因为layout抖动
        this.hideSkill()
        this.actSkill()
        await this.actLv()
        if (this.showAnim) await ut.wait(0.4, this)
        await this.actAttrs()
        if (this.showAnim) await ut.wait(0.4, this)
        this.showSkill()
        if (this.showAnim) await ut.wait(1, this)
    }

    protected canClick() {
        this.node.once(cc.Node.EventType.TOUCH_END, () => { this.close() }, this)
    }
    private playActBottom() {
        this.canClick()
        this.bottomNode_.active = true
        this.bottomNode_.opacity = 255
        cc.Tween.stopAllByTarget(this.bottomNode_)
        cc.tween(this.bottomNode_)
            .to(0.6, { opacity: 0 })
            .delay(.2)
            .to(0.4, { opacity: 255 })
            .delay(1.2)
            .union()
            .repeatForever()
            .start()
    }

    protected async actAttrs() {
        let list = this.getAttrs()
        let node = this.attrsNode_
        let content = this.attrsNode_.Child('content')
        node.active = true

        content.Items(list, (it, { index, preVal, curVal, isPercent }) => {
            it.Child('icon', cc.MultiFrame).setFrame(index)
            it.Child('rich', cc.RichText).setLocaleUpdate(() => "")
        })
        if (this.showAnim) await ut.wait(0.3, this)
        content.Items(list, (it, { index, preVal, curVal, isPercent }) => {
            it.Child('rich', cc.RichText).setLocaleUpdate(() => {
                if (isPercent) {
                    return `<color=#89FF5B>+${curVal * 100}%</c>`
                }
                if (curVal > preVal) {
                    let add = this.getRealNumber(curVal - preVal, 2)
                    return `<color=#89FF5B>+${add}</c>`
                }
                return ``
            })
        })
    }

    private getRealNumber(val: number, pos: number) {
        const minPos = 0.000001
        let realVal
        for (let i = 0; i <= pos; i++) {
            realVal = val.toFixed(i)
            if (Math.abs(val - (+realVal)) < minPos) {
                return +realVal
            }
        }
        return +realVal
    }

    private getAttrs() {
        let ary = [PassengerAttr.ATTACK, PassengerAttr.HP]
        let list = []
        ary.forEach((type, index) => {
            const cur = this.model.getArchivesAttr(type, this.diffLv)
            const prev = this.model.getArchivesAttr(type, this.diffLv - 1)
            if (cur.base != prev.base) {
                let curVal = this.model.getAttr(type, 0, 0, 0, this.diffLv)
                if (curVal != 0) {
                    let preVal = this.model.getAttr(type, 0, 0, 0, this.diffLv - 1)
                    list.push({ curVal, preVal, index, isPercent: false })
                }
                return
            }
            if (cur.rate != prev.rate) {
                list.push({ curVal: cur.rate, preVal: prev.rate, index, isPercent: true })
                return
            }
        })
        return list
    }
    private async actLv() {
        this.initLv()
        await this.act123(this.lvNode_)
        await this.actLayout(this.lvNode_, 0.3)
    }
    private async act123(node: cc.Node) {
        node.Child(2).active = false
        node.Child(3).active = false
        if (this.showAnim) await ut.wait(0.3, this)
        node.Child(2).active = true
        node.Child(3).active = true
    }

    private async actLayout(node: cc.Node, time: number) {
        let layout = node.Component(cc.Layout)
        layout.enabled = false
        layout.updateLayout()
        node.children.forEach(it => {
            cc.tween(it).set({ x: 0 }).to(time, { x: it.x }).start()
        });
        if (this.showAnim) await ut.wait(time, this)
        layout.enabled = true
    }

    private initLv() {
        let node = this.lvNode_
        let role = this.model
        let level = this.cfg.id
        let preLevel = Math.max(+level - 1, 0)
        node.active = true
        node.Child(1, cc.Label).setLocaleKey("common_guiText_6", [assetsMgr.lang(role.name), assetsMgr.lang(`archives_attrText${preLevel}`)])
        node.Child(3, cc.Label).setLocaleKey(`archives_attrText${level}`)
        let s: string
    }

    protected async actSkill() {
        this.skillsNode_.active = false
        this.skillTitleNode_.active = false
        let allSkills: BattleSkill[] = []
        for (let i = 0; i < 2; i++) {
            if (!this.model.cfg.battleSkill) continue
            let type = SkillType.BATTLE
            let skillId = cfgHelper.getIdBySkillIndex(this.model.cfg.battleSkill, i)

            const cur = this.model.getArchivesSkill(i, this.diffLv)
            const prev = this.model.getArchivesSkill(i, this.diffLv - 1)
            // 无改变
            if (cur.base == prev.base && cur.rate == prev.rate) continue

            // let skill = this.model.getSkill(i)
            // if (!skill) {
            const skill = new BattleSkill()
            skill.attrRate += cur.rate
            skill.init(skillId, cur.base, this.model)
            // }
            allSkills.push(skill)
        }

        if (!allSkills.length) return
        this.skillsNode_.active = true
        this.skillTitleNode_.active = true

        this.skillsNode_.Items(allSkills, (it, data) => {
            this.initDes(it, data)
        })
    }

    protected getSkills(): { id: number, lv: number }[] {
        return cfgHelper.getSkillsByLv(this.model.getID(), this.model.getLevel())
    }

    //技能描述
    private initDes(it: cc.Node, skill: BattleSkill) {
        let content = skill.getDesc()
        if (!content) return
        let highLines = {}
        let finalContent = skill.getDesc()
        let finalParams = finalContent.params
        let getHighLine = function (content, nextContent) {
            let params = content.params;
            let nextParams = nextContent.params;
            for (let i = 0; i < params.length; i++) {
                highLines[i] = {
                    high: params[i] != nextParams[i],
                    curVal: params[i],
                    nextVal: nextParams[i]
                }
            }
        }
        resHelper.loadIcon(it.Child('icon'), 'skill/icon', skill.icon, this.getTag())
        it.Child('icon/new').active = false
        this.setSkillLv(it.Child('desc/lv'), skill)
        it.Child("lock").active = !this.model.getSkill(skill.index)

        //it.Child('desc/lv')

        const cur = this.model.getArchivesSkill(skill.index, this.diffLv)
        const prev = this.model.getArchivesSkill(skill.index, this.diffLv - 1)

        let preSkill = new BattleSkill()
        preSkill.attrRate += prev.rate
        preSkill.init(skill.getId(), Math.max(skill.getLevel() + (prev.base - cur.base), 1), skill.getRole())

        let preContent = preSkill?.getDesc()
        if (preContent) {
            getHighLine(preContent, content);
            finalParams = preSkill.getDesc().params
        }

        let boldColor = "#89f15b"
        it.Child('desc/rt', cc.RichText).setLocaleUpdate(() => {
            let triggerStr = (skill as BattleSkill).getTriggerDesc()
            triggerStr = `${triggerStr}<img src='jiantou4'/>`
            let str = assetsMgr.lang(finalContent.key)
            for (let key in highLines) {
                const line = highLines[key]
                const regex = new RegExp('\\{' + key + '\\}', 'g');
                let indices = [];
                let match
                while ((match = regex.exec(str)) !== null) {
                    indices.push(match.index);
                }
                let i = Number(key)
                for (let index of indices) {
                    let char = str.charAt(index - 1)
                    if (char != '+' && char != '-') {
                        char = ""
                    }
                    if (line.curVal == line.nextVal) {
                        str = str.replace(`${char}{${key}}`, `${char}{${key}}<img src='jiantou4'/>${char}${line.nextVal}`)
                    }
                    else {
                        str = str.replace(`${char}{${key}}`, `${char}{${key}}<img src='jiantou4'/><color=${"#89f15b"}>${char}${line.nextVal}</>`)
                    }
                }
            }
            str = ut.stringFormat(str, finalParams)
            return triggerStr + str
        })
    }

    private setSkillLv(it: cc.Node, skill: BattleSkill) {
        it.children.forEach(m => m.active = true)
        const cur = this.model.getArchivesSkill(skill.index, this.diffLv)
        const prev = this.model.getArchivesSkill(skill.index, this.diffLv - 1)
        it.Child("1").active = true
        it.Child("2").active = false
        it.Child("3").active = false
        it.Child("1", cc.MultiColor).setColor(true)
        if (cur.base != prev.base) {

            const selfSkill = this.model.getSkill(skill.index)
            const lock = !selfSkill
            let prevVal = lock ? prev.base : Math.max(1, selfSkill.getLevel() - (cur.base - prev.base))
            let curVal = lock ? cur.base : selfSkill.getLevel()
            it.Child("1").setLocaleUpdate(() => `+${assetsMgr.lang("characterDevelop_guiText_33", curVal - prevVal)}`)
            return
        }
        if (cur.rate != prev.rate) {
            it.Child("1").setLocaleUpdate(() => `+${(cur.rate - prev.rate) * 100}%`)
        }
    }

    protected showSkill() {
        this.skillsNode_.opacity = 255
        this.skillTitleNode_.opacity = 255
    }

    protected hideSkill() {
        this.skillsNode_.opacity = 0
        this.skillTitleNode_.opacity = 0
    }

}
