import BaseCmptCtrl from "../../../core/base/BaseCmptCtrl";
import { CharacterProfileCfg } from "../../common/constant/DataType";
import { ArchivesItemType } from "../../common/constant/Enums";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { resHelper } from "../../common/helper/ResHelper";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ArchivesItemCmpt extends BaseCmptCtrl {
    public async init(data: CharacterProfileCfg) {
        if (!data) return
        await this.setItem(data)
    }

    private async setItem(data: CharacterProfileCfg) {
        let root = this.node
        let type = data.type
        let node: cc.Node
        if (type == ArchivesItemType.AGE || type == ArchivesItemType.SIGN) {
            node = root.Swih('type1')[0]
            this.setAgeAndSign(node, data)
        } else if (type == ArchivesItemType.TITLE) {
            node = root.Swih('type3')[0]
            this.setTitle(node, data)
        } else if (type == ArchivesItemType.CHAT) {
            node = root.Swih('type4')[0]
            this.setChat(node, data)
        } else if (type == ArchivesItemType.THING) {
            node = root.Swih('type5')[0]
            await this.setThing(node, data)
        } else if (type == ArchivesItemType.STORY_1 || type == ArchivesItemType.STORY_2 || type == ArchivesItemType.STORY_3) {
            node = root.Swih('type6')[0]
            this.setStory(node, data)
        } else if (type == ArchivesItemType.PARTNER_1 || type == ArchivesItemType.PARTNER_2) {
            node = root.Swih('type9')[0]
            await this.setPartner(node, data)
        } else if (type == ArchivesItemType.MAIL) {
            node = root.Swih('type10')[0]
            await this.setMail(node, data)
        }
        root.setContentSize(node.getContentSize())
    }

    private setAgeAndSign(node: cc.Node, data: CharacterProfileCfg) {
        node.Child('ly/sp', cc.MultiFrame).setFrame(data.type == ArchivesItemType.SIGN)
        let character = cfgHelper.getCharacter(data.characterId)
        if (data.type == 1) {
            node.Child('ly/lb', cc.Label).setLocaleKey('common_guiText_2', character.profile.age)
        } else {
            node.Child('ly/lb', cc.Label).setLocaleKey(character.profile.sign)
        }
        node.Child("ly", cc.Layout).updateLayout()
    }
    private setTitle(node: cc.Node, data: CharacterProfileCfg) {
        node.Child('lb', cc.Label).setLocaleKey(data.content)
    }
    private setChat(node: cc.Node, data: CharacterProfileCfg) {
        node.Child('lb', cc.Label).setLocaleKey(data.content)
    }
    private async setThing(node: cc.Node, data: CharacterProfileCfg) {
        await resHelper.loadIcon(node, 'archives/item', data.icon, this.getTag())
        node.setContentSize(node.Child('val').getContentSize())
    }
    private setStory(node: cc.Node, data: CharacterProfileCfg) {
        node.Component(cc.MultiFrame).setFrame(data.type - 6)
        node.Child('lb', cc.Label).setLocaleKey(data.content)
    }
    private async setPartner(node: cc.Node, data: CharacterProfileCfg) {
        let friend
        if (data.type == ArchivesItemType.PARTNER_1) {
            friend = cfgHelper.getCharacter(data.characterId).profile.friends[0]
        } else {
            friend = cfgHelper.getCharacter(data.characterId).profile.friends[1]
        }
        if (!friend) return false
        let icon = cfgHelper.getCharacter(friend).iconCircle
        await resHelper.loadIcon(node.Child('icon'), 'passenger/icon_circle', icon, this.getTag())
        return true
    }
    private async setMail(node: cc.Node, data: CharacterProfileCfg) {

    }

}
