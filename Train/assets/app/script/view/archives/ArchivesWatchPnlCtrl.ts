import { CharacterProfileCfg } from "../../common/constant/DataType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { resHelper } from "../../common/helper/ResHelper";

const { ccclass } = cc._decorator;

const pos = {
    1: [[623, -330], [0, 0], [410, -330]],
    2: [[663, -330], [560, -330], [370, -330]]
}

const spe = "[p]"
@ccclass
export default class ArchivesWatchPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected rootNode_: cc.Node = null // path://root_n
    protected nextNode_: cc.Node = null // path://next_be_n
    protected preNode_: cc.Node = null // path://pre_be_n
    protected backNode_: cc.Node = null // path://back_be_n
    //@end
    private _showArgs: { data: CharacterProfileCfg | number, resolve: () => void } = null
    private _maxPage: number = 0
    private _page: number = 0
    private _handlePage: () => void = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        mc.lockTouch(true)
        this.setParam({ isAct: false })
        this.node.opacity = 0
    }

    public onEnter(data: any) {
        mc.lockTouch(false)
        this._showArgs = data
        this.initView()
        cc.tween(this.node)
            .to(0.15, { opacity: 255 })
            .start()
    }

    public onRemove() {
        this._showArgs.resolve?.()
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://mask_be
    onClickMask(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://next_be_n
    onClickNext(event: cc.Event.EventTouch, data: string) {
        this.pageEvt(true)
    }

    // path://pre_be_n
    onClickPre(event: cc.Event.EventTouch, data: string) {
        this.pageEvt(false)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    initView() {
        this.preNode_.active = false
        this.nextNode_.active = false

        if (typeof this._showArgs.data == "number") {
            this.setLink()
            return
        }
        this.setStory()
    }

    setStory() {
        const it = this.rootNode_.Swih("1")[0] as cc.Node
        const cfg = this._showArgs.data as CharacterProfileCfg
        it.Child("desc").setLocaleUpdate(() => "     " + assetsMgr.lang(cfg.story))
    }

    setLink() {
        const it = this.rootNode_.Swih("2")[0] as cc.Node
        const characterId = this._showArgs.data as number

        const rolesN = it.Child("roles")

        const character = cfgHelper.getCharacter(characterId)
        const desc = assetsMgr.lang(character.profile.link)
        const infoAry = desc.split(spe)

        if (infoAry.length > 1) {
            this.nextNode_.active = true
            this._handlePage = () => it.Child("rt").setLocaleUpdate(() => infoAry[this._page])
        }
        this._maxPage = infoAry.length - 1
        it.Child("rt").setLocaleUpdate(() => infoAry[this._page])

        const list = character.profile.friends
        if (list.length == 1) {
            rolesN.Child("1").active = false
        }
        list.forEach((friend, index) => {
            const it = rolesN.Child(`${index}`)
            const p = pos[list.length]
            it.setPosition(p[index][0], p[index][1])
            resHelper.loadTmpIcon(`passenger/profile/tiezhi_character_icon_${friend}`, it.Component(cc.Sprite), this.getTag())
        })
        const self = rolesN.Child("2")
        self.setPosition(pos[list.length][2][0], pos[list.length][2][1])
        resHelper.loadTmpIcon(`passenger/profile/tiezhi_character_icon_${characterId}`, self.Component(cc.Sprite), this.getTag())

    }

    pageEvt(next: boolean) {
        const lastPage = this._page
        this._page += next ? 1 : -1
        this._page = Math.max(0, Math.min(this._maxPage, this._page))
        this.nextNode_.active = this._page < this._maxPage
        this.preNode_.active = this._page > 0
        if (lastPage != this._page) {
            this._handlePage?.()
        }
    }
}
