
const { ccclass, property } = cc._decorator;

const NUMBER_OF_GATHERED_TOUCHES_FOR_MOVE_SPEED = 5;
const OUT_OF_BOUNDARY_BREAKING_FACTOR = 0.05;
const EPSILON = 1e-4;
const MOVEMENT_FACTOR = 0.7;
const ZOOM_FACTOR = 5;
const BOUNDARY_EPSILON_RAITO = 0.1;
const ZOOM_EPSILON_RAITO = 0.2;

const VEC2_ZERO = cc.v2(0, 0)

let quintEaseOut = function (time) {
    time -= 1;
    return (time * time * time * time * time + 1);
};

let getTimeInMilliseconds = function () {
    let currentTime = new Date();
    return currentTime.getMilliseconds();
};

/**
 * !#en Enum for ScrollView event type.
 * !#zh 滚动视图事件类型
 * @enum ScrollView.EventType
 */
const EventType = cc.Enum({
    /**
     * !#en The event emmitted when <PERSON>rollVie<PERSON> scroll to the top boundary of inner container
     * !#zh 滚动视图滚动到顶部边界事件
     * @property {Number} SCROLL_TO_TOP
     */
    SCROLL_TO_TOP: 0,
    /**
     * !#en The event emmitted when ScrollView scroll to the bottom boundary of inner container
     * !#zh 滚动视图滚动到底部边界事件
     * @property {Number} SCROLL_TO_BOTTOM
     */
    SCROLL_TO_BOTTOM: 1,
    /**
     * !#en The event emmitted when ScrollView scroll to the left boundary of inner container
     * !#zh 滚动视图滚动到左边界事件
     * @property {Number} SCROLL_TO_LEFT
     */
    SCROLL_TO_LEFT: 2,
    /**
     * !#en The event emmitted when ScrollView scroll to the right boundary of inner container
     * !#zh 滚动视图滚动到右边界事件
     * @property {Number} SCROLL_TO_RIGHT
     */
    SCROLL_TO_RIGHT: 3,
    /**
     * !#en The event emmitted when ScrollView is scrolling
     * !#zh 滚动视图正在滚动时发出的事件
     * @property {Number} SCROLLING
     */
    SCROLLING: 4,
    /**
     * !#en The event emmitted when ScrollView scroll to the top boundary of inner container and start bounce
     * !#zh 滚动视图滚动到顶部边界并且开始回弹时发出的事件
     * @property {Number} BOUNCE_TOP
     */
    BOUNCE_TOP: 5,
    /**
     * !#en The event emmitted when ScrollView scroll to the bottom boundary of inner container and start bounce
     * !#zh 滚动视图滚动到底部边界并且开始回弹时发出的事件
     * @property {Number} BOUNCE_BOTTOM
     */
    BOUNCE_BOTTOM: 6,
    /**
     * !#en The event emmitted when ScrollView scroll to the left boundary of inner container and start bounce
     * !#zh 滚动视图滚动到左边界并且开始回弹时发出的事件
     * @property {Number} BOUNCE_LEFT
     */
    BOUNCE_LEFT: 7,
    /**
     * !#en The event emmitted when ScrollView scroll to the right boundary of inner container and start bounce
     * !#zh 滚动视图滚动到右边界并且开始回弹时发出的事件
     * @property {Number} BOUNCE_RIGHT
     */
    BOUNCE_RIGHT: 8,
    /**
     * !#en The event emmitted when ScrollView auto scroll ended
     * !#zh 滚动视图滚动结束的时候发出的事件
     * @property {Number} SCROLL_ENDED
     */
    SCROLL_ENDED: 9,
    /**
     * !#en The event emmitted when user release the touch
     * !#zh 当用户松手的时候会发出一个事件
     * @property {Number} TOUCH_UP
     */
    TOUCH_UP: 10,
    /**
     * !#en The event emmitted when ScrollView auto scroll ended with a threshold
     * !#zh 滚动视图自动滚动快要结束的时候发出的事件
     * @property {Number} AUTOSCROLL_ENDED_WITH_THRESHOLD
     */
    AUTOSCROLL_ENDED_WITH_THRESHOLD: 11,
    /**
     * !#en The event emmitted when ScrollView scroll began
     * !#zh 滚动视图滚动开始时发出的事件
     * @property {Number} SCROLL_BEGAN
     */
    SCROLL_BEGAN: 12
});

const eventMap = {
    'scroll-to-top': EventType.SCROLL_TO_TOP,
    'scroll-to-bottom': EventType.SCROLL_TO_BOTTOM,
    'scroll-to-left': EventType.SCROLL_TO_LEFT,
    'scroll-to-right': EventType.SCROLL_TO_RIGHT,
    'scrolling': EventType.SCROLLING,
    'bounce-bottom': EventType.BOUNCE_BOTTOM,
    'bounce-left': EventType.BOUNCE_LEFT,
    'bounce-right': EventType.BOUNCE_RIGHT,
    'bounce-top': EventType.BOUNCE_TOP,
    'scroll-ended': EventType.SCROLL_ENDED,
    'touch-up': EventType.TOUCH_UP,
    'scroll-ended-with-threshold': EventType.AUTOSCROLL_ENDED_WITH_THRESHOLD,
    'scroll-began': EventType.SCROLL_BEGAN
};

/**
 * !#en
 * Layout container for a view hierarchy that can be scrolled by the user,
 * allowing it to be larger than the physical display.
 *
 * !#zh
 * 滚动视图组件
 * @class ScrollView
 * @extends Component
 */

@ccclass
export default class CameraView extends cc.Component {

    //可滚动展示内容的节点。
    @property({
        type: cc.Node,
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.content',
        formerlySerializedAs: 'content',
    })
    _content: cc.Node = null;

    @property(cc.Node)
    get content() {
        return this._content
    }
    set content(value) {
        this._unregisterEvent();
        this._content = value;
        this._registerEvent();
        if (!CC_EDITOR) {
            this.stopAutoScroll();
        }
    }

    @property(cc.Camera)
    _camera: cc.Camera = null;
    @property(cc.Camera)
    get camera() {
        return this._camera || this.node.getComponent(cc.Camera)
    }
    set camera(value) {
        this._camera = value;
        if (!CC_EDITOR) {
            this.stopAutoScroll();
        }
    }

    //是否开启水平滚动。
    @property({
        animatable: false,
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.horizontal',
    })
    horizontal: boolean = true;

    //是否开启垂直滚动。
    @property({
        animatable: false,
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.vertical',
    })
    public vertical: boolean = true;

    //是否开启滚动惯性。
    @property({
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.inertia',
    })
    inertia: boolean = true;

    //开启惯性后，在用户停止触摸后滚动多快停止，0表示永不停止，1表示立刻停止。
    @property({
        type: cc.Float,
        range: [0, 1, 0.1],
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.brake',
    })
    brake: number = 0.7;

    //是否允许滚动内容超过边界，并在停止触摸后回弹。
    @property({
        animatable: false,
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.elastic',
    })
    elastic: boolean = true;

    //回弹持续的时间，0 表示将立即反弹。
    @property({
        range: [0, 10],
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.bounceDuration',
    })
    bounceDuration: number = 0.23;


    @property(cc.Float)
    fixedMaxZoomRatio: number = 0

    fixedMinZoomRatio: number = 0
    //MainWind会将算好的值传入
    get maxZoomRatio(): number {
        if (this.fixedMaxZoomRatio)return this.fixedMaxZoomRatio
        return this._getZoomRatioByMaxSize() / this.minViewRatio;
    }

    get minZoomRatio(): number {
        if (this.fixedMinZoomRatio)return this.fixedMinZoomRatio
        return this._getZoomRatioByMaxSize() / this.maxViewRatio;
    }

    @property(cc.Float)
    maxViewRatio: number = 0.6;

    minViewRatio: number = 1;

    get viewRatio() { //可视范围 / 实际范围，范围minViewRatio~maxViewRatio
        let stdZoomRatio = this._getZoomRatioByMaxSize();
        return stdZoomRatio / this.zoomRatio
    }
    set viewRatio(value) {
        let maxViewRatio = this._getZoomRatioByMaxSize();
        this.zoomRatio = maxViewRatio / value;
    }

    //滚动视图的事件回调函数
    @property({
        type: cc.Component.EventHandler,
        tooltip: CC_DEV && 'i18n:COMPONENT.scrollview.scrollEvents',
    })
    scrollEvents: cc.Component.EventHandler[] = [];

    @property(cc.Node)
    _touchNode: cc.Node = null;
    @property(cc.Node)
    get touchNode() {
        return this._touchNode || this.content;
    }
    set touchNode(value) {
        if (this.touchNode == value) return;
        this._unregisterEvent();
        this.touchNode = value;
        this._registerEvent();
    }

    @property(cc.Boolean)
    _touchable: boolean = true;

    @property(cc.Boolean)
    get touchable() {
        return this._touchable
    }
    set touchable(value) {
        this.stopAutoScroll();
        this._touchable = value;
    }

    // @property(cc.Boolean)
    useMaxScale: boolean = false;

    private _autoZooming: boolean;
    private _autoZoomTargetDelta: number;
    private _autoZoomAttenuate: boolean;
    private _autoZoomStartRaito: number;
    private _autoZoomTotalTime: number = 0;
    private _autoZoomAccumulatedTime: number = 0;
    private _zooming: boolean;
    private _preTouchDis: number = -1;
    private _touchs: any = [];
    private _movingNow = null
    private _zoomSwitch: boolean = true;
    private _isFocus

    get zoomSwitch() {
        return this._zoomSwitch;
    }

    set zoomSwitch(bool: boolean) {
        this._zoomSwitch = bool;
    }

    get zoomRatio() {
        if (this.camera) {
            return this.camera.zoomRatio;
        }
        return 0;
    }
    set zoomRatio(value) {
        if (this.camera) {
            if (this.zoomSwitch == true) {           
                this.camera.zoomRatio = value;
            }
        }
        else {
            console.error("need camera!!!!");
        }
    }
    get isFocus() {
        return this._isFocus
    }

    set isFocus(value: boolean) {
        this._isFocus = value
    }


    private _touchMoveDisplacements: any[];
    private _touchMoveTimeDeltas: any[];
    private _touchMovePreviousTimestamp: number;
    private _touchMoved: boolean;
    private _autoScrolling: boolean;
    private _autoScrollAttenuate: boolean;
    private _autoScrollStartPosition: cc.Vec2;
    private _autoScrollTargetDelta: cc.Vec2;
    private _autoScrollTotalTime: number;
    private _autoScrollAccumulatedTime: number;
    private _autoScrollBraking: boolean;
    private _autoScrollCurrentlyOutOfBoundary: boolean;
    private _autoScrollBrakingStartPosition: cc.Vec2;
    private _outOfBoundaryAmount: cc.Vec2;
    private _outOfBoundaryAmountDirty: boolean;
    private _stopMouseWheel: boolean;
    private _mouseWheelEventElapsedTime: number;
    private _isScrollEndedWithThresholdEventFired: boolean;
    private _scrollEventEmitMask: number;
    private _isBouncing: boolean;
    private _scrolling: boolean;
    private _moveRatio: number

    private _tempPoint = cc.v2();
    private _tempPrevPoint = cc.v2();
    private _tempContentSize = cc.size(0, 0)
    private _tempMaxSize = cc.size(0, 0)
    private _tempViewSize = cc.size(0, 0)
    private _tempViewPos = cc.v2()
    private _tempMat = cc.mat4()
    private _tempVec3 = cc.v3()
    private _tempVec2 = cc.v2()
    private _tempViewBoundary = cc.rect()
    private _tempMaxBoundary = cc.rect()

    get _viewPos() {
        this.camera.node.getPosition(this._tempViewPos);
        return this.camera.node.parent.convertToWorldSpaceAR(this._tempViewPos, this._tempViewPos);
    }

    constructor() {
        super()
        this._touchMoveDisplacements = [];
        this._touchMoveTimeDeltas = [];
        this._touchMovePreviousTimestamp = 0;
        this._touchMoved = false;

        this._autoScrolling = false;
        this._autoScrollAttenuate = false;
        this._autoScrollStartPosition = cc.v2(0, 0);
        this._autoScrollTargetDelta = cc.v2(0, 0);
        this._autoScrollTotalTime = 0;
        this._autoScrollAccumulatedTime = 0;
        this._autoScrollCurrentlyOutOfBoundary = false;
        this._autoScrollBraking = false;
        this._autoScrollBrakingStartPosition = cc.v2(0, 0);

        this._outOfBoundaryAmount = cc.v2(0, 0);
        this._outOfBoundaryAmountDirty = true;
        this._stopMouseWheel = false;
        this._mouseWheelEventElapsedTime = 0.0;
        this._isScrollEndedWithThresholdEventFired = false;
        //use bit wise operations to indicate the direction
        this._scrollEventEmitMask = 0;
        this._isBouncing = false;
        this._scrolling = false;
    }

    public init(param: {
        content?: cc.Node, camera?: cc.Camera, maxViewRatio?: number, minViewRatio?: number, viewRatio?: number, position?: cc.Vec2,
        moveRatio?: number, touchNode?: cc.Node, maxZoomRatio?: number, minZoomRatio?: number, zoomRatio?: number
    }) {
        this._unregisterEvent();
        this.stopAutoScroll();

        this._content = param.content || this._content;
        this._camera = param.camera || this._camera;
        if (param.touchNode) this._touchNode = param.touchNode

        if (param.maxZoomRatio) {
            this.fixedMaxZoomRatio = param.maxZoomRatio
        }

        if (param.minZoomRatio) {
            this.fixedMinZoomRatio = param.minZoomRatio
            this.maxViewRatio = cc.misc.clamp01(this._getZoomRatioByMaxSize() / param.minZoomRatio)
        }
        else if (param.maxViewRatio) {
            this.maxViewRatio = cc.misc.clamp01(param.maxViewRatio);
        }

        if (param.zoomRatio) {
            this.viewRatio = cc.misc.clamp01(this._getZoomRatioByMaxSize() / param.zoomRatio)
        }
        else if (param.viewRatio) {
            let viewRatio = cc.misc.clampf(param.viewRatio, this.minViewRatio, this.maxViewRatio);
            this.viewRatio = viewRatio
        }

        if (param.position) {
            this.setPosition(param.position)
        }

        this._moveRatio = param.moveRatio || 1

        this._registerEvent();
        this._adjustContentOutOfBoundary();
    }

    public scrollToPos(pos: cc.Vec2, timeInSecond?: number, attenuated?: boolean) {
        pos = this._clampPos(pos);
        let moveDelta = pos.sub(this._viewPos);
        if (timeInSecond) {
            this._startAutoScroll(moveDelta, timeInSecond, attenuated !== false);
        } else {
            this._moveView(moveDelta, true);
        }
        return pos;
    }

    /*_processAutoScrolling*
     * !#en  Stop auto scroll immediately
     * !#zh  停止自动滚动, 调用此 API 可以让 Scrollview 立即停止滚动
     * @method stopAutoScroll
     */
    stopAutoScroll() {
        this._autoScrolling = false;
        this._autoScrollAccumulatedTime = this._autoScrollTotalTime;
    }

    /**
     * !#en Modify the content position.
     * !#zh 设置当前视图内容的坐标点。
     * @method setPosition
     * @param {Vec2} position - The position in content's parent space.
     */
    setPosition(position: cc.Vec2, stopAutoScroll: boolean = false) {
        if (stopAutoScroll) this.stopAutoScroll()
        if (this._viewPos.fuzzyEquals(position, EPSILON)) {
            return;
        }
        this.camera.node.parent.convertToNodeSpaceAR(position, this._tempViewPos)
        this.camera.node.setPosition(this._tempViewPos);
        this._outOfBoundaryAmountDirty = true;
    }

    /**
     * !#en Query the content's position in its parent space.
     * !#zh 获取当前视图内容的坐标点。
     * @method getPosition
     * @returns {Vec2} - The content's position in its parent space.
     */
    getPosition(out?: cc.Vec2): cc.Vec2 {
        out = out || cc.v2();
        out.x = this._viewPos.x;
        out.y = this._viewPos.y;
        return out;
    }

    /**
     * !#en Query whether the user is currently dragging the ScrollView to scroll it
     * !#zh 用户是否在拖拽当前滚动视图
     * @method isScrolling
     * @returns {Boolean} - Whether the user is currently dragging the ScrollView to scroll it
     */
    isScrolling() {
        return this._scrolling;
    }

    /**
     * !#en Query whether the ScrollView is currently scrolling because of a bounceback or inertia slowdown.
     * !#zh 当前滚动视图是否在惯性滚动
     * @method isAutoScrolling
     * @returns {Boolean} - Whether the ScrollView is currently scrolling because of a bounceback or inertia slowdown.
     */
    isAutoScrolling() {
        return this._autoScrolling;
    }

    //当前屏幕长宽 （缩放后）
    getViewSize(zoomRatio?: number) {
        zoomRatio = zoomRatio || (this.zoomRatio) || 1;
        let width = Math.floor(cc.winSize.width / zoomRatio);
        let height = Math.floor(cc.winSize.height / zoomRatio);
        if (zoomRatio == this.zoomRatio) {
            this._tempViewSize.width = width;
            this._tempViewSize.height = height;
            return this._tempViewSize;
        }
        return { width, height }
    }

    getMaxSize() {
        let ratio = 1
        if (this.elastic) {
            ratio = (1 - BOUNDARY_EPSILON_RAITO);
        }
        this._tempMaxSize.width = this.content.width * ratio;
        this._tempMaxSize.height = this.content.height * ratio;
        return this._localSizeToWorld(this.content, this._tempMaxSize);
    }

    getViewBoundary(zoomRatioOrPos?: number | cc.Vec2) {
        let pos = this._viewPos, zoomRatio = this.zoomRatio;
        if (typeof zoomRatioOrPos == 'number') {
            zoomRatio = zoomRatioOrPos;
        }
        else if (typeof zoomRatioOrPos == 'object') {
            pos = zoomRatioOrPos;
        }
        let { width, height } = this.getViewSize(zoomRatio);

        if (zoomRatio == this.zoomRatio && pos.fuzzyEquals(this._viewPos, EPSILON)) {
            return this._updateRect(this._tempViewBoundary, pos.x - width * 0.5, pos.y - height * 0.5, width, height)
        }
        return cc.rect(pos.x - width * 0.5, pos.y - height * 0.5, width, height);
    }

    getMaxBoundary() {
        let { width, height } = this.getMaxSize()
        if (this.content) {
            let contentPos = this.content.getPosition(this._tempVec2);
            let contentSize = this._getContentSize()
            let worldPos = this.content.parent.convertToWorldSpaceAR(contentPos, this._tempVec2)
            let anchor = this.content.getAnchorPoint();
            let centerX = worldPos.x - (anchor.x - 0.5) * contentSize.width;
            let centerY = worldPos.y - (anchor.y - 0.5) * contentSize.height;
            let leftBoundary = centerX - width * 0.5;
            let bottomBoundary = centerY - height * 0.5;
            return this._updateRect(this._tempMaxBoundary, leftBoundary, bottomBoundary, width, height)
        }
        return this._updateRect(this._tempMaxBoundary, 0, 0, width, height);
    }

    //private methods
    _registerEvent() {
        let touchNode = this.touchNode;
        if (!touchNode) return
        this._touchs.length = 0;
        touchNode.on(cc.Node.EventType.TOUCH_START, this._onTouchBegan, this);
        touchNode.on(cc.Node.EventType.TOUCH_MOVE, this._onTouchMoved, this);
        touchNode.on(cc.Node.EventType.TOUCH_END, this._onTouchEnded, this);
        touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this._onTouchEnded, this);
        touchNode.on(cc.Node.EventType.MOUSE_WHEEL, this._onMouseWheel, this);
    }

    _unregisterEvent() {
        let touchNode = this.touchNode;
        if (!touchNode) return;
        touchNode.off(cc.Node.EventType.TOUCH_START, this._onTouchBegan, this);
        touchNode.off(cc.Node.EventType.TOUCH_MOVE, this._onTouchMoved, this);
        touchNode.off(cc.Node.EventType.TOUCH_END, this._onTouchEnded, this);
        touchNode.off(cc.Node.EventType.TOUCH_CANCEL, this._onTouchEnded, this);
        touchNode.off(cc.Node.EventType.MOUSE_WHEEL, this._onMouseWheel, this);
    }

    _onMouseWheel(event) {
        if (!this.enabledInHierarchy) return;
        if (!(event.eventPhase === cc.Event.AT_TARGET && event.target === this.touchNode)) return;

        let deltaDis = 0
        let wheelPrecision = -0.5;
        if (CC_JSB || CC_RUNTIME) {
            wheelPrecision = -7;
        }
        deltaDis = event.getScrollY() * wheelPrecision

        this._mouseWheelEventElapsedTime = 0;
        this._processDeltaZoom(deltaDis);

        if (!this._stopMouseWheel) {
            this._handlePressLogic();
            this.schedule(this._checkMouseWheel, 1.0 / 60);
            this._stopMouseWheel = true;
        }
    }

    _checkMouseWheel(dt) {
        let currentOutOfBoundary = this._getHowMuchOutOfBoundaryByMove();
        let maxElapsedTime = 0.1;

        if (!currentOutOfBoundary.fuzzyEquals(VEC2_ZERO, EPSILON)) {
            this.unschedule(this._checkMouseWheel);
            this._dispatchEvent('scroll-ended');
            this._stopMouseWheel = false;
            return;
        }

        this._mouseWheelEventElapsedTime += dt;

        // mouse wheel event is ended
        if (this._mouseWheelEventElapsedTime > maxElapsedTime) {
            this._handleReleaseZoomLogic();
            this.unschedule(this._checkMouseWheel);
            this._dispatchEvent('scroll-ended');
            this._stopMouseWheel = false;
        }
    }

    // touch event handler
    _onTouchBegan(event) {
        if (!this.enabledInHierarchy) return;
        if (!(event.eventPhase === cc.Event.AT_TARGET && event.target === this.touchNode)) return;
        if (!this.touchable) {
            twlog.info('_onTouchBegan1')
            return;
        }

        if (this.content && this.camera) {
            const id = event.getID()
            if (this._touchs.some(m => m.id === id) || this._touchs.length >= 2) {
                twlog.info('_onTouchBegan2')
                return
            }
            this._touchs.push({ id: id, location: event.getLocation() })
            if (this._touchs.length === 2) {
                this._handlePressZoomLogic(this._touchs)
            }
            else {
                this._preTouchDis = -1
                this._handlePressLogic();
            }
        }
        this._touchMoved = false;
    }

    _onTouchMoved(event) {
        if (!this.enabledInHierarchy) {
            return;
        }
        if (!(event.eventPhase === cc.Event.AT_TARGET && event.target === this.touchNode)) return;
        if (!this.touchable) {
            return;
        }

        const id = event.getID()
        let touch = this._touchs.find(m => m.id === id)
        if (!touch) {
            return
        }

        touch.location.set(event.getLocation())
        if (this.content && this.camera) {
            if (this._touchs.length === 2) {
                this._handleZoomLogic(this._touchs)
            }
            else {
                this._handleMoveLogic(event.touch);
            }
        }
    }

    _onTouchEnded(event) {
        if (!this.enabledInHierarchy) return;
        if (!(event.eventPhase === cc.Event.AT_TARGET && event.target === this.touchNode)) return;
        if (!this.touchable) return;

        this._dispatchEvent('touch-up');

        const id = event.getID()
        const i = this._touchs.findIndex(m => m.id === id)
        if (i === -1) {
            return
        }
        let touch = this._touchs.splice(i, 1)[0]
        touch.location.set(event.getLocation())
        if (this.content && this.camera) {
            if (this._touchs.length === 1) {
                this._handleReleaseZoomLogic();
            } else if (this._preTouchDis === -1) {// 如果只有一个 那么就是点击
                this._handleReleaseLogic(event.touch);
            } else { //这里表示是触摸了两个点之后松开 要做摄像机自动还原缩放
            }
        }
    }

    _processDeltaMove(deltaMove) {
        this._scrollChildren(deltaMove);
        this._gatherTouchMove(deltaMove);
    }

    // Contains node angle calculations
    _getLocalAxisAlignDelta(touch) {
        this.camera.getScreenToWorldPoint(touch.getLocation(), this._tempPoint);
        this.camera.getScreenToWorldPoint(touch.getPreviousLocation(), this._tempPrevPoint);
        let result = this._tempPrevPoint.sub(this._tempPoint);
        const factor = 5
        let absX = Math.abs(result.x);
        let absY = Math.abs(result.y);
        if (absX > absY * factor) {
            result.y = 0;
        }
        else if (absY > absX * factor) {
            result.x = 0;
        }
        return result
    }

    _handleMoveLogic(touch) {
        let deltaMove = this._getLocalAxisAlignDelta(touch);
        this._processDeltaMove(deltaMove);
    }

    _scrollChildren(deltaMove) {
        deltaMove = this._clampDelta(deltaMove);

        let realMove = deltaMove;
        let outOfBoundary;

        let { xMin, yMin, xMax, yMax } = this.getViewBoundary()
        let contentBoundary = this.getMaxBoundary();

        if (this.elastic) {
            outOfBoundary = this._getHowMuchOutOfBoundaryByMove();
            let contentWorldSize = this._getContentSize();
            let maxWorldSize = this.getMaxSize();
            if (outOfBoundary.x !== 0) {
                let offsetWidth = (contentWorldSize.width - maxWorldSize.width) / 2;
                if (offsetWidth > 0) {
                    let outX = Math.min(Math.abs(outOfBoundary.x), offsetWidth)
                    realMove.x *= (1 - outX / offsetWidth)
                }
            }

            if (outOfBoundary.y !== 0) {
                let offsetHeight = (contentWorldSize.height - maxWorldSize.height) / 2;
                if (offsetHeight > 0) {
                    let outY = Math.min(Math.abs(outOfBoundary.y), offsetHeight)
                    realMove.y *= (1 - outY / offsetHeight)
                }
            }

            realMove.x *= (outOfBoundary.x === 0 ? 1 : 0.5);
            realMove.y *= (outOfBoundary.y === 0 ? 1 : 0.5);
        }

        if (!this.elastic) {
            outOfBoundary = this._getHowMuchOutOfBoundaryByMove(realMove);
            realMove = realMove.sub(outOfBoundary);
        }

        let scrollEventType = 'nothing';


        if (realMove.y > 0) { //up
            if (yMax + realMove.y > contentBoundary.yMax) {
                scrollEventType = 'scroll-to-top';
            }
        }
        else if (realMove.y < 0) { //down
            if (yMin + realMove.y > contentBoundary.yMin) {
                scrollEventType = 'scroll-to-down';
            }
        }
        if (realMove.x < 0) { //left
            if (xMax + realMove.x > contentBoundary.xMax) {
                scrollEventType = 'scroll-to-right';
            }
        }
        else if (realMove.x > 0) { //right
            if (xMin + realMove.x > contentBoundary.xMin) {
                scrollEventType = 'scroll-to-left';
            }
        }
        this._movingNow = realMove
        realMove.x *= this._moveRatio
        this._moveView(realMove, false);

        if (realMove.x !== 0 || realMove.y !== 0) {
            if (!this._scrolling) {
                this._scrolling = true;
                this._dispatchEvent('scroll-began');
            }
            this._dispatchEvent('scrolling');
        }

        if (scrollEventType !== 'nothing') {
            this._dispatchEvent(scrollEventType);
        }

    }

    _handlePressLogic() {
        if (this._autoScrolling) {
            this._dispatchEvent('scroll-ended');
        }
        this._autoScrolling = false;
        this._isBouncing = false;

        this._touchMovePreviousTimestamp = getTimeInMilliseconds();
        this._touchMoveDisplacements.length = 0;
        this._touchMoveTimeDeltas.length = 0;
    }

    _clampDelta(delta) {
        let contentSize = this.getMaxSize();
        let scrollViewSize = this.getViewSize();
        if (contentSize.width < scrollViewSize.width) {
            delta.x = 0;
        }
        if (contentSize.height < scrollViewSize.height) {
            delta.y = 0;
        }

        return delta;
    }

    _gatherTouchMove(delta) {
        delta = this._clampDelta(delta);

        while (this._touchMoveDisplacements.length >= NUMBER_OF_GATHERED_TOUCHES_FOR_MOVE_SPEED) {
            this._touchMoveDisplacements.shift();
            this._touchMoveTimeDeltas.shift();
        }

        this._touchMoveDisplacements.push(delta);

        let timeStamp = getTimeInMilliseconds();
        this._touchMoveTimeDeltas.push((timeStamp - this._touchMovePreviousTimestamp) / 1000);
        this._touchMovePreviousTimestamp = timeStamp;
    }

    _startBounceBackIfNeeded() {
        if (!this.elastic) {
            return false;
        }

        let bounceBackAmount = this._getHowMuchOutOfBoundaryByMove();
        bounceBackAmount = this._clampDelta(bounceBackAmount);

        if (bounceBackAmount.fuzzyEquals(VEC2_ZERO, EPSILON)) {
            return false;
        }

        let bounceBackTime = Math.max(this.bounceDuration, 0);
        this._startAutoScroll(bounceBackAmount.neg(), bounceBackTime, true);

        if (!this._isBouncing) {
            if (bounceBackAmount.y > 0) this._dispatchEvent('bounce-top');
            if (bounceBackAmount.y < 0) this._dispatchEvent('bounce-bottom');
            if (bounceBackAmount.x > 0) this._dispatchEvent('bounce-right');
            if (bounceBackAmount.x < 0) this._dispatchEvent('bounce-left');
            this._isBouncing = true;
        }

        return true;
    }

    _processInertiaScroll() {
        let bounceBackStarted = this._startBounceBackIfNeeded();
        if (!bounceBackStarted && this.inertia) {
            let touchMoveVelocity = this._calculateTouchMoveVelocity();
            if (!touchMoveVelocity.fuzzyEquals(VEC2_ZERO, EPSILON) && this.brake < 1) {
                this._startInertiaScroll(touchMoveVelocity);
            }
        }
    }

    _handleReleaseLogic(touch) {
        let delta = this._getLocalAxisAlignDelta(touch);
        this._gatherTouchMove(delta);
        this._processInertiaScroll();
        if (this._scrolling) {
            this._scrolling = false;
            if (!this._autoScrolling) {
                this._dispatchEvent('scroll-ended');
            }
        }
    }

    _isOutOfBoundary() {
        let { xMin, xMax, yMax, yMin } = this._getHowMuchOutOfBoundary();
        return !this._fuzzyEquals(xMin, 0) ||
            !this._fuzzyEquals(xMax, 0) ||
            !this._fuzzyEquals(yMax, 0) ||
            !this._fuzzyEquals(yMin, 0)
    }

    _fuzzyEquals(a, b) {
        return Math.abs(a - b) <= EPSILON;
    }

    _isNecessaryAutoScrollBrake() {
        if (this._autoScrollBraking) {
            return true;
        }

        if (this._isOutOfBoundary()) {
            if (!this._autoScrollCurrentlyOutOfBoundary) {
                this._autoScrollCurrentlyOutOfBoundary = true;
                this._autoScrollBraking = true;
                this._autoScrollBrakingStartPosition = cc.v2(this._viewPos);
                return true;
            }

        } else {
            this._autoScrollCurrentlyOutOfBoundary = false;
        }

        return false;
    }

    getScrollEndedEventTiming() {
        return EPSILON;
    }

    _processAutoScrolling(dt) {
        let isAutoScrollBrake = this._isNecessaryAutoScrollBrake();
        let brakingFactor = isAutoScrollBrake ? OUT_OF_BOUNDARY_BREAKING_FACTOR : 1;
        this._autoScrollAccumulatedTime += dt * (1 / brakingFactor);

        let percentage = Math.min(1, this._autoScrollAccumulatedTime / this._autoScrollTotalTime);
        if (this._autoScrollAttenuate) {
            percentage = quintEaseOut(percentage);
        }

        let newPosition = this._autoScrollStartPosition.add(this._autoScrollTargetDelta.mul(percentage));
        let reachedEnd = Math.abs(percentage - 1) <= EPSILON;

        let fireEvent = Math.abs(percentage - 1) <= this.getScrollEndedEventTiming();
        if (fireEvent && !this._isScrollEndedWithThresholdEventFired) {
            this._dispatchEvent('scroll-ended-with-threshold');
            this._isScrollEndedWithThresholdEventFired = true;
        }

        if (this.elastic) {
            let brakeOffsetPosition = newPosition.sub(this._autoScrollBrakingStartPosition);
            if (isAutoScrollBrake) {
                brakeOffsetPosition = brakeOffsetPosition.mul(brakingFactor);
            }
            newPosition = this._autoScrollBrakingStartPosition.add(brakeOffsetPosition);
            let moveDelta = newPosition.sub(this._viewPos);
            let outOfBoundary = this._getHowMuchOutOfBoundaryByMove(moveDelta);
            let maxSize = this.getMaxSize();
            let contentSize = this._getContentSize();
            if (Math.abs(outOfBoundary.x) >= (contentSize.width - maxSize.width) * 0.5 || Math.abs(outOfBoundary.y) >= (contentSize.height - maxSize.height) * 0.5) {
                newPosition = this._viewPos;
                reachedEnd = true;
            }
        } else {
            let moveDelta = newPosition.sub(this._viewPos);
            let outOfBoundary = this._getHowMuchOutOfBoundaryByMove(moveDelta);
            if (!outOfBoundary.fuzzyEquals(VEC2_ZERO, EPSILON)) {
                newPosition = newPosition.sub(outOfBoundary);
                reachedEnd = true;
            }
        }

        if (reachedEnd) {
            this._autoScrolling = false;
        }

        let deltaMove = newPosition.sub(this._viewPos);
        this._moveView(this._clampDelta(deltaMove), reachedEnd);
        this._dispatchEvent('scrolling');

        // scollTo API controll move
        if (!this._autoScrolling) {
            this._isBouncing = false;
            this._scrolling = false;
            this._dispatchEvent('scroll-ended');
        }
    }

    _startInertiaScroll(touchMoveVelocity) {
        let inertiaTotalMovement = touchMoveVelocity.mul(MOVEMENT_FACTOR);
        this._startAttenuatingAutoScroll(inertiaTotalMovement, touchMoveVelocity);
    }

    _calculateAttenuatedFactor(distance) {
        if (this.brake <= 0) {
            return (1 - this.brake);
        }

        //attenuate formula from: http://learnopengl.com/#!Lighting/Light-casters
        return (1 - this.brake) * (1 / (1 + distance * 0.000014 + distance * distance * 0.000000008));
    }

    _startAttenuatingAutoScroll(deltaMove, initialVelocity) {
        let time = this._calculateAutoScrollTimeByInitalSpeed(initialVelocity.mag());


        let targetDelta = deltaMove.normalize();
        let contentSize = this.getMaxSize();
        let scrollviewSize = this.getViewSize();

        let totalMoveWidth = (contentSize.width - scrollviewSize.width);
        let totalMoveHeight = (contentSize.height - scrollviewSize.height);

        let attenuatedFactorX = this._calculateAttenuatedFactor(totalMoveWidth);
        let attenuatedFactorY = this._calculateAttenuatedFactor(totalMoveHeight);

        targetDelta = cc.v2(targetDelta.x * totalMoveWidth * (1 - this.brake) * attenuatedFactorX, targetDelta.y * totalMoveHeight * attenuatedFactorY * (1 - this.brake));

        let originalMoveLength = deltaMove.mag();
        let factor = targetDelta.mag() / originalMoveLength;
        targetDelta = targetDelta.add(deltaMove);

        if (this.brake > 0 && factor > 7) {
            factor = Math.sqrt(factor);
            targetDelta = deltaMove.mul(factor).add(deltaMove);
        }

        if (this.brake > 0 && factor > 3) {
            factor = 3;
            time = time * factor;
        }

        if (this.brake === 0 && factor > 1) {
            time = time * factor;
        }

        this._startAutoScroll(targetDelta, time, true);
    }

    _calculateAutoScrollTimeByInitalSpeed(initalSpeed) {
        return Math.sqrt(Math.sqrt(initalSpeed / 5));
    }

    _startAutoScroll(deltaMove, timeInSecond, attenuated) {
        let adjustedDeltaMove = this._flattenVectorByDirection(deltaMove);

        this._autoScrolling = true;
        this._autoScrollTargetDelta = adjustedDeltaMove;
        this._autoScrollAttenuate = attenuated;
        this._autoScrollStartPosition = cc.v2(this._viewPos);
        this._autoScrollTotalTime = timeInSecond;
        this._autoScrollAccumulatedTime = 0;
        this._autoScrollBraking = false;
        this._isScrollEndedWithThresholdEventFired = false;
        this._autoScrollBrakingStartPosition = cc.v2(0, 0);

        let currentOutOfBoundary = this._getHowMuchOutOfBoundaryByMove();
        if (!currentOutOfBoundary.fuzzyEquals(VEC2_ZERO, EPSILON)) {
            this._autoScrollCurrentlyOutOfBoundary = true;
        }
    }

    _calculateTouchMoveVelocity() {
        let totalTime = 0;
        totalTime = this._touchMoveTimeDeltas.reduce(function (a, b) {
            return a + b;
        }, totalTime);

        if (totalTime <= 0 || totalTime >= 0.5) {
            return cc.v2(0, 0);
        }

        let totalMovement = cc.v2(0, 0);
        totalMovement = this._touchMoveDisplacements.reduce(function (a, b) {
            return a.add(b);
        }, totalMovement);

        return cc.v2(totalMovement.x * (1 - this.brake) / totalTime,
            totalMovement.y * (1 - this.brake) / totalTime);
    }

    _flattenVectorByDirection(vector) {
        let result = vector;
        result.x = this.horizontal ? result.x : 0;
        result.y = this.vertical ? result.y : 0;
        return result;
    }

    _moveView(deltaMove, canStartBounceBack?) {
        let adjustedMove = this._flattenVectorByDirection(deltaMove);
        let newPosition = this._viewPos.add(adjustedMove);

        this.setPosition(newPosition);

        if (this.elastic && canStartBounceBack) {
            this._startBounceBackIfNeeded();
        }
    }

    private _getHowMuchOutOfBoundaryByMove(addition?: cc.Vec2) {
        addition = addition || cc.v2(0, 0);
        if (addition.fuzzyEquals(VEC2_ZERO, EPSILON) && !this._outOfBoundaryAmountDirty) {
            return this._outOfBoundaryAmount;
        }

        let pos = this._viewPos;
        pos = pos.add(addition, this._tempVec2);

        let { xMin, xMax, yMin, yMax } = this._getHowMuchOutOfBoundary(this.getViewBoundary(pos))
        let outOfBoundaryAmount = cc.v2(xMin + xMax, yMin + yMax)

        if (addition.fuzzyEquals(VEC2_ZERO, EPSILON)) {
            this._outOfBoundaryAmount = outOfBoundaryAmount;
            this._outOfBoundaryAmountDirty = false;
        }

        outOfBoundaryAmount = this._clampDelta(outOfBoundaryAmount);

        return outOfBoundaryAmount;
    }

    _dispatchEvent(event) {
        if (event === 'scroll-ended') {
            this._scrollEventEmitMask = 0;

        } else if (event === 'scroll-to-top'
            || event === 'scroll-to-bottom'
            || event === 'scroll-to-left'
            || event === 'scroll-to-right') {

            let flag = (1 << eventMap[event]);
            if (this._scrollEventEmitMask & flag) {
                return;
            } else {
                this._scrollEventEmitMask |= flag;
            }
        }
        cc.Component.EventHandler.emitEvents(this.scrollEvents, this, eventMap[event]);
        this.node.emit(event, this);
    }

    _adjustContentOutOfBoundary() {
        if (!this.content || !this.camera) return;
        this._outOfBoundaryAmountDirty = true;
        // console.log(this.getViewBoundary())
        // console.log(this.getMaxBoundary())
        // console.log(this.zoomRatio)

        if (this.zoomRatio > this.maxZoomRatio) {
            this.zoomRatio = this.maxZoomRatio;
        }
        else if (this.zoomRatio < this.minZoomRatio) {
            this.zoomRatio = this.minZoomRatio;
        }

        if (this._isOutOfBoundary()) {
            let boundary = this._getHowMuchOutOfBoundary();
            let { xMin, yMin, xMax, yMax } = boundary;
            this._tempVec2.x = -(xMin + xMax);
            this._tempVec2.y = -(yMin + yMax)
            this._moveView(this._tempVec2);
        }

        // console.log(this.getViewBoundary())
        // console.log(this.getMaxBoundary())
        // console.log(this.zoomRatio, this.maxZoomRatio, this.minZoomRatio)
    }

    start() {
        cc.director.once(cc.Director.EVENT_BEFORE_DRAW, this._adjustContentOutOfBoundary, this);
    }

    onDisable() {
        if (!CC_EDITOR) {
            this._unregisterEvent();
        }
        this.stopAutoScroll();
    }

    onEnable() {
        if (!CC_EDITOR) {
            this._registerEvent();
        }
    }

    update(dt) {
        if (this._autoScrolling) {
            this._processAutoScrolling(dt);
        }
        if (this._autoZooming) {
            this._processAutoZooming(dt);
        }
    }

    isStopMoving() {
        return this._autoScrolling
    }

    _handlePressZoomLogic([a, b]) {
        if (this._autoScrolling) {
            this._dispatchEvent('scroll-ended');
        }
        this._autoScrolling = false;
        this._isBouncing = false;
        const c = a.location.sub(b.location)
        this._preTouchDis = c.mag() * ZOOM_FACTOR;
    }

    private _getHowMuchOutOfBoundaryByZoom(delta?: number) {
        delta = delta || 0;
        let zoomRatio = this.zoomRatio + delta;
        return this._getHowMuchOutOfBoundary(this.getViewBoundary(zoomRatio))
    }

    private _getHowMuchOutOfBoundary(viewBoundary?: { xMin: number, yMin: number, xMax: number, yMax: number }) {
        let { xMin, yMin, xMax, yMax } = viewBoundary || this.getViewBoundary();
        let maxBoundary = this.getMaxBoundary();

        let outOfBoundaryAmount = { xMin: 0, xMax: 0, yMin: 0, yMax: 0 };
        if (maxBoundary.xMin > xMin) {
            outOfBoundaryAmount.xMin = xMin - maxBoundary.xMin;
        }
        if (maxBoundary.xMax < xMax) {
            outOfBoundaryAmount.xMax = xMax - maxBoundary.xMax;
        }

        if (maxBoundary.yMax < yMax) {
            outOfBoundaryAmount.yMax = yMax - maxBoundary.yMax;
        }
        if (maxBoundary.yMin > yMin) {
            outOfBoundaryAmount.yMin = yMin - maxBoundary.yMin;
        }
        return outOfBoundaryAmount;
    }

    _isZoomOut({ xMin, xMax, yMin, yMax }) {
        return (xMin != 0 && xMax != 0) || (yMin != 0 && yMax != 0);
    }

    _handleZoomLogic([a, b]) {
        let currentDis = a.location.sub(b.location).mag() * ZOOM_FACTOR;
        this._processDeltaZoom(this._preTouchDis - currentDis);
        this._preTouchDis = currentDis;
    }
    //放大 = 缩小当前屏幕长宽 -> -，缩小是+
    _processDeltaZoom(deltaDis) {
        let viewSize = this.getViewSize();
        let diagonal = Math.sqrt(viewSize.width * viewSize.width + viewSize.height * viewSize.height);
        let raito = (diagonal + deltaDis) / diagonal;
        let newZoomRatio = this._calZoomRatioBySize({ width: viewSize.width * raito, height: viewSize.height * raito })
        let delta = newZoomRatio - this.zoomRatio;

        if (!this.elastic) {
            if (newZoomRatio > this.maxZoomRatio) {
                delta = this.maxZoomRatio - this.zoomRatio
            }
            else if (newZoomRatio < this.minZoomRatio) {
                delta = this.minZoomRatio - this.zoomRatio
            }
            newZoomRatio = this.zoomRatio + delta
        }

        let boundary = this._getHowMuchOutOfBoundaryByZoom(delta)
        let { xMin, xMax, yMin, yMax } = boundary;
        let out = this._isZoomOut(boundary)

        let outOfMax = newZoomRatio > this.maxZoomRatio;
        let outOfMin = newZoomRatio < this.minZoomRatio;
        if (this.elastic) {

            if (out) {
                let minZoomRatio = this._calZoomRatioBySize(this.getMaxSize());
                let limitZoomRatio = this._calZoomRatioBySize(this._getContentSize());
                if (limitZoomRatio < minZoomRatio) {
                    let minZoomOffset = minZoomRatio - newZoomRatio;
                    let limitZoomOffset = minZoomRatio - limitZoomRatio;
                    minZoomOffset = Math.min(minZoomOffset, limitZoomOffset)
                    delta *= (1 - minZoomOffset / limitZoomOffset);
                }
            }
            
            else if (outOfMax) {
                let maxZoomOffset = newZoomRatio - this.maxZoomRatio;
                let limitZoomOffset = ZOOM_EPSILON_RAITO;
                maxZoomOffset = Math.min(maxZoomOffset, limitZoomOffset)
                delta *= (1 - maxZoomOffset / limitZoomOffset);
            }
            else if (outOfMin) {
                let minZoomOffset = this.minZoomRatio - newZoomRatio;
                let limitZoomOffset = ZOOM_EPSILON_RAITO;
                minZoomOffset = Math.min(minZoomOffset, limitZoomOffset)
                delta *= (1 - minZoomOffset / limitZoomOffset);
            }

            delta *= ((out || outOfMin || outOfMax) ? 0.5 : 1)
        }

        if (!this.elastic) {
            if (out || outOfMin || outOfMax) {
                delta = 0;
            }
        }

        this._tempVec2.x = -(xMin + xMax);
        this._tempVec2.y = -(yMin + yMax);
        this._moveView(this._tempVec2);
        delta *= this._moveRatio
        if (!this.isFocus) {
            this.zoomRatio += delta
        }
        // console.log(this.zoomRatio)
    }

    _handleReleaseZoomLogic() {
        this._startZoomBounceBackIfNeeded()
    }

    _startZoomBounceBackIfNeeded() {
        if (!this.elastic) {
            return false;
        }

        let bounceBackAmount = this._getHowMuchOutOfBoundary();
        let out = this._isZoomOut(bounceBackAmount);
        let outOfMax = this.zoomRatio > this.maxZoomRatio
        let outOfMin = this.zoomRatio < this.minZoomRatio

        let zoomRatio
        if (outOfMax) {
            zoomRatio = this.maxZoomRatio;
        }
        else if (outOfMin) {
            zoomRatio = this.minZoomRatio
        }
        else if (out) {
            zoomRatio = this._calZoomRatioBySize(this.getMaxBoundary());
        }
        else {
            return false;
        }

        let bounceBackTime = Math.max(this.bounceDuration, 0);
        this._startAutoZoom(zoomRatio - this.zoomRatio, bounceBackTime, true);

        return true;
    }

    _startAutoZoom(zoomRatio, timeInSecond, attenuated) {
        this._autoZooming = true;
        this._autoZoomTargetDelta = zoomRatio;
        this._autoZoomAttenuate = attenuated;
        this._autoZoomStartRaito = this.zoomRatio;
        this._autoZoomTotalTime = timeInSecond;
        this._autoZoomAccumulatedTime = 0;
    }

    private _processAutoZooming(dt) {
        this._autoZoomAccumulatedTime += dt;

        let percentage = Math.min(1, this._autoZoomAccumulatedTime / this._autoZoomTotalTime);
        if (this._autoZoomAttenuate) {
            percentage = quintEaseOut(percentage);
        }

        let newZoomRatio = this._autoZoomStartRaito + this._autoZoomTargetDelta * percentage;
        let reachedEnd = Math.abs(percentage - 1) <= EPSILON;

        let zoomeDelta = newZoomRatio - this.zoomRatio;
        let boundary = this._getHowMuchOutOfBoundaryByZoom(zoomeDelta);

        let out = this._isZoomOut(boundary);
        let outOfMax = this.zoomRatio > this.maxZoomRatio
        let outOfMin = this.zoomRatio < this.minZoomRatio

        if (this.elastic) {
        } else {
            if (out || outOfMin || outOfMax) {
                zoomeDelta = 0;
                reachedEnd = true
            }
        }

        this._tempVec2.x = -(boundary.xMax + boundary.xMin);
        this._tempVec2.y = -(boundary.yMax + boundary.yMin)
        this._moveView(this._tempVec2);
        this.zoomRatio = newZoomRatio

        if (reachedEnd) {
            this._autoZooming = false;
        }

        this._dispatchEvent('zooming');

        // scollTo API controll move
        if (!this._autoZooming) {
            this._zooming = false;
            this._dispatchEvent('zoom-ended');
        }
    }
    //屏占比 以前是按照长和宽的屏占比取max，现在改为宽的占比
    public _calZoomRatioBySize(size: { width: number, height: number }) {
        //let ws = size.width / cc.winSize.width;
        let wh = size.height / cc.winSize.height;
        //let minScale = Math.min(ws, wh);
        //return 1 / minScale
        return 1 / wh;
    }

    _localSizeToWorld(node: cc.Node, out: { width: number, height: number }) {
        node.getWorldMatrix(this._tempMat)
        cc.Mat4.getScaling(this._tempVec3, this._tempMat);
        out.width *= this._tempVec3.x;
        out.height *= this._tempVec3.y;
        return out;
    }

    _updateRect(rect: cc.Rect, x: number, y: number, width: number, height: number) {
        rect.x = x;
        rect.y = y;
        rect.width = width;
        rect.height = height;
        return rect;
    }

    _clampPos(position) {
        let { width, height } = this.getViewSize();
        let { xMin, xMax, yMin, yMax } = this.getMaxBoundary();
        let x = cc.misc.clampf(position.x, xMin + width * 0.5, xMax - width * 0.5)
        let y = cc.misc.clampf(position.y, yMin + height * 0.5, yMax - height * 0.5)
        return cc.v2(x, y)
    }

    _getContentSize() {
        this._tempContentSize.width = this.content.width;
        this._tempContentSize.height = this.content.height;
        return this._localSizeToWorld(this.content, this._tempContentSize)
    }
    //初始宽度屏占比
    _getZoomRatioByMaxSize() {
        if (this.useMaxScale) {
            let maxScale = Math.max(this._tempContentSize.width / cc.winSize.width, this._tempContentSize.height / cc.winSize.height);
            return this._calZoomRatioBySize({ width: this._tempContentSize.width * maxScale, height: this._tempContentSize.height * maxScale });
        }
        else {
            return this._calZoomRatioBySize(this.getMaxSize())
        }
    }
}