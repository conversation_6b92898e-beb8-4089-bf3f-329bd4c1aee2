import { TILE_SIZE_HALF } from "../../common/constant/Constant";
import CameraView from "./CameraView";

const { ccclass, property } = cc._decorator;

const MOVERATIO = 0.1
const ZOOMRATIO = 10

@ccclass
export default class BgCameraCtrl extends cc.Component {

    @property(cc.Camera)
    bgCamera: cc.Camera = null

    private cameraView: CameraView = null
    private oldLoc: cc.Vec2
    private loc: cc.Vec2
    private oldZoomRatio: number
    private zoomRatio: number
    private move: cc.Vec2
    private zoomDiff: number
    private bgSize: cc.Size
    private maxZoomRatio: number
    private canvasBoundary: cc.Vec2
    private bgNode: cc.Node

    private startLoc: cc.Vec2

    onLoad() {
    }

    init() {
        this.bgNode = cc.find('Canvas').FindChild('Wind/MainWind/bg_n')
        this.startLoc = this.bgNode.getPosition()
        this.bgSize = this.bgNode.getContentSize()
        let pos = this.node.parent.convertToNodeSpaceAR(this.bgNode.parent.convertToWorldSpaceAR(this.bgNode.getPosition()))
        this.node.setPosition(pos)
        this.cameraView = cc.find('Canvas').FindChild('Main Camera', CameraView)
        this.maxZoomRatio = Math.min(this.bgSize.width / 2688, this.bgSize.height / 1242)
        this.canvasBoundary = new cc.Vec2(pos.x - 2688 / 2, pos.x + 2688 / 2)
    }

    getMove() {
        return this.move
    }

    outOfBoundary() {
        if (this.bgCamera.zoomRatio + this.zoomDiff * MOVERATIO > this.maxZoomRatio) return true
        let posTemp
        let cameraX = this.node.x + this.move.x
        this.canvasBoundary = new cc.Vec2(cameraX - 2688 / 2, cameraX + 2688 / 2)
        let viewSizeX = this.bgSize.width / this.bgCamera.zoomRatio
        posTemp = new cc.Vec2(this.startLoc.x - viewSizeX / 2, 0)
        let bgBoundaryL = this.node.parent.convertToNodeSpaceAR(this.bgNode.parent.convertToWorldSpaceAR(posTemp)).x
        posTemp = new cc.Vec2(this.startLoc.x + viewSizeX / 2, 0)
        let bgBoundaryR = this.node.parent.convertToNodeSpaceAR(this.bgNode.parent.convertToWorldSpaceAR(posTemp)).x
        let case1 = this.canvasBoundary.x < bgBoundaryL
        let case2 = this.canvasBoundary.y > bgBoundaryR
        if (case1 || case2) return true
        return false
    }

    update(dt) {
        if (!this.cameraView) return
        this.loc = this.cameraView.getPosition()
        if (!this.oldLoc) this.oldLoc = this.loc
        this.move = this.loc.sub(this.oldLoc)
        this.oldLoc = this.loc

        this.zoomRatio = this.cameraView.zoomRatio
        if (!this.oldZoomRatio) this.oldZoomRatio = this.zoomRatio
        this.zoomDiff = this.zoomRatio - this.oldZoomRatio
        this.oldZoomRatio = this.zoomRatio

        if (!this.outOfBoundary()) {
            this.node.x += this.move.x * MOVERATIO
            this.bgCamera.zoomRatio += this.zoomDiff * MOVERATIO
        }
        this.move = new cc.Vec2(0, 0)
        this.zoomDiff = 0
    }
}
