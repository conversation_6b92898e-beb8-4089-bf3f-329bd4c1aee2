import { DIR_POINTS_8 } from "../../common/constant/Constant";
import BaseMap from "./BaseMap";

/**
 * 并查集
 */
export default class UnioinFind {
    protected unionFind: {[key: number]: number} = {}
    protected r: {[key: number]: cc.Vec2[]} = {};

    private map: BaseMap = null

    private needReBuild: boolean = false

    public init(map: BaseMap) {
        this.map = map
        this.r = {}
        this.unionFind = {}
        let size = map.getSize()
        for(let x = 0; x < size.width; x++) {
            for(let y = 0; y < size.height; y++) {
                let blockId = x * size.height + y;
                this.unionFind[blockId] = blockId;
                if(!this.r[blockId]) this.r[blockId] = [];
                this.r[blockId].push(cc.v2(x, y));
            }
        }

        for(let x = 0; x < size.width; x++) {
            for(let y = 0; y < size.height; y++) {
                if(!map.checkCanPass(x, y)) continue;
                for(let dir of DIR_POINTS_8) {
                    let nX = x + dir.point.x;
                    let nY = y + dir.point.y;
                    if(map.checkCanPass(nX, nY)) {
                        this.union(cc.v2(x, y), cc.v2(nX, nY));
                    }
                }
            }
        }
        return this
    }

    private find(blockId: number) {
        if(blockId != this.unionFind[blockId]) {
            this.unionFind[blockId] = this.find(this.unionFind[blockId]);
        }
        return this.unionFind[blockId];
    }

    public union(point1: cc.Vec2, point2: cc.Vec2) {
        let size = this.map.getSize()
        let blockId1 = this.find(point1.x * size.height + point1.y);
        let blockId2 = this.find(point2.x * size.height + point2.y);

        if(blockId1 == blockId2) return;
        if(this.r[blockId1].length < this.r[blockId2].length) {
            this.unionFind[blockId1] = blockId2;
            for(let point of this.r[blockId1]) {
                this.r[blockId2].push(point);
            }
            delete this.r[blockId1];
        } else {
            this.unionFind[blockId2] = blockId1;
            for(let point of this.r[blockId2]) {
                this.r[blockId1].push(point);
            }
            delete this.r[blockId2];
        }
    }

    public getConnectPoints(point: cc.Vec2): cc.Vec2[] {
        let size = this.map.getSize()
        let blockId = this.find(point.x * size.height + point.y);
        return this.r[blockId] || [];
    }

    public isConnected(p1: cc.Vec2, p2: cc.Vec2) {
        let {height} = this.map.getSize() 
        if (p1.y >= height || p2.y >= height) return false
        let size = this.map.getSize()
        let id1 = this.find(p1.x * size.height + p1.y);
        let id2 = this.find(p2.x * size.height + p2.y);
        return id1 === id2
    }

    update() {
        if (this.needReBuild) {
            this.init(this.map)
            this.needReBuild = false
        }
    }
}