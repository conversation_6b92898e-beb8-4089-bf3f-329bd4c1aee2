import { Msg } from "../../../proto/msg-define";
import { ConditionType, EquipAttrCompareState, EquipEffectTarget, EquipEffectType, IntType, OreMakePnlType, ROLE_ATTR_ID } from "../../common/constant/Enums";
import { EquipCfg, EquipEffectCfg, EquipLevelCfg, EquipMakeCfg } from "../../common/constant/DataType";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BattleRole from "../battle/BattleRole";
import ConditionObj from "../common/ConditionObj";
import { OreItemObj } from "../ore/OreModel";
import { dbHelper } from "../../common/helper/DatabaseHelper";

class EquipEffect {
    public type: EquipEffectType
    public target: EquipEffectTarget
    public attr: number = 0
    public id: number = 0
    public level: number = 0
    public cfg: EquipEffectCfg = null

    public init(data: proto.IEquipEffect, cfg) {
        this.cfg = cfg
        this.id = cfg.id
        this.type = cfg.type
        this.target = cfg.target
        this.attr = data.attr
        this.level = data.level || 0
        return this
    }

    public getDesc() {
        if (this.type == EquipEffectType.ATTR) {
            let img = "xue"
            if (this.target == EquipEffectTarget.ATTACK) {
                img = "gong"
            }
            return `<img src='${img}'/><color=#68c81a>+${this.attr}</>`
        }
        else {
            return `${this.target + 1}技能等级<color=#68c81a>+${this.attr}</>`
        }
    }

    /**
     * 对比效果 
     * @param targetEffect 
     * @param useThis 
     * @returns { type: 状态类型（平，提升，下降）, val：数值}
     */
    private getCompareAttrValState(targetEffect?: EquipEffect, useThis?: boolean): { type: EquipAttrCompareState, val: number } {

        if (!targetEffect) return {
            type: useThis ? EquipAttrCompareState.UP : EquipAttrCompareState.DOWN,
            val: useThis ? this.attr : 0
        }

        let type = EquipAttrCompareState.FLAT

        if (this.attr < targetEffect.attr) {
            type = EquipAttrCompareState.UP
        }

        if (this.attr > targetEffect.attr) {
            type = EquipAttrCompareState.DOWN
        }
        return { type, val: useThis ? this.attr : targetEffect.attr }
    }

    private getAttrIcon() {
        if (this.type == EquipEffectType.ATTR) {
            if (this.target == EquipEffectTarget.ATTACK) return "gong"
            return "xue"
        }
        return ""
    }

    // flat
    // up
    // down
    public getCompareDesc(targetEffect?: EquipEffect, useThis?: boolean) {
        const state = this.getCompareAttrValState(targetEffect, useThis)
        const attrIcon = this.getAttrIcon()

        let icon = `<img src='${attrIcon}'/>`
        let valTag = "+"
        let val = state.val

        if (this.type != EquipEffectType.ATTR) {
            icon = ""
            valTag = `${this.target + 1}技能等级+`
        }
        let stateTag = state.type == EquipAttrCompareState.FLAT ? "" : `<img src='${state.type}'/>`
        return icon + valTag + val + stateTag
    }

    public clone(): EquipEffect {
        const effect = new EquipEffect()
        effect.type = this.type
        effect.id = this.id
        effect.target = this.target
        effect.attr = this.attr
        return effect
    }
    public setAttr(val: number): EquipEffect {
        this.attr = 0
        return this
    }
}

export class Equip {
    private uid: string = null
    private id: number = null
    public cfg: EquipCfg = null
    private level: number = 0
    public effects: EquipEffect[] = []
    public used: boolean = false

    public init(data: proto.IEquipItem) {
        this.uid = data.uid
        this.id = data.id
        this.level = data.level || 1
        this.used = data.used
        this.initCfg(this.id)
        let datas = assetsMgr.getJson<EquipEffectCfg>("EquipEffect").datas.filter(d => {
            let lvData = assetsMgr.getJsonData<EquipLevelCfg>("EquipLevel", this.level)
            return d.equipIndex == this.index && lvData.effect.some(e => e.id == d.id)
        })
        if (data.effects) {
            this.effects = data.effects.map((it, index) => {
                return new EquipEffect().init(it, datas[index])
            })
        }
        return this
    }

    private initCfg(id: number) {
        this.cfg = assetsMgr.getJsonData<EquipCfg>("Equip", id)
    }

    public getUid() { return this.uid }
    public getId() { return this.id }
    public getLv() { return this.level }
    public getCfgIndex() { return this.index == OreMakePnlType.RING ? this.level - 3 : this.level - 1 }
    public get name() { return this.cfg?.name[this.getCfgIndex()] }
    public get icon() { return this.cfg?.icon[this.getCfgIndex()] || this.cfg?.icon.last() }
    public get sortId() { return this.cfg?.sortId }
    public get index() { return this.cfg?.index }
    public get roleId() { return this.cfg?.roleId }
    public get quality() { return this.level }

    public getDesc() {
        return this.effects.map(e => e.getDesc()).join("\n")
    }

    public getDesc2() {
        let roleId = this.roleId
        let cfg = cfgHelper.getCharacter(roleId)
        // let effects = cfgHelper.getEquipEffetcs(this.id, this.getLv())
        return assetsMgr.lang("ore_make_guiText_8", assetsMgr.lang(cfg.name))
    }

    // 技能等级*5 + 属性
    public getEffectsLevel() {
        let lv = 0
        for (const effect of this.effects) {
            if (effect.type == EquipEffectType.SKILL) {
                lv += effect.level * 5
            }
            else {
                lv += effect.level
            }
        }
        return lv
    }

    public getCompareDesc(targetEquip?: Equip) {
        const useThis = !targetEquip

        const buffer = []
        const tmp: EquipEffect[] = !targetEquip ? [] : [].concat(targetEquip.effects)

        for (const effect of this.effects) {
            const matchEffectIndex = tmp.findIndex(e => e.type == effect.type)
            let compareEffect = null
            if (matchEffectIndex != -1) {
                compareEffect = tmp.splice(matchEffectIndex, 1)[0]
            }
            const str = effect.getCompareDesc(compareEffect, useThis)
            buffer.push(str)
        }
        if (tmp.length) {
            for (const effect of tmp) {
                buffer.push(effect.getCompareDesc(null, !useThis))
            }
        }

        return buffer.join("\n")
    }

    public toCondition() {
        return new ConditionObj().init2({
            type: ConditionType.EQUIP,
            id: this.id,
            num: 1,
            extra: { uid: this.uid, level: this.level }
        })
    }

    /**
     * 获取装备可打造的属性范围描述
     */
    public getPreviewAttrRangeDesc() {
        let buffer = ""
        let effects = cfgHelper.getEquipEffetcs(this.id, this.getLv(), this.getProficiencyLv())
        for (const data of effects) {
            if (data.type == EquipEffectType.ATTR) {
                let img = "xue"
                if (data.target == EquipEffectTarget.ATTACK) {
                    img = "gong"
                }
                if (data.min == data.max) {
                    buffer += `<img src='${img}'/><color=#68c81a>+${data.min}</>\n`
                }
                else {
                    buffer += `<img src='${img}'/><color=#68c81a>+${data.min}~+${data.max}</>\n`
                }
            }
            else {
                if (data.min == data.max) {
                    buffer += `${data.target + 1}技能等级<color=#68c81a>+${data.min}</>\n`
                }
                else {
                    buffer += `${data.target + 1}技能等级<color=#68c81a>+${data.min}~+${data.max}</>\n`
                }
            }
        }
        return buffer
    }

    public getProficiencyLv() {
        return gameHelper.equip.getProficiencyByTableLv(this.getLv(), this.index)
    }
}

@mc.addmodel('equip', 200)
export default class EquipModel extends mc.BaseModel {
    public data: proto.IEquip

    private equipList: Equip[] = []
    private proficiencyMap: Map<string, number> = new Map()

    public showMaxQuality = 1 //打造界面看到过的最高品质，红点作用

    public init() {
        this.equipList = this.data.data.map((it) => {
            return new Equip().init(it)
        })
        if (this.data.proficiency) {
            for (const key in this.data.proficiency) {
                this.proficiencyMap.set(key, this.data.proficiency[key])
            }
        }

        let localData = dbHelper.register("equip", 1, this.toDB, this)
        this.showMaxQuality = localData.showMaxQuality || 1
        this.showMaxQuality = Math.max(this.getMadeMaxQuality(), this.showMaxQuality)
    }

    protected toDB() {
        return {
            showMaxQuality: this.showMaxQuality
        }
    }

    public updateInfo(data: proto.IEquip) {
        let equipDatas = data.data
        for (let equipData of equipDatas) {
            if (!this.getEquip(equipData.uid)) {
                let equip = new Equip().init(equipData)
                this.equipList.push(equip)
            }
        }
    }

    public getEquipBuff() {
        let equips = []
        let skills = equips.map(e => e.skill)
        let r = new BattleRole().initData({ id: Number(ROLE_ATTR_ID.EQUIP), skills })
        for (let skill of skills) {
            skill.setRole(r)
        }
        return r
    }

    public getEquipList() { return this.equipList }

    public getEquip(uid: string) {
        return this.equipList.find(equip => equip.getUid() == uid)
    }

    public getEquipsByRole(roleId: number, index: number) {
        return this.equipList.filter(equip => equip.roleId == roleId && equip.index == index)
    }

    // 等级 -> 熟练度
    public getProficiencyByTableLv(lv: number, index: number) { 
        let key = this.getProficiencyKey(lv, index)
        return this.proficiencyMap.get(key) || 0
    }
    
    public getEquipNum(id: number) {
        return this.equipList.filter(t => t.getId() == id && !t.used).length
    }

    public getDailyEquip(id: number, num: number) {
        return this.equipList.filter(t => t.getId() == id && !t.used).slice(0, num)
    }

    public addEquip(id: number, { uid, level, effects }: any) {
        let equip = new Equip().init({ uid, id, level, effects })
        this.equipList.push(equip)
        return equip
    }
    public delEquip(uid: string) {
        this.equipList.remove('uid', uid)
    }

    public async changeEquip(equipUid: string) {
        let msg = new proto.C2S_WearEquipMessage({ uid: equipUid })
        const _res = await gameHelper.net.request(Msg.C2S_WearEquipMessage, msg, true)
        const { code } = proto.S2C_WearEquipMessage.decode(_res)
        if (code == 0) {
            let equip = this.getEquip(equipUid)
            for (let e of this.getEquipList()) {
                if (e.used && e.getId() == equip.getId()) {
                    e.used = false
                }
            }
            equip.used = true
            eventCenter.emit(EventType.BATTLE_EQUIP_CHANGE, equip.roleId)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public async takeOffEquip(equipUid: string) {
        //发消息给后端
        let msg = new proto.C2S_UnWearEquipMessage({ uid: equipUid })
        const _res = await gameHelper.net.request(Msg.C2S_UnWearEquipMessage, msg, true)
        const res = proto.S2C_UnWearEquipMessage.decode(_res)
        const { code } = res
        //前端乘客更改
        if (code == 0) {
            let equip = this.getEquip(equipUid)
            equip.used = false
            eventCenter.emit(EventType.BATTLE_EQUIP_CHANGE, equip.roleId)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    //这里id用的equip里的id
    public async make(id: number, tableId: number) {
        let msg = new proto.C2S_MakeEquipMessage({ id, tableId })
        const _res = await gameHelper.net.request(Msg.C2S_MakeEquipMessage, msg, true)
        const res = proto.S2C_MakeEquipMessage.decode(_res)
        const { code, equip } = res
        if (code == 0) {
            let data = assetsMgr.getJsonData<EquipCfg>("Equip", id)
            let key = this.getProficiencyKey(tableId, data.index)
            let cur = this.proficiencyMap.get(key) || 0
            let cost = cfgHelper.getEquipMakeById(tableId, cur).cost
            gameHelper.deductConditions(gameHelper.toConditions(cost))
            let e = new Equip().init(equip)
            this.equipList.push(e)
            this.proficiencyMap.set(key, cur + 1)
            eventCenter.emit(EventType.ORE_EQUIP_MAKE)
            return e
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    private getProficiencyKey(level: number, index: number) {
        return `${level}-${index}`
    }

    public async buy(id: number, level: number, storeId: number) {
        let msg = new proto.C2S_BuyEquipMessage({ id, level })
        const _res = await gameHelper.net.request(Msg.C2S_BuyEquipMessage, msg, true)
        const res = proto.S2C_BuyEquipMessage.decode(_res)
        const { code, equip } = res
        if (code == 0) {
            let cost = cfgHelper.getEquipStoreById(storeId).cost
            gameHelper.deductConditions(gameHelper.toConditions(cost))
            let e = new Equip().init(equip)
            this.equipList.push(e)
            return e
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public getEquipEffectLvRange(id: number, level: number) {
        let data = assetsMgr.getJsonData<EquipCfg>("Equip", id)
        let pro = this.getProficiencyByTableLv(level, data.index)
        let effects = cfgHelper.getEquipEffetcsLv(id, level, pro)
        let max = effects.reduce((sum, effect) => {
            let lv = effect.max
            if (effect.type == EquipEffectType.SKILL) {
                lv *= 5
            }
            return sum + lv
        }, 0)
        let min = effects.reduce((sum, effect) => {
            let lv = effect.min
            if (effect.type == EquipEffectType.SKILL) {
                lv *= 5
            }
            return sum + lv
        }, 0)
        return {min, max}
    }

    //打造过的最高品质，不管部位
    public getMadeMaxQuality() {
        const lvCfgs = assetsMgr.getJson<EquipLevelCfg>("EquipLevel").datas
        let quality = 0
        for (let lvCfg of lvCfgs) {
            for (let i = 1; i <= 3; i++) {
                let pro = gameHelper.equip.getProficiencyByTableLv(lvCfg.id, i)
                if (pro > 0) { 
                    quality = Math.max(quality, lvCfg.quality)
                }
            }
        }
        return quality
    }
}
