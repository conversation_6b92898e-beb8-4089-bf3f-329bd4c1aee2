import { util } from "../../../core/utils/Utils"
import { Msg } from "../../../proto/msg-define"
import { StoreId } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import PropObj from "../bag/PropObj"
import ConditionObj from "../common/ConditionObj"
import StoreModel from "./StoreModel"

@mc.addmodel("store")
export default class StoreMgr extends mc.BaseModel {

    public stores: StoreModel[] = []

    public data: proto.IStore = null

    public init() {
        this.updateInfo(this.data)
    }

    public updateInfo(data) {
        this.stores = data.list.map(s => new StoreModel().init(s))
    }

    public getStore(id: StoreId) {
        return this.stores.find(s => s.id == id)
    }

}