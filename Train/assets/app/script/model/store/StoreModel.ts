import { Msg } from "../../../proto/msg-define"
import { ConditionType, StoreId, UIFunctionType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ConditionObj from "../common/ConditionObj"

export class StoreGoods {

    public index: number = 0
    public stock: number = 0
    public item: ConditionObj = null
    public discount: number = 0
    public cost: ConditionObj = null
    public store: StoreModel = null

    public init(data) {
        this.stock = data.stock
        this.item = gameHelper.toCondition(data.item)
        this.discount = ut.toFixed(data.discount / 10, 1)
        this.cost = !!data.cost ? gameHelper.toCondition(data.cost) : null
        return this
    }

    public isSellOut() {
        return this.stock <= 0
    }

    public isDiscount() {
        return this.discount < 10
    }

    public buy() {
        return this.store.buy(this.index)
    }
}

export default class StoreModel {

    public id: StoreId = null

    private refreshCount: number = 0

    private goods: StoreGoods[] = []

    private refreshEndTime: number = -1

    private cfg = null

    public init(data: proto.IStoreInfo) {
        this.updateInfo(data)

        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.PLAY_BLACKHOLE && this.id == StoreId.BLACK_HOLE) {
                this.sync()
            }
        })

        this.cfg = cfgHelper.getMiscData("stores")[this.id - 1]

        return this
    }

    public updateInfo(data: proto.IStoreInfo) {
        this.id = data.id
        data.goods.forEach((g, i) => {
            this.setGoods(i, g)
        })
        this.refreshCount = data.refreshCount
        this.updateRefreshTime(data.refreshTime)
    }

    private updateRefreshTime(time) {
        this.refreshEndTime = gameHelper.now() + time
    }

    public maxRefreshCount() {
        return this.cfg.refreshNum || 0
    }

    public canRefresh() {
        let count = this.maxRefreshCount()
        return count <= 0 || this.refreshCount < count
    }

    public getRefreshCost() {
        return new ConditionObj().init(ConditionType.DIAMOND, -1, this.cfg.refreshPrice)
    }

    public getRefreshSurplusTime() {
        return this.refreshEndTime - gameHelper.now()
    }

    public getGoods() {
        return this.goods
    }

    public async refresh() {
        let msg = new proto.C2S_StoreRefreshMessage({ id: this.id })
        const res = await gameHelper.net.request(Msg.C2S_StoreRefreshMessage, msg, true)
        const { code, info } = proto.S2C_StoreRefreshMessage.decode(res)
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        this.init(info)
        gameHelper.deductConditions([this.getRefreshCost()])
        eventCenter.emit(EventType.STORE_REFRESH)
        return true
    }

    public async buy(index: number) {
        let msg = new proto.C2S_StoreBuyMessage({ id: this.id, pos: index })
        const res = await gameHelper.net.request(Msg.C2S_StoreBuyMessage, msg, true)
        const { code, item } = proto.S2C_StoreBuyMessage.decode(res)
        if (code != 0) {
            viewHelper.showNetError(code)
            return
        }
        let goods = this.goods[index]
        gameHelper.deductCondition(goods.cost)
        goods.stock--

        eventCenter.emit(EventType.STORE_REFRESH)
        return gameHelper.toCondition(item)
    }

    public setGoods(index, data: proto.IGoods) {
        let goods = new StoreGoods().init(data)
        goods.index = index
        goods.store = this
        this.goods[index] = goods
    }

    public isInit() {
        return this.goods.length > 0
    }

    public checkSync() {
        if (!this.isInit()) {
            return this.sync()
        }
        return true
    }

    public async sync() {
        let msg = new proto.C2S_SyncStoreMessage({ id: this.id })
        const res = await gameHelper.net.request(Msg.C2S_SyncStoreMessage, msg)
        const { code, info } = proto.S2C_SyncStoreMessage.decode(res)
        if (code == 0) {
            this.updateInfo(info)
            return true
        }
        return false
    }
}