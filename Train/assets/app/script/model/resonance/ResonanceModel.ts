import { Msg } from "../../../proto/msg-define"
import { TalentAttrCfg } from "../../common/constant/DataType"
import { ConditionType, EquipEffectType, TalentAttrType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ConditionObj from "../common/ConditionObj"
import { Equip } from "../equip/EquipModel"
import PassengerModel from "../passenger/PassengerModel"


class ResonanceSlot {
    public id: number

    init(data) {
        this.id = data.id
        return this
    }
}
@mc.addmodel('resonance', 110)
export default class ResonanceModel extends mc.BaseModel {

    public data: proto.IResonance = null

    public slots: ResonanceSlot[] = []
    public init() {
        this.slots = this.data.slots.map((d, i) => {
            return new ResonanceSlot().init(d)
        })

        eventCenter.on(EventType.PASSENGER_LEVEL_UP, this.checkUnsetRole, this)
        eventCenter.on(EventType.UNLOCK_PASSENGER, this.checkUnsetRole, this)
        eventCenter.on(EventType.PASSENGER_RESET, this.checkUnsetRole, this)
        eventCenter.on(EventType.PASSENGER_STARLV_UP, this.checkUnsetRole, this)
        eventCenter.on(EventType.PASSENGER_TRANS, this.checkUnsetRole, this)
    }

    public getSlot(index) {
        return this.slots[index]
    }

    public async setRole(id: number, isAdd: boolean) {
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_SetResonanceRoleMessage, { type: Number(isAdd), id })
        if (code == 0) {
            if (isAdd) {
                this.add(id)
            }
            else {
                this.remove(id)
            }
            eventCenter.emit(EventType.SET_RESONANCE)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public add(id: number) {
        let slot = new ResonanceSlot().init({ id })
        this.slots.push(slot)
    }

    public remove(id: number) {
        this.slots.remove("id", id)
    }

    public isActive() {
        return this.getResonaceLv() > 0
    }

    public isBeResonanced(roleId: number) {
        return !!this.slots.find(r => r.id == roleId)
    }

    public isResonance(roleId: number) {
        let roles = this.getResonanceRoles()
        return roles.has("id", roleId)
    }

    public getResonanceRoles() {
        let passengers = gameHelper.passenger.getPassengers().slice()
        passengers.sort((a, b) => {
            if (a.getLevel() != b.getLevel()) {
                return b.getLevel() - a.getLevel();
            }
            if (a.getStarLv() != b.getStarLv()) {
                return b.getStarLv() - a.getStarLv();
            }
            return a.sortId - b.sortId;
        })
        return passengers.slice(0, 5)
    }

    public getResonaceLv() {
        let roles = this.getResonanceRoles()
        if (roles.length < 5) return 0
        return roles.min(p => p.getLevel()).getLevel()
    }

    public checkUnsetRole() {
        let roles = this.getResonanceRoles()
        for (let role of roles) {
            this.remove(role.id)
        }
    }

    public getSlotById(id: number) {
        return this.slots.find(r => r.id == id)
    }

    public getResonaceInfo() {
        let roles = this.getResonanceRoles()
        if (roles.length < 5) return

        let level = roles.min(p => p.getLevel()).getLevel()

        let equipInfo: { [k: number]: Equip } = {}

        for (let index = 1; index <= 3; index++) {
            for (let role of roles) {
                let equip = role.getEquip(index)
                if (!equip) {
                    equipInfo[index] = null
                    break
                }
                let lv = equip.getEffectsLevel()
                if (!equipInfo[index]) equipInfo[index] = equip
                let orgEquip = equipInfo[index]
                let orgLv = orgEquip.getEffectsLevel()
                if (orgLv > lv) {
                    equipInfo[index] = equip
                }
                else if (orgLv == lv && orgEquip.getId() < equip.getId()) {
                    equipInfo[index] = equip
                }
            }
        }

        let talentInfo: { [k: number]: number } = {}
        for (let id = 1; id <= 5; id++) {
            for (let role of roles) {
                let lv = role.getTalentLevel(id)
                if (!talentInfo[id]) {
                    talentInfo[id] = lv
                }
                else {
                    talentInfo[id] = Math.min(talentInfo[id], lv)
                }
                if (talentInfo[id] <= 0) {
                    break
                }
            }
        }

        return { level, talentInfo, equipInfo }
    }

    public isCanOperate(passenger: PassengerModel) {
        const id = passenger.id
        if (!gameHelper.resonance.isActive()) {
            return true
        }
        return this.isResonance(id)
    }
}