import EventType from "../../common/event/EventType";
import PassengerPlot from "../passenger/PassengerPlot";
import PassengerModel from "../passenger/PassengerModel";
import CharacterPlotLogic from "./CharacterPlotLogic";
import { CharacterPlotStep } from "../../common/constant/DataType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

@mc.addmodel('characterPlot')
export default class CharacterPlotModel extends mc.BaseModel {

    public id: string = "" // 剧情对话id
    private steps: CharacterPlotStep[][] = [] // 剧情对话配置
    private curIdx: number = 0    // 当前步骤下标
    private npcIds: object = {}//记录用到了那些npcId

    public logic: CharacterPlotLogic = null
    public curRole: PassengerModel = null

    public init() {
        this.logic = new CharacterPlotLogic()
    }

    public async start(role: PassengerModel, id: string, plotObj?: PassengerPlot) {
        let steps = cfgHelper.getCharacterPlotData(id)
        if (!steps) return
        await this.checkPnl()
        let idx = plotObj ? plotObj.getStartIdx() : 0
        this.id = id
        this.curIdx = idx
        this.curRole = role
        this.steps = steps
        cc.log("characterPlot start ", id, idx, plotObj)
        let plot = this.getPrePlot()
        if (plot) {
            this.emit(EventType.RECOVER_C_PLOT, plot[0], plot)
        }
        if (plotObj && !plotObj.done) {
            this.doCall(plotObj.json)
        } else {
            this.updatePlot()
        }
    }

    public hidePnl() {
        viewHelper.hidePnl('guide/RelaterPnl')
    }

    private async checkPnl() {
        let pnlKey = 'guide/RelaterPnl'
        if (viewHelper.checkPnlEnter(pnlKey)) return
        viewHelper.showPnl(pnlKey)
        await viewHelper.waitEnterUI(pnlKey)
        viewHelper.showUI(false)
    }

    private updatePlot() {
        let plot = this.getCurPlot()
        if (plot) {
            this.changePlot(plot)
        } else {
            this.end()
        }
    }

    private async changePlot(plot: CharacterPlotStep[]) {
        await this.checkPnl()
        this.emit(EventType.CHANGE_C_PLOT, plot[0], plot)
    }

    public async select(plot: CharacterPlotStep) {
        let bol = await this.syncSelect(plot)
        if (!bol) return
        this.doCall(plot)
    }

    private async doCall(plot: CharacterPlotStep) {
        let call = plot.call
        if (call) {
            cc.log("characterPlot select call", call)
            let mod = this.logic
            await mod[call](plot)
        }
        await this.syncComplete(plot)
        this.nextStep(plot)
    }

    private async syncSelect(plot: CharacterPlotStep) {
        let ary = this.getCurPlot()
        if (!cfgHelper.haveKeyStepCharacterPlot(ary)) return true
        let index = ary.findIndex(m => m.id == plot.id)
    }

    private async syncComplete(plot: CharacterPlotStep) {
        let ary = this.getCurPlot()
        if (!cfgHelper.haveKeyStepCharacterPlot(ary)) return true
    }

    public nextStep(plot?: CharacterPlotStep) {
        if (!plot) {
            plot = this.getCurPlot()[0]
        }
        if (plot.next) {
            this.curIdx = plot.next - 1
        } else {
            this.curIdx = -1
        }
        this.updatePlot()
    }

    public end() {
        let key = this.id
        this.id = null
        this.curRole = null
        cc.log("characterPlot end ", key)
        this.emit(EventType.END_C_PLOT, key)
        viewHelper.showUI(true)
    }

    public getCurPlot() {
        return this.steps[this.curIdx]
    }

    public getPrePlot() {
        return this.steps[this.curIdx - 1]
    }
}