import { CharacterPlotStep, GuideStep, PlotStep } from "../../common/constant/DataType";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

type PlotParam = {
    guideOpOne?: GuideStep
    cPlotStep?: CharacterPlotStep
}

@mc.addmodel('plot')
export default class PlotModel extends mc.BaseModel {

    private id: string = "" // 剧情对话id
    private steps: PlotStep[][] = [] // 剧情对话配置
    private curIdx: number = 0    // 当前步骤下标
    private npcIds: object = {}//记录用到了那些npcId
    public param: PlotParam = null

    public start(id: string, idx: number = 0, param: PlotParam = {}) {
        let steps = cfgHelper.getPlotData(id)
        if (!steps) {
            cc.error('no plotkey', id)
            return
        }
        let isNew = this.param == null
        this.id = id
        this.curIdx = idx
        this.steps = this.parseSteps(steps)
        this.param = param

        if (isNew) {
            viewHelper.showPnl('common/Plot')
            this.emit(EventType.START_PLOT)
            viewHelper.showUI(false)
        }

        this.updatePlot()
    }

    public getId() {
        return this.id
    }

    private updatePlot() {
        let plot = this.getCurPlot()
        if (plot) {
            this.changePlot(plot)
        } else {
            this.end()
        }
    }

    private async changePlot(plot: PlotStep[]) {
        this.addNpcId(plot[0])
        this.emit(EventType.CHANGE_PLOT, plot[0], plot)
    }

    public async select(plot: PlotStep) {
        let call = plot.call
        if (call) {
            cc.log("plot select call", call)
            let mod = gameHelper.guide.logic
            await mod[call](plot)
        }
        this.nextStep(plot)
    }

    public nextStep(plot?: PlotStep) {
        if (!plot) {
            plot = this.getCurPlot()[0]
        }
        if (plot.next) {
            this.curIdx = plot.next - 1
        } else {
            this.curIdx = -1
        }
        this.updatePlot()
    }

    public end() {
        if (this.param == null) return
        this.param = null
        this.removeAllNpc()
        this.emit(EventType.END_PLOT, this.id)
        viewHelper.showUI(true)
    }

    private getCurPlot() {
        return this.steps[this.curIdx]
    }

    //npc在说话的时候 在宿舍中 站住
    private addNpcId(plot: PlotStep) {
        let id = plot.npcId
        if (!this.npcIds[id] && typeof id == 'number' && mc.currWindName == "main") {
            if (gameHelper.passenger.stopRole(id)) {
                this.npcIds[id] = true
            }
        }
    }

    private removeAllNpc() {
        for (const id in this.npcIds) {
            gameHelper.passenger.startRole(Number(id))
        }
        this.npcIds = {}
    }

    private parseSteps(steps: PlotStep[][]) {
        for (let step of steps) {
            for (let s of step) {
                if (s.npcId == "deep_explore") {
                    let explore = gameHelper.deepExplore.getGuideExplore()
                    if (explore) {
                        s.npcId = explore.info.roles[0]
                    }
                }
            }
        }
        return steps
    }
}