import { Reward } from "../../common/constant/DataType"
import { gameHelper } from "../../common/helper/GameHelper"
import ConditionObj from "./ConditionObj"

class RewardObj {
    public cond: ConditionObj = null
    public weight: number = 0

    public init(reward: Reward) {
        this.cond = new ConditionObj().init2(reward)
        this.weight = reward.weight || 0
        return this
    }

}

// 通用随机奖励对象
export default class RewardPackage {
    public rewards: RewardObj[] = []

    public init(rewards: Reward[]) {
        if (rewards) {
            this.rewards = rewards.map(m => new RewardObj().init(m))
            return this
        }
    }

    public random() {
        let index = gameHelper.randomByWeight(this.rewards)
        return this.rewards[index].cond
    }
}