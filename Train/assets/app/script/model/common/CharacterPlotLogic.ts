import { CharacterPlotStep } from "../../common/constant/DataType"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import { animHelper } from "../../common/helper/AnimHelper"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { localConfig } from "../../common/LocalConfig"
import EventType from "../../common/event/EventType"
import TimeScaleCmpt from "../../view/cmpt/common/TimeScaleCmpt"

// 表演类型
enum PerformType {
    Back,//倒放
    Speed,//正放加速
}

export default class CharacterPlotLogic {
    private perform: PerformType = PerformType.Back
    public performBack() { return this.perform == PerformType.Back }

    public async useStone1005_1() {
        gameHelper.characterPlot.hidePnl()
        let p1 = this.useTimeStone()
        this.timeBackOn()
        let windKey = 'repair'
        let p2 = viewHelper.preloadWind(windKey)
        await Promise.all([p1, p2])
        viewHelper.gotoWind(windKey)

        if (this.performBack()) {
            mc.lockTouch(true)
            this.timeScaleSpeedUp1005_1_back()
            await eventCenter.wait(EventType.HEAD_BACK_PLAY_OVER)
            this.timeScaleRecover1005_1()
            await ut.wait(2)
        } else {
            await eventCenter.wait(EventType.GUIDE_REPAIR_TRAIN_START)
            gameHelper.autoClick = true
            mc.lockTouch(true)
            this.timeScaleSpeedUp1005_1()
            await eventCenter.wait(EventType.GUIDE_ENTER_TRAIN_END)
            eventCenter.once(EventType.GUIDE_RELATER_BLACK_IN, this.timeScaleRecover1005_1, this)
        }
    }
    public async useStoneOver1005_1(plot: CharacterPlotStep) {
        this.timeBackOff()
        let windKey = 'main'
        await viewHelper.preloadWind(windKey)
        viewHelper.gotoWind(windKey)

        // await eventCenter.get(EventType.FADEOUT_C_BLACK, plot)
        this.flyMail(1005, plot.key)
        mc.lockTouch(false)
    }
    private timeScaleRecover1005_1() {
        let root = cc.find('Canvas')
        root.stopAllActions()
        this.setScaleNodes1005_1(1)
    }
    private timeScaleSpeedUp1005_1_back() {
        let root = cc.find('Canvas')
        let cur = 1.5
        let per = 0
        cc.tween(root).repeatForever(cc.tween().delay(0.1).call(() => {
            cur += per
            this.setScaleNodes1005_1(cur)
        })).start()
    }
    private timeScaleSpeedUp1005_1() {
        let root = cc.find('Canvas')
        let cur = 1
        let per = 0.1
        let cb = function () { cur += per }
        cc.tween(root)
            .repeat(1, cc.tween().delay(7).call(cb))
            .call(() => per *= 2)
            .repeat(2, cc.tween().delay(1.5).call(cb))
            .call(() => per *= 2)
            .repeat(5, cc.tween().delay(0.8).call(cb))
            .start()
        cc.tween(root).repeatForever(cc.tween().delay(0.25).call(() => {
            this.setScaleNodes1005_1(cur)
        })).start()
    }
    private setScaleNodes1005_1(cur: number) {
        this.setScaleByNodes(cur, [
            mc.currWind.node,
            cc.find("Canvas/Main Camera"),
            mc.getPnl("common/Plot").node,
            mc.getPnl("planet/planetMove")?.node,
        ])
    }

    private setScaleByNodes(cur: number, ary: cc.Node[]) {
        ary.forEach(n => {
            if (!n) return
            let t = n.getComponent(TimeScaleCmpt)
            if (!t) t = n.addComponent(TimeScaleCmpt)
            t.setScale(cur)
        })
    }
    private useTimeStone() {
        let pnlKey = 'timeStone/TimeStoneUsePnl'
        viewHelper.showPnl(pnlKey)
        return eventCenter.wait(EventType.TIME_STONE_USED)
    }
    private timeBackOn() {
        gameHelper.plot.end()
        gameHelper.isTimeBack = true
        if (!this.performBack()) {
            gameHelper.isFakeGuide = true
            this.setGuideReCheck()
        }
    }
    private timeBackOff() {
        gameHelper.plot.end()
        gameHelper.isTimeBack = false
        gameHelper.autoClick = false
        if (!this.performBack()) {
            gameHelper.isFakeGuide = false
            gameHelper.guide.timeBackOff()
        }
    }
    // 不要触发新手
    private setGuideNoCheck() {
        if (localConfig.openGuide) return
        let list: proto.GuideInfo[] = []
        for (let id = 1; id <= 2; id++) {
            list.push(new proto.GuideInfo({ id, keySteps: cfgHelper.getGuideKeySteps(id) }))
        }
        gameHelper.guide.timeBackOn(0, list)
    }
    // 重新触发新手
    private setGuideReCheck() {
        gameHelper.isEnterSecne = false
        gameHelper.guide.timeBackOn(0, [new proto.GuideInfo({ id: 1, keySteps: [101] })])
    }
    private flyMail(roleId: number, key: string) {
        let role = gameHelper.passenger.getPassenger(roleId)
        if (!role) return
        let plot = role.getLastPlot(key)
        if (!plot) return
        if (plot.rewards.length == 0) return
        let ary = gameHelper.toConditions(plot.rewards)
        animHelper.flyMailToBag(ary[0], cc.v2())
    }

}
