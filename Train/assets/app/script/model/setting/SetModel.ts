import ViewLayerCtrl from '../../../core/layer/ViewLayerCtrl';
import { Msg } from '../../../proto/msg-define';
import { SoundState } from '../../common/constant/Enums';
import { dbHelper } from '../../common/helper/DatabaseHelper';
import { gameHelper } from '../../common/helper/GameHelper';
import { viewHelper } from '../../common/helper/ViewHelper';

/**玩家个性化设置 */
@mc.addmodel('set', 100)
export default class SetModel extends mc.BaseModel {
    public music: number = SoundState.OPEN;
    public effect: number = SoundState.OPEN;

    public dormSortMode: boolean = null //false/true 默认排序/空余排序
    public cameraZoomRatio: number = null
    public cameraPosition: {x: number, y: number} = null

    private realName: string = null
    private IDNumber: string = null

    public noResonanceTips: boolean = false

    public setRealName(val: string) {
        this.realName = val
    }
    public setIDNumber(val: string) {
        this.IDNumber = val
    }

    public init() {
        let datas = dbHelper.register('setting', 1, this.toDB, this);
        this.fromDB(datas);
    }

    private toDB() {
        return {
            music: this.music,
            effect: this.effect,
            dormSortMode: this.dormSortMode,
            cameraZoomRatio: this.cameraZoomRatio,
            cameraPosition: this.cameraPosition,
            noResonanceTips: this.noResonanceTips
        }
    }

    private fromDB(data: any) {
        audioMgr.bgmVolume = data.music !== undefined ? data.music : SoundState.OPEN;;
        audioMgr.sfxVolume = data.effect !== undefined ? data.effect : SoundState.OPEN;
        this.dormSortMode = data.dormSortMode !== undefined ? data.dormSortMode : true
        this.cameraZoomRatio = data.cameraZoomRatio
        this.cameraPosition = data.cameraPosition
        this.noResonanceTips = data.noResonanceTips || false
    }

    public async SignOut() {
        let msg = new proto.C2S_SignOutMessage({ authField1: this.realName, authField2: this.IDNumber })
        const res = await gameHelper.net.request(Msg.C2S_SignOutMessage, msg, true)
        const { code } = proto.S2C_SignOutRespMessage.decode(res)
        if (code == 0) {
            //注销成功
            return true
        }
        else {
            //todo 身份信息不正确
            return false
        }
    }

}