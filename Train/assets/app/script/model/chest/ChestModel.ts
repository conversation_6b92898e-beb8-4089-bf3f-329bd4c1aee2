import { Msg } from "../../../proto/msg-define";
import { ConditionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import PropObj from "../bag/PropObj";
import ConditionObj from "../common/ConditionObj";

const { ccclass, property } = cc._decorator;

@mc.addmodel('chest')
export default class ChestModel extends mc.BaseModel {

    public chestData: proto.IChest

    private chests: PropObj[] = [];

    private chestPoints: number //积分

    public init() {
        this.chestPoints = this.chestData.medal
        for (let type in this.chestData.data) {
            let num = 0
            for (let i = 0; i < this.chestData.data[type].data?.length; i++) {
                num += this.chestData.data[type].data[i].num
            }
            if (num > 0) {
                this.chests.push(new PropObj().init(+type, num))
            }
        }
    }

    public getChests() { return this.chests }

    public changeChest(id: number, count: number, isEmit: boolean = true): number {
        let prop = this.getChestById(id)
        if (!prop) {
            prop = new PropObj().init(id, count)
            this.chests.push(prop)
        } else {
            prop.count += count
        }
        if (prop.count <= 0) {
            this.delChest(id)
        }
        if (isEmit) {
            eventCenter.emit(EventType.CHANGE_NUM_PROP, id, count)
        }
        return prop.count;
    }

    public getChestById(id: number) {
        return this.chests.find(p => p.id == id)
    }

    public delChest(id: number) {
        this.chests.remove("id", id)
    }

    public getChestCountById(id: number) {
        let prop = this.getChestById(id)
        if (!prop) return 0
        return prop.count
    }

    public async chestUse(type: number, num: number) {
        let msg = new proto.C2S_ChestOpenMessage({ type: type, cnt: num })
        const res = await gameHelper.net.request(Msg.C2S_ChestOpenMessage, msg, true)
        const { code, rewards } = proto.S2C_ChestOpenMessage.decode(res)
        if (code != 0) {
            viewHelper.showNetError(code)
        } else {
            this.chestPoints += cfgHelper.getChestPoint(type) * num
            gameHelper.grantRewardAndShowUI(gameHelper.toConditions(rewards), false)
            this.changeChest(type, -num)
            return true
        }
    }

    public chestUseRedDot(id: number) {
        return this.chests.find(chest => chest.id == id)?.count > 0
    }

}
