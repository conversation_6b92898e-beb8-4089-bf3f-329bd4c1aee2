import { Msg } from '../../../proto/msg-define';
import { MarkNewLength, MarkNewType } from '../../common/constant/Enums';
import { gameHelper } from '../../common/helper/GameHelper';

/** 暂时用来给各个地方打上new标签 */
@mc.addmodel('new', 400)
export default class NewModel extends mc.BaseModel {
    public data: proto.INewMarkInfo[] = []

    public removeNew(typeId: MarkNewType, aryVal: number[]) {
        if (!this.isNew(typeId, aryVal)) return
        this.removeByServer(typeId, aryVal)
    }

    private async removeByServer(typeId: MarkNewType, aryVal: number[]) {
        let msg = new proto.C2S_RemoveNewMarkMessage({ typeId, aryVal })
        let res = await gameHelper.net.request(Msg.C2S_RemoveNewMarkMessage, msg)
        let { code } = proto.S2C_RemoveNewMarkMessage.decode(res)
        if (code != 0) return
        this.removeByClient(typeId, aryVal)
    }

    public removeByClient(typeId: MarkNewType, aryVal: number[]) {
        let info = this.getInfoByTypeId(typeId)
        if (!info) return
        let ary = info.aryVal
        let len = this.getTypeLength(typeId)
        let i = this.getIdxByEqual(ary, len, aryVal)
        if (i == null) return
        ary.splice(i, len)
        if (ary.length > 0) return
        let idx = this.getIdxByTypeId(typeId)
        this.data.splice(idx, 1)
    }

    public isNew(typeId: MarkNewType, aryVal: number[]) {
        let info = this.getInfoByTypeId(typeId)
        return info && this.getIdxByEqual(info.aryVal, this.getTypeLength(typeId), aryVal) != null
    }

    public pushNew(typeId: MarkNewType, aryVal: number[]) {
        let len = this.getTypeLength(typeId)
        if (aryVal.length != len)
            return twlog.error("pushNew error len", len, typeId, aryVal)
        let info = this.getInfoByTypeId(typeId)
        if (!info) {
            this.data.push({ typeId, aryVal })
            return
        }
        let ary = info.aryVal
        if (this.getIdxByEqual(ary, len, aryVal) != null)
            return
        ary.pushArr(aryVal)
    }

    private getIdxByEqual(ary: number[], len: number, aryVal: number[]) {
        for (let i = 0; i < ary.length; i += len) {
            if (this.isEqual(ary, i, aryVal)) {
                return i
            }
        }
    }

    private isEqual(ary: number[], i: number, aryVal: number[]) {
        for (let j = 0; j < aryVal.length; j++) {
            if (ary[i + j] != aryVal[j]) {
                return false
            }
        }
        return true
    }

    private getTypeLength(typeId: MarkNewType) {
        return MarkNewLength[typeId] || 1
    }

    public getInfoByTypeId(typeId: MarkNewType) {
        return this.data.find(m => m.typeId == typeId)
    }

    private getIdxByTypeId(typeId: MarkNewType) {
        return this.data.findIndex(m => m.typeId == typeId)
    }

}