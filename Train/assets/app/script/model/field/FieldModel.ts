import { util } from "../../../core/utils/Utils"
import { Msg } from "../../../proto/msg-define"
import { FieldSeedCfg, ItemCfg } from "../../common/constant/DataType"
import { ConditionType, FieldOperationType, FieldState, FieldType, ItemType, MarkNewType, UIFunctionType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { timeHelper } from "../../common/helper/TimeHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ConditionObj from "../common/ConditionObj"

export class FieldLandNode {
    private id: number      //第几块土地
    private type: FieldType
    private state: FieldState
    private endTime: number = -1
    private seed: Seed = null

    public init(id: number, data?: proto.IFieldCeil) {
        if (!data) {
            this.id = id
            this.type = id < 9 ? FieldType.VEGETABLE : FieldType.FRUIT
            this.state = FieldState.LOCK
            return this
        }
        this.id = id
        this.type = data.type
        if (data.state == proto.FieldCeilState.Empty) {
            this.state = FieldState.FREE
        }
        else {
            this.seed = new Seed().init(data.plantId, 1)
            if (data.state == proto.FieldCeilState.NotWater) {
                this.state = FieldState.SEED
            }
            if (data.state == proto.FieldCeilState.Growing) {
                this.state = FieldState.TREE
            }
            if (data.state == proto.FieldCeilState.GrowDone) {
                this.state = FieldState.FRUIT
            }
            this.updateEndTime(data.surplusTime)
        }
        return this
    }

    public getId() { return this.id }
    public getType() { return this.type }
    public getState() { return this.state }
    public getWater() { return this.seed?.waterCost }
    public getReward() { return this.seed?.reward }
    public getPrice() { return this.seed?.buyCost }
    public getFruitIcon() { return this.seed?.fruitIcon }
    public getTreeIcon() { return this.seed?.treeIcon }
    public getSeedIcon() { return this.seed?.seedIcon }
    public getSeedType() { return this.seed?.type }
    public getName() { return this.seed?.name }
    public getSurplusTime() { return Math.max(0, this.endTime - gameHelper.now()) }
    public getSeedTime() { return this.seed.timeMillisecond }
    public getPercent() { return 1 - this.getSurplusTime() / this.getSeedTime() }
    public getIndex() { return this.id < 9 ? this.id : this.id - 8 }
    public getRow() {
        if (this.type == FieldType.VEGETABLE) {
            return this.getIndex() > 4 ? 2 : 1
        }
        else {
            return this.getIndex() > 2 ? 2 : 1
        }
    }
    public isTreeIcon() { return this.getPercent() > 0.5 }  //成长时间过半再变形态

    public update() {
        if (this.state == FieldState.TREE) {
            if (gameHelper.now() >= this.endTime) {
                this.sync()
            }
        }
    }

    private updateEndTime(surplusTime) {
        this.endTime = gameHelper.now() + surplusTime
    }

    // 农场格子同步
    @util.addLock
    private async sync() {
        const { code, data } = await gameHelper.net.requestWithData(Msg.C2S_CeilSyncMessage, { id: this.id - 1 })
        if (code == 0) {
            this.init(this.id, data)
            eventCenter.emit(EventType.FIELD_TREE_MATURE, this.id)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

}

export class Seed {
    public id: number = null
    public count: number = null
    private cfg: FieldSeedCfg = null

    public init(id, count) {
        this.id = id
        this.count = count
        this.cfg = cfgHelper.getSeedInfoById(id)
        return this
    }

    public get icon() { return this.cfg?.icon }
    public get treeIcon() { return this.cfg?.treeIcon }
    public get seedIcon() { return this.cfg?.seedIcon }
    public get fruitIcon() { return this.cfg?.fruitIcon }
    public get propIcon() { return this.cfg?.propIcon }
    public get name() { return this.cfg?.name }
    public get type() { return this.cfg?.type }
    public get time() { return this.cfg?.time }
    public get timeMillisecond() { return this.cfg?.time * ut.Time.Minute }
    public get buyCost() { return this.cfg?.price }
    public get waterCost() { return new ConditionObj().init2(this.cfg?.waterCost) }
    public get reward() { return this.cfg?.reward }
    public get isShow() { return true }
    public get level() { return 0 }
    public get sortId() { return 1 }
    public get content() { return this.cfg?.content || "" }

    public canUse() {return false}

    public removeNew() { gameHelper.new.removeNew(MarkNewType.SEED, [this.id]) }
    public markNew() { gameHelper.new.pushNew(MarkNewType.SEED, [this.id]) }
    public isNew() { return gameHelper.new.isNew(MarkNewType.SEED, [this.id]) }
    public showName(it: cc.Node) { it.setLocaleKey(this.name) }
    public showContent(it: cc.Node) { it.setLocaleKey(this.content) }

}

export class Fertilizer {
    public id: number = null
    public count: number = null
    private cfg: ItemCfg = null

    public init(id: number, count: number) {
        this.id = id
        this.count = count
        this.cfg = cfgHelper.getPropData(id)
        return this
    }

    public get name() { return this.cfg?.name }
    public get icon() { return this.cfg?.icon }
}

@mc.addmodel('field')
export default class FieldModel extends mc.BaseModel {
    public data: proto.IField = null

    private seedList: Seed[] = [] //种子列表
    private fertilizerList: Fertilizer[] = []  //肥料列表
    private landList: FieldLandNode[] = []
    private level: number = 1
    private maxLevel: number = 1
    private condition: { [type: string]: number } = {}

    public getSeedList() { return this.seedList }
    public getFertilizerList() {
        return gameHelper.bag.getPropsByType(ItemType.FERTILIZER)
    }
    public getLandList() { return this.landList }
    public getGainTime() { return this.getCondition('FIELD_GAIN_TIME') }
    public isMaxLevel() { return !cfgHelper.getFieldLevelCfg(this.level).target }

    public init() {
        let data = this.data
        this.level = data.Level
        this.maxLevel = cfgHelper.getFieldMaxLevel()
        for (let i = 1; i <= 12; i++) {
            let data = this.data.ceilData.find(e => e.id == i - 1)
            this.landList.push(new FieldLandNode().init(i, data))
        }

        this.data.seedData.forEach(e => {
            this.seedList.push(new Seed().init(e.id, e.num))
        })

        for (let t in this.data.levelCond) {
            this.condition[t] = this.data.levelCond[t]
        }

        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.PLAY_FIELD) {
                this.onUnlock()
            }
        })
    }

    public update(dt: any) {
        this.landList.forEach(e => {
            e.update()
        })
    }

    public getCondition(type: string) {
        return this.condition[type] || 0
    }

    public getLevel() {
        return this.level
    }

    public getLandCouldUnlock(id: number) {
        let cfg = cfgHelper.getFieldLevelCfg(this.level)
        let land = this.getLandById(id)
        if (land.getState() != FieldState.LOCK) return false
        let type = land.getType()
        let lands = gameHelper.field.getLands(type)
        let max = cfg.fruit
        if (type == FieldType.VEGETABLE) {
            max = cfg.vegetable
        }
        return lands.findIndex(n => n == land) < max
    }

    public getLandById(id: number) {
        return this.landList.find(data => data.getId() == id)
    }

    public changeSeed(id: number, count: number) {
        let seed = this.getSeedById(id)
        if (!seed) {
            seed = new Seed().init(id, count)
            this.seedList.push(seed)
            seed.markNew()
        }
        else {
            seed.count += count
        }
        if (seed.count <= 0) {
            this.delSeed(id)
        }
        return seed.count;
    }

    public getSeedById(id: number) {
        return this.seedList.find(p => p.id == id)
    }

    private delSeed(id: number) {
        this.seedList.remove('id', id)
    }

    //肥料直接走背包，操作背包里的数量
    public changeFertilizer(id: number, count: number) {
        gameHelper.bag.changeProp(id, count)
    }

    public getFertilizerTimeStr(id: number) {
        let time = cfgHelper.getMiscData('fertilizer').find(data => +data.id == id).effect
        if (time < 0) {
            return "-∞"
        }
        return `-${timeHelper.getTimeShortText(time * ut.Time.Minute / ut.Time.Second)}`
    }


    private getFertilizerById(id: number) {
        return this.fertilizerList.find(p => p.id == id)
    }

    public async operateLand(landId: number, type: number, usingId?: number) {
        const prop = { id: landId - 1, type, powerValue: usingId }
        const { code, rewards, data } = await gameHelper.net.requestWithData(Msg.C2S_CeilOperationMessage, prop)
        if (code == 0) {
            let _rewards = gameHelper.toConditions(rewards)
            this.updateData(landId, type, data, usingId)
            gameHelper.grantRewards(_rewards)
            return _rewards
        }
        viewHelper.showNetError(code)
        return
    }

    private updateData(id: number, operateType: FieldOperationType, data: proto.IFieldCeil, usingId?) {
        let land = this.getLandById(id)
        land.init(id, data)

        if (operateType == FieldOperationType.UNLOCK) {
            gameHelper.deductCondition(this.getUnlockLandCost(id))
        }
        else if (operateType == FieldOperationType.PLANET) {
            this.changeSeed(usingId, -1)
        }
        else if (operateType == FieldOperationType.FERTILIZER) {
            this.changeFertilizer(usingId, -1)
        }
        else if (operateType == FieldOperationType.HARVEST) {
            !!this.condition['FIELD_GAIN_TIME'] ? this.condition['FIELD_GAIN_TIME']++ : this.condition['FIELD_GAIN_TIME'] = 1
        }
        this.levelUp()
        // eventCenter.emit(EventType.FIELD_TREE_MATURE, id)
    }

    private levelUp() {
        if (this.isMaxLevel()) return
        let cfg = cfgHelper.getFieldLevelCfg(this.level)
        let ok = true
        cfg.target.forEach(t => {
            if (!this.condition[t.type] || this.condition[t.type] < t.num) { ok = false }
        })
        if (ok) {
            this.level++
            this.condition = {}
            eventCenter.emit(EventType.FIELD_LEVEL_UP)
        }
        eventCenter.emit(EventType.FIELD_HARVEST)
    }

    public getUnlockLandCost(id: number) {
        let cfg = cfgHelper.getMiscData("field")
        let costs = cfg.foodLandUnlockCost
        let land = this.getLandById(id)
        let type = land.getType()
        let lands = this.getLands(type)
        let index = (lands.findIndex(n => n == land) + 1) - this.getInitUnlockLandCount(type)
        index = Math.min(costs.length - 1, index)
        return new ConditionObj().init(ConditionType.STAR_DUST, -1, costs[index])
    }

    public getInitUnlockLandCount(type) {
        let cfg = cfgHelper.getMiscData("field")
        return type == FieldType.FRUIT ? cfg.fruitLandUnlock : cfg.foodLandUnlock
    }

    public getLands(type: FieldType) {
        return this.landList.filter(n => n.getType() == type)
    }

    private onUnlock() {
        let unlockItems = cfgHelper.getMiscData("fieldUnlockGet") || []
        gameHelper.grantRewards(unlockItems)
    }
}
