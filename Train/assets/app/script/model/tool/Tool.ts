import { Msg } from "../../../proto/msg-define";
import { ToolCfg, ToolLevelCfg } from "../../common/constant/DataType";
import { ConditionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Tool {

    protected type: number
    protected lv: number = 0
    protected id: string    //toolLevel表的id
    protected cfg: ToolLevelCfg = null
    protected skinInfo: ToolCfg = null
    protected preCfg = null
    protected preSkinInfo = null


    public getPreCfg() { return this.preCfg }
    public getPreSkinInfo() { return this.preSkinInfo }
    public getType() { return this.type }
    public getLv() { return this.lv }
    public getQuality() { return this.quality }
    public getIconId() { return this.toolId || Tool.getDefaultId(this.type) }
    public get toolId() { return this.cfg?.toolId }
    public get quality() { return this.cfg?.quality || 1 }
    public get attack() { return this.cfg?.attack || 0 }
    public get amp() { return this.cfg?.amp || 0 }
    public get break() { return this.cfg?.break || 0 }
    public get hit() { return this.cfg?.hit || 0 }
    public get cost() { return this.lv > 0 ? this.cfg?.buyCost || [] : cfgHelper.getMiscData('toolInitial')[2].buyCost }
    public get skin() { return this.skinInfo?.skin }
    public get anim() { return this.skinInfo?.anim }
    public get icon() { return this.skinInfo?.icon }
    public get name() { return this.skinInfo?.name }
    public static getDefaultId(type: number) { return type * 1000 + 1 }

    public init(data: proto.IToolInfo) {
        this.type = data.type
        this.lv = data.lv
        this.initJson()
        return this
    }

    protected initJson() {
        this.id = `${this.type}-${this.lv}`
        this.cfg = cfgHelper.getToolLevel(this.id)
        this.skinInfo = cfgHelper.getTool(this.toolId)
    }

    public async make() {
        let msg = new proto.C2S_ToolMakeMessage({ type: this.type })
        const res = await gameHelper.net.request(Msg.C2S_ToolMakeMessage, msg, false)
        const { code } = proto.S2C_ToolMakeRespMessage.decode(res)
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        else {
            //客户端消耗物品
            gameHelper.deductConditions(gameHelper.toConditions(this.cost))
            this.lv++
            this.preCfg = this.cfg
            this.preSkinInfo = this.skinInfo
            this.initJson()
            if (this.preCfg?.quality != this.quality || this.preSkinInfo?.icon != this.icon) {
                eventCenter.emit(EventType.TOOL_CHANGE, this.type)
            }
            eventCenter.emit(EventType.TOOL_MAKE, this.type)
            return true
        }
    }
}
