import { TalentAttrCfg } from "../../common/constant/DataType"
import PassengerModel from "./PassengerModel"

export default class PassengerTalent {
    public id: number
    public level: number
    private cfg: TalentAttrCfg = null
    private role: PassengerModel = null

    public init(data: proto.IPassengerTalent, role: PassengerModel) {
        this.id = data.id
        this.level = data.level || 0
        this.role = role
        this.initJson()
        return this
    }

    protected initJson() {
        this.cfg = assetsMgr.getJsonData<TalentAttrCfg>("TalentAttr", this.id)
    }

    public get type() { return this.cfg?.type }
    public get target() { return this.cfg?.target }

    public get icon() { return `talent_icon_${this.role.id}_${this.id}`}
    public get name() { return `name_talent_${this.role.id}_${this.id}`}

}