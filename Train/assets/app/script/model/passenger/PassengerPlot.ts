import { CharacterPlotStep } from "../../common/constant/DataType"

export default class PassengerPlot {
    public id: string = ""
    public done: boolean = false

    public json: CharacterPlotStep = null
    public get costs() { return this.json.buyCost || [] }
    public get rewards() { return this.json.reward || [] }

    public init(data: proto.IPassengerPlot) {
        this.id = data.id
        this.done = data.done
        return this
    }
}
