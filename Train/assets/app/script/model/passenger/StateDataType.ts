class IStateData {
    public init(...params: any) { return this }
    public clean() { }
}

// 时间相关状态
class TimeStateData extends IStateData {

    time: number
    elapsed: number
    lastElapsed: number

    public init(time: number) {
        this.time = time
        this.elapsed = 0
        this.lastElapsed = 0
        return this
    }

    public ratio() {
        return this.time > 0 ? this.elapsed / this.time : 0
    }

    public isEnd() {
        if (this.time < 0) {
            return false
        }
        return this.elapsed >= this.time
    }

    public getSurplusTime() {
        return Math.max(this.time - this.elapsed, 0)
    }

    // 直接完成
    public complete() {
        this.elapsed = this.time
    }

    public update(dt: number) {
        this.elapsed += dt
        return this.isEnd()
    }

    public reset() {
        this.elapsed = 0
        this.lastElapsed = 0;
    }
}

export {
    IStateData,
    TimeStateData,
}