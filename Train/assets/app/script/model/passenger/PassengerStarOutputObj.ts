import { CACHE_UPDATE_TIME, SPEED_UP_RANDOM } from "../../common/constant/Constant"
import { ConditionType, SpeedUpType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { unlockHelper } from "../../common/helper/UnlockHelper"
import ConditionObj from "../common/ConditionObj"
import PassengerModel from "./PassengerModel"

export default class PassengerStarOutObj {

    protected output: number = 0 //产出累计值
    protected outputTime: number = 0 //计算产出累计时间，单位s
    protected genTime: number = 0 //下次产出的剩余时间(只是表现), 单位s
    protected debugTime: number = 0
    protected item: ConditionObj = new ConditionObj().init(ConditionType.STAR_DUST)

    protected cache: number = -1
    protected cacheUpdateTime: number = 0

    public init(output: number = 0) {
        this.updateOfflineOutput(output)
        this.reset()
        return this
    }

    public update(dt) {
        if (!this.canOutput()) return

        if (this.checkOutput()) {
            let succ = this.genOutput()
            this.checkReset(succ)
        }
    }

    protected canOutput() {
        if (!unlockHelper.canOutput()) return false
        return true
    }

    public setOutput(output: number = 0) {
        this.output = output
        this.outputTime = 0
        this.debugTime = 0
    }

    public getOutput() {
        return this.output
    }

    public addOutput(output: number) {
        this.output += output
    }

    public updateOfflineOutput(starOutput: number) {
        let moneys = []
        for (let carriage of gameHelper.train.getCarriages()) {
            moneys.pushArr(carriage.getAllDrops().filter(data => data.tips && data.moneySources.find(source => source.type == ConditionType.PASSENGER)))
        }
        let moneysVal: number = 0
        moneys.forEach((money) => {
            let s = money.moneySources.find(s => s.type == ConditionType.PASSENGER)
            if (s && s.num) {
                moneysVal += s.num
            }
        })
        let offlineOutput = starOutput - moneysVal
        if (offlineOutput > 0) {
            twlog.info(`[乘客同步恢复星尘: ${offlineOutput}, 服务器下发: ${starOutput}, 客户端已有: ${moneysVal}`)
            let roles = gameHelper.passenger.getPassengers().filter(p => p.carriageId)
            if (roles.length <= 0) {
                this.setOutput(offlineOutput)
                return
            }
            let oneNum = 10
            let num = Math.floor(offlineOutput / oneNum)
            num = Math.min(100, num)
            if (num > 0) {
                ut.numAvgSplit(offlineOutput, num).forEach((val) => {
                    if (val > 0) {
                        this.addDrop(roles.random(), val, 1)
                    }
                })
                this.setOutput(0)
            }
            else {
                this.setOutput(offlineOutput)
            }

        }
    }

    public genOutput() {
        let roles = gameHelper.passenger.getPassengers().filter(p => p.carriageId)
        let role = roles.random()
        if (!role) return
        let output = Math.floor(this.output)
        //新规则：产出少于每次丢出小费数量，此次不丢
        if (output <= role.starNum) return
        // twlog.info(`${gameHelper.world.formatWorldTime()} 乘客 ${role.getID()} ${ut.toFixed(this.debugTime)}秒, 产出${output}星尘`)
        this.debugTime = 0
        this.addDrop(role, output)
        this.output -= output
        return true
    }

    protected addDrop(role: PassengerModel, output: number, count?) {
        let carriage = role.carriage
        carriage.addDropMoneyByRole(new ConditionObj().init(ConditionType.STAR_DUST, -1, output), role, count)
    }

    private checkOutput() {
        let intervalTime = ut.Time.Hour
        return Math.floor(gameHelper.world.getTime() / intervalTime) - Math.floor(this.genTime / intervalTime) > 0
    }

    public checkReset(succ) {
        let intervalTime = ut.Time.Hour
        let now = gameHelper.world.getTime()
        if (succ || now % intervalTime >= ut.Time.Minute) this.reset()
    }

    public reset() {
        this.genTime = gameHelper.world.getTime()
    }
}
