import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"

export default class PassengerObj {
    public id: number = null;
    private unlock: boolean = null;
    private _skin: string = null;
    private _face: string = null;
    private _facePosition: cc.Vec2 = cc.v2();
    private imgSizeList: any[] = null;

    private data: any = null;

    public create(id: number) {
        this.id = id;
        this.initJson();
        this.initGridJson();
        return this;
    }

    public formDB(id: number, data: any) {
        this.id = id;
        this.unlock = true;
        this.data = data;
        this.initJson();
        this.initGridJson();
        return this;
    }

    public toDB() {
        return {
            _skin: this._skin,
            _face: this._face,
        }
    }

    public init(data: any) {
        this.id = this.id;
        this.unlock = true
        this.data = data;
        this.initJson();
        return this;
    }

    private initJson() {
        let cfg = cfgHelper.getCharacter(this.id);
        try {
            if (this.data) {
                this._skin = this.data._skin || cfg.skin[0].id;
                this._face = this.data._face || cfg.skin[0].face[0].id;
            } else {
                this._skin = cfg.skin[0].id;
                this._face = cfg.skin[0].face[0].id;
            }
        } catch (error) {

        }

    }

    private initGridJson() {
        const json = assetsMgr.getJsonData<any>('_CharacterGrid', this.skin);
        if (json) {
            let [x, y] = json.facePos.split(',');
            this._facePosition = cc.v2(x, y);
            this.imgSizeList = json.sizeList || [];
        } else {
            this.imgSizeList = [];
        }
    }

    public set skin(skin: string) {
        this._skin = skin;
        this.initGridJson();
    }

    public set face(face: string) {
        this._face = face;
    }

    public get skin() {
        return this._skin;
    }

    public get face() {
        return this._face;
    }

    public get facePosition() {
        return this._facePosition;
    }

    public getSizeData(size: cc.Size) {
        let id = `${ut.toFixed(size.width)},${ut.toFixed(size.height)}`
        for (let sizeData of this.imgSizeList) {
            if (sizeData && sizeData.size == id) {
                return sizeData;
            }
        }
        return null;
    }

    private getJsonName() { return "CharacterConfig" }

    private getSkinJsonName() { return "CharacterSkinConfig" }
    private getFaceJsonName() { return "CharacterFaceConfig" }


}