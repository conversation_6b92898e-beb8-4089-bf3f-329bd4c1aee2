import { PassengerLocation, PassengerAttr, PassengerQuality, PassengerLifeEvent, ConditionType, RoleDir, LifeSkillEffectType, CarriageUsePosType, CarriageIntoType, MarkNewType, ItemID, SkillType, ValueType, PassengerLifeAnimation, WantedState, CarriageID, BuildAttr, TalentAttrType, EquipEffectTarget, EquipEffectType, TrainDailyTaskType, TrainBurstTaskType } from "../../common/constant/Enums";
import { CharacterCfg, CharacterProfileCfg, CharacterStarLvCfg, Condition, EquipCfg, FocusCfg, TalentAttrCfg, Trigger } from "../../common/constant/DataType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { mapHelper } from "../../common/helper/MapHelper";
import MoveModel from "../map/MoveModel";
import PassengerActionModel from "./PassengerActionModel";
import EventType from '../../common/event/EventType';
import BattleSkill from '../battle/BattleSkill';
import LifeSkill from './Lifeskill';
import CarriageModel from "../train/common/CarriageModel";
import { StateType } from "./StateEnum";
import { Msg } from "../../../proto/msg-define";
import { viewHelper } from "../../common/helper/ViewHelper";
import PassengerPlot from "./PassengerPlot";
import DropMoneyObj from "../train/common/DropMoneyObj";
import ConditionObj from "../common/ConditionObj";
import { Equip } from "../equip/EquipModel";
import PassengerTalent from "./PassengerTalent";

export default class PassengerModel {
    public id: number = null;
    public viewWidth: number = 189;
    public view: cc.Node = null

    //属性相关
    private level: number = 1; //等级
    private starLv: number = 0 //突破等级

    public cfg: CharacterCfg = null;

    private location: PassengerLocation = null //乘客所在位置(背包，车厢，星球)
    public carriageId: number = null //当前所在车厢id
    public dormId: number = null //所在寝室id
    private dormIndex: number = 0
    public workId: number = 0 //工作车厢id
    private workIndex: number = 0 //工位下标
    public moveAgent: MoveModel = null
    public actionAgent: PassengerActionModel = null
    public events: { type: PassengerLifeEvent, times: number[], pros: number[], timeRange }[] = []

    private skills: BattleSkill[] = []; //战斗技能
    private lifeSkills: LifeSkill[] = [] //生活技能

    public get carriage(): CarriageModel { return gameHelper.train.getCarriageById(this.carriageId) }
    public get dorm() { return gameHelper.train.getCarriageById(this.dormId) }
    public get workCarrige() { return gameHelper.train.getCarriageById(this.workId) }
    public awakeWorkTime: number = -1

    public get likes() { return this.cfg?.likes || [] }
    public get hates() { return this.cfg?.hates || [] }
    public get starNum() { return this.cfg?.starNum || 1 } //每次丢出的小费数量（纯表现）
    public get suitcaseScale() { return this.cfg?.suitcaseScale || 1 }
    public get sortId() { return this.cfg?.sortId }
    public get battleType() { return this.cfg?.battleType }
    public get animalType() { return this.cfg?.animalType }
    public get isTmp() { return this.uid != String(this.id) } //临时拥有的角色

    private starLvCfg: CharacterStarLvCfg[] = []

    public plots: PassengerPlot[] = [] //剧情
    private readyPlotId: string = null //待触发的剧情key

    private skinIndex: number = 1

    public heart: number = 0

    public uid: string = null

    public isHide: boolean = false

    public talents: PassengerTalent[] = []

    private equips: Equip[] = null //如果初始化的时候传入装备数据，就用传入的；否则用equip模块的数据

    private attrRate: number = 1

    private profile: { [k: string]: number } = {}

    private tmpId: number = 0

    // 上一次对话气泡
    public lastDialogBubbleKey: string = ""
    private passTime: number = 0
    private dialogBubbleShowing: boolean = false
    private dialogWaitTime: number = 0

    public init(data: proto.IPassengerInfo) {
        this.initData(data);
        this.initListener()
        return this;
    }

    public initById(id: number) {
        return this.init({ id, level: 1, starLv: cfgHelper.getInitStarLv(id), useSkinIndex: 1 })
    }

    public initData(data: proto.IPassengerInfo) {
        this.id = data.id;
        this.uid = String(data.id)
        this.skinIndex = data.useSkinIndex
        let cfg = cfgHelper.getCharacter(data.id);
        this.cfg = cfg;
        this.starLvCfg = assetsMgr.getJson<CharacterStarLvCfg>('StarUp').datas
        this.starLv = data.starLv || 0
        this.level = data.level || 1;
        if (cfg.event) {
            this.events = this.cfg.event.map(({ type, time, randomMin, randomMax, pro }) => {
                let times = time.map(t => {
                    return ut.stringToMs(t)
                })
                return { type, times, pros: pro, timeRange: [randomMin, randomMax] }
            })
        }
        // 天赋
        if (data.talents) {
            this.talents = data.talents.map(t => {
                return new PassengerTalent().init(t, this)
            }).filter(t => t.level > 0)
        }

        if (data["equips"]) {
            this.equips = data["equips"].map((d) => { return new Equip().init(d) })
        }

        if (data["attrRate"]) {
            this.attrRate = data["attrRate"]
        }

        this.profile = data.profile || {}
        this.tmpId = data["tmpId"]
        this.initSkills()

        this.dormId = data.dormId
        this.workId = data.workId
        this.workIndex = data.workIndex
        this.dormIndex = data.dormIndex
        this.plots = data.plots?.map((d) => { return new PassengerPlot().init(d) }) || []
        this.initDialogWaitTime()

        return this
    }

    public updateInfo(data: proto.IPassengerInfo) {
        this.dormId = data.dormId
        this.workId = data.workId
        this.workIndex = data.workIndex
    }

    protected initPos(pos?: cc.Vec2) {
        if (!pos) {
            let posList = this.carriage.getUsePosListByType(CarriageUsePosType.START)
            pos = this.getRandomMovePos(posList.map(p => { return { rect: p.rect } }))
        }
        this.moveAgent.setPointAndPosition(null, pos)
    }

    public updateLocation(intoType?: CarriageIntoType) {
        let location: PassengerLocation
        let carriage: CarriageModel

        let hasWork = this.hasWork()

        if (intoType == CarriageIntoType.CHECK_IN) {
            location = PassengerLocation.CARRIAGE
            carriage = this.dorm
        } else if (intoType == CarriageIntoType.CHANGE_WORK) {
            location = hasWork ? PassengerLocation.CARRIAGE : (this.isCheckIn() ? PassengerLocation.CARRIAGE : PassengerLocation.TOP)
            carriage = hasWork ? this.workCarrige : (this.isCheckIn() ? this.dorm : null)
        } else {
            let canWork = this.hasWork() && this.awakeWorkTime <= 0
            location = canWork ? PassengerLocation.CARRIAGE : (this.isCheckIn() ? PassengerLocation.CARRIAGE : PassengerLocation.TOP)
            carriage = canWork ? this.workCarrige : (this.isCheckIn() ? this.dorm : null)
        }

        let { location: taskLocation, carriage: taskCarriage } = this.getLocationByTrainDailyTask()
        if (taskLocation) {
            location = taskLocation
            carriage = taskCarriage
        }

        let { location: burstLocation, carriage: burstCarriage } = this.getLocationByTrainBurstTask()
        if (burstLocation) {
            location = burstLocation
            carriage = burstCarriage
        }

        if (!hasWork && this.isShowPlot()) {
            location = PassengerLocation.CARRIAGE
            carriage = gameHelper.train.head
        }

        if (location == PassengerLocation.CARRIAGE) {
            if (!this.inCarriage() || this.carriage != carriage) {
                this.putToCarriage(carriage, intoType)
            }
        } else if (location == PassengerLocation.TOP) {
            if (this.location !== PassengerLocation.TOP) {
                this.putToTop(carriage)
            }
        }
    }

    private getLocationByTrainDailyTask() {
        let task = this.getTrainDailyTask()
        let location: PassengerLocation
        let carriage: CarriageModel
        if (task) {
            if (task.trainId) {
                location = PassengerLocation.CARRIAGE
                carriage = gameHelper.train.getCarriageById(task.trainId)
            }
            else {
                location = PassengerLocation.TOP
                let carriages = gameHelper.train.getAllCarriages()
                if (task.id == TrainDailyTaskType.STAND) {
                    let list = gameHelper.trainDailyTask.getListByID(task.id)
                    let standRoles = list.reduce((acc, item) => {
                        acc.push(...item.roles)
                        return acc
                    }, []).filter(id => id != this.id)
                    let useMap = {}
                    for (let id of standRoles) {
                        let role = gameHelper.passenger.getPassenger(id)
                        if (role) {
                            useMap[role.carriageId] = true
                        }
                    }
                    // console.log(this.id, JSON.stringify(useMap))
                    carriages = carriages.filter(c => { return !useMap[c.getID()] })
                    if (carriages.length == 0) {
                        twlog.error('updateLocation 没有空闲车厢')
                        carriages = gameHelper.train.getAllCarriages()
                    }
                }
                carriage = carriages.random()
            }
            if (carriage.getID() != CarriageID.HEAD) {
                if (task.id == TrainDailyTaskType.PATROL || task.id == TrainDailyTaskType.STAND) {
                    location = PassengerLocation.TOP
                }
            }
        }
        return { location, carriage }
    }

    private getLocationByTrainBurstTask() {
        let task = this.getTrainBurstTask()
        let location: PassengerLocation
        let carriage: CarriageModel
        if (task) {
            if (task.trainId) {
                carriage = gameHelper.train.getCarriageById(task.trainId)
                location = PassengerLocation.CARRIAGE
            }
            else {
                location = PassengerLocation.OUT
            }
        }
        return { location, carriage }
    }

    public inCarriage() {
        return this.location == PassengerLocation.CARRIAGE
    }

    private initListener() {
        if (this.isTmp) return
        eventCenter.on(EventType.PASSENGER_STATE_CHANGE, (model, state) => {
            if (this != model) return
        }, this)
        eventCenter.on(EventType.BATTLE_EQUIP_CHANGE, (id) => {
            if (this.id != id) return
            this.initSkills()
        })
        eventCenter.on(EventType.END_PLOT, (id) => {
            if (!this.readyPlotId) return
            let key = this.getPlotKey(this.readyPlotId)
            if (key != id) return
            this.completePlot(this.readyPlotId)
        })
    }

    public get name() { return this.cfg?.name }

    public getSkill(index: number) { return this.skills.find(s => s.index == index) }
    public getSkills() { return this.skills }
    public getLifeSkills() { return this.lifeSkills }
    public getLifeSkillByEffectType(type: LifeSkillEffectType) {
        for (let skill of this.lifeSkills) {
            if (skill.effects.find(effect => effect.type == type)) {
                return skill
            }
        }
    }
    public getID() { return this.id }
    public getLevel() { return this.level }
    public getMaxLv(starLv: number) {
        return cfgHelper.getMaxLvByStarLv(starLv)
    }
    public isMaxLv() {
        return this.level >= this.getMaxLv(this.starLv)
    }
    public getStarLv() { return this.starLv }
    public getMaxStarLv() { return this.starLvCfg.last().lv }
    public isMaxStarLv() { return this.starLv >= this.getMaxStarLv() }
    public getStarLvWithQuality() {
        return cfgHelper.getRealStarLv(this.starLv)
    }
    public getMaxStarLvWithQuality() {
        return cfgHelper.getMaxStarLvByStarLv(this.starLv)
    }
    public isMaxStarLvWithQuality() { return this.getStarLvWithQuality() >= this.getMaxStarLvWithQuality() }

    public getHp() {
        let val = this.getAttr(PassengerAttr.HP)
        return val
    }

    public getAttack() {
        let val = this.getAttr(PassengerAttr.ATTACK)
        return val
    }

    public getName() { return assetsMgr.lang(this.cfg?.name) }

    public markNew() {
        gameHelper.new.pushNew(MarkNewType.ROLE_NEW, [this.id])
        gameHelper.new.pushNew(MarkNewType.ROLE_UNREAD, [this.id])
    }
    public removeRoleNew() {
        gameHelper.new.removeNew(MarkNewType.ROLE_NEW, [this.id])
    }
    public removeRoleUnRead() {
        gameHelper.new.removeNew(MarkNewType.ROLE_UNREAD, [this.id])
    }

    public initSkills() {
        let skills = cfgHelper.getSkillsByLv(this.id, this.level, this.tmpId)
        this.skills = skills.map(({ id, lv }) => {
            let index = cfgHelper.getIndexBySkillId(id)
            let incLv = this.getEquipAttr(PassengerAttr.SKILL, index)
            let profile = this.getArchivesSkill(index, 0)

            let skill = new BattleSkill()
            skill.attrRate = this.attrRate + profile.rate
            return skill.init(id, lv + incLv + profile.base, this)
        })
    }

    public async addLevel(level: number) {
        while (level > 0) {
            let msg = new proto.C2S_PassengerLevelUpMessage({ type: 1, id: this.id })
            const res = await gameHelper.net.request(Msg.C2S_PassengerLevelUpMessage, msg, true)
            const { code } = proto.S2C_PassengerLevelUpResultMessage.decode(res)
            if (code != 0) {
                viewHelper.showNetError(code)
                return false
            }

            this.level++
            level--
        }
        this.initSkills()
        return true
    }

    public newSkill(skillId: number, giveLv?: number) {
        let lv = giveLv || (cfgHelper.getSkillLv(this.id, cfgHelper.getUnlockSkillLv(this.id, skillId), skillId))
        return new BattleSkill().init(skillId, lv, this)
    }

    public async addStarLv(level: number = 1) {
        while (level > 0) {
            let msg = new proto.C2S_PassengerLevelUpMessage({ type: 2, id: this.id })
            const res = await gameHelper.net.request(Msg.C2S_PassengerLevelUpMessage, msg, true)
            const { code } = proto.S2C_PassengerLevelUpResultMessage.decode(res)
            if (code != 0) {
                viewHelper.showNetError(code)
                return false
            }
            level--
            let cfg = cfgHelper.getStarLvCfg(this.starLv)
            gameHelper.passenger.changeFrag(+cfgHelper.getFragByCharacterIdAndQuality(String(this.id), this.quality).id, -cfg.upCost)
            this.starLv++
            this.initSkills()
        }
        eventCenter.emit(EventType.PASSENGER_STARLV_UP, this);
        return true
    }

    //drtLevelVal查当前lv + drtLevelVal,drtStarVal同理
    public getAttr(type: PassengerAttr, drtLevelVal: number = 0, drtStarVal: number = 0, drtTalentVal = 0, drtArchivesLv = 0) {
        let lv = Math.max(this.level + drtLevelVal, 1)
        let effectId = EquipEffectTarget.HP
        if (type == PassengerAttr.ATTACK) {
            effectId = EquipEffectTarget.ATTACK
        }
        let talentLv = this.getTalentLevel(effectId)
        talentLv = Math.max(talentLv + drtTalentVal, 0)
        let archivesAttr = this.getArchivesAttr(type, drtArchivesLv)
        let talentVal = cfgHelper.getRoleAttrByAdd(this.id, talentLv * 5, 0, type)
        let lvVal = cfgHelper.getRoleAttr(this.id, lv, 0, type)
        let equipVal = this.getEquipAttr(type, effectId)

        let val = lvVal + equipVal
        const rate = 1 + this.getStarLvPerInc(drtStarVal) + archivesAttr.rate

        return Math.round((val * rate + talentVal + archivesAttr.base) * this.attrRate)
    }

    public getEquipAttr(type: PassengerAttr, target: number) {
        let equips = this.getEquips()
        let sum = 0
        for (let equip of equips) {
            for (let effect of equip.effects) {
                if (type == PassengerAttr.HP && effect.target == EquipEffectTarget.HP) {
                    sum += effect.attr
                }
                else if (type == PassengerAttr.ATTACK && effect.target == EquipEffectTarget.ATTACK) {
                    sum += effect.attr
                }
                else if (type == PassengerAttr.SKILL && effect.type == EquipEffectType.SKILL && effect.target == target) {
                    sum += effect.attr
                }
            }
        }
        return sum
    }

    public getArchivesSkill(skillIndex: number, drtVal: number = 0) {
        const num = this.getProfilesNum()
        let unlockLv = cfgHelper.getCharacterProfileLvByUnlockCount(this.id, num)
        let lv = Math.max(0, unlockLv + drtVal)
        const effects = cfgHelper.getCharacterProfileAttrEffectById(lv)
        let base = 0
        let rate = 0.0
        for (const effect of effects) {
            if (effect.type == 1) continue
            // 加成的技能序号
            if (effect.target != skillIndex) continue
            if (effect.isPercent) {
                rate += effect.val
            }
            else {
                base += effect.val
            }
        }
        return { base, rate }
    }

    public getArchivesAttr(type: PassengerAttr, drtVal = 0) {
        const num = this.getProfilesNum()
        let unlockLv = cfgHelper.getCharacterProfileLvByUnlockCount(this.id, num)
        let lv = Math.max(0, unlockLv + drtVal)
        const effects = cfgHelper.getCharacterProfileAttrEffectById(lv)

        const attack = this.cfg.attack
        const hp = this.cfg.hp
        let base = 0
        let rate = 0.0
        for (const effect of effects) {
            if (effect.type == 2) continue
            // 属性加成,如果target是1则取攻击血量较低的进行加成,2则是取最高的进行加成计算,如果相等1取攻击2取血量
            let attrType = null
            switch (effect.target) {
                case 1:
                    if (attack > hp) {
                        attrType = PassengerAttr.HP
                    }
                    else {
                        attrType = PassengerAttr.ATTACK
                    }
                    break
                case 2:
                    if (attack > hp) {
                        attrType = PassengerAttr.ATTACK
                    }
                    else {
                        attrType = PassengerAttr.HP
                    }
                    break
            }
            if (attrType != type) continue
            if (!effect.isPercent) {
                base += cfgHelper.getRoleAttrByAdd(this.id, effect.val, 0, attrType)
            }
            else {
                rate += effect.val
            }
        }
        return { base, rate }
    }

    //星级提升百分比
    public getStarLvPerInc(drtStarVal: number = 0) {
        let starPre = -drtStarVal
        let starLvAttr = cfgHelper.getRoleAttrByStarLv(Math.max(this.starLv - starPre, 0))
        return ut.toFixed(starLvAttr, 2)
    }

    public getSkinAttr(effectType: LifeSkillEffectType, valueType: ValueType) {
        let skins = this.getSkins() || []
        return skins.reduce((sum, skin) => sum + skin.getAttr(effectType, valueType), 0)
    }

    public setLocation(loc: PassengerLocation) {
        this.location = loc;
    }

    public get quality() {
        return cfgHelper.getQualityByStarLv(this.starLv)
    }

    public getLocation() {
        return this.location;
    }

    public isCheckIn() {
        return !!this.dormId
    }

    update(dt) {
        //角色剧情
        this.checkTriggerPlot()
        // 气泡对话
        this.checkDialog()

        this.actionAgent && this.actionAgent.update(dt)

        this.checkAwakeWork()
        this.passTime += dt * ut.Time.Second

    }

    public getMap() {
        if (this.getLocation() == PassengerLocation.TOP) return this.carriage.getTopMap()
        return this.carriage.getMap();
    }

    public getRandomMovePos<T extends { rect?: cc.Rect }>(_areas?: T[], out?: { [key: string]: any }) {
        if (this.inCarriage()) {
            return this._getCarriageRandomMovePos(_areas, out)
        }
        else {
            let map = this.getMap()
            let posList = this.carriage.getTopPassengers().map(p => {
                let pos = p.getPosition()
                if (p.isMoving()) { //如果在移动就选终点位置，否则选当前位置
                    pos = p.moveAgent.getPaths().last() || pos
                }
                return pos
            })
            let reachablePoints = map.getConnectPoints(cc.v2());
            let index = this.carriage.getIndex()
            let half = Math.ceil(gameHelper.train.getCarriages().length / 2)
            if (index < half) {
                let offset = 10
                reachablePoints = reachablePoints.filter(p => {
                    return p.x < map.getSize().width - offset
                })
            }
            let randomFunc = () => {
                return map.getActPixelByPoint(reachablePoints.random())
            }
            let pos = mapHelper.sparsePosRandom(randomFunc, posList, null, 50)
            return pos
        }
    }

    private _getCarriageRandomMovePos<T extends { rect?: cc.Rect }>(_areas?: T[], out?: { [key: string]: any }) {
        let map = this.getMap()
        let carriage = this.carriage
        // let emptyAreas = carriage.getEmptyAreas()
        // let areas = _areas || emptyAreas
        let areas = _areas || []

        // 获取当前车厢所有乘客的位置
        let posList = carriage.getPassengers().map(p => {
            let pos = p.getPosition()
            return pos
        })

        let randomFunc
        let retryCount = 30
        let moveAgent = this.moveAgent
        if (areas.length > 0) {
            //先选人数最少的区域
            let cnts = areas.map(area => {
                let cnt = 0
                for (let pos of posList) {
                    if (area.rect.contains(pos)) {
                        cnt++
                    }
                }
                return cnt
            })
            let min = cnts.min(c => c)
            let minAreas = []
            for (let i = 0; i < cnts.length; i++) {
                if (cnts[i] == min) minAreas.push(areas[i])
            }
            let area = minAreas.random()
            if (out) {
                out.index = area.findIndex(a => a == area)
            }
            randomFunc = () => {
                let pos = cc.v2()
                while (retryCount--) {
                    let rdx = ut.randomRange(0, area.rect.width)
                    let rdy = ut.randomRange(0, area.rect.height)
                    pos.set2(area.rect.x + rdx, area.rect.y + rdy)
                    let point = map.getActPointByPixel(pos)
                    if (moveAgent.checkCanPass(point.x, point.y)) {
                        return pos
                    }
                    else {
                        // twlog.error("checkCanPass fail1", this.id, point.toJson())
                    }
                }
                return pos
            }
        }
        else { //没配空地，只能随机个看起来空的地方
            let reachablePoints = map.getConnectPoints(cc.v2());
            if (reachablePoints.length <= 1) { //与四周都不连通了
                reachablePoints = map.getMainEmptyPoints()
            }
            randomFunc = () => {
                let pos = cc.v2()
                while (retryCount--) {
                    let point = reachablePoints.random()
                    if (moveAgent.checkCanPass(point.x, point.y)) {
                        return map.getActPixelByPoint(point)
                    }
                    else {
                        // twlog.error("checkCanPass fail2", this.id, point.toJson())
                    }
                }
                return pos
            }
        }

        //尽量找一个远离所有人的位置
        let pos = mapHelper.sparsePosRandom(randomFunc, posList, null, 50)
        return pos
    }

    public onEnterCarriage(carriage: CarriageModel, intoType: CarriageIntoType) {
        this.carriageId = carriage.getID()
        this.setLocation(PassengerLocation.CARRIAGE)
        this.initMoveAgent()
        this.initActionAgent(intoType == CarriageIntoType.CHECK_IN)
    }

    public onEnterCarriageTop(carriage: CarriageModel, intoType: CarriageIntoType) {
        this.carriageId = carriage.getID()
        this.setLocation(PassengerLocation.TOP)
        this.initMoveAgent()
        this.initActionAgent()
    }

    //需先设置carriageId
    public initMoveAgent() {
        if (!this.moveAgent) {
            this.moveAgent = new MoveModel()
        }
        this.moveAgent.initMapInfo(this.getMap())
        this.moveAgent.setSpeed(this.getPassengerSpeed());
    }

    private getPassengerSpeed(): number {
        let speed = ut.random.call(null, ...[-30, 30]);
        return 200 + speed;
    }

    private initActionAgent(fromTop: boolean = false) {
        if (!this.actionAgent) {
            this.actionAgent = new PassengerActionModel()
        }
        this.actionAgent.enterFromTop = fromTop
        this.actionAgent.init(this);
    }

    public async changeCheckIn(carriage: CarriageModel, index: number, role: PassengerModel) {
        if (!!role) {
            let bol = await role.checkOutBySever()
            if (!bol) return
            bol = await this.checkInBySever(carriage, index, null, true)
            return bol
        } else {
            let succ = await this.checkInBySever(carriage, index, null, true)
            return succ
        }
    }

    public async checkInBySever(carriage: CarriageModel, index: number, position?: cc.Vec2, isChange: boolean = false) {
        let msg = new proto.C2S_ChangePassengerDormMessage({ id: this.id, index, dormId: carriage.getID() })
        const res = await gameHelper.net.request(Msg.C2S_ChangePassengerDormMessage, msg, true)
        const { code } = proto.S2C_ChangePassengerDormRespMessage.decode(res)
        if (code == 0) {
            this.checkIn(carriage, index, position, CarriageIntoType.CHECK_IN, isChange)
            viewHelper.backCameraFromEdit(this.carriage, this.id, 1.4)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public checkIn(carriage: CarriageModel, index: number, position?: cc.Vec2, intoType: CarriageIntoType = CarriageIntoType.CHECK_IN, isChange: boolean = false) {
        this.dormId = carriage.getID()
        this.dormIndex = index
        this.updateLocation(intoType)
        eventCenter.emit(EventType.PASSENGER_CHECK_IN, this, isChange)
    }

    public async checkOutBySever() {
        let msg = new proto.C2S_ChangePassengerDormMessage({ id: this.id })
        const res = await gameHelper.net.request(Msg.C2S_ChangePassengerDormMessage, msg, true)
        const { code } = proto.S2C_ChangePassengerDormRespMessage.decode(res)
        if (code == 0) {
            this.checkOut()
            viewHelper.backCameraFromEdit(this.carriage, this.id)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public checkOut() {
        this.dormId = null
        this.dormIndex = 0
        this.updateLocation()
        eventCenter.emit(EventType.PASSENGER_CHECK_OUT, this)
    }

    public putToCarriage(carriage: CarriageModel, infoType: CarriageIntoType = CarriageIntoType.NORMAL, pos?: cc.Vec2) {
        if (this.inCarriage()) {
            if (this.carriage == carriage) return
            this.carriage.roleExit(this)
        }
        else {
            this.carriage?.roleExitTop(this)
        }
        this.onExit()
        carriage.roleEnter(this, infoType)
        if (infoType != CarriageIntoType.CHECK_IN) {
            this.initPos(pos)
        }
    }

    public putToTop(targetCarriage?: CarriageModel) {
        let curCarriage = this.carriage
        if (this.inCarriage()) {
            curCarriage.roleExit(this);
        }
        this.onExit()

        targetCarriage = targetCarriage || curCarriage
        if (!targetCarriage || targetCarriage.getID() == CarriageID.HEAD) {
            targetCarriage = gameHelper.train.getCarriages().random()
        }

        targetCarriage.roleEnterTop(this)
        this.initPos()
    }

    public onExit() {
        this.cleanAction()
        this.carriageId = null
        this.moveAgent = null
    }

    public startAction() {
        if (this.location != PassengerLocation.CARRIAGE) return
        this.cleanAction()
        this.initActionAgent()
    }

    public cleanAction() {
        if (this.actionAgent) {
            this.actionAgent.clean()
            this.actionAgent = null
        }
    }

    public setPosition(position: cc.Vec2) {
        this.moveAgent.setPointAndPosition(null, position)
        eventCenter.emit(EventType.UPDATE_PASSENGER_POS, this)
    }

    public setDir(dir: RoleDir) {
        this.moveAgent?.setDir(dir)
        eventCenter.emit(EventType.PASSENGER_FLIP, this)
    }

    public setDirToPos(targetPos: cc.Vec2) {
        let pos = this.getPosition()
        let dir = targetPos.x - pos.x
        if (dir > 0) {
            this.setDir(RoleDir.RIGHT)
        }
        else if (dir < 0) {
            this.setDir(RoleDir.LEFT)
        }
    }

    public getDir() {
        return this.moveAgent?.dir
    }

    public getScale() {
        return this.actionAgent.getScale()
    }

    public setPointAndPosition(point: cc.Vec2, position?: cc.Vec2) {
        this.moveAgent.setPointAndPosition(point, position)
    }

    public getPosition() {
        if (this.moveAgent) {
            return this.moveAgent.getPosition()
        }
        return cc.v2(0, 0)
    }

    public getPoint() {
        if (this.moveAgent) {
            return this.moveAgent.getPoint()
        }
        return cc.v2()
    }

    public isMoving() {
        return this.moveAgent && this.moveAgent.isMoving()
    }

    public hasHeart() {
        return this.getHeart() > 0
    }

    public getHeart() {
        return this.heart
    }

    public addHeart(heart) {
        this.setHeart(this.heart + heart)
    }

    public setHeart(heart) {
        this.heart = heart
        eventCenter.emit(EventType.PASSENGER_HEART_CHANGE, this)
    }

    public hasDialog() { return this.dialogBubbleShowing }

    private getRandomDialogBubbleKey(isClick: boolean = false) {
        if (this.actionAgent.getState(StateType.SLEEP)) {
            this.lastDialogBubbleKey = "carriage_dialog_sleep"
            return
        }
        const cfg = cfgHelper.getCharacterDialog(this.id)
        let keys = []
        if (isClick) {
            keys = cfg.langAry
        }
        else {
            let key = String(this.carriageId)
            if (!this.inCarriage()) {
                key = "top"
            }
            if (cfg.auto) {
                keys = cfg.auto.filter(k => k.includes(key))
            }
        }
        if (keys.length < 0) {
            this.lastDialogBubbleKey = "character_dialog_common"
            return
        }
        // 尽量和上次key不相同
        const canIndexes = []
        keys.forEach((key) => key != this.lastDialogBubbleKey && canIndexes.push(key))
        this.lastDialogBubbleKey = canIndexes[ut.randomIndex(canIndexes.length)]
        return this.lastDialogBubbleKey
    }

    public checkDialog(force?: boolean) {
        if (!gameHelper.planet.getPlanet(1001)?.isDone()) return //通关学院星再说话
        if (this.getReadyPlotId()) return
        if (!force) {
            if (this.hasDialog()) {
                // dialog只出现4s
                if (this.passTime >= ut.Time.Second * 4) {
                    this.passTime = 0
                    this.dialogBubbleShowing = false
                    return void eventCenter.emit(EventType.PASSENGER_DIALOG_END, this)
                }
                return
            }
        }
        if (this.actionAgent.getState(StateType.SLEEP)) {
            // if (this.hasDialog()) {
            //     this.passTime = 0
            //     this.dialogBubbleShowing = false
            //     eventCenter.emit(EventType.PASSENGER_DIALOG_END, this)
            // }
            this.passTime = 0
            if (!force) {
                return
            }
        }
        const ok = () => {
            this.passTime = 0
            this.initDialogWaitTime()
            this.dialogBubbleShowing = true
            this.getRandomDialogBubbleKey(force)
            return void eventCenter.emit(EventType.PASSENGER_DIALOG_START, this, force)
        }
        if (force) {
            return ok()
        }
        if (this.passTime >= this.dialogWaitTime) {
            ok()
        }
    }

    private initDialogWaitTime() {
        this.dialogWaitTime = ut.randomRange(10, 60) * ut.Time.Second
    }

    public getSleepTime() {
        let now = gameHelper.world.getDayTime()
        let sleepTimes = this.events.find(event => event.type == PassengerLifeEvent.SLEEP)?.times || []
        let awakeTimes = this.events.find(event => event.type == PassengerLifeEvent.WAKEUP)?.times || []
        let len = sleepTimes.length
        for (let i = 0; i < len; i++) {
            let sleepTime = sleepTimes[i]
            let awakeTime = awakeTimes[i]
            if (sleepTime > awakeTime) { //跨天
                if (now <= awakeTime) {
                    return awakeTime - now
                }
                else if (now >= sleepTime) {
                    return ut.Time.Day - now + awakeTime
                }
            }
            else if (sleepTime <= now && now <= awakeTime) {
                return awakeTime - now
            }
        }
        return 0
    }

    public getLeaveTime() {
        let now = gameHelper.world.getDayTime()
        let leaveTimes = this.events.find(event => event.type == PassengerLifeEvent.LEAVE)?.times || []
        let goHomeTimes = this.events.find(event => event.type == PassengerLifeEvent.GOHOME)?.times || []
        let len = leaveTimes.length
        for (let i = 0; i < len; i++) {
            let leaveTime = leaveTimes[i]
            let goHomeTime = goHomeTimes[i]
            if (leaveTime > goHomeTime) { //跨天
                if (now <= goHomeTime) {
                    return goHomeTime - now
                }
                else if (now >= leaveTime) {
                    return ut.Time.Day - now + goHomeTime
                }
            }
            else if (leaveTime <= now && now <= goHomeTime) {
                return goHomeTime - now
            }
        }
        return 0
    }

    public getEvent(type: PassengerLifeEvent) {
        return this.events.find(event => event.type == type)
    }

    public getLastEventInfo(type: PassengerLifeEvent, time?) {
        let event = this.events.find(event => event.type == type)
        if (!event) return
        time = time || gameHelper.world.getDayTime()
        let index = event.times.findIndex(t => t > time)
        if (index <= 0) {
            index = event.times.length
        }
        index -= 1
        let pro = event.pros?.[index]
        if (pro === undefined) pro = 100
        return { time: event.times[index], pro, index }
    }

    public getNextEventInfo(type: PassengerLifeEvent, time?) {
        let event = this.events.find(event => event.type == type)
        if (!event) return
        time = time || gameHelper.world.getDayTime()
        let index = event.times.findIndex(t => t > time)
        if (index == -1) {
            index = 0
        }
        let pro = event.pros?.[index]
        if (pro === undefined) pro = 100
        return { time: event.times[index], pro, index }
    }

    public getWanted() {
        let wanteds = gameHelper.wanted.getWanteds()
        return wanteds.find(w => w.getState() == WantedState.START && w.getRoles().some(id => id == this.id))
    }

    public getSkillByEffectType(type: LifeSkillEffectType) {
        for (let skill of this.lifeSkills) {
            if (skill.effects.some(e => e.type == type))
                return skill
        }
    }

    public getSkillByWorkInTrain(trainId: number) {
        for (let skill of this.lifeSkills) {
            if (skill.canWorkInTrain(trainId))
                return skill
        }
    }

    //雇佣
    public async hire(carriageId: number, index: number) {
        let succ = await this.changeWorkBySever(carriageId, index)
        if (succ) {
            this.onHire()
        }
        return succ
    }

    //辞退
    public async fire() {
        let succ = await this.changeWorkBySever()
        if (succ) {
            this.onFire()
        }
        return succ
    }

    private async changeWorkBySever(workId?: number, workIndex?: number) {
        let msg = new proto.C2S_ChangePassengerWorkMessage({ id: this.getID(), workId, workIndex })
        let res = await gameHelper.net.request(Msg.C2S_ChangePassengerWorkMessage, msg, true)
        const { code } = proto.S2C_ChangePassengerWorkMessage.decode(res)
        if (code == 0) {
            this.workId = workId
            this.workIndex = workIndex
            eventCenter.emit(EventType.PASSENGER_CHANGE_WORK, this)
            eventCenter.emit(EventType.ACHIEVE_COMPLETE)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    // 身上是否有工作
    public hasWork() {
        return !!this.workId
    }

    public getWorkIndex() {
        return this.workIndex
    }

    public getDormIndex() {
        return this.dormIndex
    }

    public getCheckInBuildAttr() {
        if (this.isCheckIn()) {
            return this.dormIndex % 2 == 1 ? BuildAttr.STAR : BuildAttr.HEART
        }
    }

    public onHire() {
        this.actionAgent?.clean()
        this.updateLocation(CarriageIntoType.CHANGE_WORK)
        this.actionAgent?.restart()

        viewHelper.backCameraFromEdit(this.workCarrige, this.id)
    }

    public onFire() {
        this.updateLocation(CarriageIntoType.CHANGE_WORK)
        this.actionAgent?.restart()

        viewHelper.backCameraFromEdit(this.workCarrige, this.id)
    }

    private checkAwakeWork() {
        if (this.awakeWorkTime < 0 || this.awakeWorkTime > gameHelper.world.getTime()) return
        this.awakeWorkTime = -1
        this.updateLocation(CarriageIntoType.CHANGE_WORK)
    }

    public async completePlot(id: string) {
        let plot = this.getPlot(id)
        if (plot?.done) {
            return true
        }
        let msg = new proto.C2S_CompletePassengerPlotMessage({ id })
        let res = await gameHelper.net.request(Msg.C2S_CompletePassengerPlotMessage, msg, true)
        const { code } = proto.S2C_CompletePassengerPlotMessage.decode(res)
        if (code == 0) {
            if (!plot) {
                plot = new PassengerPlot().init({ id, done: true })
                this.plots.push(plot)
            }
            plot.done = true
            this.setReadyPlotId(null)
            this.checkTriggerPlot()
            if (!this.getReadyPlotId()) {
                if (this.hasWork() || !this.isCheckIn()) {
                    this.updateLocation()
                }
            }
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public getPlot(id?: string) {
        if (id == null) { //为了方便调用，目前版本只有一个
            return this.plots[0]
        }
        return this.plots.find(p => p.id == id)
    }

    private isPlotDone(id: string) {
        return this.getPlot(id)?.done
    }

    public checkTriggerPlot() {
        if (this.readyPlotId) return
        let datas = cfgHelper.getPlotControlData(this.id).filter(m => !this.isPlotDone(m.id))
        for (let { id, trigger, preId } of datas) {
            if (this.checkCanTriggerPlot(trigger, preId)) {
                return this.setReadyPlotId(id)
            }
        }
    }

    private checkCanTriggerPlot(trigger?: Condition[], preId?: string) {
        if (preId && !this.isPlotDone(preId)) {
            return
        }
        if (!trigger) return true
        for (let { type, id } of trigger) {
            if (type == ConditionType.PLANET_COMPLETE) {
                return gameHelper.planet.getPlanet(Number(id))?.isDone()
            } else if (type == ConditionType.BUILD_ID) {
                return gameHelper.train.isUnlockBuild(String(id))
            }
        }
        return true
    }

    public getReadyPlotId() {
        return this.readyPlotId
    }

    private setReadyPlotId(plotKey: any) {
        this.readyPlotId = plotKey
        if (plotKey) {
            if (!this.hasWork() && this.isShowPlot()) {
                this.updateLocation()
            }
        }
        eventCenter.emit(EventType.PASSENGER_PLOT_CHANGE, this)
    }

    public isShowPlot() {
        return this.readyPlotId && !this.actionAgent?.guideType
    }

    public startPlot() {
        if (!this.readyPlotId) return
        // gameHelper.characterPlot.start(this, this.readyPlotId, this.getLastPlot(this.readyPlotId))
        let key = this.getPlotKey(this.readyPlotId)
        gameHelper.plot.start(key)
        eventCenter.emit(EventType.PASSENGER_PLOT_CHANGE, this)
    }

    private getPlotKey(id: string) {
        let cfg = cfgHelper.getPlotControlData(this.id).find(m => m.id == id)
        return cfg?.plotKey
    }

    public getSkin() {
        return gameHelper.passenger.getSkinsById(this.id).find(s => s.index == this.skinIndex)
    }

    public getContent() {
        let idx = this.getLetter()
        let str = this.cfg.content
        if (idx >= 0) {
            return `${str}_${idx + 1}`
        }
        return str
    }

    public getLetter() {
        let letters = this.cfg.letter || []
        let index = -1
        for (let i = letters.length - 1; i >= 0; i--) {
            let id = letters[i]
            if (gameHelper.bag.getPropById(id)) {
                index = i
                break
            }
        }
        return index
    }

    public getDropByHeart() {
        let drop = new DropMoneyObj().init(this.id, cc.v2(), new ConditionObj().init(ConditionType.HEART, -1))
        drop.addMoney(new ConditionObj().init(ConditionType.PASSENGER, this.id, this.getHeart()))
        drop.carriageId = this.carriageId
        return drop
    }


    public getResetRewards() {
        let rewards = []
        for (let i = 1; i < this.level; i++) {
            let buyCost = cfgHelper.getLvUpCost(this.id, i)
            rewards.pushArr(gameHelper.toConditions(buyCost))
        }
        // 天赋
        if (this.talents.length > 0) {
            let num = 0
            const tCfgs = assetsMgr.getJson<any>('TalentAttrLevel').datas
            for (let talent of this.talents) {
                for (let i = 1; i <= talent.level; i++) {
                    const bean = tCfgs.find(c => c.id == i)
                    bean && (num += bean.cost)
                }
            }
            num && rewards.push(new ConditionObj().init(ConditionType.PROP, ItemID.VITALITY, num))
        }
        rewards.sort((a, b) => {
            return gameHelper.getLevelByCond(b) - gameHelper.getLevelByCond(a)
        })
        return rewards
    }

    public getResonanceLv() {
        if (!gameHelper.resonance.isBeResonanced(this.id)) {
            return this.level
        }
        return this.getCanResonanceLv()
    }

    public getCanResonanceLv() {
        let starLv = this.getStarLv()
        let maxLevel = this.getMaxLv(starLv)
        return cc.misc.clampf(gameHelper.passenger.getResonaceLv(), this.level, maxLevel)
    }

    public toResonance() {
        if (!gameHelper.resonance.isBeResonanced(this.id)) {
            return this
        }
        let { level, equipInfo, talentInfo } = gameHelper.resonance.getResonaceInfo()
        let equips = []
        for (let key in equipInfo) {
            let index = Number(key)
            let resonanceEquip = equipInfo[index]
            let equip = this.getEquip(index)
            let resonanceLv = resonanceEquip?.getEffectsLevel() || 0
            let equipLv = equip?.getEffectsLevel() || 0
            if (equipLv < resonanceLv) {
                let data = assetsMgr.getJson<EquipCfg>("Equip").datas.find(d => d.roleId == this.id && d.index == index)
                let effects = resonanceEquip.effects.map((e) => {
                    let attr = cfgHelper.getEquipEffectAttrByLv(data.id, e.cfg, e.level)
                    return { id: e.id, level: e.level, attr }
                })
                equip = new Equip().init({
                    id: data.id,
                    level: resonanceEquip.getLv(),
                    effects,
                })
            }
            if (equip) {
                equips.push(equip)
            }
        }

        let talents = []
        for (let id in talentInfo) {
            let resonanceLv = talentInfo[id]
            let maxLevel = this.getTalentMaxLevel(Number(id))
            let level = cc.misc.clampf(resonanceLv, this.getTalentLevel(Number(id)), maxLevel)
            talents.push({ id: Number(id), level })
        }
        let starLv = this.getStarLv()
        let maxLevel = this.getMaxLv(starLv)
        level = Math.min(level, maxLevel)

        let data = { id: this.id, starLv, level, talents, equips }
        let p = new PassengerModel().initData(data)
        p.uid = this.uid
        return p
    }

    public getProfiles() { return this.profile }
    public getProfile(type: string) { return this.profile[type] }
    public getProfileByPosition(position: number) {
        for (const key in this.profile) {
            if (this.profile[key] == position) {
                return key
            }
        }
        return null
    }

    public changeProfile(type: number, position: number) {
        this.profile[type] = position
        if (position == 0) {
            delete this.profile[type]
        }
    }

    public setProfile(profile: CharacterProfileCfg, position: number) {
        if (!this.profile[profile.type]) {
            this.profile[profile.type] = position
        }
        this.initSkills()
    }

    public getProfilesNum() {
        let num = 0
        for (let key in this.profile) {
            if (this.profile[key]) num++
        }
        return num
    }

    public getSkins() {
        return gameHelper.passenger.getSkinList()[String(this.id)]
    }

    public async changeSkin(skinIndex: number) {
        let msg = new proto.C2S_ChangeSkinMessage({ passengerId: this.id, skinIndex })
        const res = await gameHelper.net.request(Msg.C2S_ChangeSkinMessage, msg, true)
        const { code } = proto.S2C_ChangeSkinMessage.decode(res)
        if (code == 0) {
            this.setSkin(skinIndex)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public setSkin(skinIndex: number) {
        this.skinIndex = skinIndex
        eventCenter.emit(EventType.PASSENGER_SKIN_CHANGE, this)
    }

    public getTalent(id: number) {
        return this.talents.find(t => t.id == id)
    }

    // 获取天赋等级
    public getTalentLevel(id: number) {
        return this.getTalent(id)?.level || 0
    }

    public getTalentMaxLevel(id: number) {
        return 1000
        // let data = assetsMgr.getJsonData<TalentAttrCfg>("TalentAttr", id)
        // if (data.type == EquipEffectType.SKILL) {
        //     let isUnlock = this.skills.some(s => s.index == data.target)
        //     if (!isUnlock) {
        //         return 0
        //     }
        // }
        // return 100
    }

    public addTalentLevel(id: number, lv: number = 1) {
        let talent = this.getTalent(id)
        if (!talent) {
            talent = new PassengerTalent().init({ id }, this)
            this.talents.push(talent)
        }
        talent.level += lv

        if (talent.type == EquipEffectType.SKILL) {
            let skill = this.skills.find(s => s.index == talent.target)
            if (skill) {
                skill.init(skill.getId(), skill.getLevel(), skill.getRole())
            }
        }
    }

    public getEquip(index: number) {
        if (this.equips) {
            return this.equips.find(e => e.index == index)
        }
        return gameHelper.equip.getEquipList().find(equip => equip.used && equip.roleId == this.id && equip.index == index)
    }

    public getEquips() {
        if (this.equips) return this.equips
        return gameHelper.equip.getEquipList().filter(equip => equip.used && equip.roleId == this.id)
    }

    /**
     * 能不能升星，会判断低星碎片
     * @returns 
     */
    public isCanStarUp() {
        const starUpCfg = cfgHelper.getStarLvCfg(this.getStarLv())
        if (!starUpCfg) return false
        const needFrag = starUpCfg.upCost
        let fragId = cfgHelper.getFragByCharacterIdAndQuality(String(this.getID()), this.quality).id
        let hasFrag = gameHelper.passenger.getFragCountById(+fragId)
        if (needFrag <= hasFrag) return true
        // 计算低星碎片
        let mergeMap = {}
        for (let i = 1; i < this.quality; i++) {
            let fragId = cfgHelper.getFragByCharacterIdAndQuality(String(this.getID()), i).id
            let has = gameHelper.passenger.getFragCountById(+fragId)
            let pre = mergeMap[i] || 0
            has += pre
            if (has <= 0) continue
            const rate = cfgHelper.getMiscData('fragUp')[i - 1]
            if (has < rate) continue
            mergeMap[i + 1] = Math.floor(has / rate)
        }
        const mergeRealCnt = mergeMap[this.quality] || 0
        return mergeRealCnt + hasFrag >= needFrag
    }

    public getTrainDailyTask() {
        // let roles = [1031]
        // if (roles.includes(this.id)) {
        //     return {
        //         id: TrainDailyTaskType.PATROL,
        //         roles,
        //         trainId: 1016,
        //     }
        // }
        // roles = [1010]
        // if (roles.includes(this.id)) {
        //     return {
        //         id: TrainDailyTaskType.CLOTHES_CLEAN,
        //         roles,
        //         trainId: 1013,
        //     }
        // }
        return gameHelper.trainDailyTask.getList().find(t => t.inProcess() && t.roles.includes(this.id))
    }

    public onTrainDailyTaskStart() {
        this.updateLocation()
        this.actionAgent?.restart()
    }

    public onTrainDailyTaskDone() {
        this.updateLocation()
        this.actionAgent?.restart()
    }

    public getTrainBurstTask() {
        return gameHelper.getTrainBurstTask().find(t => t.roles.includes(this.id))
    }

    public onTrainBurstTaskStart() {
        this.updateLocation()
        this.actionAgent?.restart()
    }

    public onTrainBurstTaskDone() {
        this.updateLocation()
        this.actionAgent?.restart()
    }
}

