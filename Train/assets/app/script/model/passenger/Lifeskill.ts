import { CharacterLifeSkillCfg } from '../../common/constant/DataType';
import { SkillEffectiveType, SkillType } from '../../common/constant/Enums';
import { gameHelper } from '../../common/helper/GameHelper';
import Skill from './Skill';

export default class LifeSkill extends Skill {
    protected cfg: CharacterLifeSkillCfg

    public get objects() { return this.cfg?.object || [] }
    public get effects() { return this.cfg?.effect || [] }
    public get effectives() { return this.cfg?.effective || [] }

    public cd: number = 10
    public lastUseTime: number = 0

    public init(skillId: number, level: number = 1, role?: any) {
        this.lastUseTime = gameHelper.now()
        return super.init(skillId, level, role)
    }

    public getCfg() { return this.cfg }
    public getType() { return SkillType.LIFE }

    public canWorkInTrain(trainId: number) {
        for (let effective of this.effectives) {
            if (effective.type != SkillEffectiveType.WORKING) continue
            let ary = effective.id as number[]
            if (!ary) continue
            if (ary.some(id => id == trainId)) return true
        }
    }

    public canUse() {
        let passTime = gameHelper.now() -  this.lastUseTime
        return passTime >= this.cd * ut.Time.Second
    }

    public use() {
        this.lastUseTime = gameHelper.now()
    }
}