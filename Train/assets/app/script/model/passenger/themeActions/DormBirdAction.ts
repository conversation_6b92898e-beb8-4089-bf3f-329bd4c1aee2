import { BUILD_MOUNT_POINT, PassengerLifeAnimation } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"
import BuildObj from "../../train/common/BuildObj"
import DormBirdModel from "../../train/dorm/DormBirdModel"
import DormModel from "../../train/dorm/DormModel"
import ActionTree, { ActionNode } from "../ActionTree"
import { TimeStateData } from "../StateDataType"
import { StateType } from "../StateEnum"
import BaseAction from "./BaseAction"

enum TragetType {
    FLOOR, // 地面
    LEFT_CHAIR, // 左边树桩椅子
    RIGHT_CHAIR, // 右边树桩椅子
    RIGHT_BED, // 右边床的树杆
}

export default class DormBirdAction extends BaseAction {

    protected carriage: DormModel = null
    protected _model: DormBirdModel = null

    public setBy(v: DormBirdModel) {
        this._model = v
        this.actionTree = new ActionTree().init(this)
        this.carriage = v.carriage as DormModel
        this.actionTree.start(this.start)
        return this
    }

    update(dt: number) {
        if (!this._model) return
        this.actionTree.update(dt)
    }

    debug(...params) { console.log(`${this._model.skin} :`, ...params) }

    protected async onBeforeStart(action: ActionNode) {
        // 从外部随机飞入
        if (!this._model.flyIn) {
            await action.run(this.flyIn)
        }
    }

    protected async start(action: ActionNode) {
        this.debug('DormBirdAction start')
        await this.onBeforeStart(action)
        if (action.isOK()) return
        let cfgs = [
            // { act: this.flyIn, check: this.checkStandAct, weight: 25 },
        ]
        await this.runRandomAct(action, cfgs)
        await action.wait(ut.random(1, 3))
    }

    private async flyIn(action: ActionNode) {
        this.debug('flyIn', this._model.skin)
        // 随机飞到地面  or 其他设施上
        const target = [TragetType.FLOOR, TragetType.LEFT_CHAIR, TragetType.RIGHT_CHAIR, TragetType.RIGHT_BED].random()
        let build = null
        switch (target) {
            case TragetType.FLOOR:
                break
            case TragetType.LEFT_CHAIR:
                build = this.carriage.getLeftChair()
                break
            case TragetType.RIGHT_CHAIR:
                build = this.carriage.getRightChair()
                break
            case TragetType.RIGHT_BED:
                build = this.carriage.getRightBed()
                break
        }
        if (build) {
            await action.run(this.flyToBuild, { build })
        }
        else {
            await action.run(this.flyToAnyWhere)
        }
        action.ok()
    }

    private async flyToBuild(action: ActionNode) {
        let build: BuildObj = action.params.build
        this.debug('flyToBuild', build.id)

        await action.wait(5)
        action.ok()
    }

    private async flyToAnyWhere(action: ActionNode) {
        this.debug('flyToAnyWhere')

        await action.wait(5)
        action.ok()
    }

}