import { BUILD_MOUNT_POINT, BuildAttr, EntrustLifeType, PassengerLifeAnimation } from "../../../common/constant/Enums";
import { gameHelper } from "../../../common/helper/GameHelper";
import { MAX_CLOUD } from "../../train/water/WaterCloudObj";
import WaterModel from "../../train/water/WaterModel";
import { ActionNode } from "../ActionTree";
import { TimeStateData } from "../StateDataType";
import { StateType } from "../StateEnum";
import { ActionCfg, WATER_OUTPUT_COUNT } from "./ActionCfg";
import BaseAction from "./BaseAction";

export default class WaterAction extends BaseAction {
    protected carriage: WaterModel = null

    private genOutputTime: number = 0

    protected async start(action: ActionNode) {
        await this.onBeforeStart(action)
        if (action.isOK()) return

        if (this.checkMakeCloud()) {
            let cfgs = [
                { act: this.toTable, check: this.checkTable, weight: 1 }, //去实验台
                { act: this.toAir, check: this.checkAir, weight: 1 }, //去打气机
            ]
            await this.runRandomAct(action, cfgs)
        }
        else {
            if (this.checkAddCoal()) {
                await action.run(this.toAddCoal)
            }
            else if (this.checkGame()) {
                await action.run(this.toGame)
            }
            else {
                await action.run(this.toRandomPos)
            }
        }
        await action.wait(ut.random(2, 4))
    }

    private checkMakeCloud() {
        let clouds = this.carriage.clouds
        return clouds.length < MAX_CLOUD && (this.checkTable() || this.checkAir())
    }

    private checkTable() {
        return this.checkBuildPlay(this.carriage.getTable())
    }

    private checkAir() {
        return this.checkBuildPlay(this.carriage.getAir(), ["enter"])
    }

    private checkAddCoal() {
        return this.checkBuildPlay(this.carriage.getSauna(), ["add"])
    }

    private checkGame() {
        return this.checkBuildPlay(this.carriage.getGame()) && this.getUnlockOutput() > 0
    }

    private getUnlockOutput(isAvg: boolean = false) {
        let lockOutput = 0
        let count = 0
        for (let cloud of this.carriage.clouds) {
            if (cloud.lockOutput) {
                lockOutput += cloud.lockOutput 
                count++
            }
        }
        for (let role of this.carriage.getPassengers()) {
            let val = role.actionAgent?.lockOutput[BuildAttr.WATER]
            if (val) {
                lockOutput += val
                count++
            }
        }
        let val = this.carriage.waterOutputObj.getOutputByPlay() - lockOutput
        if (isAvg) {
            val = Math.floor(val / count)
        }
        return val
    }


    //去实验台
    protected async toTable(action) {
        let params = action.params || {}
        let build = this.carriage.getTable()
        let index = build.getUseIndexById("use")
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(this.useTable, Object.assign(params, { build }))
        action.onTerminate()
        action.ok()
    }

    protected async useTable(action) {
        this.debug("useTable")
        let { build } = action.params || {}
        build = build || this.carriage.getTable()
        let actionAgent = this.actionAgent
        let type = StateType.WATER_USE_TABLE
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        let anim = PassengerLifeAnimation.WATER_PULL
        let animInfo = actionAgent.getAnim(anim)
        let waitTime = 0
        if (animInfo) {
            let event = animInfo.events?.find(e => e.name == "effect")
            if (event) {
                waitTime = event.time
            }
        }
        else {
            anim = PassengerLifeAnimation.IDLE
        }
        let time = actionAgent.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { timeData, anim, loop: false })

        action.wait(waitTime).then(()=>{
            build.use()
        })

        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    //去打气机
    protected async toAir(action) {
        let build = this.carriage.getAir()
        let index = build.getUseIndexById("enter")
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(this.air)
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }


    //打气
    protected async air(action) {
        let build = this.carriage.getAir()
        let role = this.role
        build.onEnter(role)
        let actionAgent = this.actionAgent
        let type = StateType.WATER_AIR
        action.onTerminate = () => {
            build.onExit(role)
            actionAgent.popState(type)
        }
        let anim = PassengerLifeAnimation.WATER_AIR
        if (!actionAgent.getAnim(anim)) {
            anim = PassengerLifeAnimation.IDLE
        }

        let carriage = this.carriage as WaterModel
        let cloud = carriage.addCloud()
        cloud.startByAir()
        let pos = build.getUseById('birth').pos
        cloud.setPosition(pos)

        let time = cloud.getAnimTime('aniAir')
        let timeData = new TimeStateData().init(time)
        actionAgent.pushState(type, { anim, build, mountPoint: BUILD_MOUNT_POINT.USE })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    //去游戏机
    protected async toGame(action) {
        let params = action.params || {}
        let build = this.carriage.getGame()
        let index = 0
        build.setUseLock(true, index, this.role.id)
        this.resetOutputTime()
        this.actionAgent.lockOutput[BuildAttr.WATER] = this.getUnlockOutput()
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
            this.actionAgent.lockOutput[BuildAttr.WATER] = 0
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(this.playGame, Object.assign(params, { build }))
        await action.run(this.playGameCry)
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }

    //玩游戏
    protected async playGame(action) {
        let build = this.carriage.getGame()
        build.use()
        let actionAgent = this.actionAgent
        let type = StateType.WATER_GAME
        action.onTerminate = () => {
            build.reset()
            actionAgent.popState(type)
        }
        let anim = PassengerLifeAnimation.WATER_PLAY
        if (!actionAgent.getAnim(anim)) {
            anim = PassengerLifeAnimation.IDLE
        }
        let time = actionAgent.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        actionAgent.pushState(type, { anim, loop: false })
        await action.wait(timeData)
        actionAgent.popState(type)
        action.ok()
    }

    //玩游戏后哭哭
    protected async playGameCry(action) {
        let actionAgent = this.actionAgent
        let type = StateType.WATER_CRY
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        let anim = PassengerLifeAnimation.WATER_CRY
        if (!actionAgent.getAnim(anim)) {
            anim = PassengerLifeAnimation.IDLE
        }
        actionAgent.pushState(type, { anim })

        let time = this.getTimeByCfg(type, anim)
        if (!this.canOutput() && gameHelper.world.getNextHourSurplusWorldTime() < ut.Time.Minute) {
            await action.run(this.waitOutput)
        }
        if (this.canOutput()) {
            this.doOutput(time, type)
        }
        await action.wait(time)
 
        action.onTerminate()
        action.ok()
    }

    private doOutput(time, type, cntRange = WATER_OUTPUT_COUNT) {
        let output = this.actionAgent.lockOutput[BuildAttr.WATER] || 0
        output += this.getUnlockOutput(true)
        this.actionAgent.lockOutput[BuildAttr.WATER] = output
        let count = ut.random(Math.min(cntRange[0], output), Math.min(cntRange[1], output))
        this._doOutput(time / count, count, output, type)
        return true
    }

    private _doOutput(inteval, count, output, type) {
        let outputObj = this.carriage.waterOutputObj
        let pos = this.role.getPosition();
        (async()=> {
            let ary = ut.numAvgSplit(output, count)
            for (let val of ary) {
                await ut.wait(inteval)
                if (!this.actionAgent.getState(type)) return
                if (outputObj.getOutputByPlay() < val) return
                outputObj.genOutputByPlay(val)
                outputObj.addDropByPos(val, pos)
                this.actionAgent.lockOutput[BuildAttr.WATER] -= val
            }
        })()
    }

    //去桑拿机放煤
    protected async toAddCoal(action) {
        let params = action.params || {}
        let build = this.carriage.getSauna()
        let index = build.getUseIndexById("add")
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(this.addCoal, Object.assign(params, { build }))
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }

    protected async addCoal(action) {
        this.debug("addCoal")
        let build = this.carriage.getSauna()
        let actionAgent = this.actionAgent
        let type = StateType.WATER_ADD
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        let anim = PassengerLifeAnimation.WATER_ADD
        if (!actionAgent.getAnim(anim)) {
            anim = PassengerLifeAnimation.IDLE
        }
        let time = this.actionAgent.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        actionAgent.pushState(type, { anim, loop: false })
        await action.wait(timeData)
        build.add()
        action.onTerminate()
        action.ok()
    }

    private waitOutput(action: ActionNode) {
        if (this.canOutput()) {
            action.ok()
        }
    }

    private canOutput() {
        let intervalTime = ut.Time.Hour
        return Math.floor(gameHelper.world.getTime() / intervalTime) - Math.floor(this.genOutputTime / intervalTime) > 0
    }

    private resetOutputTime() {
        this.genOutputTime = gameHelper.world.getTime()
    }
}