
import BaseAction from "./BaseAction"
import { TrainHeadModel } from "../../train/TrainHeadModel"
import { ActionNode } from "../ActionTree"
import { ActionCfg, ActType, CARRIAGE_MAX_STAY_TIME } from "./ActionCfg"
import { StateType } from "../StateEnum"
import { TimeStateData } from "../StateDataType"
import { PassengerLifeAnimation, RoleGuideActionType } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"

export default class TrainHeadAction extends BaseAction {

    protected carriage: TrainHeadModel = null

    protected waitRecord = { wait: false, time: 0 }

    protected async onLoad(action) {
        await action.run(super.onLoad)
        if (this.actionAgent.guideType == RoleGuideActionType.TRIAN_HEAD_DRINK) {
            await action.run(this.toGuideDrink)
        }
        action.ok()
    }

    protected async handleReadyPlot(action: ActionNode) {
        let build = this.carriage.getChair()
        if (!build) {
            return action.ok()
        }
        let index = 0
        build.resumeBuild()

        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        let pos = build.getUsePos(index)
        this.role.setPosition(pos)

        let waitEnd = (action) => {
            if (!this.role.getReadyPlotId()) {
                action.ok()
            }
        }
        action.run(this.sit, {build, time: -1})
        await action.run(waitEnd)
        action.onTerminate()
        action.ok()
    }

    protected async start(action: ActionNode) {
        this.debug('start')

        if (this.role.getReadyPlotId()) {
            await action.run(this.handleReadyPlot)
        }

        await this.onBeforeStart(action)
        if (action.isOK()) return

        let cfgs = [
            { act: this.standPlayAct, check: this.checkStandAct, weight: 25 }, //站立表演
            { act: this.toRandomPos, weight: 25 }, //闲逛
            { act: this.toChairPlay, weight: 75, check: this.checkChairPlay }, //坐椅子
            { act: this.toCatchStar, weight: 25, check: this.checkCatchStar }, //捕星
            { act: this.toStargaze, weight: 25, check: this.checkStargaze }, //观星
        ]
        this.waitRecord.wait = true
        await this.runRandomAct(action, cfgs)
        await action.run(this.idle)
        this.waitRecord.wait = false

        await this.checkAndLeaveCarriage(action)
    }

    private checkChairPlay(isPlay?: boolean) {
        let index = this.getUseChairIndex(isPlay)
        return index >= 0
    }

    private checkFish() {
        return this.checkActUse([0]) && !!this.actionAgent.getAnim(PassengerLifeAnimation.FISH)
    }

    private checkStargaze() {
        return this.checkActUse([1]) && !!this.actionAgent.getAnim(PassengerLifeAnimation.TRAIN_HEAD_ACT)
    }

    private checkCatchStar() {
        return !!this.actionAgent.getAnim(PassengerLifeAnimation.CATCH_STAR)
    }

    private checkActUse(useIndexes) {
        return useIndexes.some(i => {
            if (!this.carriage.checkUsePos(i)) return false
            let record = this.actionAgent.useBuildRecords.find(record => record.build == null && (record.index == -1 || i == record.index))
            if (record) return false
            return true
        })
    }

    //-------------------- chair -----------------------------
    // 凳子交互
    protected async toChairPlay(action) {
        let cfgs = [
            { act: this.toChairSit, weight: 40 }, //坐着
            { act: this.toChairEat, check: this.checkSitEat, weight: 10 }, //吃东西
            { act: this.toChairDrink, check: this.checkSitDrink, weight: 10 }, //喝饮料
            { act: this.toChairPlayAct, check: this.checkSitAct, weight: 40 }, //弹吉他
        ]
        await this.runRandomAct(action, cfgs)
        action.ok()
    }

    // 凳子吃东西
    protected async toChairEat(action) {
        await this.toChairAct(action, this.sitEat)
    }

    // 凳子喝饮料
    protected async toChairDrink(action) {
        await this.toChairAct(action, this.sitDrink)
    }

    // 凳子坐着
    protected async toChairSit(action) {
        await this.toChairAct(action, this.sit)
    }

    // 坐在凳子表演
    protected async toChairPlayAct(action) {
        await this.toChairAct(action, this.sitPlayAct)
    }

    protected async toChairAct(action, act) {
        let params = action.params || {}

        let index = params.index
        if (index === undefined) {
            index = this.getUseChairIndex()
        }
        if (index > 0) {
            params.mountPoint = "sit2"
        }
        let build = this.carriage.getChair()
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(act, Object.assign(params, { build }))
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(build, -1)
        action.ok()
    }

    protected getUseChairIndex(isPlay = true) {
        let build = this.carriage.getChair()
        if (!build) return -1
        let indexes = [0, 1].filter(index => this.getUseBuild([build], isPlay, [index]))
        if (indexes.length <= 0) return -1
        let index = indexes.random()
        return index
    }
    //----------------------------------------------

    protected async toCatchStar(action) {
        let cfgs = [
            { act: this.toFish, check: this.checkFish, weight: 25 }, //钓星星
            { act: this.standPlayAct, check: this.checkCatchStar, weight: 25, params: { anim: PassengerLifeAnimation.CATCH_STAR } }, //捕星
        ]
        await this.runRandomAct(action, cfgs)
        action.ok()
    }

    protected async toFish(action) {
        let index = 0
        let pos = this.carriage.getUsePos(index)
        this.carriage.setUseLock(true, index)
        action.onTerminate = () => {
            this.carriage.setUseLock(false, index)
        }
        await action.run(this.move, pos)
        await action.run(this.fish)
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(null, index)
        action.ok()
    }

    protected async fish(action) {
        this.debug("fish")
        let { build, mountPoint } = action.params || {}
        let actionAgent = this.actionAgent
        let type = StateType.FISH
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        let time = this.actionAgent.getAnimsTime([PassengerLifeAnimation.FISH_START, PassengerLifeAnimation.FISH_END])
        let waitTime = this.getTimeByCfg(type, PassengerLifeAnimation.FISH)
        time += waitTime
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { build, timeData, mountPoint, waitTime })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    protected async toStargaze(action) {
        let index = 1
        let pos = this.carriage.getUsePos(index)
        this.carriage.setUseLock(true, index)
        action.onTerminate = () => {
            this.carriage.setUseLock(false, index)
        }
        await action.run(this.move, pos)
        await action.run(this.stargaze)
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(null, index)
        action.ok()
    }

    protected async stargaze(action) {
        this.debug("stargaze")
        let { time, build, anim, mountPoint } = action.params || {}
        anim = anim || PassengerLifeAnimation.TRAIN_HEAD_ACT
        let actionAgent = this.actionAgent
        let carriage = this.carriage
        carriage.stargazeStart()
        let type = StateType.STARGAZE
        action.onTerminate = () => {
            carriage.stargazeEnd()
            actionAgent.popState(type)
        }
        if (!time) {
            time = this.getActAnimTime(anim)
        }
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { build, timeData, anim, mountPoint })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    protected async toGuideDrink(action) {
        let build = this.carriage.getChair()
        if (!build) {
            return action.ok()
        }
        build.resumeBuild()

        let waitEnd = (action) => {
            if (!this.role.getReadyPlotId()) {
                action.ok()
            }
        }

        let act = async (action)=>{
            let delayTime = this.getSitDelayTime()
            action.run(this.sit, {build, time: -1})
            await action.wait(delayTime)
            action.run(this.drink, {drinkCount: 1, tasteCount: 1})
            await action.run(waitEnd)
            await action.wait(3)
            action.ok()
        }
        action.params = {index: 0}
        await this.toChairAct(action, act)
        action.ok()
    }

}