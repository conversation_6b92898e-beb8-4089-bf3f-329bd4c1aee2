import { PassengerLifeAnimation } from "../../../common/constant/Enums";
import { TimeStateData } from "../StateDataType";
import { ActionNode } from "../ActionTree";
import { StateType } from "../StateEnum";
import BaseAction from "./BaseAction";
import BathRoomModel from "../../train/bathRoom/BathRoomModel";
import BathRoomPoolObj from "../../train/bathRoom/BathRoomPoolObj";
import { CARRIAGE_MAX_STAY_TIME } from "./ActionCfg";

export default class BathAction extends BaseAction {
    protected carriage: BathRoomModel = null

    protected waitRecord = { over: false, wait: false, time: 0 }

    protected async start(action: ActionNode) {
        this.spawnMoveAction(action)
        await action.run(this.startBath)
    }
    protected async startBath(action: ActionNode) {
        await this.onBeforeStart(action)
        if (action.isOK()) return

        if (this.checkCanBath()) {
            await action.run(this.toBath)
        }
        else {
            let cfgs = [
                { act: this.standPlayAct, check: this.checkStandAct, weight: 25 }, //站立表演
                { act: this.toRandomPos, weight: 25 }, //闲逛
            ]
            this.waitRecord.wait = true
            await this.runRandomAct(action, cfgs)
            this.waitRecord.wait = false
        }

        this.waitRecord.wait = true
        await action.run(this.idle)
        this.waitRecord.wait = false

        await this.checkAndLeaveCarriage(action)
    }

    private checkCanBath() {
        if (!this.actionAgent.getAnim(PassengerLifeAnimation.ANI_BATH)) return false
        return this.carriage.canBath()
    }

    private async toBath(action: ActionNode) {
        let { build, index } = this.carriage.randomBath()
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        if (this.checkTea()) {
            await action.run(this.toTea)
        }
        await action.run(this.moveToBuild, { build, paths: this.carriage.getPoolPaths(build.type) })
        let paoIcon = this.actionAgent.getState(StateType.HOLD_FOOD)?.data?.paopao
        this.removeFood()
        this.waitRecord.over = true
        await action.run(this.takeBath, { build, index, paoIcon })
        action.onTerminate()
        action.ok()
    }
    private async takeBath(action: ActionNode) {
        let params = action.params
        let build = params.build as BathRoomPoolObj
        let index = params.index as number
        let paoIcon = params.paoIcon
        let type = StateType.TAKE_BATH
        action.onTerminate = () => {
            this.actionAgent.popState(type)
        }
        this.actionAgent.pushState(type, { buildId: build.id, index, paoIcon })
        let time = this.getTimeByCfg(type, PassengerLifeAnimation.ANI_BATH)
        let timeData = new TimeStateData().init(time)
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }
    private checkTea() {
        return this.checkBuildQueue(this.carriage.getFrog(), false)
    }
    private async toTea(action: ActionNode) {
        await action.run(this.toQueueTea)
        await this.toBuildCommon(action, this.carriage.getFrog(), this.tea1)
    }
    private async toQueueTea(action: ActionNode) {
        action.onTerminate = () => { this.carriage.queue.popQueueTea(this.role) }
        this.carriage.queue.pushQueueTea(this.role)
        await action.run(this.waitQueue, { typeQueue: StateType.FROG_QUEUE })
        action.onTerminate()
        action.ok()
    }
    private async waitQueue(action: ActionNode) {
        let { typeQueue } = action.params
        if (!action.onTerminate) {
            action.onTerminate = () => {
                if (this.actionAgent.getState(typeQueue)) {
                    this.actionAgent.popState(typeQueue)
                }
            }
        }
        let build = this.carriage.getFrog()
        let index = this.carriage.queue.getQueueIndex(this.role)
        if (index == 0 && build.isEmpty()) {
            let doPos = this.carriage.queue.getDoPos(build)
            let rolePos = this.role.getPosition()
            if (rolePos.equals(doPos)) {
                action.onTerminate()
                action.ok()
                return
            }
            await action.run(this.closeToBuild)
        } else {
            if (this.actionAgent.getState(typeQueue) && index > 0) {
                await action.wait(index)
            }
            await action.run(this.closeToBuild)
            if (!this.actionAgent.getState(typeQueue)) {
                this.actionAgent.pushState(typeQueue)
            }
        }
    }
    protected getCloseToPos() {
        return this.carriage.queue.getCloseTeaPos(this.role)
    }
    private async tea1(action: ActionNode) {
        let build = this.carriage.getFrog()
        let timeShow = build.getAnimEventTime('aniTake')
        let timeData = new TimeStateData().init(timeShow)
        action.onTerminate = () => { build.onTerminate() }
        build.onTake({ timeData, id: this.role.id })
        await action.wait(timeData)
        let timeTake = build.getAnimTime('aniTake') + 0.1
        let timeWait = timeTake - timeShow
        timeData.init(timeWait)
        build.onWait({ timeData })
        await action.wait(timeData)
        action.run(this.roleTakeTea)
        build.onOff()
        action.ok()
    }
    private async roleTakeTea(action: ActionNode) {
        let build = this.carriage.getFrog()
        this.addFood({
            slot: 'handsFood',
            food: build.getTeaUrl(),
            paopao: build.getPaopaoUrl(),
            animMove: PassengerLifeAnimation.WALK_WITH_HAND,
        })
    }
}
