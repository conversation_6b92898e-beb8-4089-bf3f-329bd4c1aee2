import { ActionNode } from "../ActionTree"
import { StateType } from "../StateEnum"
import { TimeStateData } from "../StateDataType"
import { PassengerLifeAnimation } from "../../../common/constant/Enums"
import BuildObj from "../../train/common/BuildObj"
import BaseAction from "./BaseAction"
import Dorm3Model from "../../train/dorm3/Dorm3Model"

export default class Dorm3Action extends BaseAction {
    protected carriage: Dorm3Model = null
    private cfgsBuildPlay = [
        { act: this.toWC, check: this.checkWC, weight: 10 }, //去上厕所
        { act: this.toChairPlay, check: this.checkChairPlay, weight: 10 }, //去凳子交互
    ]

    protected async start(action: ActionNode) {
        this.spawnMoveAction(action)
        await action.run(this.startDorm3)
    }
    protected async startDorm3(action: ActionNode) {
        await this.onBeforeStart(action)
        if (action.isOK()) return

        let cfgs = [
            { act: this.standPlayAct, check: this.checkStandAct, weight: 25 }, //站立表演
            { act: this.toRandomPos, weight: 25 }, //闲逛
            { act: this.toBuildPlay, weight: 75, check: this.checkPlay }, //和设施交互
        ]
        await this.runRandomAct(action, cfgs)
        if (this.isSpawnMoving()) {
            await action.run(this.waitSpawnMoveEnd)
        } else {
            await action.wait(ut.random(1, 3))
        }
    }
    private checkPlay() {
        for (const { check } of this.cfgsBuildPlay) {
            if (check.call(this)) {
                return true
            }
        }
        return false
    }
    private async toBuildPlay(action: ActionNode) {
        await this.runRandomAct(action, this.cfgsBuildPlay)
        action.ok()
    }
    private async crossToBuild(action: ActionNode) {
        this.startSpawnMove(this.getCloseToPos())
        await action.run(this.crossCheck)
        action.ok()
    }
    private crossCheck(action: ActionNode) {
        let pos = this.getMoveSpawnPos()
        if (pos.equals(this.getCloseToPos())) {
            if (this.carriage.queue.crossWCLine(this.role)) return action.ok()
        } else {
            this.stopSpawnMove()
            action.ok()
        }
    }
    protected getCloseToPos() {
        return this.carriage.queue.getCloseWCPos(this.role)
    }
    private async waitQueue(action: ActionNode) {
        let { typeQueue } = action.params
        if (!action.onTerminate) {
            action.onTerminate = () => {
                if (this.actionAgent.getState(typeQueue)) {
                    this.actionAgent.popState(typeQueue)
                }
            }
        }
        let build = this.carriage.getToilet()
        let index = this.carriage.queue.getQueueIndex(this.role)
        if (index == 0 && build.isEmpty()) {//直接去厕所
            if (this.carriage.queue.crossWCLine(this.role)) {
                action.onTerminate()
                action.ok()
                return
            }
            await action.run(this.crossToBuild)
        } else {//去排队
            if (this.actionAgent.getState(typeQueue) && index > 0) {
                await action.wait(index)
            }
            await action.run(this.closeToBuild)
            if (!this.actionAgent.getState(typeQueue)) {
                this.actionAgent.pushState(typeQueue)
            }
        }
    }
    private checkWC() {
        return this.checkBuildQueue(this.carriage.getToilet())
    }
    private async toWC(action: ActionNode) {
        await action.run(this.toQueueWC)
        await this.toBuildCommon(action, this.carriage.getToilet(), this.wc1)
    }
    private async toQueueWC(action: ActionNode) {
        action.onTerminate = () => { this.carriage.queue.popQueueWC(this.role) }
        this.carriage.queue.pushWCQueue(this.role)
        await action.run(this.waitQueue, { typeQueue: StateType.WC_QUEUE })
        action.onTerminate()
        action.ok()
    }
    private async wc1(action: ActionNode) {
        let build = this.carriage.getShed()
        let timeWC = build.random()
        let timeData = new TimeStateData().init(timeWC)
        build.onGuan({ timeData })
        action.onTerminate = () => { build.onTerminate() }
        await action.wait(timeData)
        let timeKai = build.getAnimTime('kai')
        timeData.init(timeKai)
        build.onKai({ timeData })
        this.roleMoveOutShed()
        await action.wait(timeData)
        build.onOff()
        action.ok()
    }
    private roleMoveOutShed() {
        let { start, end } = this.carriage.getOutPoint()
        this.role.setPosition(start)
        this.startSpawnMove(end)
    }
    //去睡觉
    protected async toSleep(action: ActionNode) {
        let bed1 = this.carriage.getBedCenter()
        let bed6 = this.carriage.getBedRight()
        let cfgs = []
        let anim = this.actionAgent.getAnim(PassengerLifeAnimation.SLEEP)
        if (anim) {
            if (bed1 && bed1.canUse()) {
                cfgs.push({ act: this.toBed1Sleep })
            }
            if (bed6 && bed6.canUse()) {
                cfgs.push({ act: this.toBed6Sleep })
            }
            if (cfgs.length <= 0) {
                let build = this.getUseChair(false)
                if (build) {
                    cfgs.push({ act: this.toChairSleep, params: { build } })
                }
            }
        }
        if (cfgs.length <= 0) {
            cfgs.push({ act: this.sleep, params: { anim: PassengerLifeAnimation.STAND_SLEEP } })
        }
        await this.runRandomAct(action, cfgs)
        action.ok()
    }
    protected async toBed1Sleep(action: ActionNode) {
        await this.toBuildCommon(action, this.carriage.getBedCenter(), this.sleep, [{ index: 0 }])
    }
    protected async toBed6Sleep(action: ActionNode) {
        await this.toBuildCommon(action, this.carriage.getBedRight(), this.sleep, [{ index: 0 }])
    }
    protected async toChairSleep(action: ActionNode) {
        let { build } = action.params
        await this.toBuildCommon(action, build, this.sitSleep, [{ index: 0 }])
    }
    private async toChairAct(action, act) {
        let { build } = action.params
        await this.toBuildCommon(action, build, act, [{ index: 0 }])
    }
    private getUseChair(isPlay = true) {
        return this.getUseBuild(this.carriage.getChairs(), isPlay)
    }
    private checkChairPlay() {
        return this.carriage.getChairs().some(b => this.checkBuildPlay(b))
    }
    private async toChairPlay(action) {
        let build = this.getUseChair()
        let cfgs = [
            { act: this.toChairSit, weight: 40 }, //坐着
            { act: this.toChairEat, check: this.checkSitEat, weight: 10 }, //吃东西
            { act: this.toChairDrink, check: this.checkSitDrink, weight: 10 }, //喝饮料
            { act: this.toChairPlayAct, check: this.checkSitAct, weight: 40 }, //弹吉他
        ]
        await this.runRandomAct(action, cfgs, { build })
        action.ok()
    }
    protected async toChairSit(action) {
        await this.toChairAct(action, this.sit)
    }
    protected async toChairEat(action) {
        await this.toChairAct(action, this.sitEat)
    }
    protected async toChairDrink(action) {
        await this.toChairAct(action, this.sitDrink)
    }
    protected async toChairPlayAct(action) {
        await this.toChairAct(action, this.sitPlayAct)
    }
}