import { ActionNode } from "../ActionTree"
import { PassengerLifeAnimation } from "../../../common/constant/Enums"
import BuildObj from "../../train/common/BuildObj"
import BaseAction from "./BaseAction"
import Dorm4Model from "../../train/dorm4/Dorm4Model"

const CanSitBedCenter: boolean = false

export default class Dorm4Action extends BaseAction {
    protected carriage: Dorm4Model = null

    protected async start(action: ActionNode) {
        await this.onBeforeStart(action)
        if (action.isOK()) return
        let cfgs = [
            { act: this.standPlayAct, check: this.checkStandAct, weight: 25 }, //站立表演
            { act: this.toRandomPos, weight: 25 }, //闲逛
            { act: this.toBuildPlay, weight: 75, check: this.checkPlay }, //和设施交互
        ]
        await this.runRandomAct(action, cfgs)
        await action.wait(ut.random(1, 3))
    }
    private checkPlay() {
        return this.checkBedPlay() || this.checkChairPlay()
    }
    private checkLeftBedPlay() {
        return this.checkBuildPlay(this.carriage.getBedLeft())
    }
    private checkRightBedPlay() {
        return CanSitBedCenter && this.checkBuildPlay(this.carriage.getBedCenter(), [0, 1])
    }
    private checkBedPlay() {
        return this.checkLeftBedPlay() || this.checkRightBedPlay()
    }
    private checkChairPlay() {
        return this.carriage.getChairs().some(b => this.checkBuildPlay(b))
    }
    private getUseBed(isPlay = true) {
        let arry = []
        let bed1 = this.carriage.getBedLeft()
        let bed4 = CanSitBedCenter ? this.carriage.getBedCenter() : null
        if (bed1) {
            arry.push({ build: bed1, index: 0, mountPoint: "sleep" })
        }
        if (bed4) {
            arry.push({ build: bed4, index: 0, mountPoint: "sleep" })
            arry.push({ build: bed4, index: 1, mountPoint: "sleep2" })
        }
        return this.randomUseBuild(arry, isPlay) as { build: BuildObj, index: number, mountPoint: string }
    }
    private getUseChair(isPlay = true) {
        return this.getUseBuild(this.carriage.getChairs(), isPlay)
    }
    protected async toCommon(action: ActionNode, build: BuildObj, call: Function, index: number) {
        let params = action.params || {}
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(call, Object.assign(params, { build }))
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }
    //去睡觉
    protected async toSleep(action: ActionNode) {
        let bed1 = this.carriage.getBedLeft()
        let bed4 = this.carriage.getBedCenter()
        let cfgs = []
        let anim = this.actionAgent.getAnim(PassengerLifeAnimation.SLEEP)
        if (anim) {
            if (bed1 && bed1.canUse()) {
                cfgs.push({ act: this.toBed1Sleep })
            }
            if (bed4 && (bed4.canUse(0) || bed4.canUse(1))) {
                cfgs.push({ act: this.toBed4Sleep })
            }
            if (cfgs.length <= 0) {
                let build = this.getUseChair(false)
                if (build) {
                    cfgs.push({ act: this.toChairSleep, params: { build } })
                }
            }
        }
        if (cfgs.length <= 0) {
            cfgs.push({ act: this.sleep, params: { anim: PassengerLifeAnimation.STAND_SLEEP } })
        }
        await this.runRandomAct(action, cfgs)
        action.ok()
    }
    protected async toBed1Sleep(action: ActionNode) {
        await this.toCommon(action, this.carriage.getBedLeft(), this.sleep, 0)
    }
    protected async toBed4Sleep(action: ActionNode) {
        let index = this.randomBed4Index()
        if (index > 0) {
            action.params = { mountPoint: "sleep2" }
        }
        await this.toCommon(action, this.carriage.getBedCenter(), this.sleep, index)
    }
    private randomBed4Index() {
        let ary: number[] = []
        let build = this.carriage.getBedCenter()
        if (build.isEmpty(0))
            ary.push(0)
        if (build.isEmpty(1))
            ary.push(1)
        return ary.random()
    }
    protected async toChairSleep(action: ActionNode) {
        let { build } = action.params
        await this.toCommon(action, build, this.sitSleep, 0)
    }
    //去和设施交互
    private async toBuildPlay(action: ActionNode) {
        let cfgs = [
            { act: this.toBedPlay, check: this.checkBedPlay, weight: 10 }, //去床交互
            { act: this.toChairPlay, check: this.checkChairPlay, weight: 10 }, //去凳子交互
        ]
        await this.runRandomAct(action, cfgs)
        action.ok()
    }
    private async toBedPlay(action: ActionNode) {
        let data = this.getUseBed()
        let cfgs = [
            { act: this.toBedSit, weight: 40 }, //坐着
            { act: this.toBedPlayAct, check: this.checkSitAct, weight: 60 }, //弹吉他
        ]
        await this.runRandomAct(action, cfgs, data)
        action.ok()
    }
    private async toBedSit(action: ActionNode) {
        await this.toBedAct(action, this.sit)
    }
    private async toBedPlayAct(action: ActionNode) {
        await this.toBedAct(action, this.sitPlayAct)
    }
    private async toBedAct(action: ActionNode, act: Function) {
        let { build, index } = action.params
        await this.toCommon(action, build, act, index)
    }
    private async toChairPlay(action: ActionNode) {
        let build = this.getUseChair()
        let cfgs = [
            { act: this.toChairSit, weight: 40 }, //坐着
            { act: this.toChairEat, check: this.checkSitEat, weight: 10 }, //吃东西
            { act: this.toChairDrink, check: this.checkSitDrink, weight: 10 }, //喝饮料
            { act: this.toChairPlayAct, check: this.checkSitAct, weight: 40 }, //弹吉他
        ]
        await this.runRandomAct(action, cfgs, { build })
        action.ok()
    }
    protected async toChairSit(action: ActionNode) {
        await this.toChairAct(action, this.sit)
    }
    protected async toChairEat(action: ActionNode) {
        await this.toChairAct(action, this.sitEat)
    }
    protected async toChairDrink(action: ActionNode) {
        await this.toChairAct(action, this.sitDrink)
    }
    protected async toChairPlayAct(action: ActionNode) {
        await this.toChairAct(action, this.sitPlayAct)
    }
    private async toChairAct(action: ActionNode, act: Function) {
        let { build } = action.params
        await this.toCommon(action, build, act, 0)
    }
}
