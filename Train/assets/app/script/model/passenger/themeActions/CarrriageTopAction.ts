
import BaseAction from "./BaseAction"
import { TrainHeadModel } from "../../train/TrainHeadModel"
import { ActionNode } from "../ActionTree"
import { ActionCfg, ActType, CARRIAGE_MAX_STAY_TIME } from "./ActionCfg"
import { StateType } from "../StateEnum"
import { TimeStateData } from "../StateDataType"
import { GuideModule, PassengerLifeAnimation, RoleGuideActionType } from "../../../common/constant/Enums"
import CarriageModel from "../../train/common/CarriageModel"
import { gameHelper } from "../../../common/helper/GameHelper"

export default class CarrriageTopAction extends BaseAction {

    protected carriage: CarriageModel = null

    private leave: boolean = false

    protected async start(action: ActionNode) {
        await this.onBeforeStart(action)
        if (action.isOK()) return

        if (this.checkSleep()) {
            await action.run(this.toSleep)
        }

        let cfgs = [
            { act: this.standPlayAct, check: this.checkStandAct, weight: 25 }, //站立表演
            { act: this.toRandomPos, weight: 25 }, //闲逛
            { act: this.toChangeCarriage, weight: 5 }, //闲逛
        ]
        await this.runRandomAct(action, cfgs)
        if (this.leave) {
            return action.ok()
        }
        await action.run(this.idle)
    }

    protected getUseCarriage() {
        let carriages: CarriageModel[]
        if (false && gameHelper.guide.getGuideInfo(GuideModule.TRIAN_HEAD).isFinish()) {
            carriages = gameHelper.train.getAllCarriages()
        }
        else {
            carriages = gameHelper.train.getCarriages()
        }
        carriages = carriages.filter((carriage: CarriageModel) => {
            if (carriage == this.role.carriage) return false
            if (!carriage.overBuilt) return false //还没建造完成的不去
            return true
        })
        if (carriages.length <= 0) return

        let weights = carriages.map(c => Math.max(100 - c.getTopPassengers().length * 10, 1))
        let index = gameHelper.randomByWeight(weights)
        return carriages[index]
    }

    protected async moveToCarriage(action: ActionNode) {
        let carriage: CarriageModel = action.params
        if (!carriage) {
            twlog.error("moveToCarriage no carriage", this.role.getID())
            return action.ok()
        }
        if (carriage == this.carriage) {
            twlog.error("moveToCarriage same", this.role.id, carriage.getID())
            return action.ok()
        }
        this.debug('moveToCarriage', `${this.carriage.getID()} -> ${carriage.getID()}`)
        let orgCarriage = this.carriage
        this.actionAgent.targetCarriage = carriage

        action.onTerminate = () => {
            this.actionAgent.targetCarriage = null
            this.actionAgent.fromCarriage = null
            this.actionAgent.nextCarriage = null
        }

        let nextCarriage = carriage
        this.actionAgent.fromCarriage = orgCarriage
        this.actionAgent.nextCarriage = nextCarriage

        await action.run(this.exitCarriage, nextCarriage)

        action.ok()

        this.carriage.roleExitTop(this.role)
        nextCarriage.roleEnterTop(this.role)

        this.leave = true

        action.ok()
    }

    protected async exitCarriage(action: ActionNode) {
        let target: CarriageModel = action.params
        this.debug('exitCarriage', this.carriage.getID())
        let curIndex = this.carriage.getIndex()
        let targetIndex = target.getIndex()
        let isLeft = targetIndex > curIndex

        let map = this.role.getMap()
        let pos = map.getActPointByPixel(this.role.getPosition())
        pos = map.getActPixelByPoint(map.getTransPoint(isLeft))
        await action.run(this.move, pos)

        action.ok()
    }

    protected async enterCarriage(action: ActionNode) {
        let fromCarriage = this.actionAgent.fromCarriage
        this.debug('enterCarriage', this.carriage.getID())
        let fromIndex = fromCarriage.getIndex()
        let curIndex = this.carriage.getIndex()
        let isLeft = curIndex > fromIndex
        let map = this.role.getMap()
        let pos = map.getActPixelByPoint(map.getTransPoint(!isLeft))
        this.role.setPointAndPosition(null, pos)

        action.ok()
    }
}