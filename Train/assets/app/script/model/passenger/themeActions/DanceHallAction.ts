import { CarriageUsePosType, PassengerLifeAnimation, RoleDir } from "../../../common/constant/Enums";
import { TimeStateData } from "../StateDataType";
import { ActionNode } from "../ActionTree";
import { StateType } from "../StateEnum";
import BaseAction from "./BaseAction";
import DanceHallModel from "../../train/danceHall/DanceHallModel";
import { CARRIAGE_MAX_STAY_TIME } from "./ActionCfg";

export default class DanceHallAction extends BaseAction {
    protected carriage: DanceHallModel = null
A
protected waitRecord = { over: false, wait: false, time: 0 }

    protected async start(action: ActionNode) {
        await this.onBeforeStart(action)
        if (action.isOK()) return

        if (this.checkCanDance()) {
            await action.run(this.toDance)
        }
        else {
            this.waitRecord.wait = true
            await action.run(this.toRandomPos)
            this.waitRecord.wait = false
        }

        this.waitRecord.wait = true
        await action.run(this.idle)
        this.waitRecord.wait = false

        await this.checkAndLeaveCarriage(action)
    }

    private checkCanDance() {
        if (!this.actionAgent.getAnim(PassengerLifeAnimation.ANI_DANCE)) return false
        return this.haveDancePos()
    }
    private async toDance(action: ActionNode) {
        let type = StateType.DANCING
        let info = this.randomDancePos()
        let dir = info.dir
        action.onTerminate = () => {
            this.carriage.setUseLock(false, info.index)
            this.actionAgent.popState(type)
        }
        this.carriage.setUseLock(true, info.index)
        await action.run(this.move, info.pos)
        this.waitRecord.over = true
        let time = this.getTimeByCfg(type, PassengerLifeAnimation.ANI_DANCE)
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { dir })
        await action.wait(timeData)
        action.onTerminate()
        await action.run(this.toDirEmpty, dir)
        action.ok()
    }

    private haveDancePos() {
        let ary = this.carriage.getUsePosListByType(CarriageUsePosType.DANCING)
        return ary.some(m => this.carriage.checkUsePos(m.index))
    }
    private randomDancePos() {
        let ary = this.carriage.getUsePosListByType(CarriageUsePosType.DANCING)
        return ary.filter(m => this.carriage.checkUsePos(m.index)).random()
    }
    private async toDirEmpty(action: ActionNode) {
        let dir = action.params as RoleDir
        let ary = this.carriage.getEmptyAreas()
        let idx = dir || RoleDir.NONE
        let area = ary[idx]
        let pos = this.role.getRandomMovePos([area])
        let succ = await action.run(this.move, pos)
        if (succ) {
            action.ok()
        }
    }
}
