import { BaseSkillCfg, CharacterCfg, EntrustLifeCfg, TrainCfg } from "../../common/constant/DataType";
import { SkillType } from "../../common/constant/Enums";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";

export default abstract class Skill {
    protected level: number = 0
    protected id: number
    protected cfg: BaseSkillCfg
    protected role: any = null

    public init(id: number, level: number = 1, role?: number) {
        this.id = id;
        this.role = role
        this.updateLevel(level)
        return this
    }

    public get icon() { return this.cfg?.icon }
    public get name() { return this.cfg?.name }
    public getId() { return this.id }
    public getLevel() { return this.level }
    public getNextLv() {
        return this.level + cfgHelper.getNextSkillLv(this.role.id, this.role.getLevel(), this.id) - cfgHelper.getSkillLv(this.role.id, this.role.getLevel(), this.id)
    }
    public getPreLv() {
        let base = cfgHelper.getSkillLv(this.role.id, this.role.getLevel(), this.id)
        if (base <= 0) return 0
        return this.level + cfgHelper.getPreSkillLv(this.role.id, this.role.getLevel(), this.id) - base
    }
    public getRole() {
        return this.role
    }
    public setRole(r) {
        return this.role = r
    }

    public initCfg() {
        this.cfg = Skill.getJsonData(this.getType(), this.id, this.level)
    }

    public levelUp(val: number = 1) {
        this.level += val;
        this.initCfg()
    }

    public updateLevel(level) {
        this.level = level
        this.initCfg()
    }

    public static getJsonData(type: SkillType, id: number, lv: number) {
        let jsonName = Skill.getJsonName(type)
        if (!jsonName) return
        return assetsMgr.getJsonData<BaseSkillCfg>(jsonName, `${id}-${lv}`)
    }

    public static getJsonName(type: SkillType) {
        switch (type) {
            case SkillType.BATTLE:
                return "BattleSkill"
            case SkillType.LIFE:
                return "GrowSkill"
            default:
                cc.error("skill json error:", type)
        }
    }

    public abstract getType(): SkillType

    public getDesc(...args): { key: string, params: any[] } {
        let cfg = this.cfg;
        if (!cfg) return
        let params = this.getEscParams()
        return { key: cfg.content, params }
    }

    public getEscParams() {
        let cfg = this.cfg;
        if (!cfg) return
        let escs = cfg.ESC
        return escs.map(({ type, value }) => {
            let name: string
            if (type == "CHARACTER_NAME") {
                if (value == 0) {
                    value = this.role.id
                }
                name = assetsMgr.getJsonData<CharacterCfg>("Character", value)?.name
            }
            else if (type == "TRAIN_TITLE") {
                name = assetsMgr.getJsonData<TrainCfg>("Train", value)?.title
            }
            else if (type == "TRAIN_NAME") {
                name = assetsMgr.getJsonData<TrainCfg>("Train", value)?.name
            }
            else if (type == "ENTRUST_LIFE") {
                name = assetsMgr.getJson<EntrustLifeCfg>("EntrustLife").datas.find(d => d.type == value)?.name
            }
            if (name) {
                return assetsMgr.lang(name)
            }
            return value
        })
    }

    public getAddValue() {
        let cfg = this.cfg;
        if (!cfg) return
        let ary = cfg.ESC
        return ary[ary.length - 1].value
    }
}