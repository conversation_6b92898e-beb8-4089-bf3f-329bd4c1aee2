import { Msg } from "../../../proto/msg-define";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import BattleRole from "../battle/BattleRole";
import Monster from "../battle/Monster";
import ConditionObj from "../common/ConditionObj";


const { ccclass, property } = cc._decorator;

//未来可能需要一个pos来定怪物放在哪
export class Arrest {
    public id: string
    private star: number = 0
    private planetId: number = 0
    private rewards: ConditionObj[] = []
    private monsters: Monster[] = []
    private state: proto.ArrestState
    private clues: proto.IArrestClue[] = []
    private criminalId: number = null
    private bg: string = 'planet_wanted_0_0'
    private monsterId: number = 0
    private timeType: proto.ArrestTimeType = proto.ArrestTimeType.ALL

    public getStar() { return this.star }
    public getPlanetId() { return this.planetId }
    public getRewards() { return this.rewards }
    public getMonsters() { return this.monsters }
    public getState() { return this.state }
    public getIconBag() { return `monster_wanted_big_${this.monsterId}` }
    public getIconBig() { return !!this.monsterId && `monster_${this.monsterId}_icon_big` }
    public getBg() { return this.bg }
    public getCriminal() { return cfgHelper.getArrestContent(this.criminalId) }
    public getClues() { return this.clues }
    public getTimeType() { return this.timeType }

    public get level() { return cfgHelper.getMiscData('arrest')?.level }
    public get sortId() { return cfgHelper.getMiscData('arrest')?.sortId }
    public get isShow() { return true }


    public init(data: proto.IArrest) {
        this.monsters.splice(0, this.monsters.length)
        this.clues.splice(0, this.clues.length)
        this.id = data.id
        this.star = data.star
        this.planetId = data.planetId
        this.rewards = gameHelper.toConditions(data.rewards)
        data.monsters.forEach(m => this.monsters.push(new Monster().init(m.id, m.lv, m.starLv)))
        this.state = data.state
        this.criminalId = data.storyId
        this.setClues(data.clues)
        return this
    }

    private setClues(clues: proto.IArrestClue[]) {
        clues.forEach(t => {
            if (t.type == proto.ArrestClueType.PLACE) {
                if (t.placeType == proto.ArrestPlaceType.PIC) {
                    this.bg = `planet_wanted_${this.planetId}_${t.isHideInfo ? 2 : 1}`
                    this.monsterId = this.monsters[0]?.id
                } else if (t.placeType == proto.ArrestPlaceType.TEXT) {
                    t.planets.push(this.planetId)
                }
            }
            if (t.type == proto.ArrestClueType.TIME) {
                this.timeType = t.timeType
            }
            if (t.type != proto.ArrestClueType.TIME || t.timeType != proto.ArrestTimeType.ALL) {
                this.clues.push(t)
            }
        })
    }

    public async acceptArrests() {
        let msg = new proto.C2S_AcceptArrestMessage({ id: this.id })
        const res = await gameHelper.net.request(Msg.C2S_AcceptArrestMessage, msg, true)
        const { code } = proto.S2C_AcceptArrestMessage.decode(res)
        if (code == 0) {
            if (this.state == proto.ArrestState.NotCollected) {
                this.state = proto.ArrestState.OnGoing
            }
            eventCenter.emit(EventType.ARREST_GET)
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    public async setArrestBattleResult(battleRes) {
        let uids: string[] = []
        let isWin = battleRes.isWin
        if (isWin) {
            uids = battleRes.roles.map(t => {
                return t.uid
            })
        }
        else {
            uids = battleRes.monsters.map(t => {
                return t.uid
            })
        }
        let msg = new proto.C2S_SetArrestBattleResultMessage({ id: this.id, result: { isWin, uids } })
        const res = await gameHelper.net.request(Msg.C2S_SetArrestBattleResultMessage, msg, true)
        const { code, data } = proto.S2C_SetArrestBattleResultMessage.decode(res)
        if (code == 0) {
            if (battleRes.isWin) {
                this.state = proto.ArrestState.DoneNoReward
                this.updateBattle(true, false)
            } else if (!data) {
                //没逃
                this.updateBattle(false, false)
            } else {
                //逃了
                this.init(data)
                this.updateBattle(false, true)
            }
            return true
        }
        viewHelper.showNetError(code)
        return false

    }


    public updateBattle(isWin: boolean, isRun: boolean) {
        let failTip = (!isWin && isRun) ? 'arrest_guiTest_11' : null
        let winTip = isWin ? 'arrest_guiTest_12' : null
        viewHelper.showPnl('battle/BattleResult', { isWin, failTip, winTip, isArrest: true })
    }

    public async finishReward() {
        let msg = new proto.C2S_FinishArrestMessage({ id: this.id })
        const res = await gameHelper.net.request(Msg.C2S_FinishArrestMessage, msg, true)
        const { code } = proto.S2C_FinishArrestMessage.decode(res)
        if (code == 0) {
            this.state = proto.ArrestState.Finished
            gameHelper.grantRewardAndShowUI(this.rewards)
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    public expired() {
        this.state = proto.ArrestState.Expired
    }

}

@mc.addmodel('arrest')
export default class ArrestModel extends mc.BaseModel {
    public data: proto.IArrestModule
    private arrestList: Arrest[] = []
    private score: number = 0
    private result: proto.IArrestResult = null
    private currency: number = 0

    public getArrestList() { return this.arrestList }
    public getArrestListInView() { return this.arrestList.filter(e => e.getState() != proto.ArrestState.Expired) }
    public getArrestListInBag() { return this.arrestList.filter(e => e.getState() != proto.ArrestState.Finished && e.getState() != proto.ArrestState.NotCollected) }
    public getArrestById(id: string) {
        return this.arrestList.find(e => e.id == id)
    }
    public getArrestByPlanetId(planetId: number) {
        return this.arrestList.find(e => e.getState() == proto.ArrestState.OnGoing && e.getPlanetId() == planetId)
    }
    public getScore() { return this.score }
    public getResult() { return this.result }
    public getNeedShowResult() { return this.result != null }

    public init(data?: proto.IArrestModule) {
        data = data || this.data
        this.arrestList = data?.arrests.map(m => new Arrest().init(m))
        this.score = data.score
        this.result = data.result
        this.currency = data.currency || 0
    }

    public async acceptArrests(id: string) {
        let arrest = this.getArrestById(id)
        return arrest.acceptArrests()
    }

    public async refreshArrest() {
        let msg = new proto.C2S_RefreshAllArrestMessage()
        const res = await gameHelper.net.request(Msg.C2S_RefreshAllArrestMessage, msg, true)
        const { data } = proto.S2C_RefreshAllArrestMessage.decode(res)
        let code = 0
        if (code == 0) {
            this.arrestList = data.map(m => new Arrest().init(m))
            eventCenter.emit(EventType.ARREST_REFRESH)
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    public async destroyArrest(id: string) {
        let msg = new proto.C2S_DestroyArrestMessage({ id })
        const res = await gameHelper.net.request(Msg.C2S_DestroyArrestMessage, msg, true)
        const { code } = proto.S2C_DestroyArrestMessage.decode(res)
        if (code == 0) {
            this.destroy(id)
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    public async showArrestResult() {
        let { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_ShowArrestResultMessage)
        if (code == 0) {
            this.result = null
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    private destroy(id: string) {
        let index = this.arrestList.findIndex(e => e.id == id)
        this.arrestList.splice(index, 1)
    }

    public async getReward(id: string) {
        let arrest = this.arrestList.find(e => e.id == id)
        return await arrest.finishReward()
    }

    public getNextSurplusTime() {
        return gameHelper.world.getNextDaySurpluTime()
    }

    public changeCurrency(val: number) {
        this.currency += val
        eventCenter.emit(EventType.UPDATE_CURRENCY)
    }

    public getCurrency() {
        return this.currency
    }
}
