import { ANIMAL_TYPE_ICON, BATTLE_TYPE_ICON } from "../../common/constant/Constant"
import { BlackHoleEquipLevelCfg } from "../../common/constant/DataType"
import { BlackHoleEquipTarget, PassengerAttr, ROLE_ATTR_ID, RoleAnimalType, SkillType } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { BattleEffectValueType, BattleSkillEffectType, BattleSkillObjectType, BattleSkillTriggerType, SkillEffectPerAttrType, SkillEffectPerObjType } from "../battle/BattleEnum"
import BattleSkill, { BattleSkillEffect, BattleSkillObject, BattleSkillTrigger } from "../battle/BattleSkill"

export default class BlackHoleEquip {
    public id: number = 0
    public target: number = 0
    public level: number = 0
    public cfg = null
    public skill: BlackHoleEquipSkill = null

    public init(data: proto.IBlackHoleEquip) {
        this.id = data.id
        this.target = data.target
        this.level = data.level
   
        this.cfg = assetsMgr.getJsonData<any>("BlackHoleEquip", this.id)
        let attrRate = gameHelper.blackHole.add
        if (this.cfg.trigger.type == BattleSkillTriggerType.NONE) {
            if (this.cfg.effect.find(e => e.type == BattleSkillEffectType.CHANGE_SKILL)) {
                attrRate /= gameHelper.blackHole.getAvgStarRate()
            }
        }

        this.skill = new BlackHoleEquipSkill()
        this.skill.attrRate = attrRate
        this.skill.init(this.id, this.level, null, this.target)
        return this
    }

    public get icon() { return this.cfg?.icon }
    public get name() { return `name_blackHoleItem_${this.id}` }
    public get sender() { return this.cfg.sender }
    public getDescStr() {
        return this.skill.getDescStr()
    }
}

export class BlackHoleEquipSkill extends BattleSkill{

    public target: number = 0

    public getType() { return SkillType.BLACKHOLE_EQUIP }
    public init(id: number, lv: number = 1, role?: any, target?: number) {
        this.target = target
        super.init(id, lv, role);
        return this
    }

    protected getInitParams() {
        return [this.target]
    }

    public initCfg() {
        let cfg:any = assetsMgr.getJsonData("BlackHoleEquip", this.getId())
        this.cfg = cfg
        this._initCfg()
    }

    protected initViewCfg() {
        if (this.viewCfg) return
        this.viewCfg = assetsMgr.checkJsonData("BlackHoleEquip", this.getId())
    }

    protected initEffect() {
        let { trigger, object, effect } = this.cfg
        if (trigger.object == BattleSkillObjectType.ANIMAL_TYPE) {
            trigger = Object.assign({}, trigger)
            trigger.count = this.target
        }
        if (object) {
            object = object.map(d => {
                if (d.type == BattleSkillObjectType.ANIMAL_TYPE) {
                    d = Object.assign({}, d)
                    d.count = this.target
                }
                return d
            })
        }
     
        this._initCfg()
        return this.initData(trigger, object, effect)
    }

    protected getAttrParams() {
        return {roleId: ROLE_ATTR_ID.BLACK_HOLE, roleLv: this.level, roleStarLv: 0}
    }

    public getTriggerDesc() {
        return ""
        if (!this.viewCfg) return ""
        return assetsMgr.lang(this.viewCfg.triggerContent)
    }

    protected getContentValue() {
        let cfg = this.viewCfg
        let contentValue = this.contentValue.slice()
        if (!cfg) return contentValue
        let img
        if (cfg.target == BlackHoleEquipTarget.ANIMAL_TYPE) {
            img = ANIMAL_TYPE_ICON[this.target]
        }
        else if (cfg.target == BlackHoleEquipTarget.BATTLE_TYPE) {
            img = BATTLE_TYPE_ICON[this.target]
        }
        if (img) {
            contentValue.unshift(`<img src='${img}'/>`)
        }
        return contentValue
    }

    public clone() {
        let skill = super.clone(this.target)
        return skill
    }

    public isSame(skill: any): boolean {
        if (!super.isSame(skill)) return    
        return this.target == skill.target
    }

    public getHash() {
        return `${super.getHash()}-${this.target}`
    }

}