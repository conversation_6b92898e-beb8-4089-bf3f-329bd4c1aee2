import { Msg } from "../../../proto/msg-define";
import { ConditionType, EnergyRecoverType, ItemID } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../common/ConditionObj";

export default class EnergyModel {
    public hasSpeedUp: boolean = false

    private cfg: any = null

    private energy: number = 0 //游戏实时值，本质是毫秒
    public costRecoverNum: number = 0 //花费剩余恢复次数
    public freeRecoverNum: number = 0 //免费剩余恢复次数
    public isSpeedUp: boolean = false
    public reqSpeedUp: number = 0

    get recoverSpeed() { return this.cfg?.recoverSpeed || 0 } //x秒回1点体力
    get max() {
        let base = 0
        if (this.cfg) {
            base = this.cfg.max
        }
        return base * ut.Time.Second
    }

    public init(data: any = {}) {
        this.cfg = cfgHelper.getMiscData('speedUp')
        this.updateInfo(data)
        return this
    }

    public updateInfo(data: proto.IEnergy) {
        this.energy = data.energy
        this.costRecoverNum = data.costRecoverNum
        this.freeRecoverNum = data.freeRecoverNum
        this.hasSpeedUp = data.used
        this.isSpeedUp = data.isSpeedUp
    }

    public addEnergy(val: number) {
        this.energy = cc.misc.clampf(0, this.max, this.energy + val)
    }

    public setEnergy(val: number) {
        this.energy = val
    }

    public getEnergy() {
        return this.energy
    }

    public getTotalRecoverCnt() {
        return this.cfg.recoverFree + this.cfg.recoverBuy
    }

    public getRecoverCost(costRecoverNum: number = this.costRecoverNum) {
        let data = this.cfg
        let buyTimes: number[] = data.buyTime
        let buyPrices: number[] = data.buyPrice
        let cost = buyPrices.last()
        let recoverTimes = data.recoverBuy - costRecoverNum
        for (let i = 0; i < buyTimes.length; i++) {
            let times = buyTimes[i]
            if (recoverTimes < times) {
                cost = buyPrices[i]
                break
            }
        }
        return cost
    }

    public async recoverEnergy(type: EnergyRecoverType) {
        let msg = new proto.C2S_RecoverEnergyMessage({ type })
        let costRecoverNum = this.costRecoverNum
        let res = await gameHelper.net.request(Msg.C2S_RecoverEnergyMessage, msg, true)
        let { code, energy } = proto.S2C_RecoverEnergyRespMessage.decode(res)
        if (code == 0) {
            this.updateInfo(energy)

            let cost = this.getRecoverCost(costRecoverNum)
            let cond
            if (type == EnergyRecoverType.ENERGY) {
                cond = new ConditionObj().init(ConditionType.PROP, ItemID.ENERGY, cost)
            }
            else if (type == EnergyRecoverType.DIAMOND) {
                cond = new ConditionObj().init(ConditionType.DIAMOND, -1, cost * cfgHelper.getMiscData("speedUp").recoverEnergy)
            } else if (type == EnergyRecoverType.AD) {
                cond = new ConditionObj().init(ConditionType.AD, proto.AdType.RecoveryTrainEnergy, 1)
            }
            if (cond) {
                gameHelper.deductCondition(cond)
            }
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    update(dt) {
        if (gameHelper.world.isSpeedUp()) {
            this.addEnergy(-dt * ut.Time.Second)
        }
    }

    public checkRedDot() {
        let res: boolean = false
        res = this.energy <= 0 && this.freeRecoverNum > 0
        return res
    }

    // 解锁自动加速
    public unlockAuto() {
        this.addEnergy((this.cfg.autoMax - this.cfg.max) * ut.Time.Second)
        eventCenter.emit(EventType.ENERGY_UNLOCK_AUTO)
    }

}
