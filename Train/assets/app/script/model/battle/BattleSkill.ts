import { BattleSkillCfg, BattleSkillEffectBuffCfg, BattleSkillEffectCfg, BattleSkillTriggerCfg, BattleSkillViewCfg } from "../../common/constant/DataType"
import { EquipEffectType, PassengerAttr, SkillType, ValueType } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import Skill from '../passenger/Skill'
import { BattleEffectValueType, BattleSkillCampType, BattleSkillEffectType, BattleSkillObjectType, BattleSkillTriggerType, SkillEffectPerAttrType, SkillEffectPerObjType, SkillEffectRateType } from "./BattleEnum"
import BattleRole from "./BattleRole"
import Monster from "./Monster"

class BattleSkillEffectBuff {
    public repeat: number = 0 //可叠加次数
    public dep: BattleSkillEffectType = null //依赖buff，依赖消失时自己消失
    public times: number = -1 // 生效次数
    public activeObject: BattleSkillObjectType
    public rounds: number = -1 //生效回合数
    public delayRounds: number = 0

    private cfg: BattleSkillEffectBuffCfg = null

    public init(cfg) {
        this.cfg = cfg
        this.repeat = cfg.repeat || 0
        this.dep = cfg.dep
        this.times = cfg.times || -1
        this.rounds = cfg.rounds || -1
        this.activeObject = cfg.activeObject
        this.delayRounds = cfg.delayRounds
        return this
    }
}

export class BattleSkillEffect {
    public type: BattleSkillEffectType
    public times: number //使用次数
    public rounds: number = -1 //生效x回合
    public roleTimes: number //同一个角色生效几次
    public roundTimes: number = -1 //每回合生效次数
    public buff: BattleSkillEffectBuff = null
    public valueType: BattleEffectValueType
    public value: number
    private orgValue: number
    public perObjType: SkillEffectPerObjType = null //按百分比计算伤害是基于的对象
    public perAttrType: SkillEffectPerAttrType = null //按百分比计算伤害是基于的属性
    public skill: BattleSkill
    public rateType: SkillEffectRateType
    public simpleContent: number = 0
    public objectIndex = 0
    public buffSkill: BattleSkill = null
    public isTrans: boolean = false //是否进行过转换
    public activeObject: BattleSkillObjectType = null //作为buff时的生效对象
    public dep: BattleSkillEffectType = null
    public useCount: number //触发次数
    public cd: number = 0 //冷却回合数
    public delayRounds: number = 0 //延迟回合数

    public cfg: BattleSkillEffectCfg = null

    public init(effect) {
        this.cfg = effect
        this.type = effect.type
        this.resetTimes()
        this.valueType = effect.valueType
        this.value = effect.value
        this.orgValue = this.value
        this.perObjType = effect.perObjectType
        this.perAttrType = effect.perAttributeType
        this.simpleContent = effect.simpleContent
        this.objectIndex = effect.objectIndex || 0
        this.buff = new BattleSkillEffectBuff().init(effect.buff || {})
        this.rateType = effect.rateType
        this.useCount = 0
        this.rounds = effect.rounds || -1
        return this
    }

    public setValue(value) {
        this.value = value
        this.orgValue = value
    }

    public reset() {
        this.resetTimes()
        this.buffSkill?.reset()
        this.value = this.orgValue
        this.useCount = 0
    }

    public resetTimes() {
        this.times = this.cfg.times || -1
        this.rounds = -1
        this.roleTimes = this.cfg.roleTimes || -1
        this.resetRoundTimes()
        this.cd = 0
    }

    public resetRoundTimes() {
        this.roundTimes = this.cfg.roundTimes || -1
    }

    public canUse() {
        if (this.cd > 0) {
            return false
        }
        if (this.times == 0 || this.rounds == 0 || this.roundTimes == 0) return false
        return true
    }

    public use() {
        let succ = this.canUse()
        if (this.times > 0) {
            this.times--
        }
        if (succ) {
            this.useCount++
            if (this.roundTimes > 0) {
                this.roundTimes--
            }
            this.setCd()
        }
        return succ
    }

    private setCd() {
        if (this.cfg.cd) {
            this.cd = this.cfg.cd
        }
    }

    public canMerge(effect: BattleSkillEffect) {
        if (this.type != effect.type) return
        if (this.type == BattleSkillEffectType.BUFF || this.type == BattleSkillEffectType.TRIGGER) return
        if (this.valueType != effect.valueType) return
        if (this.valueType != BattleEffectValueType.INT) {
            if (this.perObjType != effect.perObjType) return
            if (this.perAttrType != effect.perAttrType) return
        }
        if (this.rateType != effect.rateType) return
        return true
    }
}

export class BattleSkillObject {
    public type: BattleSkillObjectType
    public camp: BattleSkillCampType
    public count: number
    public effects: BattleSkillEffect[] = []
    public skill: BattleSkill
    public cfg = null

    public init(object, skill: BattleSkill) {
        this.cfg = object
        this.skill = skill
        this.type = object.type
        this.camp = object.camp
        this.count = object.count
        return this
    }

    public isSame(object) {
        if (this.type != object.type) return
        if (this.camp != object.camp) return
        if (this.count != object.count) return
        return true
    }

}

export class BattleSkillTrigger {
    public type: BattleSkillTriggerType
    public object: BattleSkillObjectType
    public camp: BattleSkillCampType
    public count: number = 0
    public delayRounds: number = 0
    private cfg: BattleSkillTriggerCfg = null

    public init(data) {
        this.type = data.type
        this.object = data.object
        this.camp = data.camp
        this.count = data.count || 0
        this.cfg = data
        return this
    }

    public set(type: BattleSkillTriggerType, object?: BattleSkillObjectType, camp?: BattleSkillCampType, count?: number) {
        this.type = type
        this.object = object
        this.camp = camp
        this.count = count || 0
        return this
    }

    public check(trigger) {
        if (!this.isSame(trigger)) return
        if (this.type == BattleSkillTriggerType.HP_REDUCE) {
            return this.count <= trigger.count
        }
        else if (this.type == BattleSkillTriggerType.HP_GT) {
            return this.count < trigger.count
        }
        else if (this.type == BattleSkillTriggerType.HP_LTE) {
            return this.count >= trigger.count
        }
        else {
            return this.count == trigger.count
        }
    }

    public isSame(trigger) {
        if (this.type != trigger.type) return
        if (this.object != trigger.object) return
        if (this.camp != trigger.camp) return
        return true
    }

    public resetCount() {
        this.count = this.cfg.count || 0
    }

    public reset() {
        this.resetCount()
        this.delayRounds = 0
    }
}

export default class BattleSkill extends Skill {
    protected cfg: BattleSkillCfg = null
    public viewCfg: BattleSkillViewCfg

    public trigger: BattleSkillTrigger = null
    public objects: BattleSkillObject[] = []
    public effects: BattleSkillEffect[] = []

    public getCfg() { return this.cfg }
    public getType() { return SkillType.BATTLE }

    public get icon() { return this.viewCfg?.icon }
    public get name() { return this.viewCfg?.name }
    public get contentValue() { return this.cfg?.contentValue || [] }

    public orgSkill: BattleSkill = null;

    public attrRate: number = 1

    public index: number = 0

    public dep: BattleSkillEffectType = null

    public sender: BattleRole = null

    public skills: BattleSkill[] = []
    public usedObjectMap = {}

    public talentLv: number = 0

    public delayRounds: number = 0 //延迟回合数

    constructor(data?) {
        super()
        if (!data) return
        let { talentLv } = data
        if (!isNaN(talentLv)) {
            this.talentLv = talentLv
        }
    }

    public init(id: number, lv: number = 1, role?: any) {
        this.index = cfgHelper.getIndexBySkillId(id)
        super.init(id, lv, role);
        this.initViewCfg()
        this.initEffect()
        return this
    }

    protected getInitParams() {
        return []
    }

    public reset() {
        this.trigger.reset()

        for (let effect of this.effects) {
            effect.reset()
        }

        this.usedObjectMap = {}
        this.delayRounds = 0
        return this
    }

    public initCfg() {
        let cfg = assetsMgr.getJsonData<BattleSkillCfg>("BattleSkill", this.id)
        this.cfg = cfg
        this._initCfg()
    }

    protected initViewCfg() {
        if (this.viewCfg) return
        this.viewCfg = assetsMgr.checkJsonData("BattleSkillControl", `${this.role.id}-${this.getId()}`)
    }

    public levelUp(val: number = 1) {
        this.level += val;
        this.init(this.getId(), this.level, this.getRole())
    }

    protected initEffect() {
        let { trigger, object, effect } = this.cfg
        return this.initData(trigger, object, effect)
    }

    protected _initCfg() {
        let { effect, times, cd, roundTimes } = this.cfg
        if (effect) {
            for (let e of effect) {
                if (times) {
                    e.times = times
                }
                if (cd) {
                    e.cd = cd
                }
                if (roundTimes) {
                    e.roundTimes = roundTimes
                }
            }
        }
    }

    protected initData(trigger, objects, effects: any[] = []) {
        this.trigger = new BattleSkillTrigger().init(trigger)

        let { roleId, roleLv, roleStarLv, roleTalentLv, isAdd } = this.getAttrParams()

        let attrMap = {
            [SkillEffectPerAttrType.ATTACK]: PassengerAttr.ATTACK,
            [SkillEffectPerAttrType.HP]: PassengerAttr.HP,
        }

        let buffCount = 0
        this.effects = effects.map(e => {
            let effect = new BattleSkillEffect().init(e)
            if (effect.type == BattleSkillEffectType.BUFF || effect.type == BattleSkillEffectType.TRIGGER) {
                buffCount++
                //@ts-ignore
                let buffSkill = new this.constructor()
                buffSkill.attrRate = this.attrRate
                buffSkill.orgSkill = this
                effect.buffSkill = buffSkill
                buffSkill.init(this.getId() + buffCount * 100000, this.level, this.getRole(), ...this.getInitParams())
            }
            else if (effect.valueType == BattleEffectValueType.PER &&
                (this.orgSkill ? effect.perObjType == SkillEffectPerObjType.BUFFER : effect.perObjType == SkillEffectPerObjType.SELF) &&
                (effect.perAttrType == SkillEffectPerAttrType.HP || effect.perAttrType == SkillEffectPerAttrType.ATTACK)) {
                effect.valueType = BattleEffectValueType.INT
                let orgValue = Number(effect.value)
                let attrType = attrMap[effect.perAttrType]
                let orgAttr = cfgHelper.getRoleAttr(roleId, roleLv, roleStarLv, attrType)
                if (isAdd) {
                    orgAttr = cfgHelper.getRoleAttrByAdd(roleId, roleLv, roleStarLv, attrType)
                }
                if (roleTalentLv) {
                    orgAttr += cfgHelper.getRoleAttrByAdd(roleId, roleTalentLv * 5, 0, attrType)
                }
                let attr = orgAttr * this.attrRate
                let value = Math.round(Math.abs(orgValue) * attr)
                value = Math.max(1, value) * ut.normalizeNumber(orgValue)
                effect.setValue(value)
                effect.isTrans = true
            }
            effect.skill = this
            return effect
        })

        if (objects) {
            this.objects = objects.map((object, i) => {
                let o = new BattleSkillObject().init(object, this)
                o.effects = this.effects.filter(e => e.objectIndex == i)
                return o
            })
        }
        return this
    }

    protected getAttrParams(): { roleId: number, roleLv: number, roleStarLv: number, roleTalentLv?: number, isAdd?: boolean } {
        let role = this.getRole()
        let roleId = this.getRole()?.id
        let roleLv = 0
        let roleStarLv = this.getRole()?.getStarLv()

        let tmpId = cfgHelper.getCharacter(this.role.id)?.skillTmpId
        let datas = cfgHelper.getSkillTemplateDatas(tmpId)
        let tmpData = datas.find(d => d.skill[this.index]?.lv == this.level)
        if (tmpData) {
            roleLv = tmpData.skill[this.index].calcLv
        }
        else {
            let nextIndex = datas.findIndex(d => d.skill[this.index]?.lv > this.level)
            if (nextIndex < 0) {
                nextIndex = datas.length - 1
            }
            let preIndex = Math.max(0, nextIndex - 1)
            let nextRoleLv = datas[nextIndex].skill[this.index].calcLv
            let preRoleLv = datas[preIndex].skill[this.index].calcLv
            let nextLv = datas[nextIndex].skill[this.index].lv
            let preLv = datas[preIndex].skill[this.index].lv
            roleLv = Math.round(cc.misc.lerp(preRoleLv, nextRoleLv, (this.level - preLv) / (nextLv - preLv)))
        }

        let roleTalentLv = this.talentLv
        if (!roleTalentLv) {
            if (role.talents) {
                roleTalentLv = role.talents.find(t => {
                    let index = this.orgSkill?.index || this.index
                    return t.type == EquipEffectType.SKILL && t.target == index
                })?.level || 0
            }
        }
        return { roleId, roleLv, roleStarLv, roleTalentLv }
    }

    public canUse() {
        return this.effects.some(e => e.canUse())
    }

    public use() {
        this.effects.forEach(e => e.use())
    }

    public getDescStr(trigger: boolean = true, colorParams?) {
        if (this.trigger.type == "NONE" && this.role?.id == 2065) { //临时处理南瓜boss
            let m1 = new Monster().init(3001, this.role.getLevel(), this.role.getStarLv())
            let m2 = new Monster().init(3002, this.role.getLevel(), this.role.getStarLv())
            return assetsMgr.lang(this.viewCfg.content, m1.getSkills()[0].getDescStr(), m2.getSkills()[0].getDescStr())
        }
        let { key, params } = this.getDesc()
        let triggerStr = ""
        let boldColor = "#1b1b1b"
        if (trigger) {
            triggerStr = this.getTriggerDesc()
            if (triggerStr) {
                triggerStr = `<color=${boldColor}>${triggerStr}</><img src='jiantou'/>`
            }
        }
        return triggerStr + gameHelper.colorfulSkill(assetsMgr.lang(key, params), {boldColor})
    }

    public getTriggerDesc() {
        let viewCfg = this.viewCfg
        let triggerContent = viewCfg?.triggerContent
        if (!triggerContent) {
            let mainId = cfgHelper.getMainSkillId(this.id)
            triggerContent = assetsMgr.checkJsonData<any>("BattleSkillControl", `${this.role.id}-${mainId}`)?.triggerContent
        }
        if (!triggerContent) return ""
        return assetsMgr.lang(triggerContent)
    }

    public getDesc(): { key: string, params: any[] } {
        let cfg = this.viewCfg
        if (!cfg) return { key: "not found", params: [] }
        let content = cfg.content

        let contentValue = this.getContentValue()
        let getEffectVal = (effect) => {
            let { valueType, value } = effect
            value = Math.abs(Number(effect.value))
            if (valueType == BattleEffectValueType.PER) {
                value = value * 100 + "%"
            }
            return value
        }
        for (let effect of this.effects) {
            if (effect.buffSkill) {
                contentValue.pushArr(effect.buffSkill.contentValue)
                let vals = effect.buffSkill.effects.filter(e => e.perObjType).map(e => getEffectVal(e))
                contentValue.pushArr(vals)
            }
            else {
                let value = getEffectVal(effect)
                contentValue.push(value)
            }
        }
        return { key: content, params: contentValue }
    }

    protected getContentValue() {
        return this.contentValue.slice()
    }

    public getExtraDesc(isSimple: boolean = false) {
        let cfg = this.viewCfg
        if (!cfg) return
        if (isSimple && !cfg.extraContent2) return
        return cfg.extra
    }

    public clone(...params) {
        //@ts-ignore
        let skill = new this.constructor()
        skill.attrRate = this.attrRate
        skill.orgSkill = this.orgSkill
        return skill.init(this.id, this.level, this.getRole(), ...params)
    }

    public isSame(skill) {
        if (!skill) return
        return this.getType() == skill.getType() && this.getId() == skill.getId()
    }

    public getHash() {
        return `${this.getType()}-${this.getId()}`
    }
}
