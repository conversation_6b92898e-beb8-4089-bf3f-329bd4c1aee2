import { game<PERSON>el<PERSON> } from "../../common/helper/GameHelper"
import { BattleEffectValueType, BattleRoleType, BattleSkillEffectType, BattleSkillTriggerType } from "./BattleEnum"
import { PlanetMonsterLevelCfg } from "../../common/constant/DataType"
import BattleSkill, { BattleSkillEffect, BattleSkillObject } from "./BattleSkill"
import { SkillType } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"

function mergeEffects(effects1: BattleSkillEffect[], effects: BattleSkillEffect[]) {
    for (let effect of effects) {
        let e = effects1.find(e => e.canMerge(effect))
        if (e) {
            e.value = Number(e.value) + Number(effect.value)
        }
        else {
            let newEffect = new BattleSkillEffect().init(effect.cfg)
            newEffect.setValue(effect.value)
            newEffect.times = effect.times
            newEffect.valueType = effect.valueType
            newEffect.buffSkill = effect.buffSkill
            newEffect.skill = effect.skill
            effects1.push(newEffect)
        }
    }
    return effects1
}

export {
    mergeEffects
}

export default class BattleRole {
    public uid: string = ""
    public id: number = null
    public hp: number = 0
    public attack: number = 0
    public skills: BattleSkill[] = []
    public type: BattleRoleType
    public lv: number = 1
    public starLv: number = 0

    public buffs: BattleSkillEffect[] = [] //一般用于免疫/减伤类技能
    public buffSkills: BattleSkill[] = [] //需一定条件触发的buff效果

    public orgHp: number = 0
    public orgAttack: number = 0
    public orgIndex: number = -1

    public preDamage: number = 0 //上次攻击造成的伤害
    public preHit: number = 0 //上次受到的伤害
    public accHit: number = 0 //累计受到的伤害

    public summoner: BattleRole = null //召唤者

    private roleType: BattleRoleType = null

    public isCopy: boolean = false
    private isMergeSkill: boolean = false

    public role: any = null

    public initData(data: {
        id: number, hp?: number, attack?: number, uid?: string, type?: BattleRoleType, lv?: number, starLv?: number,
        skill?: BattleSkill, skills?: BattleSkill[], role?: any,
    }) {
        this.role = data.role
        this.id = data.id
        this.uid = data.uid || `${this.id}_${ut.uid()}`
        this.hp = data.hp
        this.attack = data.attack
        this.lv = data.lv || this.role?.getLevel() || 1
        this.starLv = data.starLv || this.role?.getStarLv() || 0
        this.skills = []
        if (data.skills?.length) {
            //@ts-ignore
            this.skills = data.skills.map(skill => skill.reset())
        }
        else {
            if (data.skill) {
                let skill = data.skill.reset()
                this.skills.push(skill)
            }
        }

        this.type = this.type || data.type

        this.hp = this.hp || 1
        this.attack = this.attack || 1

        this.roleType = gameHelper.getRoleType(this.id)

        this.orgAttack = this.attack
        this.orgHp = this.hp

        return this
    }

    public reset() {
        for (let skill of this.skills) {
            skill.reset()
        }

        this.hp = this.orgHp
        this.attack = this.orgAttack
        this.preDamage = 0
        this.preHit = 0
        this.accHit = 0

        this.summoner = null
        this.buffs = []
        this.buffSkills = []
        return this
    }

    public getAttack() {
        let atkInc = this.getAtkInc()
        let attack = Math.max(1, this.attack + atkInc)
        return attack
    }

    public getHp() {
        return this.hp
    }

    //攻击绝对值加成
    private getAtkInc() {
        let sum = 0
        for (let buff of this.buffs) {
            if (!buff.canUse()) continue
            if (buff.valueType != BattleEffectValueType.INT) continue
            if (buff.type == BattleSkillEffectType.ATTACK_BUFF) {
                sum += Number(buff.value)
            }
        }
        return sum
    }

    public hit(damge, sender?) {
        this.hp -= damge
    }

    public isDeath() {
        return this.hp <= 0
    }

    public addBuff(buff: BattleSkillEffect) {
        // let curBuff = this.buffs.find((b)=> b.type == buff.type)
        // if (curBuff) {
        //     curBuff.times = buff.times
        // }
        // else {
        //     this.buffs.push(buff)
        // }
        //即使是同类型的，参数不一样效果也不一样，不能简单叠加次数，先这样处理

        this.buffs.push(buff)
    }

    public removeBuff(buff) {
        this.buffs.remove(buff)
    }

    public checkAndRemoveBuffs() {
        let buffs = []
        for (let i = this.buffs.length - 1; i >= 0; i--) {
            let buff = this.buffs[i]
            if (!buff.canUse()) {
                buffs.push(buff)
                this.removeBuff(buff)
            }
        }
        return buffs
    }

    public checkAndRemoveByDepBuff(depBuff: BattleSkillEffect) {
        let buffs = []
        for (let i = this.buffs.length - 1; i >= 0; i--) {
            let buff = this.buffs[i]
            if (buff.dep == depBuff.type) {
                buffs.push(buff)
                this.removeBuff(buff)
            }
        }

        for (let i = this.buffSkills.length - 1; i >= 0; i--) {
            let buff = this.buffSkills[i]
            if (buff.dep == depBuff.type) {
                buffs.push(buff)
                this.removeBuffSkill(buff)
            }
        }
        return buffs
    }

    public removeBuffSkill(skill) {
        this.buffSkills.remove(skill)
    }

    public getType() {
        return this.roleType
    }

    public getSkills(type: SkillType = SkillType.BATTLE) {
        // return this.skills.filter(s => s.getType() == type)
        return this.skills
    }

    public get quality() {
        return cfgHelper.getQualityByStarLv(this.starLv)
    }
    public get sortId() {
        return cfgHelper.getCharacter(this.id).sortId
    }
    public get animalType() {
        return cfgHelper.getCharacter(this.id)?.animalType
    }
    public get battleType() {
        return cfgHelper.getCharacter(this.id)?.battleType
    }

    public getStarLv() { return this.starLv }
    public getStarLvWithQuality() {
        return cfgHelper.getRealStarLv(this.starLv)
    }
    public getLevel() { return this.lv }

    public mergeSkills() {
        if (this.skills.length < 2) return
        if (this.isMergeSkill) return

        this.isMergeSkill = true

        let mergeObject = (obj1: BattleSkillObject, obj2: BattleSkillObject) => {
            for (let effect of obj2.effects) {
                let e = obj1.effects.find(e => e.canMerge(effect))
                if (e) {
                    e.value = Number(e.value) + Number(effect.value)
                }
                else {
                    obj1.effects.push(effect)
                    obj1.skill.effects.push(effect)
                }
            }
        }

        let mergeSkill = (s1: BattleSkill, s2: BattleSkill) => {
            for (let obj of s2.objects) {
                let o = s1.objects.find(o => o.isSame(obj))
                if (o) {
                    mergeObject(o, obj)
                }
                else {
                    s1.objects.push(obj)
                    s1.effects.pushArr(obj.effects)
                }
            }
        }

        let skills = []

        for (let skill of this.skills) {
            if (skill.getType() != SkillType.BATTLE) {
                skills.push(skill)
                continue
            }
            let s = skills.find(s => s.trigger.isSame(skill.trigger))
            if (s) {
                mergeSkill(s, skill)
                s.skills.push(skill)
            }
            else {
                skills.push(skill.clone())
            }
            for (let effect of skill.effects) {
                effect.skill = skill
            }
        }

        this.skills = skills
    }

}