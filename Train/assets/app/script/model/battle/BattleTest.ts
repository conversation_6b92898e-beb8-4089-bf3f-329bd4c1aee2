import { PassengerAttr, ROLE_ATTR_ID, RoleAnimalType } from "../../common/constant/Enums";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import BlackHoleEquip from "../blackHole/BlackHoleEquip";
import EquipModel, { Equip } from "../equip/EquipModel";
import PassengerModel from "../passenger/PassengerModel";
import Battle from "./Battle";
import { BattleRoleType } from "./BattleEnum";
import BattleRole from "./BattleRole";
import Monster from "./Monster";

export default class BattleTest {

    public passengers: BattleRole[] = []
    public monsters: BattleRole[] = []
    public buffs: BattleRole[] = []

    public init() {
        let testFunc = this.anyTest
        // let testFunc = this.summonTest

        let [team1, team2, buffs] = testFunc.call(this)
        // team1 = team1.map(({id, lv, type})=>{
        //     return new BattleRole().init(id, lv || 1, type || BattleRoleType.PASSENGER)
        // })
        // team2 = team2.map(({id, lv, type})=>{
        //     return new BattleRole().init(id, lv || 1, type || BattleRoleType.MONSTER)
        // })
        let getEquip = ()=> {
            // return new EquipSkill().init(2009, 1)
        }

        // let passengers = team1.map(({id, lv, starLv})=>{
        //     return new PassengerModel().init({id, level: lv || 1, starLv: starLv || 1})
        // })
        team1 = team1.map(({id, lv, starLv})=>{
            let role = new PassengerModel().init({id, level: lv || 1, starLv})
            return new BattleRole().initData({ id: role.getID(), hp: role.getHp(), attack: role.getAttack(), 
                skills: role.getSkills(), type: BattleRoleType.PASSENGER, lv, starLv })
        })

        // team1 = team1.map(({ id, lv, skillLv }) => {
        //     lv = lv || 1
        //     let monster = new Monster().init(id, lv)
        //     let hp = monster.hp
        //     let attack = monster.attack
        //     let skill = monster.getSkills()
        //     return new BattleRole().initData({ id, hp, attack, skill, type: BattleRoleType.PASSENGER })
        // })
        team2 = team2.map(({ id, lv, starLv }) => {
            lv = lv || 1
            let monster = new Monster().init(id, lv, starLv)
            let hp = monster.hp
            let attack = monster.attack
            let skills = monster.getSkills()
            return new BattleRole().initData({ id, hp, attack, skills, type: BattleRoleType.MONSTER, lv, starLv })
        })

        // team2 = team2.map(({id, lv, starLv})=>{
        //     let role = new PassengerModel().init({id, level: lv || 1, starLv})
        //     return new BattleRole().initData({ id: role.getID(), hp: role.getHp(), attack: role.getAttack(), 
        //         skills: role.getSkills(), type: BattleRoleType.MONSTER, lv, starLv })
        // })

        this.passengers = team1.map(p => new BattleRole().initData(p))
        this.monsters = team2.map(p => new BattleRole().initData(p))
        
        this.buffs = buffs?.map(p => new BattleRole().initData(p)) || []

        return this.test(team1, team2, buffs)
    }

    public test(team1, team2, buffs) {
        let battle = new Battle()
        battle.init(team1, team2, buffs)
        return battle
    }

    private anyTest() {
        let team1 = [1006, 1024, 1015, 1017, 1019].map(id => {
            return {id, lv: 10, starLv: 0}
        })
        // let team2 = [1031, 1031, 1031, 1031, 1031].map(id => {
        //     return {id, lv: 70, starLv: 0}
        // })
        let team2 = [2010, 2018, 2040, 2034, 2005].map(id => {
            return {id, lv: 30, starLv: 0}
        })


        // let team2 = [{id: 2001, lv: 1, starLv: -5}, {id: 1002004, lv: 1, starLv:  0}]

        // let team1 = [
        //     {id: 1035, lv: 9},
        //     {id: 1006, lv: 9},
        //     {id: 1029, lv: 8},
        //     {id: 1005, lv: 8},
        //     {id: 1007, lv: 8},
        //   ]
        // let team2 = [
        //     { id: 2013, lv: 9, starLv: 0 },
        //     { id: 2040, lv: 9, starLv: 0 },
        //     { id: 2036, lv: 8, starLv: 0 },
        //     { id: 2016, lv: 8, starLv: 0 },
        //     { id: 2045, lv: 8, starLv: 0 }
        //   ]

    // let techs = [new BlackHoleEquip().init({id: 1006, target: RoleAnimalType.CAT, level: 30})].map(e => {
    //         return e.skill
    //     })
    //     let techBuff = new BattleRole().initData({id: Number(ROLE_ATTR_ID.BLACK_HOLE), skills: techs})
    //     for (let skill of techs) {
    //         skill.setRole(techBuff)
    //     }

        let buffs = []

        // buffs = [train1, train2]

        // let team1: any = [ {id: 1026, lv: 40}, {id: 1020, lv: 40} ]
        // let team2: any = [  {id: 2004, lv: 100}, {id: 2004, lv: 40}, {id: 2004, lv: 1}]
        // let team1: any = [  {id: 1006, lv: 22},  {id: 1005, lv: 20}, {id: 1007, lv: 20} ]
        // let team1: any = [  {id: 1006, lv: 22}, {id: 1024, lv: 20}, {id: 1005, lv: 20}, {id: 1007, lv: 20} ]
        // let team2 = [{id: 2004, lv: 1}, {id: 2004, lv: 1}, {id: 2004, lv: 1}, {id: 2004, lv: 1}, {id: 2064, lv: 15}]
        // let team2 = [{id: 2061, lv: 26}]
        // let team1: any = [  {id: 1003, lv: 30}, {id: 1031, lv: 30}, {id: 1006, lv: 30}, {id: 1005, lv: 30}, {id: 1010, lv: 30} ]
        // let team2: any = [  {id: 1042, lv: 30}, {id: 1031, lv: 30}, {id: 1040, lv: 30}, {id: 1036, lv: 30}, {id: 1025, lv: 30} ]

        // let team1: any = [  {id: 1003, lv: 30}, {id: 1010, lv: 30}, {id: 1036, lv: 30}, {id: 1004, lv: 30}, {id: 1038, lv: 30} ]
        // let team2: any = [  {id: 1027, lv: 30}, {id: 1010, lv: 30}, {id: 1029, lv: 30}, {id: 1016, lv: 30}, {id: 1004, lv: 30} ]

        return [team1, team2, buffs]
    }

    private buffTest() {
        let team2: any = [{ id: 1008, lv: 3 }, { id: 1002 }, { id: 1004 }, { id: 1005 }]
        let team1: any = [{ id: 1008 }]
        return [team1, team2]
    }

    private summonTest() {
        let team1: any = [{ id: 1007 }, { id: 1005 }]
        let team2: any = [{ id: 1007 }, { id: 1005 }]
        return [team1, team2]
    }

    private aniTest() {
        let team1: any = [{ id: 1002 }, { id: 1002 }, { id: 1002 }, { id: 1010, lv: 1 }]
        let team2: any = [{ id: 1010, lv: 3 }, { id: 1010, lv: 3 }]
        return [team1, team2]
    }

    private skillTest() {
        let team1: any = [{ id: 1001 }, { id: 1001 }, { id: 1001 }, { id: 1001 }, { id: 1001 }]
        // let team1: any = [{id: 1014}, {id: 1014}, {id: 1014}, {id: 1014 }, {id: 1014}]
        // let team2:any = [{id: 1004}, {id: 1001}, {id: 1001}, {id: 1001}, {id: 1001}]
        // let team2:any = [{id: 1002}, {id: 1007}, {id: 1012}, {id: 1004}, {id: 1006}]
        // let team2:any = [{id: 1014}, {id: 1004}, {id: 1004}, {id: 1004}, {id: 1004}]
        let team2: any = [{ id: 1014 }, { id: 1004 }, { id: 1014 }, { id: 1014 }, { id: 1014 }]
        return [team1, team2]
    }
}