/**
 * 触发时机
 */
enum BattleSkillTriggerType {
    NONE = "NONE",
    BATTLE_BEFORE = "BATTLE_BEFORE",
    BATTLE_START = "BATTLE_START",  //战斗开始前
    BATTLE_AFTER = "BATTLE_AFTER",
    ATTACK_BEFORE = "ATTACK_BEFORE", //开始对撞前
    ATTACK = "ATTACK",        //对撞时（可以用来做溅射伤害
    ATTACK_AFTER = "ATTACK_AFTER", //对撞结束后
    HIT = "HIT",                   //被击中时 (伤害为0时不会触发)
    SUMMON = "SUMMON",      //召唤
    KILL = "KILL", //击杀
    ROUND_START = "ROUND_START", //回合开始
    ROUND_WIN = "ROUND_WIN", //对撞胜利时
    POS_HEAD = "POS_HEAD", //位于最前排时
    HP_REDUCE = "HP_REDUCE", //血量每降低x
    USE_SKILL = "USE_SKILL", //使用技能时
    ATTACKED = "ATTACKED", // 被攻击（伤害=0也会触发）
    DEATH = "DEATH",     //死亡时
    DEATH_BEFORE = "DEATH_BEFORE",  //死亡前，和DEATH动画表现不一样
    DEATH_KILL = "DEATH_KILL",  //死亡击杀
    LIVE = "LIVE",  //存活
    SKILL_KILL = "SKILL_KILL", //用技能击杀
    HP_GT = "HP_GT", //血量大于x
    NEAR_DEATH = "NEAR_DEATH", //濒死
    HP_LTE = "HP_LTE", //血量小于等于x%
}

/**
 * 作用对象
 */
enum BattleSkillObjectType {
    NONE = "NONE",
    SELF = "SELF",      //自己
    HEAD = "HEAD", //从头开始连续几位
    TAIL = "TAIL", //从尾开始连续几位
    HEAD_INDEX = "HEAD_INDEX",      //从头开始数第x位
    TAIL_INDEX = "TAIL_INDEX",      //从末尾开始数第x位
    FRONT = "FRONT",    //从当前位置往前连续x位
    BEHIND = "BEHIND",  //从当前位置往后连续x位
    RANDOM = "RANDOM",  //随机x个
    MIN_HP = "MIN_HP",  //血量最少的x个
    MIN_HP_ROLE = "MIN_HP_ROLE", //血量最少的x个非召唤物
    MAX_ATTACK = "MAX_ATTACK", //攻击最高的x个
    SELF_INDEX = "SELF_INDEX", //自己对应站位，没有选最后一个
    INDEX = "INDEX", //初始站位

    ALL = "ALL",        //所有
    MAX_HP = "MAX_HP",  //血量最高的x个
    SUMMON = "SUMMON", //通用召唤技能
    SUMMON_MUMMY = "SUMMON_MUMMY", //召唤木乃伊，队友死亡后在他的位置上召唤

    SENDER = "SENDER", //触发技能的发起者 
    RECIPIENT = "RECIPIENT", //触发技能的接收者
    RECIPIENT_BEHIND = "RECIPIENT_BEHIND", //从上面的位置往后找x位
    /*
    SUMMON: A召唤B, A是发起者，B是接收者
    HIT; A打B(B受伤)，A是发起者，B是接收者
    DEAD：A杀B(B死亡), A是发起者，B是接收者
    */

    HP_LESS_SELF = "HP_LESS_SELF", //血量小于自己
    ROLE = "ROLE", //非召唤物
    SAME_BUFF = "SAME_BUFF", //拥有相同buff的
    REBIRTH = "REBIRTH", //自己复活
    REBIRTH_SENDER = "REBIRTH_SENDER", //复活别人
    BUFFER = "BUFFER", //buff类技能的施加者
    ANIMAL_TYPE = "ANIMAL_TYPE", //统一种族

    SKILL = "SKILL", //技能
}

/**
 * 作用阵营
 */
enum BattleSkillCampType {
    ENEMY = "ENEMY",        //敌人
    TEAMMATE = "TEAMMATE",   //队友
    ALL = "ALL", //所以阵营
    SELF_TEAMMATE = "SELF_TEAMMATE", //自己和队友
}

/**
 * 技能效果
 */
enum BattleSkillEffectType {
    CHANGE_HP = "CHANGE_HP",            //增加/减少血
    CHANGE_ATTACK = "CHANGE_ATTACK",    //增加/减少攻击
    IMMUNE_DEADTH = "IMMUNE_DEADTH",    //免疫死亡
    DAMAGE = "DAMAGE",                  //造成x点伤害 
    DEATH = "DEATH",                    //直接死亡

    IMMUNE_DAMAGE = "IMMUNE_DAMAGE",  //buff 免疫x点伤害
    CHANGE_DAMAGE = "CHANGE_DAMAGE",  //buff 增加受到的x点伤害
    SKILL_DAMAGE = "SKILL_DAMAGE", //增加/减少技能的伤害
    ATTACK = "ATTACK", //赋予攻击
    HP = "HP", //赋予血量
    SUMMONN_ID = "SUMMONN_ID", //召唤物id
    CHANGE_POSITION = "CHANGE_POSITION", //更换到某个位置 (绝对量)
    MOVE_POSITION = "MOVE_POSITION", //往前移动x个位置 (增量)
    ATTACK_BUFF = "ATTACK_BUFF", //攻击力buff，增加/减少攻击力
    ATTACK_GAIN_BUFF = "ATTACK_GAIN_BUFF", // 攻击力增益buff
    HP_GAIN_BUFF = "HP_GAIN_BUFF", // 攻击力增益buff
    ATTACK_GAIN_DEBUFF = "ATTACK_GAIN_DEBUFF", //攻击减益buff
    HP_GAIN_DEBUFF = "HP_GAIN_DEBUFF", //血量减益buff
    SILENCE = "SILENCE",

    CHANGE_SKILL = "CHANGE_SKILL", //修改技能数值
    BUFF = "BUFF", //技能附加效果
    TRIGGER = "TRIGGER", //技能即时触发的效果
    COPY = "COPY", //复制技能
    TURE_DAMAGE = "TURE_DAMAGE", //真实伤害，免疫护盾和减伤
    RUN_AWAY = "RUN_AWAY", // 逃跑
    REBIRTH = "REBIRTH",
    USE_BUFFER_SKILL = "USE_BUFFER_SKILL", //用于传递buff
}

enum BattleRoleType {
    PASSENGER = 1,
    MONSTER,
    SUMMON,
}

enum BattleLogType {
    USE_SKILL,
    USE_BUFF_SKILL,
    CHANGE_HP,
    CHANGE_ATTACK,
    CHANGE_HP_ATTACK,
    ATTACK,
    COLLIDE,
    HIT,
    DEATH,
    SUMMON,
    ADD_BUFF,
    REMOVE_BUFF,
    CHANGE_POSITION,
    EFFECT_FAIL,
    COPY,
    REBIRTH,
    ROUND_START,
    RUN_AWAY,
}

enum BattleEffectValueType {
    INT = "INT", //数值
    PER = "PER", //百分比
}

enum BattleSummonID {
    BASE = 10000, //用来区分是不是召唤物
    EQUIP_BASE = 20000, //装备召唤物其实id
}

enum BattleHitType {
    NORMAL,
    COLLIDE,
    CHANGE_HP,
    TRUE,
}

enum BattleDeathType {
    DIRECT, //无技能，直接死
    DEATH_BEFOR, //触发死亡前放技能
    DEATH, //死亡时放技能
    SUMMON, //技能是召唤
}

//技能效果如果是百分比时基于的对象
enum SkillEffectPerObjType {
    SELF = "SELF", //基于自己
    RECIPIENT =  "RECIPIENT", //基于接受者
    MAX_HP =  "MAX_HP", //血量最高的队友，如果以后需要区分阵营，再加一个字段
    BUFFER = "BUFFER", //给buff的人
}

//技能效果如果是百分比时基于的属性
enum SkillEffectPerAttrType {
    ATTACK = "ATTACK", //基于攻击
    HP = "HP", //基于血量
    DAMAGE = "DAMAGE", //基于造成的伤害
    HIT = "HIT", //基于上次受到的伤害
    ACC_HIT = "ACC_HIT", //基于累计受到的伤害
    ORG_HP = "ORG_HP", //基于进场血量
}

//技能效果倍率
enum SkillEffectRateType {
    REV_COUNT = "REV_COUNT", //基于receivers数量
    ANIMAL_TYPE = "ANIMAL_TYPE", //基于同族的数量
    USE_COUNT = "USE_COUNT", //基于触发技能的次数
}

enum BattleStage {
    BEFORE_BATTLE,
    ROUND_START,
    BEFORE_ATTACK,
    ATTACK,
    AFTER_ATTACK,
}

export {
    BattleSkillTriggerType,
    BattleSkillObjectType,
    BattleSkillCampType,
    BattleSkillEffectType,
    BattleRoleType,
    BattleLogType,
    BattleEffectValueType,
    BattleSummonID,
    BattleHitType,
    BattleDeathType,
    SkillEffectPerObjType,
    SkillEffectPerAttrType,
    SkillEffectRateType,
    BattleStage,
}