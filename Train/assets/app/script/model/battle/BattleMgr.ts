import { db<PERSON><PERSON><PERSON> } from "../../common/helper/DatabaseHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import Observer from "../../../core/utils/Observer";
import { Msg } from "../../../proto/msg-define";
import { viewHelper } from "../../common/helper/ViewHelper";

export class BattleTeam {
    public id: number = 0
    public uids: string[] = []

    public init(data: proto.IBattleTeam) {
        this.id = data.id
        this.uids = data.uids || []
        return this
    }

    public async setRolesBySever(uids: string[]) {
        uids = uids.filter(uid => !!uid) 
        let isSame = uids.length === this.uids.length
        for (let i = 0; i < uids.length; i++) {
            if (uids[i] !== this.uids[i]) {
                isSame = false
            }
        }
        if (isSame) return true
        let msg = new proto.C2S_SetBattleTeamMessage({ team: { id: this.id, uids } })
        const res = await gameHelper.net.request(Msg.C2S_SetBattleTeamMessage, msg, true)
        const { code } = proto.S2C_SetBattleTeamMessage.decode(res)
        if (code == 0) {
            this.setRoles(uids)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public setRoles(roles: string[]) {
        this.uids = roles
    }

    public getRoles() {
        return this.uids
    }
}

@mc.addmodel('battle')
export default class BattleMgr extends mc.BaseModel {

    public teams: BattleTeam[] = []
    public isAuto: boolean = false
    public record: object = {}
    public speed: number = 1

    public pasue: boolean = false

    public data: proto.IBattle = null

    public init() {
        let data = dbHelper.register("battle", 1, this.toDB, this)
        this.fromDB(data)
        return this
    }

    private fromDB(data: any) {
        this.isAuto = data.isAuto || false
        this.speed = data.speed || 1
        this.record = data.record || {}

        this.teams = this.data.teams.map(t => new BattleTeam().init(t))
    }

    public toDB() {
        return {
            isAuto: this.isAuto,
            record: this.record,
            speed: this.speed,
        }
    }

    public getTeam(id: number = 0) {
        let team = this.teams.find(t => t.id == id)
        if (!team) {
            team = new BattleTeam().init({ id })
            this.teams.push(team)
        }
        return team
    }

    public setSpeed(speed: number) {
        this.speed = speed
    }

    public setRecord(key: string) {
        if (this.isRecorded(key)) return
        Observer.addKey(this.record, key, true)
    }

    public isRecorded(key: string) {
        return this.record[key] != null
    }


}
