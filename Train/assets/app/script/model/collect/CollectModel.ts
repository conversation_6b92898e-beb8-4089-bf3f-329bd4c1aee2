import { ChapterPlanetMineCfg, CollectMapCfg } from "../../common/constant/DataType"
import { game<PERSON>el<PERSON> } from "../../common/helper/GameHelper"
import CollectMineModel from "./CollectMineModel"
import RoleCollectModel from "./RoleCollectModel"
import { cfgHelper } from "../../common/helper/CfgHelper";


@mc.addmodel("collect")
export default class CollectModel extends mc.BaseModel {

    protected role: RoleCollectModel = null
    public mines: CollectMineModel[] = []

    public size: cc.Size = null
    public landSize: cc.Size = null

    public data: proto.ICollect = null

    public init() {
        this.role = new RoleCollectModel().init()
        this.size = cfgHelper.getMiscData("collect").map

        this.initMines()
    }

    public updateInfo(data: proto.ICollect) {
        this.data = data
        this.initMines()
    }

    public getRole() {
        return this.role
    }

    private initMines() {
        this.mines = []
        this.data.mine.forEach(m => {
            this.addMine(m)
        })
    }

    public getMines() {
        return this.mines
    }

    public removeMine(mine) {
        this.mines.remove(mine)
    }

    public update(dt: any) {
        this.role.update(dt)

        if (mc.currWindName == "collect") {
            for (let mines of this.mines) {
                mines.update(dt)
            }
        }
    }

    public addMine(data: proto.IMapMineItemData): CollectMineModel {
        let mine = new CollectMineModel().init(data.uid, data.id)
        if (!mine) return
        mine.scale = data.scale || 1
        mine.setPosition(cc.v2(data.position.x, data.position.y))
        mine.rewards = gameHelper.toConditions(data.reward)
        this.mines.push(mine)
        return mine
    }
}
