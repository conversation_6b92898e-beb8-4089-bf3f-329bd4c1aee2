import { CurMaxData, IdTargets, PlanetCfg, TaskTarget } from "../../common/constant/DataType"
import { ConditionType, GuideStepMark, PlanetNodeType, TaskType, UIFunctionType } from "../../common/constant/Enums"
import { gameHelper } from "../../common/helper/GameHelper"
import { cfgHelper } from "../../common/helper/CfgHelper"
import EventType from "../../common/event/EventType"
import TaskBaseModel from "./TaskBaseModel"
import TaskModel from "./TaskModel"
import BuildObj from "../train/common/BuildObj"
import { BuildHeadObj } from "../train/TrainHeadModel"
import PlanetNodeModel from "../planet/PlanetNodeModel"
import { unlockHelper } from "../../common/helper/UnlockHelper"
import CoreEventType from "../../../core/event/CoreEventType"

let helper = {
    SimpleLineWidth: 16,//一行显示多少个(英文)字符
    SimpleLineHeight: 3,//最多显示几行
    strCode(str: string) {//估算字符串显示长度
        let count = 0
        if (str) {
            for (let i = 0; i < str.length; i++) {
                count += str.charCodeAt(i) > 255 ? 2 : 1
            }
        }
        return count
    },
    checkWrap(pre: string, after: string) {
        let numPre = helper.strCode(pre)
        let numAll = helper.strCode(after) + numPre
        let lineAll = Math.ceil(numAll / helper.SimpleLineWidth)
        if (lineAll <= 1) return false
        if (lineAll > helper.SimpleLineHeight) return false
        let linePre = Math.ceil(numPre / helper.SimpleLineWidth)
        return linePre != lineAll
    },
    showLabelCurMax(obj: TaskObject, model: TaskBaseModel, cfg: any, preStr: string, isMax?: boolean) {
        let { cur, max } = obj.getCurMax(model, cfg, obj)
        let strM = `(${max}/${max})`
        let str = isMax ? strM : `(${cur > max ? max : cur}/${max})`
        if (helper.checkWrap(preStr, strM)) {
            str = '\n' + str
        }
        return preStr + str
    },
    showJustSimple(model: TaskBaseModel, cfg: any, obj: TaskObject) {
        return assetsMgr.lang(cfg.simple)
    },
    showCurMaxSimple(model: TaskBaseModel, cfg: any, obj: TaskObject, isMax?: boolean) {
        return helper.showLabelCurMax(obj, model, cfg, assetsMgr.lang(cfg.simple), isMax)
    },
    checkCurMaxComplete(model: TaskBaseModel, cfg: IdTargets, obj: TaskObject) {
        let { cur, max } = obj.getCurMax(model, cfg, obj)
        return cur >= max
    },
    checkCompleteByMap(model: TaskBaseModel, cfg: IdTargets, obj: TaskObject) {
        for (let oneTag of cfg.target) {
            if (!obj.checkOneTag(oneTag)) {
                return false
            }
        }
        return true
    },
    getCurMaxByMap(model: TaskBaseModel, cfg: IdTargets, obj: TaskObject) {
        let ary = cfg.target
        let cur = 0, max = ary.length
        ary.forEach(oneTag => {
            if (obj.checkOneTag(oneTag)) {
                cur++
            }
        })
        return { cur, max }
    },
    getCurMaxByComplete(model: TaskBaseModel, cfg: IdTargets, obj: TaskObject) {
        let bol = obj.checkComplete(model, cfg, obj)
        let cur = bol ? 1 : 0
        let max = 1
        return { cur, max }
    },
    getSimpleParamTaget0Num(cfg: IdTargets) {
        return [cfg.target[0]?.num]
    },
    getSimpleParamTaget01Num(cfg: IdTargets) {
        let ary = cfg.target
        return [ary[0]?.num, ary[1]?.num]
    },
}

interface TaskObject {
    type: TaskType
    getSimple?(model: TaskBaseModel, cfg: any, obj: TaskObject, isMax?: boolean): string
    getAchiveSimpleParams?(cfg: IdTargets): any[],
    getCurMax(model: TaskBaseModel, cfg: IdTargets, obj: TaskObject): CurMaxData
    calculateCur?(cfg: IdTargets): number
    checkOneTag?(oneTag: TaskTarget): boolean
    checkComplete(model: TaskBaseModel, cfg: IdTargets, obj: TaskObject): boolean
    eventOn(model: TaskBaseModel, obj: TaskObject): void
}
let aryTaskObj: TaskObject[] = [
    {
        type: TaskType.GOTO_PLANET,
        getSimple: helper.showJustSimple,
        getCurMax: helper.getCurMaxByComplete,
        checkComplete(model, cfg) {
            let planetId = model.getIdInTarget0(cfg)
            return gameHelper.planet.moveTargetId == planetId
                || gameHelper.planet.curPlanetId == planetId
                || gameHelper.planet.getPlanet(planetId)?.isDone()
        },
        eventOn(model, obj) { eventCenter.on(EventType.TRAIN_MOVING_PLANET, (planetId: number) => { model.onAddByOneTagId(obj.type, planetId) }, model) },
    },
    {
        type: TaskType.EXPLORE_PLANET,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) {
            let oneTag = model.getOnlyOneTarget(cfg)
            let pMod = gameHelper.planet.getPlanet(oneTag.id)
            let max = oneTag.num//百分比进度
            let cur = pMod ? pMod.get0To100Percent() : 0
            return { cur, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PLANET_NODE_COMPLETE, (planetId: number) => { model.onAddByOneTagId(obj.type, planetId) }, model) },
    },
    {
        type: TaskType.EXPLORE_PLANET_COMPLETE,
        getAchiveSimpleParams(cfg) {
            let oneTag = cfg.target[0]
            let planet = gameHelper.planet.getPlanet(Number(oneTag.id))
            return [planet.getShowName()]
        },
        getCurMax: helper.getCurMaxByComplete,
        checkComplete(model, cfg) {
            let oneTag = model.getOnlyOneTarget(cfg)
            let pMod = gameHelper.planet.getPlanet(oneTag.id)
            return pMod && pMod.isDone()
        },
        eventOn(model, obj) { eventCenter.on(EventType.PLANET_COMPLETE, (planetId: number) => { model.onAddByOneTagId(obj.type, planetId) }, model) },
    },
    {
        type: TaskType.BUILD_TRAINITEM,
        getSimple(model, cfg, obj, isMax?) {
            let str = assetsMgr.lang(cfg.simple)
            if (cfg.target.length <= 1)
                return str
            return helper.showLabelCurMax(obj, model, cfg, str, isMax)
        },
        getCurMax: helper.getCurMaxByMap,
        checkOneTag(oneTag) { return gameHelper.train.isUnlockBuild(oneTag.id) },
        checkComplete: helper.checkCompleteByMap,
        eventOn(model, obj) { eventCenter.on(EventType.UNLOCK_BUILD, (build: BuildObj | BuildHeadObj) => { model.onAddByOneTagId(obj.type, build.id) }, model) },
    },
    {
        type: TaskType.BUILD_TRAINITEM_LEVEL,
        getAchiveSimpleParams(cfg) {
            let num = cfg.target[0]?.num
            let str = `name_trainTheme_${cfg.target[0].id}_${num}`
            return [assetsMgr.lang(str)]
        },
        getCurMax(model, cfg) {
            let tOne = model.getOnlyOneTarget(cfg)
            let trId = tOne.id
            let carriage = gameHelper.train.getCarriageById(trId)
            if (!carriage) return
            return carriage.getBuildsCurMaxData(tOne.num)
        },
        checkComplete(model, cfg) {
            let tOne = model.getOnlyOneTarget(cfg)
            let trId = tOne.id
            let carriage = gameHelper.train.getCarriageById(trId)
            if (!carriage) return false
            return carriage.isAllBuildsMaxLv(tOne.num)
        },
        eventOn(model, obj) { eventCenter.on(EventType.UNLOCK_ALL_BUILD, (carriageId: number, themeIndex: number) => { model.onAddByOneTagId(obj.type, carriageId) }, model) },
    },
    {
        type: TaskType.TRAINITEM_LEVELUP,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) {
            let tOne = model.getOnlyOneTarget(cfg)
            return { cur: gameHelper.train.getBuildLevel(tOne.id), max: tOne.num }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.LEVEL_UP_BUILD, (build: BuildObj) => { model.onAddByOneTagId(obj.type, build.getCarriageOrder()) }, model) },
    },
    {
        type: TaskType.UNLOCK_THEME,
        getSimple: helper.showJustSimple,
        getCurMax: helper.getCurMaxByMap,
        checkOneTag(oneTag) { 
            let [carriageId, themeIndex] = oneTag.id.split('-')
            let carriage = gameHelper.train.getCarriageById(Number(carriageId))
            return carriage && carriage.getThemeLv() >= Number(themeIndex)
        },
        checkComplete: helper.checkCompleteByMap,
        eventOn(model, obj) { eventCenter.on(EventType.UNLOCK_THEME, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.TRAINITEM_LEVELUP2,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) {
            let cur = 0
            let tOne = model.getOnlyOneTarget(cfg)
            let trId = tOne.id
            let builds = cfgHelper.getBuilds(trId)
            let carriage = gameHelper.train.getCarriageById(trId)
            if (carriage) {
                let maxLv = tOne.num
                for (const buildCfg of builds) {
                    let build = carriage.getBuildByOrder(buildCfg.order)
                    if (build && build.lv >= maxLv) {
                        cur++
                    }
                }
            }
            return { cur, max: builds.length }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.LEVEL_UP_BUILD, (build: BuildObj) => { model.onAddByOneTagId(obj.type, build.carriageId) }, model) },
    },
    {
        type: TaskType.BUILD_TRAIN_INDEX,
        getSimple: helper.showJustSimple,
        getCurMax(model, cfg) {
            let cur = gameHelper.train.getCarriages().length
            let max = model.getNumInTarget0(cfg)
            return { cur, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.CARRIAGE_BUILD_END, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.BUILD_TRAIN,
        getSimple: helper.showJustSimple,
        getCurMax: helper.getCurMaxByMap,
        checkOneTag(oneTag) { return gameHelper.train.isBuiltCarriage(oneTag.id) },
        checkComplete: helper.checkCompleteByMap,
        eventOn(model, obj) { eventCenter.on(EventType.CARRIAGE_BUILD_END, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.GET_ITEM,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) { return model.getCurMaxAllTarget(cfg, model instanceof TaskModel) },
        checkComplete(model, cfg) { return model.checkCompleteAllTarget(cfg) },
        eventOn(model, obj) {
            let onAddHeart = function (addNum: number) { model.onAddCurrency(addNum, ConditionType.HEART) }
            let onAddDiamond = function (addNum: number) { model.onAddCurrency(addNum, ConditionType.DIAMOND) }
            let onAddStarDust = function (addNum: number) { model.onAddCurrency(addNum, ConditionType.STAR_DUST) }
            eventCenter.on(EventType.UPDATE_STARDUST, onAddStarDust, model)
            eventCenter.on(EventType.UPDATE_HEART, onAddHeart, model)
            eventCenter.on(EventType.UPDATE_DIAMOND, onAddDiamond, model)
            eventCenter.on(EventType.CHANGE_NUM_PROP, model.onChangeProp, model)
        },
    },
    {
        type: TaskType.CHARACTER_LEVEL,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget01Num,
        getCurMax(model, cfg) {
            let list = gameHelper.passenger.getPassengers()
            let max = model.getNumInTarget0(cfg)
            let cur = 0
            list.forEach(m => cur += m.getLevel())
            return { cur, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PASSENGER_LEVEL_UP, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.CHARACTER_LEVELUP,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget01Num,
        getCurMax(model, cfg) {
            let list = gameHelper.passenger.getPassengers()
            let tags = cfg.target
            let x = tags[0].num, y = tags[1].num
            let ary = list.filter(m => m.getLevel() >= y)
            return { cur: ary.length, max: x }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PASSENGER_LEVEL_UP, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.CHARACTER_RANK,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget01Num,
        getCurMax(model, cfg) {
            let list = gameHelper.passenger.getPassengers()
            let max = model.getNumInTarget0(cfg)
            let cur = 0
            list.forEach(m => cur += m.getStarLv())
            return { cur, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PASSENGER_STARLV_UP, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.CHARACTER_RANKUP,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) {
            let list = gameHelper.passenger.getPassengers()
            let tags = cfg.target
            let x = tags[0].num, y = tags[1].num
            let ary = list.filter(m => m.getStarLv() >= y)
            return { cur: ary.length, max: x }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PASSENGER_STARLV_UP, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.CHARACTER_GETON,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) {
            let onGetOnCharacter = function () {
                model.onAddCommon(obj.type, null, function (cfg) {
                    if (obj.checkComplete(model, cfg, obj)) return//进度满了就不会再下降了
                    let list = gameHelper.passenger.getPassengers()
                    let ary = list.filter(m => m.isCheckIn())
                    model.setTaskKeyProgress(cfg.id, '0', ary.length)
                })
            }
            eventCenter.on(EventType.PASSENGER_CHECK_IN, onGetOnCharacter, model)
            eventCenter.on(EventType.PASSENGER_CHECK_OUT, onGetOnCharacter, model)
        },
    },
    {
        type: TaskType.CHARACTER_GETON2,
        getSimple: helper.showJustSimple,
        getCurMax: helper.getCurMaxByComplete,
        checkComplete(model, cfg) {
            let tags = cfg.target
            let x = tags[0].id
            let p = gameHelper.passenger.getPassenger(x)
            if (!p) return false
            let t = tags[1]
            if (t) {
                return p.dormId == t.id
            }
            return p.isCheckIn()
        },
        eventOn(model, obj) {
            let onGetOnCharacter = function () { model.onAddCommon(obj.type) }
            eventCenter.on(EventType.PASSENGER_CHECK_IN, onGetOnCharacter, model)
            eventCenter.on(EventType.PASSENGER_CHECK_OUT, onGetOnCharacter, model)
        },
    },
    {
        type: TaskType.GET_CHARACTER_INDEX,
        getSimple: helper.showJustSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) {
            let max = model.getNumInTarget0(cfg)
            let list = gameHelper.passenger.getPassengers()
            return { cur: list.length, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.UNLOCK_PASSENGER, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.TOOL_BUILD,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.TOOL_MAKE, () => { model.onAddByNum(obj.type, 1) }, model) },
    },
    {
        type: TaskType.TOOL_CHANGE,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) {
            let curTotal = 0, maxTotal = 0
            cfg.target.forEach((oneTag) => {
                let toolId = model.getTaskProgressNum(cfg.id, oneTag.id)
                if (toolId) curTotal++
                maxTotal++
            })
            return { cur: curTotal, max: maxTotal }
        },
        checkComplete(model, cfg, obj) {
            let ary = cfg.target
            for (const oneTag of ary) {
                let toolId = model.getTaskProgressNum(cfg.id, oneTag.id)
                if (!toolId) {
                    return false
                }
            }
            return true
        },
        eventOn(model, obj) {
            eventCenter.on(EventType.TOOL_CHANGE_OK, (toolType: number, oldToolId: number) => {
                model.onAddCommon(obj.type, (cfg) => {
                    return model.checkConditionId(cfg, toolType)
                }, (cfg) => {
                    if (!model.getTaskProgressNum(cfg.id, String(toolType))) {
                        model.setTaskKeyProgress(cfg.id, String(toolType), oldToolId)
                    }
                })
            }, model)
        },
    },
    {
        type: TaskType.TOOL_LEVELUP2,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) {
            eventCenter.on(EventType.TOOL_MAKE, (type: number) => {
                model.onAddCommon(obj.type, (cfg) => {
                    let one = model.getOnlyOneTarget(cfg)
                    let id = one.id
                    return id == null ? true : id == type
                }, (cfg) => {
                    model.addTaskKeyProgress(cfg.id, '0', 1)
                })
            }, model)
        },
    },
    {
        type: TaskType.TOOL_LEVELUP,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) {
            let one = model.getOnlyOneTarget(cfg)
            let num = one.num
            let id = one.id
            if (id != null) {
                let t = gameHelper.tool.getToolByType(id)
                return { cur: t ? t.getLv() : 0, max: num }
            }
            let tools = gameHelper.tool.getTools()
            let cur = 0, max = tools.length
            tools.forEach(t => { if (t.getLv() >= num) cur++ })
            return { cur, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.TOOL_MAKE, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.TOOL_RANKUP,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) {
            let tools = gameHelper.tool.getTools()
            let cur = 0, max = tools.length, num = model.getNumInTarget0(cfg)
            tools.forEach(t => { if (t.quality >= num) cur++ })
            return { cur, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.TOOL_MAKE, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.TOOL_TABLEUP,
        getSimple: helper.showJustSimple,
        getCurMax(model, cfg) { return { cur: gameHelper.tool.getLv(), max: model.getNumInTarget0(cfg) } },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.TOOL_TABLE_UP, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.ENTRUST_START,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.ENTRUST_START, () => { model.onAddByNum(obj.type, 1) }, model) },
    },
    {
        type: TaskType.ENTRUST_COMPLETE,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.ENTRUST_COMPLETE, () => { model.onAddByNum(obj.type, 1) }, model) },
    },
    {
        type: TaskType.GOTO_WORK,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg, obj) {
            let data = model.getCurMaxTarget0(cfg)
            if (data.cur == 0)
                data.cur = obj.calculateCur(cfg)
            return data
        },
        calculateCur(cfg) {
            let one = cfg.target[0]
            let car = gameHelper.train.getCarriageById(one.id)
            return car ? car.getWorkers().length : 0
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) {
            eventCenter.on(EventType.TRAIN_CHANGE_WORK, () => {
                model.onAddCommon(obj.type, null, (cfg) => {
                    if (obj.checkComplete(model, cfg, obj)) return//进度满了就不会再下降了
                    model.setTaskKeyProgress(cfg.id, '0', obj.calculateCur(cfg))
                })
            }, model)
        },
    },
    {
        type: TaskType.GOODS_UNLOCK,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) {
            let tOne = model.getOnlyOneTarget(cfg)
            let trId = tOne.id
            let carriage = gameHelper.train.getCarriageById(trId)
            if (!carriage) return
            return { cur: carriage.getGoods().length, max: tOne.num || cfgHelper.getTrainGoods(trId)?.length }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.UNLOCK_TRAIN_GOODS, (trainId: number) => { model.onAddByOneTagId(obj.type, trainId) }, model) },
    },
    {
        type: TaskType.PLANET_BATTLE_ID,
        getSimple: helper.showCurMaxSimple,
        checkComplete(model, cfg) {
            let battleId = model.getIdInTarget0(cfg)
            return gameHelper.planet.isPassBattle(battleId)
        },
        getCurMax(model, cfg) {
            let tOne = model.getOnlyOneTarget(cfg)
            let startId = "1006-2-1"
            let endId = tOne.id
            let planetId = startId.split('-')[0]
            let planet = gameHelper.planet.getPlanet(Number(planetId))
            if (!planet) return { cur: 0, max: 0 }
            let checkPoints = planet.getCheckPoints()
            let startIndex = checkPoints.findIndex(m => m.id == startId)
            let curNode = planet.getPreCheckPoint()
            let curIndex = checkPoints.findIndex(m => m.id == curNode?.id)
            let endIndex = checkPoints.findIndex(m => m.id == endId)
            curIndex = Math.max(curIndex, startIndex - 1)
            let cur = curIndex - startIndex + 1
            let max = endIndex - startIndex + 1
            return { cur, max }
        },
        eventOn(model, obj) { eventCenter.on(EventType.PLANET_NODE_COMPLETE, (_: number, node: PlanetNodeModel) => { 
            if (!node) return
            if (node.nodeType != PlanetNodeType.CHECK_POINT) return
            let battleId = node.getId()
            model.onAddByOneTagId(obj.type, battleId) }, model) 
        },
    },
    {
        type: TaskType.TOWER,
        getSimple: helper.showJustSimple,
        checkComplete(model, cfg) {
            let num = model.getNumInTarget0(cfg)
            let layer = Math.floor(num / 5)
            if (gameHelper.tower.layer == layer) {
                let index = ((num - 1) % 5) + 1
                return gameHelper.tower.index > index
            }
            return gameHelper.tower.layer > layer
        },
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        eventOn(model, obj) { eventCenter.on(EventType.TOWER_BATTLE_WIN, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.TO_DEEP_EXPLORE,
        getSimple: helper.showJustSimple,
        checkComplete(model, cfg) {
            return unlockHelper.isUnlockFunction(UIFunctionType.DEEP_EXPLORE)
        },
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        eventOn(model, obj) { eventCenter.on(EventType.GUIDE_UNLOCK_FUNTION, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.DEEP_EXPLORE,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PLANET_DEEP_EXPLORE_END, () => { model.onAddByNum(obj.type, 1) }, model) },
    },
    {
        type: TaskType.PROFILE,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PLANET_PROFILE_UNLOCK, () => { model.onAddByNum(obj.type, 1) }, model) },
    },
    {
        type: TaskType.GOTO_PLANET_ENTRY_1009,
        getSimple: helper.showJustSimple,
        checkComplete(model, cfg) {
            return gameHelper.guide.isStepEnd(GuideStepMark.START_ORE_PUZZLE)
        },
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        eventOn(model, obj) { eventCenter.on(CoreEventType.WIND_ENTER, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.ORE_PUZZLE,
        getSimple: helper.showJustSimple,
        checkComplete(model, cfg) {
            return unlockHelper.isUnlockFunction(UIFunctionType.PLAY_ORE)
        },
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        eventOn(model, obj) { eventCenter.on(EventType.GUIDE_UNLOCK_FUNTION, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.EXPLORE_PLANET_AREA,
        getSimple: helper.showCurMaxSimple,
        getCurMax(model, cfg) {
            let oneTag = model.getOnlyOneTarget(cfg)
            let [planetId, areaId] = oneTag.id.split('-')
            let planet = gameHelper.planet.getPlanet(Number(planetId))
            let cur = 0
            let max = oneTag.num//百分比进度
            if (planet) {
                let area = planet.areas[Number(areaId)-1]
                cur = area ? Math.floor(area.getPercent() * 100) : 0
            }
            return { cur, max }
        },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.PLANET_NODE_COMPLETE, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.TO_TRANSPORT,
        getSimple: helper.showJustSimple,
        checkComplete(model, cfg) {
            return unlockHelper.isUnlockFunction(UIFunctionType.PLAY_TRANSPORT)
        },
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        eventOn(model, obj) { eventCenter.on(EventType.GUIDE_UNLOCK_FUNTION, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.TRANSPORT,
        getSimple: helper.showCurMaxSimple,
        getAchiveSimpleParams: helper.getSimpleParamTaget0Num,
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        checkComplete: helper.checkCurMaxComplete,
        eventOn(model, obj) { eventCenter.on(EventType.TRANSPORT_FINISH, () => { model.onAddByNum(obj.type, 1) }, model) },
    },
    {
        type: TaskType.GOTO_PLANET_ENTRY_1007,
        getSimple: helper.showJustSimple,
        checkComplete(model, cfg) {
            return unlockHelper.isUnlockFunction(UIFunctionType.PLAY_SPACE_STONE)
        },
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        eventOn(model, obj) { eventCenter.on(CoreEventType.WIND_ENTER, () => { model.onAddCommon(obj.type) }, model) },
    },
    {
        type: TaskType.INSTANCE_PUZZLE,
        getSimple: helper.showJustSimple,
        checkComplete(model, cfg) {
            return unlockHelper.isUnlockFunction(UIFunctionType.PLAY_INSTANCE)
        },
        getCurMax(model, cfg) { return model.getCurMaxTarget0(cfg) },
        eventOn(model, obj) { eventCenter.on(EventType.GUIDE_UNLOCK_FUNTION, () => { model.onAddCommon(obj.type) }, model) },
    },

]
let dicTaskObj: { [type: string]: TaskObject } = {}
aryTaskObj.forEach(element => { dicTaskObj[element.type] = element })
let getObjectTask = function (typeKey: string): TaskObject { return dicTaskObj[typeKey] }
let registerTaskEvent = function (model: TaskBaseModel) { aryTaskObj.forEach(element => { element.eventOn && element.eventOn(model, element) }) }

export {
    registerTaskEvent,
    getObjectTask,
}
