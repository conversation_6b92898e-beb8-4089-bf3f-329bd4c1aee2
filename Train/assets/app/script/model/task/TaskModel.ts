import { game<PERSON><PERSON><PERSON> } from "../../common/helper/GameHelper"
import { ConditionType, TaskType } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { BuildCfg, Condition, IdTargets, TaskCfg } from "../../common/constant/DataType"
import { Msg } from "../../../proto/msg-define"
import { viewHelper } from "../../common/helper/ViewHelper"
import { richHelper } from "../../common/helper/RichHelper"
import { getObjectTask } from "./TaskObject"
import EventType from "../../common/event/EventType"
import TaskBaseModel from "./TaskBaseModel"

type DataSort = {
    priority: number
    sortI: number
}
export class TaskData {
    cfg: TaskCfg
    canOver: boolean
    sortDic: DataSort

    public init(data) {
        this.cfg = data.cfg
        this.canOver = data.canOver
        this.sortDic = data.sortDic
        return this
    }

    public getTaskId() {
        return `task-${this.cfg.id}`
    }
}

/**任务数据*/
@mc.addmodel('task', -1)
export default class TaskModel extends TaskBaseModel {
    private isWillCheckNum: boolean = false
    private aryTaskCanDo: TaskData[]
    private dicTaskSort: { [id: string]: DataSort } = {}

    public data: proto.ITaskInfo = null

    public initListener() {
        super.initListener()
        // eventCenter.on(EventType.TIME_ACCELERATE_END, this.onHastenEnd, this)
        // eventCenter.on(EventType.TIME_ACCELERATE_START, this.onHastenStart, this)
        eventCenter.on(EventType.TRAIN_BUILD_OVER, this.unlockNewTask, this)
        eventCenter.on(EventType.CARRIAGE_BUILD_END, this.unlockNewTask, this)
        eventCenter.on(EventType.GUIDE_STEP_END, this.unlockNewTask, this)
        eventCenter.on(EventType.PLANET_COMPLETE, this.unlockNewTask, this)
        eventCenter.on(EventType.PLANET_NODE_COMPLETE, this.unlockNewTask, this)
        eventCenter.on(EventType.PLANET_NODE_END, this.unlockNewTask, this)
        eventCenter.on(EventType.UNLOCK_FUNTION, this.unlockNewTask, this)
        eventCenter.on(EventType.UNLOCK_ALL_BUILD, this.unlockNewTask, this)
    }
    update(dt) {
        if (this.isWillCheckNum) {
            this.isWillCheckNum = false
            // 此时建设类任务的材料可能变化了
            return eventCenter.emit(EventType.TASK_UPDATE)
        }
    }

    //获取现在所有可做的任务(排序后)
    public getCanDoTasks() {
        if (!this.aryTaskCanDo) {
            this.setCanDoTasks()
            this.sortCanDoTasks()
        }
        return this.aryTaskCanDo
    }
    public getDataById(id: string) {
        let ary = this.aryTaskCanDo
        if (ary) {
            return ary.find(m => m.cfg.id == id)
        }
    }
    public getDataByCfg(cfg: TaskCfg) {
        let ary = this.aryTaskCanDo
        if (ary) {
            return ary.find(m => m.cfg.id == cfg.id)
        }
    }
    //获取现在可领取奖励的任务个数
    public getCanRewardTaskNum() {
        let num = 0
        this.getCanDoTasks().forEach(data => {
            if (data.canOver) {
                num++
            } else if (this.checkBuildTrainItem(data.cfg)) {
                num++
            }
        })
        return num
    }
    private completeTaskCheck(id: string) {
        let temp = this.getDataFromCanDo(id)
        if (!temp) {
            return twlog.error("没有此任务", id)
        }
        if (!temp.data.canOver) {
            return twlog.error("此任务不能完成", id)
        }
        return temp
    }

    public async completeTaskAndGetRewardBySever(id: string) {
        let msg = new proto.C2S_ClaimTaskRewardMessage({ id })
        let res = await gameHelper.net.request(Msg.C2S_ClaimTaskRewardMessage, msg, true)
        const { code } = proto.S2C_ClaimTaskRewardResultMessage.decode(res)
        if (code == 0) {
            this.completeTaskAndGetReward(id)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    //完成某任务且获取奖励
    public async completeTaskAndGetReward(id: string) {
        let temp = this.completeTaskCheck(id)
        if (!temp) return
        let cfg = temp.data.cfg
        if (this.checkRewardNull(cfg)) {
            return twlog.error("此任务没有reward", id)
        }
        await gameHelper.grantRewardAndShowUI(gameHelper.toConditions(cfg.reward))
        this.markTaskComplete(cfg)
        eventCenter.emit(EventType.TASK_COMPLETE, id, this.calculateNextId(id, temp.nextId))
    }
    //自动完成某无奖励任务
    public completeRewardNullTask(id: string) {
        let temp = this.completeTaskCheck(id)
        if (!temp) return
        let cfg = temp.data.cfg
        if (!this.checkRewardNull(cfg)) {
            return twlog.error("此任务有reward", id)
        }
        this.markTaskComplete(cfg)
        eventCenter.emit(EventType.TASK_COMPLETE, id, this.calculateNextId(id, temp.nextId))
    }
    public checkAutoCompleteTask() {
        for (const data of this.getCanDoTasks()) {
            if (data.canOver && this.checkRewardNull(data.cfg)) {
                this.completeRewardNullTask(data.cfg.id)
                break
            }
        }
    }
    public checkRewardNull(cfg: TaskCfg) {
        let ary = cfg.reward
        return !ary || ary.length == 0
    }
    //解锁某任务
    public unlockNewTask() {
        this.setCanDoTasks()
        this.sortCanDoTasks()
        eventCenter.emit(EventType.TASK_UNLOCK)
    }
    public checkCanBuildOne(tCfg: BuildCfg, isBuilt?: boolean) {
        if (!tCfg) return
        if (isBuilt == null)
            isBuilt = gameHelper.train.isUnlockBuild(tCfg.id)
        if (isBuilt) return
        if (!this.checkBuildIsUnLock(tCfg)) return
        return this.checkBuildUnlockCost(tCfg)
    }
    public checkBuildIsUnLock(tCfg: BuildCfg) {
        return gameHelper.train.isCarriageOverBuilt(tCfg.carriageId) && gameHelper.train.checkCanBuildByPre(tCfg)
    }
    public checkBuildUnlockCost(tCfg: BuildCfg) {
        let cfg = cfgHelper.getBuildLvCfg(tCfg.carriageId, tCfg.order, 1)
        if (!cfg) return false
        return gameHelper.checkConditions(gameHelper.toConditions(cfg.buyCost))
    }
    //有一个可建设即可
    public canBuildTrainItem(cfg: TaskCfg) {
        for (let oneTag of cfg.target) {
            let tCfg = cfgHelper.getBuildById(oneTag.id)
            if (this.checkCanBuildOne(tCfg)) {
                return true
            }
        }
    }
    public checkBuildTrainItem(cfg: TaskCfg) {
        let type = cfg.type
        return (type == TaskType.BUILD_TRAINITEM) && this.canBuildTrainItem(cfg)
    }
    public getAryInfoGetItem(cfg: TaskCfg) {
        let arry: { condition: Condition, kp: KeyParams }[] = []
        cfg.target.forEach((item, idx) => {
            let id = item.id
            let num = item.num
            let cur = this.getTaskProgressCheckMax(cfg.id, String(idx), num)
            let type = item.type || ConditionType.PROP
            let condition = { type, id, num }
            arry.push({ condition, kp: this.getRichByCurMax(cur, num) })
        })
        return arry
    }
    private getTaskObject(cfg: TaskCfg) {
        let type = cfg.type
        let obj = getObjectTask(type)
        if (!obj) return twlog.error('getTaskObject error-type', type)
        return obj
    }
    public getTaskCurMax(cfg: TaskCfg) {
        let obj = this.getTaskObject(cfg)
        return obj && obj.getCurMax(this, cfg, obj)
    }
    //简略描述+任务完成进度
    public getStringSimple(cfg: TaskCfg, isMax: boolean = false) {
        let obj = this.getTaskObject(cfg)
        if (!obj) return
        let fun = obj.getSimple
        if (!fun) return twlog.error('getStringSimple no fun', cfg.type)
        return fun(this, cfg, obj, isMax)
    }
    private checkTaskCanComplete(cfg: TaskCfg) {
        let obj = this.getTaskObject(cfg)
        return obj && obj.checkComplete(this, cfg, obj)
    }
    public getRichByCurMax(cur: number, max: number): KeyParams {
        let str = cur >= max ? max : richHelper.richColor(cur, 'ff7960')
        return {
            key: '<b>{0}/{1}</b>',
            params: [str, max],
        }
    }
    private setCanDoTasks(cfgComplete?: TaskCfg) {
        let ary: TaskData[] = []
        let all = assetsMgr.getJson<TaskCfg>('Task').datas
        let max = all.length
        all.forEach((cfg, cur) => {
            if (this.checkTaskCanDo(cfg)) {
                let task = new TaskData().init({ cfg, canOver: this.checkTaskCanComplete(cfg), sortDic: this.getSortDic(cfg, cur, max)})
                ary.push(task)
            }
        })
        if (cfgComplete) {
            this.setNewTaskSort(ary, cfgComplete)
        }
        this.aryTaskCanDo = ary
    }
    /**
     * 任务框排序规则：
     * 第一优先级：任务完成状态，已完成>未完成
     * 第二优先级：根据任务释放先后顺序排序，先释放任务>后释放任务
     * 
     * 第一优先级：根据priority排序，数值越大越前
     * 第二优先级：根据任务释放先后顺序排序，先释放任务>后释放任务
     * 第三优先级：根据任务id的大小排序，id越小越前
    */
    private sortCanDoTasks() {
        /*取消可完成的置顶
            this.getCanDoTasks().sort((a, b) => {
                if (a.canOver && b.canOver) {
                    return a.sortI - b.sortI
                } else if (a.canOver) {
                    return -1
                } else if (b.canOver) {
                    return 1
                } else {
                    return a.sortI - b.sortI
                }
            })
        */
        this.getCanDoTasks().sort((a, b) => {
            let sa = a.sortDic
            let sb = b.sortDic
            let pa = sa.priority || 0
            let pb = sb.priority || 0
            if (pa != pb) {
                return pb - pa
            }
            return sa.sortI - sb.sortI
        })
    }
    // 获取任务的释放顺序(越小越先释放)
    private getSortI(cfg: TaskCfg, cur: number, max: number) {
        let preTask = cfg.preTask
        if (preTask) {
            return this.dicTaskComplete[preTask] + max
        }
        return cur
    }
    private getSortDic(cfg: TaskCfg, cur: number, max: number) {
        let dic = this.dicTaskSort[cfg.id]
        if (!dic) {
            dic = this.dicTaskSort[cfg.id] = { priority: cfg.priority, sortI: this.getSortI(cfg, cur, max) }
        }
        return dic
    }
    private setNewTaskSort(ary: TaskData[], cfgComplete: TaskCfg) {
        let data = this.getDataByCfg(cfgComplete)
        if (!data) return
        let dic = {}
        this.aryTaskCanDo.forEach(one => { dic[one.cfg.id] = true })
        let all = ary.filter(one => !dic[one.cfg.id])
        all.forEach(one => {
            one.sortDic.sortI = data.sortDic.sortI
        })
    }
    /**
     * 新任务获取条件
     * 前置任务：前置任务完成时，自动获取任务
     * 特殊条件：特殊条件触发时，自动获取任务
    */
    private checkTaskCanDo(cfg: TaskCfg) {
        if (this.isTaskComplete(cfg.id)) {
            return false
        }
        if (!this.checkTaskCanTrigger(cfg)) {
            return false
        }
        let preTask = cfg.preTask
        if (preTask) {
            return this.isTaskComplete(preTask)
        }
        return true
    }
    private checkTaskCanTrigger(cfg: TaskCfg) {
        return this.checkCanTrigger(cfg.trigger)
    }
    // 当货币/道具数量有变化时(不要马上dosomething下一帧再做)
    private onWillCheckNums() {
        this.isWillCheckNum = true
    }
    public onChangeProp(id: number, addNum: number) {
        this.onWillCheckNums()
        super.onChangeProp(id, addNum)
    }
    public onAddCurrency(addNum: number, type: ConditionType) {
        this.onWillCheckNums()
        super.onAddCurrency(addNum, type)
    }
    public onAddCommon(giveType: TaskType, conditionFun?: (cfg: IdTargets) => Boolean, addFun?: (cfg: IdTargets) => void) {
        let bolSort = false
        let bolUpdate = false
        let autoOver = null
        this.getCanDoTasks().forEach(data => {
            let cfg = data.cfg
            if (cfg.type === giveType && (!conditionFun || conditionFun(cfg))) {
                bolUpdate = true
                addFun && addFun(cfg)
                let newOver = this.checkTaskCanComplete(cfg)
                if (data.canOver != newOver) {
                    data.canOver = newOver
                    bolSort = true
                    if (newOver && this.checkRewardNull(data.cfg)) {
                        if (autoOver) {
                            twlog.error("autoOver too many:", autoOver.cfg.id, data.cfg.id)
                        }
                        autoOver = data
                    }
                }
            }
        })
        if (autoOver) {
            return this.completeRewardNullTask(autoOver.cfg.id)
        }
        if (bolSort) {
            this.sortCanDoTasks()
        }
        if (bolUpdate) {
            eventCenter.emit(EventType.TASK_UPDATE, bolSort)
        }
    }
    private getDataFromCanDo(id: number | string) {
        let ary = this.getCanDoTasks()
        for (let i = 0; i < ary.length; i++) {
            let data = ary[i];
            if (data.cfg.id === id) {
                let nextDt = ary[i + 1]
                let nextId = nextDt ? nextDt.cfg.id : null
                return { data, nextId }
            }
        }
    }
    private markTaskComplete(cfg: TaskCfg) {
        let key = cfg.id
        this.setTaskComplete(key)
        this.delTaskKeyProgress(key)
        this.setCanDoTasks(cfg)
        this.sortCanDoTasks()
    }
    /**
     * 当有任务被移除（完成）时：
     * 该任务为系列任务，且非系列任务的最后一条：
     *     自动打开下一条系列任务的任务详情
     * 该任务非系列任务，或是系列任务的最后一条：
     *     该任务非列表中最后一条任务：打开列表中的下一条任务
     *     该任务为列表中最后一条任务：打开列表中的第一条任务
    */
    private calculateNextId(id: number | string, nextId?: number | string) {
        let ary = this.getCanDoTasks()
        for (const data of ary) {
            if (data.cfg.preTask === id) {
                return data.cfg.id//系列任务
            }
        }
        if (nextId) {
            return nextId//下一条
        }
        let first = ary[0]
        if (first) {
            return first.cfg.id//第一条
        }
    }
    private testPreTask(id: string) {
        let cfg = assetsMgr.getJsonData<TaskCfg>('Task', id)
        if (!cfg || !cfg.preTask) return
        this.aryTaskComplete.unshift(cfg.preTask)
        this.testPreTask(cfg.preTask)
    }
    public test14() {
        this.aryTaskComplete = []
        this.testPreTask('1003_2')
        this.aryChangeDic()
        this.setCanDoTasks()
        this.sortCanDoTasks()
    }

    public isHutaoBedComplete() {
        let task = assetsMgr.getJson<TaskCfg>('Task').datas.find(task => task.mark == "HUTAO_BED")
        if (!task) return
        return this.isTaskComplete(task.id)
    }
}
