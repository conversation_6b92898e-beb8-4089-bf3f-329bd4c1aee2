import Observer from "../../../core/utils/Observer"
import { IdTargets, Trigger } from "../../common/constant/DataType"
import { ConditionType, TaskTriggerType, TaskType } from "../../common/constant/Enums"
import { gameHelper } from "../../common/helper/GameHelper"
import { unlockHelper } from "../../common/helper/UnlockHelper"
import { registerTaskEvent } from "./TaskObject"

type DataIdxNum = {
    [tagIdx: string]: number//target数组索引(从0开始):进度(number)
}

/**任务数据基类*/
export default abstract class TaskBaseModel extends mc.BaseModel {
    protected aryTaskComplete: (string)[] = []
    protected dicTaskComplete: { [id: string]: number } = {}
    protected keyTaskProgress: { [id: string]: DataIdxNum } = {}

    public data: proto.IAchievementInfo | proto.ITaskInfo = null

    public init() {
        this.initListener()
        this.fromDB()
        return this
    }
    protected initListener() {
        registerTaskEvent(this)
    }
    protected fromDB() {
        this.syncTaskComplete()
        this.syncTaskProgress()
    }

    protected syncTaskComplete() {
        this.aryTaskComplete = this.data.completes || []
        this.aryChangeDic()
    }
    protected syncTaskProgress() {
        for (let { id, targets } of this.data.tasks) {
            this.syncOneTargets(id, targets)
        }
    }
    protected syncOneTargets(id: string, targets: proto.ITaskTarget[]) {
        for (let target of targets) {
            this.addTaskKeyProgress(id, target.id, target.num)
        }
    }
    protected aryChangeDic() {
        let dic = {}
        let ary = this.aryTaskComplete
        ary.forEach((key, index) => {
            dic[key] = index
        })
        this.dicTaskComplete = dic
    }
    public isTaskComplete(id: string) {
        return this.dicTaskComplete[id] != null
    }
    protected setTaskComplete(id: string) {
        this.aryTaskComplete.push(id)
        this.dicTaskComplete[id] = this.aryTaskComplete.length - 1
    }
    public setTaskKeyProgress(key: string, id: string, num: number) {
        let dic = this.keyTaskProgress[key]
        if (!dic) {
            Observer.addKey(this.keyTaskProgress, key, { [id]: num })
        } else {
            this.setNumProgress(dic, id, num)
        }
    }
    public addTaskKeyProgress(key: string, id: string, addNum: number) {
        let dic = this.keyTaskProgress[key]
        if (!dic) {
            Observer.addKey(this.keyTaskProgress, key, { [id]: addNum })
        } else {
            this.addNumProgress(dic, id, addNum)
        }
    }
    public getTaskKeyProgress(key: string, id: string) {
        let dic = this.keyTaskProgress[key]
        return dic && dic[id]
    }
    public getTaskProgressNum(key: string, id: string): number {
        return this.getTaskKeyProgress(key, id) || 0
    }
    public getTaskProgressCheckMax(key: string, id: string, max: number): number {
        let cur = this.getTaskProgressNum(key, id)
        return cur > max ? max : cur
    }
    protected delTaskKeyProgress(key: string) {
        if (this.keyTaskProgress[key]) Observer.delKeys(this.keyTaskProgress, key)
    }
    protected setNumProgress(dic: DataIdxNum, key: string, num: number) {
        let old = dic[key]
        if (old == null) {
            Observer.addKey(dic, key, num)
        } else {
            dic[key] = num
        }
    }
    protected addNumProgress(dic: DataIdxNum, key: string, addNum: number) {
        let old = dic[key]
        if (old == null) {
            Observer.addKey(dic, key, addNum)
        } else {
            dic[key] = old + addNum
        }
    }
    protected getNumProgress(dic: DataIdxNum, key: string) {
        return dic[key] || 0
    }
    protected checkCanTrigger(trigger: Trigger) {
        if (!trigger) return true
        let id = trigger.id
        switch (trigger.type) {
            case TaskTriggerType.REACH_PLANET:
                return gameHelper.planet.getPlanet(id)?.getCurMap()?.showLandAnim
            case TaskTriggerType.COMPLATE_PLANET:
                return gameHelper.planet.getPlanet(id)?.isDone()
            case TaskTriggerType.COMPLATE_PLANET_ID:
                return gameHelper.planet.isPassNode(id, true)
            case TaskTriggerType.BUILT_CARRIAGE:
                return gameHelper.train.isBuiltCarriage(id)
            case TaskTriggerType.UNLOCKFUNC:
                return unlockHelper.isUnlockFuncByMisc(id)
            case TaskTriggerType.GUIDE_UNLOCKFUNC:
                return unlockHelper.isGuideOverByUnlockFunc(id)
            case TaskTriggerType.GUIDE_COMPLATE_ID:
                return gameHelper.guide.isStepComplete2(id)
            case TaskTriggerType.COMPLATE_BATTLE:
                return gameHelper.planet.isPassBattle(id)
            case TaskTriggerType.COMPLATE_THEME:
                let [carriageId, themeLv] = id.split('-')
                return gameHelper.train.getCarriageById(Number(carriageId))?.isAllBuildsMaxLv(Number(themeLv))
            default:
                twlog.error('checkCanTrigger unknow trigger', trigger)
                return false
        }
    }
    public getIdInTarget0(cfg: IdTargets) {
        let tOne = this.getOnlyOneTarget(cfg)
        if (tOne && tOne.id) {
            return tOne.id
        }
        twlog.error('target0没有id', cfg)
        return
    }
    public getNumInTarget0(cfg: IdTargets) {
        let tOne = this.getOnlyOneTarget(cfg)
        if (tOne && tOne.num) {
            return tOne.num
        }
        twlog.error('target0没有num', cfg)
        return NaN
    }
    public getOnlyOneTarget(cfg: IdTargets) {
        let tags = cfg.target
        if (tags.length > 1) {
            twlog.error('居然有多个target', cfg)
        }
        return tags[0]
    }
    public checkCompleteAllTarget(cfg: IdTargets) {
        let ary = cfg.target
        for (let i = 0; i < ary.length; i++) {
            let oneTag = ary[i]
            let max = oneTag.num
            let cur = this.getTaskProgressNum(cfg.id, String(i))
            if (cur < max) {
                return false
            }
        }
        return true
    }
    public getCurMaxAllTarget(cfg: IdTargets, checkMax?: boolean) {
        let curTotal = 0, maxTotal = 0
        cfg.target.forEach((oneTag, idx) => {
            let max = oneTag.num
            maxTotal += max
            curTotal += checkMax ? this.getTaskProgressCheckMax(cfg.id, String(idx), max) : this.getTaskProgressNum(cfg.id, String(idx))
        })
        return { cur: curTotal, max: maxTotal }
    }
    public getCurMaxTarget0(cfg: IdTargets) {
        let cur = this.getTaskProgressNum(cfg.id, '0')
        let max = this.getNumInTarget0(cfg)
        return { cur, max }
    }
    public abstract onAddCommon(giveType: TaskType, conditionFun?: (cfg: IdTargets) => Boolean, addFun?: (cfg: IdTargets) => void): void
    public onAddByOneTagId(giveType: TaskType, tagId: number | string) {
        this.onAddCommon(giveType, (cfg) => {
            return this.checkConditionId(cfg, tagId)
        })
    }
    public onAddByNum(giveType: TaskType, addNum: number) {
        if (addNum <= 0) return
        this.onAddCommon(giveType, null, (cfg) => {
            this.addTaskKeyProgress(cfg.id, '0', addNum)
        })
    }
    public onAddCurrency(addNum: number, type: ConditionType) {
        if (addNum <= 0) return
        this.onAddCommon(TaskType.GET_ITEM, (cfg) => {
            return this.checkConditionType(cfg, type)
        }, (cfg) => {
            let idx = this.getTargetIdxByType(cfg, type)
            this.addTaskKeyProgress(cfg.id, String(idx), addNum)
        })
    }
    public onChangeProp(id: number, addNum: number) {
        if (addNum <= 0) return
        this.onAddCommon(TaskType.GET_ITEM, (cfg) => {
            return this.checkConditionId(cfg, id)
        }, (cfg) => {
            let idx = this.getTargetIdxById(cfg, id)
            this.addTaskKeyProgress(cfg.id, String(idx), addNum)
        })
    }
    public checkConditionId(cfg: IdTargets, tagId: any) {
        return this.getTargetIdxById(cfg, tagId) != null
    }
    public checkConditionType(cfg: IdTargets, tagType: ConditionType) {
        return this.getTargetIdxByType(cfg, tagType) != null
    }
    public getTargetIdxById(cfg: IdTargets, tagId: any) {
        let ary = cfg.target
        for (let i = 0; i < ary.length; i++) {
            let oneTag = ary[i]
            if (oneTag.id === tagId) {
                return i
            }
        }
    }
    public getTargetIdxByType(cfg: IdTargets, tagType: ConditionType) {
        let ary = cfg.target
        for (let i = 0; i < ary.length; i++) {
            let oneTag = ary[i]
            if (oneTag.type === tagType) {
                return i
            }
        }
    }

}