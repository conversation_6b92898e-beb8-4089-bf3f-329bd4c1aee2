import { util } from "../../../core/utils/Utils"
import { Msg } from "../../../proto/msg-define"
import { MarkNewType, NPC_ID, UIFunctionType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"

@mc.addmodel("tower")
export default class TowerModel extends mc.BaseModel {

    private recoverSurpluTime: number = 0
    private recoverEndTime = 0

    public checkPointId: string = null
    public layer: number = 0
    public index: number = 0

    public isDone: boolean = false
    public isOpen: boolean = false

    public data: proto.ITower = null

    public init() {
        this.fromDB(this.data)

        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.PLAY_TOWER) {
            }
        })
    }

    private fromDB(data: proto.ITower) {
        this.updateRecoverSurpluTime(this.recoverSurpluTime)
        this.setCheckPoint(data.checkPointId)
        this.isDone = data.isDone
    }

    public getMaxEnergy() {
        return cfgHelper.getMiscData("towerPower")?.max || 0
    }

    public getRecoverSpeed() {
        return cfgHelper.getMiscData("towerPower")?.recoverSpeed || 0
    }

    update() {
        
    }

    private updateRecoverSurpluTime(time = 0) {
        this.recoverSurpluTime = time
        this.recoverEndTime = gameHelper.now() + time
    }

    public getRecoverSurplusTime() {
        return Math.max(0, this.recoverEndTime - gameHelper.now())
    }

    public async startBattle() {
        let msg = new proto.C2S_TowerBattleMessage()
        let res = await gameHelper.net.request(Msg.C2S_TowerBattleMessage, msg)
        const { code } = proto.S2C_TowerBattleMessage.decode(res)
        if (code == 0) {
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public async battleWin(rewards) {
        let msg = new proto.C2S_TowerBattleWinMessage()
        let res = await gameHelper.net.request(Msg.C2S_TowerBattleWinMessage, msg)
        const { code, tower } = proto.S2C_TowerBattleWinMessage.decode(res)
        if (code == 0) {
            gameHelper.grantRewards(rewards)
            this.fromDB(tower)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public setCheckPoint(id) {
        this.checkPointId = id
        let [_layer, _index] = this.checkPointId.split("-")
        this.layer = Number(_layer), this.index = Number(_index)
    }
}