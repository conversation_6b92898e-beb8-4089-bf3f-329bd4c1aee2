import { Msg } from "../../../proto/msg-define";
import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import Mail from "./Mail";

const { ccclass, property } = cc._decorator;

@mc.addmodel("mail")
export default class MailModel extends mc.BaseModel {


    private mailList: Mail[] = []
    private billboardList: Mail[] = []

    public data: proto.IMailInfo[] = []

    public getMailList() { return this.mailList }
    public getBillboardList() { return this.billboardList }


    public init() {
        //监听新邮件
        gameHelper.net.on(Msg.S2C_OnNewMailMessage, async (msg) => {
            const { mail } = msg
            console.log("-----------------------------------------------------\n收到邮件\n-------------------------------------------------------------")
            this.mailList.push(new Mail().initMail(mail))
            eventCenter.emit(EventType.MAIL_REFRESH_VIEW)
            eventCenter.emit(EventType.MAIL_REFRESH_DETAIL)
        }, this)

        this.initRedDot()
    }

    private async initRedDot() {
        //初始化红点
        let msg = new proto.C2S_MailListMessage({ type: 1 })
        const res = await gameHelper.net.request(Msg.C2S_MailListMessage, msg, true)
        const { mail } = proto.S2C_MailListRespMessage.decode(res)
        if (mail.length > 0) {
            this.mailList.push(new Mail().initRedDotMail())
        }
    }

    public async deleteMail() {
        let msg = new proto.C2S_DeleteReadMailMessage()
        const res = await gameHelper.net.request(Msg.C2S_DeleteReadMailMessage, msg, true)
        const { code } = proto.S2C_DeleteReadMailRespMessage.decode(res)
        if (code == 0) {
            //刷新视图
            this.mailList = this.mailList.filter(mail => !mail.getIsRead() || mail.canAttach())
            eventCenter.emit(EventType.MAIL_REFRESH_VIEW)
            eventCenter.emit(EventType.MAIL_REFRESH_DETAIL)
        }
        else {
            viewHelper.showNetError(code)
            await ut.wait(10) //网挂了才走这里
            return false
        }
    }

    public async attachAllMail() {
        let msg = new proto.C2S_AttachMailMessage({ mailId: '-1' })
        const res = await gameHelper.net.request(Msg.C2S_AttachMailMessage, msg, true)
        const { code, rewards, ids } = proto.S2C_AttachMailRespMessage.decode(res)
        if (code == 0) {
            let reward = gameHelper.toConditions(rewards)
            let mailIds = ids
            gameHelper.grantRewardAndShowUI(reward)
            this.mailList.forEach((mail) => {
                let haveReward = mailIds.find(id => id == mail.getId())
                if (!!haveReward) {
                    mail.setIsAttach(true)
                    mail.setIsRead(true)
                }
            })
            eventCenter.emit(EventType.MAIL_REFRESH_VIEW)
            eventCenter.emit(EventType.MAIL_REFRESH_DETAIL)
            return true
        }
        else {
            viewHelper.showNetError(code)
            await ut.wait(10) //网挂了才走这里
            return false
        }
    }

    public async initMailList() {
        let msg = new proto.C2S_MailListMessage()
        const res = await gameHelper.net.request(Msg.C2S_MailListMessage, msg, true)
        const { mail } = proto.S2C_MailListRespMessage.decode(res)
        this.mailList.splice(0, this.mailList.length)
        mail?.forEach((val) => {
            this.mailList.push(new Mail().initMail(val))
        })
    }

    public async initBillboardList() {
        const res = await gameHelper.net.post("system/gameNotice", {})
        console.log(res)
        this.billboardList.splice(0, this.billboardList.length)
        res?.data?.forEach((val) => {
            this.billboardList.push(new Mail().initBillboard(val))
        })
    }

    public checkRedDot() {
        let res = false
        this.mailList.forEach((mail) => {
            res ||= mail.checkRedDot()
        })
        return res
    }
}
