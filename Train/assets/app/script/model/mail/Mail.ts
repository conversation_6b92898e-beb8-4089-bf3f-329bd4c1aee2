import { Msg } from "../../../proto/msg-define";
import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../common/ConditionObj";


const { ccclass, property } = cc._decorator;

@ccclass
export default class Mail {
    protected id: string = null
    protected title: string = null
    protected timeNumber: number = null
    protected time: string = null
    protected content: string = null
    public rewards: ConditionObj[] = [] //奖励
    protected isRead: boolean = false
    protected isAttach: boolean = false

    public getId() { return this.id }
    public getTitle() { return this.title }
    public getTime() { return this.time }
    public getContent() { return this.content }
    public getRewards() { return this.rewards }
    public getIsRead() { return this.isRead }
    public getIsAttach() { return this.isAttach }

    public setIsAttach(val) {
        this.isAttach = val
    }
    public setIsRead(val) {
        this.isRead = val
    }

    public initRedDotMail() {
        this.isRead = false
        return this
    }

    public initMail(data: proto.IMailInfo) {
        this.id = data.id
        this.timeNumber = data.time as number
        this.title = data.title
        this.content = data.content
        this.rewards = gameHelper.toConditions(data.rewards)
        this.isRead = data.read
        this.isAttach = data.attach
        this.time = ut.dateFormat('yyyy/MM/dd hh:mm', this.timeNumber)
        return this
    }

    public initBillboard(data) {
        this.title = data.Title
        this.id = data.ShowTime
        this.content = data.Content
        this.time = ut.dateFormat('yyyy/MM/dd hh:mm', this.timeNumber)
        return this
    }

    public canAttach() {
        return !this.isAttach
    }

    public checkRedDot() {
        return !this.isRead || this.canAttach()
    }

    public async getMail() {
        let msg = new proto.C2S_MailDetailMessage({ mailId: this.id })
        const res = await gameHelper.net.request(Msg.C2S_MailDetailMessage, msg, true)
        const { code, mail } = proto.S2C_MailDetailRespMessage.decode(res)
        if (code == 0) {
            this.initMail(mail)
            eventCenter.emit(EventType.MAIL_REFRESH_VIEW)
            eventCenter.emit(EventType.MAIL_REFRESH_DETAIL, true)
            return true
        }
        else {
            viewHelper.showNetError(code)
            await ut.wait(10) //网挂了才走这里
            return false
        }
    }

    public async attachMail() {
        let msg = new proto.C2S_AttachMailMessage({ mailId: this.id })
        const res = await gameHelper.net.request(Msg.C2S_AttachMailMessage, msg, true)
        const { code } = proto.S2C_AttachMailRespMessage.decode(res)
        if (code == 0) {
            gameHelper.grantRewardAndShowUI(this.rewards)
            this.isAttach = true
            eventCenter.emit(EventType.MAIL_REFRESH_VIEW)
            eventCenter.emit(EventType.MAIL_REFRESH_DETAIL)
            return true
        }
        else {
            viewHelper.showNetError(code)
            await ut.wait(10) //网挂了才走这里
            return false
        }
    }


}
