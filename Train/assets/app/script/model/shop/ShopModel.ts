import { db<PERSON>elper } from '../../common/helper/DatabaseHelper';

@mc.addmodel('Shop')
export default class ShopModel extends mc.BaseModel {
    private rechargeRecord: { [key: string]: number }[] = [];

    public init() {
    }

    private fromDB(data: any) {
        this.rechargeRecord = data.rechargeRecord || [];
    }

    public rechargeSucc(val: number) {
        // todo 暂时只保留当月的
        if (this.rechargeRecord.length) {
            let last = this.rechargeRecord.last();
            if (new Date(last.time).getMonth() != new Date().getMonth()) {
                this.rechargeRecord = []
            }
        }
        this.rechargeRecord.push({ val: val, time: ut.now(), state: 0 });
    }

    public getRechargeCountByMonth(month: number = new Date().getMonth()) {
        let result: number = 0
        for (let i = 0; i < this.rechargeRecord.length; i++) {
            let record = this.rechargeRecord[i];
            if (new Date(record.time).getMonth() == month)
                result += record.val;
        }
        return result;
    }
}