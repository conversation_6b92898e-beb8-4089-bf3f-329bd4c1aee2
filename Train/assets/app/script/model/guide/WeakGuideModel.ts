import ViewCtrlMgr from "../../../core/manage/ViewCtrlMgr"
import { CarriageID, ConditionType, DormBuildType, WeakGuideType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { gameHelper } from "../../common/helper/GameHelper"
import DormModel from "../train/dorm/DormModel"
import { animHelper } from "../../common/helper/AnimHelper"
import { DORM_RIGHT_BED_ID } from "../../common/constant/Constant"
import { BuildCfg, TrainCfg, WeakGuideCfg } from "../../common/constant/DataType"
import { viewHelper } from "../../common/helper/ViewHelper"
import { cfgHelper } from "../../common/helper/CfgHelper"

export class WeakGuideObj {

    public id: WeakGuideType = null
    public delay: number = 0
    private cfg: WeakGuideCfg = null
    public isShow: boolean = false

    public init(cfg: WeakGuideCfg) {
        this.cfg = cfg
        this.id = cfg.id
        this.delay = cfg.delay || 0
        return this
    }

    public get checkFunc() { return this.cfg.check.func }
    public get fingerGuide() { return this.cfg.guide }

    public show() {
        this.isShow = true
        eventCenter.emit(EventType.SHOW_WEAK_GUIDE, this)
    }

    public reset() {
        this.isShow = false
        this.delay = this.cfg.delay || 0
    }
}

class WeakGuideBusiness extends mc.BaseModel {
    // ------------------------ 去建造 ------------------------
    /** 第几步(0表示结束) */
    public buyBuildStep: number = 0
    public buyBuildData: BuildCfg = null
    protected gotoBuyBuild1(guide: WeakGuideObj) {
        return this.buyBuildStep == 1 && this.isMainScene()
    }
    protected gotoBuyBuild2(guide: WeakGuideObj) {
        return this.buyBuildStep == 2 && this.isMainScene() && viewHelper.checkPnlEnter('train/SelectBuildPnl')
    }
    public guideBuyBuild(cfg: BuildCfg) {
        this.buyBuildStep = 1
        this.buyBuildData = cfg
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    /** 第几步(0表示结束) */
    public levelUpBuildStep: number = 0
    public levelUpBuildData: { carriageId: number, order?: number } = null
    protected gotoLevelUpBuild1(guide: WeakGuideObj) {
        return this.levelUpBuildStep == 1 && this.isMainScene()
    }
    protected gotoLevelUpBuild2(guide: WeakGuideObj) {
        return this.levelUpBuildStep == 2 && this.isMainScene() && viewHelper.checkPnlEnter('train/SelectBuildPnl')
    }
    public guideLevelUpBuild(cfg: { carriageId: number, order?: number }) {
        this.levelUpBuildStep = 1
        this.levelUpBuildData = cfg
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    /** 第几步(0表示结束) */
    public buyTrainStep: number = 0
    public buyTrainData: TrainCfg = null
    protected gotoBuyTrain1(guide: WeakGuideObj) {
        return this.buyTrainStep == 1 && this.isMainScene()
    }
    protected gotoBuyTrain2(guide: WeakGuideObj) {
        return this.buyTrainStep == 2 && this.isMainScene() && viewHelper.checkPnlEnter('train/TrainInformationPnl')
    }
    protected gotoBuyTrain3(guide: WeakGuideObj) {
        return this.buyTrainStep == 3 && this.isMainScene() && viewHelper.checkPnlEnter('train/TrainConsolePnl')
    }
    public guideBuyTrain(cfg: TrainCfg) {
        this.buyTrainStep = 1
        this.buyTrainData = cfg
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    public assignWorkStep: number = 0
    public assignWorkData: number = 0
    protected assignWork1(guide: WeakGuideObj) {
        return this.assignWorkStep == 1 && this.isMainScene()
    }
    public guideAssignWork(trainId: number) {
        this.assignWorkStep = 1
        this.assignWorkData = trainId
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    public goPlanetStep: number = 0
    public goPlanetData: number = 0
    protected goPlanet1(guide: WeakGuideObj) {
        return this.goPlanetStep == 1 && this.isMainScene()
    }
    protected goPlanet2(guide: WeakGuideObj) {
        return this.goPlanetStep == 2 && this.isMainScene() && viewHelper.checkPnlEnter('planet/PlanetChoose')
    }
    public guideGoPlanet(planetId: number) {
        this.goPlanetStep = 1
        this.goPlanetData = planetId
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    public characterGetOnStep: number = 0
    public characterGetOnData: number = 0
    protected characterGetOn1(guide: WeakGuideObj) {
        return this.characterGetOnStep == 1 && this.isMainScene()
    }
    protected characterGetOn2(guide: WeakGuideObj) {
        return this.characterGetOnStep == 2 && this.isMainScene() && viewHelper.checkPnlEnter('role/RolePnl')
    }
    public guideCharacterGetOn(roleId: number) {
        this.characterGetOnStep = 1
        this.characterGetOnData = roleId
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    public characterStep: number = 0
    protected character1(guide: WeakGuideObj) {
        return this.characterStep == 1 && this.isMainScene()
    }
    public guideCharacter() {
        this.characterStep = 1
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    public jackpotStep: number = 0
    protected jackpot1(guide: WeakGuideObj) {
        return this.jackpotStep == 1 && this.isMainScene()
    }
    public guideJackpot() {
        this.jackpotStep = 1
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    public exploreStep: number = 0
    protected explore1(guide: WeakGuideObj) {
        return this.exploreStep == 1 && this.isMainScene()
    }
    public guideExplore() {
        this.exploreStep = 1
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
        this.gotoMain()
    }

    public planetControlStep: number = 0
    protected planetControl1(guide: WeakGuideObj) {
        return this.planetControlStep == 1 && viewHelper.checkPnlEnter('common/PlanetUI')
    }
    public guidePlanetControl() {
        this.planetControlStep = 1
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
    }

    public backToPlanetStep: number = 0
    protected backToPlanet1(guide: WeakGuideObj) {
        return this.backToPlanetStep == 1 && gameHelper.guide.logic.isPlanetEntry()
    }

    public guideBackToPlanet() {
        this.backToPlanetStep = 1
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
    }

    public ToolMakeStep: number = 0
    public ToolMakeData?: number = null
    protected ToolMake1(guide: WeakGuideObj) {
        return this.ToolMakeStep == 1
    }
    public guideToolMake(typeId?: number) {
        this.ToolMakeStep = 1
        this.ToolMakeData = typeId
        eventCenter.emit(EventType.HIDE_TASK_DETAIL)
    }

    protected buyRightBed(guide: WeakGuideObj) {
        if (!this.isMainScene()) return
        if (this.hasRightBed()) return
        if (!gameHelper.guide.logic.checkCanUnlockRightBed()) return
        let viewMgr = mc.getViewMgr() as ViewCtrlMgr
        let pnls = ["train/SelectBuildPnl", "train/BuildDetailPnl"]
        let id = guide.id
        if (id == WeakGuideType.BUY_RIGHT_BED_1) {
            return !viewMgr.__getForCache(pnls[0]) && !viewMgr.__getForCache(pnls[1])
        }
        else if (id == WeakGuideType.BUY_RIGHT_BED_2) {
            return viewMgr.__getForCache(pnls[0])?.getActive() && !viewMgr.__getForCache(pnls[1])
        }
        else if (id == WeakGuideType.BUY_RIGHT_BED_3) {
            let pnl: any = viewMgr.__getForCache(pnls[1])
            if (pnl && pnl.getActive() && pnl.getId() == DORM_RIGHT_BED_ID) {
                return true
            }
        }
    }

    protected collectStar() {
        if (!this.isMainScene()) return
        let carriage = gameHelper.train.getCarriageById(CarriageID.DORM) as DormModel
        let bed = carriage?.getRightBed()
        if (bed) return
        let type = ConditionType.STAR_DUST
        let cur = gameHelper.getCurrency(type)
        let cfg = cfgHelper.getBuildLvCfg(carriage.getID(), DormBuildType.RIGHT_BED, 1)
        let cost = cfg.buyCost.find(c => c.type == type)
        let num = cost.num
        if (cur >= num) return
        let sum = carriage.getAllDrops().filter(drop => drop.item.type == type).reduce(((sum, drop) => drop.getNum() + sum), 0)
        return cur + sum >= num
    }

    protected hasRightBed() {
        let carriage = gameHelper.train.getCarriageById(CarriageID.DORM) as DormModel
        return !!carriage?.getRightBed()
    }

    protected isMainScene() {
        return mc.currWindName == "main"
    }
    private gotoMain() {
        if (this.isMainScene()) return
        viewHelper.gotoWind('main')
    }
}

@mc.addmodel('weakGuide')
export default class WeakGuideModel extends WeakGuideBusiness {

    public show: boolean = false
    public uid: number = 0
    public finger: cc.Node = null
    private isStart: boolean = false

    private guideList: WeakGuideObj[] = []
    public curGuide: WeakGuideObj = null

    // 初始化
    public init() {
        // const data = dbHelper.register('weakGuide', 1, this.toDB, this) || {}
        this.initGuideList()
    }

    public checkStart() {
        this.isStart = true
    }

    // 获取教程配置
    private get guideConfig() {
        return assetsMgr.getJson<WeakGuideCfg>('_WeakGuide')
    }

    // 获取教程模块信息
    public getGuideInfo(type: WeakGuideType) {
        return this.guideList.find(m => m.id === type)
    }

    private initGuideList() {
        let config = this.guideConfig.datas
        for (let idx in config) {
            let cfg = config[idx]
            let mod = this.getGuideInfo(cfg.id)
            if (!mod) {  // 全新模块
                mod = new WeakGuideObj().init(cfg)
                this.guideList.push(mod)
            }
        }
    }

    update(dt) {
        if (!this.isStart) return
        if (gameHelper.guide.isWorking()) {
            return this.cancel()
        }

        let curGuide = this.curGuide
        if (curGuide) {
            if (this.checkTrigger(curGuide)) {
                if (curGuide.delay > 0) {
                    curGuide.delay -= dt
                }
                else {
                    if (!curGuide.isShow) {
                        curGuide.show()
                    }
                }
            }
            else {
                this.cancel()
            }
        }

        this.checkTriggers()
    }

    public cancel() {
        if (!this.curGuide) return
        this.curGuide.reset()
        this.curGuide = null
        animHelper.removeWeakGuideFinger()
    }

    private checkTriggers() {
        for (let guide of this.guideList) {
            if (this.checkTrigger(guide)) {
                break
            }
        }
    }

    private checkTrigger(guide: WeakGuideObj) {
        let target = this
        let checkFunc = guide.checkFunc
        let succ = target[checkFunc](guide)
        if (succ) {
            this.curGuide = guide
            return true
        }
    }
}
