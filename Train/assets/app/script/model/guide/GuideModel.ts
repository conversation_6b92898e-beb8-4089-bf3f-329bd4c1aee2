import { GuideStepMark } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import GuideInfoObj from "./GuideInfoObj"
import GuideLogic from "./GuideLogic"

@mc.addmodel('guide', 300)
export default class GuideModel extends mc.BaseModel {

    private guideId: number = 0     // 当前模块配置id
    private guideList: GuideInfoObj[] = []  // 引导模块信息

    private _isSkip: boolean = false

    public get curGuide() {
        return this.getGuideInfo(this.guideId)
    }

    public logic: GuideLogic = null
    private stateLoad: number = 0//0未加载1加载中2已加载

    public data: { guideId: number, guideList: proto.IGuideInfo[] } = null

    public init() {
        if (gameHelper.isFakeGuide) return
        const data = this.data
        this.logic = new GuideLogic()
        this.guideId = data.guideId || 0
        this.guideList = (data.guideList || []).map(m => new GuideInfoObj().fromDB(m))
        this.initGuides()
    }

    public timeBackOn(id: number, list: proto.IGuideInfo[]) {
        this.client2Data()
        this.loadByData(id, list)
    }
    public timeBackOff() {
        this.loadByData(this.data.guideId, this.data.guideList)
    }
    private cleanGuideObj() {
        this.guideList.forEach(m => m.clean())
    }
    private client2Data() {
        this.data.guideId = this.guideId
    }
    private loadByData(id: number, list: proto.IGuideInfo[]) {
        this.logic.offAllEvent()
        this.cleanGuideObj()
        this.guideId = id
        list.forEach(m => {
            this.getGuideInfo(m.id).fromDB(m)
        })
        this.initGuides()
    }
    public addKeyStep(id: number, stepId: number) {
        let list = this.data.guideList
        let info = list.find(m => m.id == id)
        if (!info) {
            info = new proto.GuideInfo({ id, keySteps: [] })
            list.push(info)
        }
        info.keySteps.push(stepId)
    }

    // 获取教程配置
    public get guideConfig() {
        return cfgHelper.getGuideCfg()
    }

    // 获取教程模块信息
    public getGuideInfo(id: number) {
        return this.guideList.find(m => m.id === id)
    }

    public getGuides() {
        return this.guideList
    }

    public isFinish(id: number) {
        return this.guideList.find(m => m.id === id).isFinish()
    }

    public nextUnfinishedId() {
        return this.guideList.find(m => !m.isFinish())?.id
    }

    // 初始化教程
    private initGuides() {
        let config = this.guideConfig

        //删除已废弃教程
        for (let i = this.guideList.length - 1; i >= 0; i--) {
            let id = this.guideList[i].id
            if (!config[id]) {
                this.guideList.splice(i, 1)
            }
        }

        for (let key in config) {
            let id = Number(key)
            let module = this.getGuideInfo(id)
            if (!module) {  // 全新模块
                module = new GuideInfoObj().init(id, -1)
                this.guideList.push(module)
            }
        }

        let guides = this.sortGuides(this.guideList.slice())
        for (let guide of guides) {
            guide.initStep()
        }

        if (this.guideId && !this.curGuide) {
            this.guideId = 0
        }
    }

    // 跳过引导
    public skip() {
        this._isSkip = true
        this.curGuide.skip()
    }

    // 获取当前id
    public getId() {
        return this.guideId
    }

    // 是否跳过了当前教程
    public isSkip() { return this._isSkip }

    public isWorking() { return this.curGuide?.isWorking() || this.stateLoad < 2 }

    public checkStart() {
        if (this.guideId) {
            let guide = this.getGuideInfo(this.guideId)
            if (guide.isFinish()) { //已完成，重新选择教程模块
                this.nextGuide()
            }
            else if (guide.checkStart()) {
                this.start(this.guideId)
            }
        } else {
            let id = this.getValidGuideId()
            id && this.start(id)
        }
    }

    public checkStop() {
        if (this.isWorking()) {
            let succ = this.curGuide.checkStep(this.curGuide.getCurStep())
            if (!succ) {
                console.log("stop!!!", this.curGuide.getCurStep())
                this.stop()
            }
        }
    }

    public stop() {
        if (this.curGuide) {
            this.curGuide.stop()
        }
    }

    // 获取有效的教程id
    private getValidGuideId() {
        if (this.isWorking()) return null
        let guides = this.guideList.filter(m=>!m.isFinish())
        guides = this.sortGuides(guides)
        for (let guide of guides) {
            if (guide.checkStart()) {
                return guide.id
            }
        }
        return null
    }

    private sortGuides(guides: GuideInfoObj[]) {
        return guides.sort((a, b) => {
            // 获取步骤数组
            const stepsA = a.getSteps();
            const stepsB = b.getSteps();
            
            // 安全地获取优先级
            const priorityA = stepsA.length > 0 ? (stepsA[0].priority || 0) : 0;
            const priorityB = stepsB.length > 0 ? (stepsB[0].priority || 0) : 0;
            
            // 如果优先级相同，使用 guide ID 作为次要排序条件
            if (priorityB === priorityA) {
                return a.id - b.id;
            }
            
            return priorityB - priorityA;
        });
    }

    public getGuidingId() {
        return this.guideId || this.nextUnfinishedId()
    }

    public async preloadGuide() {
        if (this.stateLoad == 1) return
        this.stateLoad = 1
        let list: any = ['guide/Guide', 'common/Plot']
        list = list.filter((key) => {
            return !mc.getPnl(key)
        }).map(key => viewHelper.preloadPnl(key))

        if (list.length > 0) {
            this.emit(mc.Event.LOAD_BEGIN_PNL)
            await Promise.all(list)
            this.emit(mc.Event.LOAD_END_PNL)
        }
        this.stateLoad = 2
    }

    // 开始教程
    public start(id: number, progress?) {
        if (this.stateLoad < 2) return
        if (this.isWorking()) return
        this._isSkip = false // 开始新的教程重置跳过判断
        this.guideId = id

        viewHelper.showPnl('guide/Guide')
        if (this.curGuide) {
            this.curGuide.start(progress)
        }
    }

    // 下一个教程
    public nextGuide() {
        this.guideId = 0

        let id = this.getValidGuideId()
        id && this.start(id)
    }

    public isStepEnd(key: GuideStepMark) {
        for (let guide of this.guideList) {
            if (guide.isStepEnd(key)) return true
        }
        return false
    }

    public isStepEndByUnlockFunc(key) {
        for (let guide of this.guideList) {
            if (guide.isStepEndByUnlockFunc(key)) return true
        }
        return false
    }

    public isStepComplete(id: number, index: number) {
        let module = this.getGuideInfo(id)
        if (module) {
            return module.isStepComplete(index)
        }
        twlog.error("isStepComplete no module", id)
        return false
    }
    public isStepComplete2(cfgId: number) {
        let id = Math.floor(cfgId / 100)
        let module = this.getGuideInfo(id)
        if (module) {
            let index = module.getIndexById(cfgId)
            if (index != null) {
                return module.isStepComplete(index)
            } else {
                twlog.error("isStepComplete2 no index", cfgId)
            }
        } else {
            twlog.error("isStepComplete2 no module", id)
        }
        return false
    }

    //在胡桃夹子睡觉教程期间不能去永恒花园
    public needForbidGoGarden2() {
        if (!gameHelper.openGuide) return false
        return this.isStepEnd(GuideStepMark.BUILD_BED_START) && !this.isStepEnd(GuideStepMark.BUILD_BED_END)
    }

    // 新手引导期间，模拟时间特殊处理
    public setGuideTime(cfgId: number) {
        let time = this.getGameTimeByCfgId(cfgId)
        if (time) {
            gameHelper.world.setTime(time.hour * ut.Time.Hour + time.min * ut.Time.Minute)
        }
    }
    // 假时间 展示用
    public getGuideTime() {
        let id = this.getGuidingId()
        let time = this.getGameTimeByGuide(id)
        if (time) {
            let num = gameHelper.world.toRealSecond(time.hour * ut.Time.Hour + time.min * ut.Time.Minute)
            gameHelper.world.setGuideTime(num)
            return num
        }
        twlog.error('getGuideTime fail', id)
        return gameHelper.world.time
    }
    // 当前引导步骤没有就往前找
    private getGameTimeByGuide(id: number) {
        if (id <= 0) return
        let info = this.getGuideInfo(id)
        if (info) {
            let time = this.getGameTimeByStep(info, info.getProgressDefault0())
            if (time) return time
        }
        return this.getGameTimeByGuide(id - 1)
    }
    private getGameTimeByCfgId(cfgId: number) {
        let id = Math.floor(cfgId / 100)
        let info = this.getGuideInfo(id)
        if (info) {
            let index = info.getIndexById(cfgId)
            let time = this.getGameTimeByStep(info, index)
            if (time) return time
        }
        return this.getGameTimeByGuide(id - 1)
    }
    private getGameTimeByStep(info: GuideInfoObj, id: number) {
        if (id < 0) return
        let step = info.getSteps()[id]
        if (step && step.gameTime) {
            return step.gameTime
        }
        return this.getGameTimeByStep(info, id - 1)
    }
}