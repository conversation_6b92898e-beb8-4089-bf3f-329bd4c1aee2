import { Msg } from "../../../proto/msg-define"
import { GuideStep } from "../../common/constant/DataType"
import { ConditionType, GuideStepMark } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { TaEvent } from "../../common/event/TaEvent"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { taHelper } from "../../common/helper/TaHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ConditionObj from "../common/ConditionObj"

export default class GuideInfoObj {
    public id: number = 0      // 模块id
    public progress: number = 0      // 模块进度
    private onCheck: Function = null  // 模块触发检测
    private steps: GuideStep[] = []
    private _isWorking: boolean = false

    private keySteps: number[] = [] //记录已到达的关键步骤，用来跳过一些步骤

    private keyStepMap = {}


    public init(id: number, progress: number) {
        this.id = id
        this.progress = progress
        this.steps = gameHelper.guide.guideConfig[this.id] || []
        for (let { skip, id } of this.steps) {
            if (skip) {
                this.keyStepMap[skip] = true
            }
        }

        return this
    }

    public initStep() {
        if (!this.isFinish()) {
            let index = 0
            this.progress = this.getNoSkipStepIndex(index)
        }

        twlog.info("guide init", this.id, this.progress)

        if (!this.isFinish()) {
            this.onCheckStepEvent(this.getCurStep() || this.steps[0])
        }
    }

    public fromDB(data: proto.IGuideInfo) {
        this.keySteps = data.keySteps ? data.keySteps.slice() : []
        this.init(data.id, -1)
        return this
    }

    public clean() {
        let step = this.getCurStep()
        if (step) this.offCheckEvent(step)
        if (this._isWorking) {
            this.onEnd_()
        }
        this.keySteps = []
        this.progress = -1
    }

    // 开始引导
    public start(progress: number = this.progress) {
        if (this._isWorking) return
        this._isWorking = true
        let steps = this.steps
        if (steps.length <= 0 || this.isFinish()) {
            return
        }
        this.progress = progress
        if (this.progress < 0) {
            this.progress = 0
        }

        eventCenter.emit(EventType.GUIDE_START, this)

        this.handleStep()
    }

    // 是否在引导中
    public isWorking() {
        return this._isWorking
    }

    public isFinish() {
        if (!gameHelper.openGuide) return true
        return this.progress >= this.steps.length
    }

    public isStepComplete(index: number) {
        if (!gameHelper.openGuide) return true
        if (this.progress > index) return true
        let step = this.steps[index]
        return this.checkStepSkip(step)
    }

    public getIndexById(cfgId: number) {
        return this.steps.findIndex(s => s.id == cfgId)
    }

    // 引导每一步
    private async handleStep() {
        let progress = this.progress
        let stepInfo = this.steps[progress]
        if (stepInfo) {
            if (this.checkStepSkip(stepInfo)) {
                return this.nextStep()
            }

            twlog.info("handleStep", this.id, stepInfo?.id, stepInfo)
            taHelper.track(TaEvent.TA_TUTORIAL, { tutorial_step: stepInfo.id })

            let { logic, advancedAllow, delayTime, ignoreClose } = stepInfo
            this.offCheckEvent(stepInfo)

            eventCenter.emit(EventType.GUIDE_CHANGE_PROGRESS, progress, stepInfo)

            if (logic && logic.call) await viewHelper.waitCloseGuide(ignoreClose)

            if (logic) {
                let { call, callReset, wait } = logic
                let mod = gameHelper.guide.logic
                // if (skip) {
                //     if (mod[skip](stepInfo, logic.skipArgs)) {
                //         twlog.info("guide skip", this.id, stepInfo?.id, stepInfo)
                //         return this.onStepEnd(progress)
                //     }
                // }

                let waitP
                if (wait) {
                    twlog.info("handleStep wait", wait, logic.waitArgs)
                    waitP = mod[wait](stepInfo, logic.waitArgs || [])
                }

                if (call) {
                    if (!advancedAllow && delayTime) {
                        mc.lockTouch(true)
                        await ut.wait(delayTime) //todo skip
                        mc.lockTouch(false)
                    }
                    if (callReset) {
                        twlog.info("handleStep callReset", callReset)
                        mod[callReset]()
                    }
                    twlog.info("handleStep call", call, logic.callArgs)
                    await mod[call](stepInfo, logic.callArgs || [])
                }
                
                if (waitP) {
                    await waitP
                }
            }

            let p1 = this.waitSyncOk(stepInfo)
            let p2 = this.waitAnimationOk(stepInfo)
            await Promise.all([p1, p2])

            twlog.info("handleStep step end", stepInfo.id)
            this.onStepEnd()

        } else {
            this.finish()
        }
    }

    private async waitAnimationOk(stepInfo: GuideStep) {
        let logic = stepInfo.logic
        if (!logic) return
        let waitAct = logic.waitAct
        if (!waitAct) return
        let mod = gameHelper.guide.logic
        twlog.info("handleStep waitAct", waitAct, logic.waitActArgs)
        await mod[waitAct](stepInfo, logic.waitActArgs || [])
    }

    private async waitSyncOk(stepInfo: GuideStep) {
        twlog.info("handleStep sync Step", stepInfo.id)
        if (this.isKeyStep(stepInfo)) {
            await this.syncStep(stepInfo.id)
            gameHelper.guide.addKeyStep(this.id, stepInfo.id)
        } else {
            this.syncStep(stepInfo.id)
        }
    }

    public async syncStep(id) {
        if (gameHelper.isFakeGuide) return
        let msg = new proto.C2S_RecordGuideStepMessage({ guideId: this.id, stepId: id })
        let res = await gameHelper.net.request(Msg.C2S_RecordGuideStepMessage, msg, true)
        const { code } = proto.S2C_RecordGuideStepResultMessage.decode(res)
    }

    //从from按顺序往下找到第一个没有被跳过的步骤
    private getNoSkipStepIndex(from: number) {
        let index = this.steps.length
        for (let i = from; i < this.steps.length; i++) {
            let step = this.steps[i]
            if (!this.checkStepSkip(step)) {
                index = i
                break
            }
        }
        return index
    }

    private checkStepSkip(step) {
        let skipId = step.skip
        if (this.keySteps.has(skipId)) {
            return true
        }
        let guides = gameHelper.guide.getGuides()
        for (let guide of guides) {
            if (guide.keySteps.has(skipId)) {
                return true
            }
        }
        return false
    }

    private isKeyStep(step) {
        return this.keyStepMap[step.id]
    }

    public isStepEnd(mark: GuideStepMark) {
        return this._isStepEnd(s => s.mark == mark)
    }

    public isStepEndByUnlockFunc(type) {
        return this._isStepEnd(s => s.unlockFunc == type)
    }

    private _isStepEnd(findFunc) {
        if (!gameHelper.openGuide) return true
        let index = this.steps.findIndex(findFunc)
        if (index < 0) return false
        return this.progress > index
    }

    private onStepEnd() {
        let step = this.getCurStep()
        this.nextStep()
        eventCenter.emit(EventType.GUIDE_STEP_END, step)
    }

    public getCurStep() {
        return this.steps[this.progress]
    }

    public getSteps() {
        return this.steps
    }

    public getProgressDefault0() {
        let num = this.progress
        if (num < 0) return 0
        return num
    }

    // 下一步
    private nextStep() {
        gameHelper.guide.logic.offAllEvent()

        let step = this.getCurStep()
        let index = this.progress
        if (!this.checkStepSkip(step)) { //如果当前步骤没有被跳过，取当前步骤的next；否则从当前步骤依次往下取
            index = this.getStepIndexById(step.next)
        }
        if (index >= 0) {
            this.progress = this.getNoSkipStepIndex(index)
            if (this.isFinish()) {
                return this.finish()
            }
        }
        else {
            return this.finish()
        }

        if (!this.isWorking()) {
            return
        }

        if (this.checkStep(this.getCurStep())) {
            this.handleStep()
        }
        else {
            this.onCheckStepEvent(this.getCurStep())
            this.onEnd()
        }
    }

    private getStepIndexById(id) {
        return this.steps.findIndex(step => step.id == id)
    }

    public skip() {
        this.finish()
    }

    public finish() {
        this.progress = this.steps.length
        this.onEnd()
    }

    public stop() {
        this.onCheckStepEvent(this.getCurStep())
        this.onEnd()
    }

    // 结束
    private onEnd() {
        this.onEnd_()
        gameHelper.guide.nextGuide()
    }

    private onEnd_() {
        gameHelper.guide.logic.offAllEvent()
        this._isWorking = false
        eventCenter.emit(EventType.GUIDE_END, this.id)
    }

    public checkStart(fromEvent?, eventParams?) {
        if (gameHelper.guide.isWorking()) return
        let step = this.getCurStep() || this.steps[0]
        if (!step) return false

        let guides = gameHelper.guide.getGuides()
        let firstStep = this.steps[0]
        for (let guide of guides) {
            if (guide == this) continue
            if (!guide.isFinish()) {
                let step = guide.getSteps().last()
                if (step.next == firstStep.id) {
                    return false
                }
            }
        }

        let check = step?.logic?.check
        if (check && !this.checkStep(step, fromEvent, eventParams)) {
            return false
        }
        twlog.info("guide start", this.id)
        return true
    }

    public checkStep(step, fromEvent?: boolean, eventParams?) {
        let check = step?.logic?.check
        let mod = gameHelper.guide.logic
        if (check) {
            if (!mod[check]) {
                console.error(`guideLogic.${check} not found`)
                return false
            }
            else {
                return mod[check](step, fromEvent, eventParams)
            }
        }
        return true
    }

    // 当前步骤事件监听
    private onCheckStepEvent(step) {
        let checkEvent = step?.logic?.checkEvent
        if (!checkEvent) return

        this.onCheck = (...params) => {
            if (mc.isGoingtoNextWind) return
            if (this.checkStart(true, params)) {
                console.log("onCheck success", this.id, step?.id, checkEvent)
                gameHelper.guide.start(this.id)
            }
        }

        let ary = checkEvent.split('|')
        ary.forEach(oneEvent => {
            console.log("onCheckEvent", this.id, step?.id, oneEvent, step)
            eventCenter.on(oneEvent, this.onCheck, this)
        })
    }

    // 取消事件监听
    private offCheckEvent(step) {
        if (!this.onCheck) return
        let { logic } = step
        if (!logic) return
        let { checkEvent } = logic
        if (!checkEvent) return
        let ary = checkEvent.split('|')
        ary.forEach(oneEvent => {
            eventCenter.off(oneEvent, this.onCheck, this)
        })
        this.onCheck = null
    }
}