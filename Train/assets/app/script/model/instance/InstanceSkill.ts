import { BlackHoleEquipTarget, ROLE_ATTR_ID, SkillType } from "../../common/constant/Enums";
import { BattleEffectValueType } from "../battle/BattleEnum";
import BattleSkill from "../battle/BattleSkill";

export default class InstanceSkill extends BattleSkill{

    public getType() { return SkillType.INSTANCE }

    public initCfg() {
        let cfg:any = assetsMgr.getJsonData("InstanceSkill", this.getId())
        this.cfg = cfg
        this._initCfg()
    }

    protected initViewCfg() {
        if (this.viewCfg) return
        this.viewCfg = assetsMgr.checkJsonData("InstanceSkill", this.getId())
    }

    protected getAttrParams() {
        return {roleId: ROLE_ATTR_ID.INSTANCE, roleLv: this.level, roleStarLv: 0}
    }

    public getTriggerDesc() {
        return ""
    }
}