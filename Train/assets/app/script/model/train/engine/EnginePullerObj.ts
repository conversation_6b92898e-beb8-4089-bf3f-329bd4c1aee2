import { PassengerAction } from "../../../common/constant/Enums";
import PassengerModel from "../../passenger/PassengerModel";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";
import CarriageModel from "../common/CarriageModel";
import EngineModel from "./EngineModel";

export enum EnginePullerObjState {
    ON,
    OFF,
}

export default class EnginePullerObj extends BuildObj {

    public state: StateObj<EnginePullerObjState> = null

    public onEnter(role: PassengerModel) {
        super.onEnter(role)
        this.setState(EnginePullerObjState.ON)

        let carriage = this.carriage as EngineModel
        let waterBox = carriage.getWaterBox()
        waterBox?.on()
    }

    public onExit(role: PassengerModel): void {
        super.onExit(role)
        this.setState(EnginePullerObjState.OFF)

        let carriage = this.carriage as EngineModel
        let waterBox = carriage.getWaterBox()
        waterBox?.off()
    }

    private setState(type?, data?) {
        this.state = new StateObj<EnginePullerObjState>().init(type, data)
    }

}