import { PassengerAction } from "../../../common/constant/Enums";
import ActionTree from "../../passenger/ActionTree";
import PassengerModel from "../../passenger/PassengerModel";
import { TimeStateData } from "../../passenger/StateDataType";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";
import EngineModel from "./EngineModel";

export enum EngineWaterBoxObjState {
    INTO,
    OUT,
}

export default class EngineWaterBoxObj extends BuildObj {

    public state: StateObj<EngineWaterBoxObjState> = null

    public on() {
        this.reset()
        this.actionTree.start(this.into)
    }

    public async into(action) {
        let time = this.getAnimTime("into")
        let timeData = new TimeStateData().init(time)
        this.setState(EngineWaterBoxObjState.INTO, { time: Date.now() })
        await action.wait(timeData)

        let carriage = this.carriage as EngineModel
        let power = carriage.getPower()
        power?.addPower(this)

        action.ok()
    }

    public off() {
        this.reset()
        this.setState(EngineWaterBoxObjState.OUT, { time: Date.now() })
    }

    private setState(type?, data?) {
        this.state = new StateObj<EngineWaterBoxObjState>().init(type, data)
    }

    public reset() {
        this.actionTree.terminate()
    }

    update(dt: any): void {
        this.actionTree && this.actionTree.update(dt)
    }

}