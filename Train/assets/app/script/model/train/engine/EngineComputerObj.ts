import { PassengerAction } from "../../../common/constant/Enums";
import PassengerModel from "../../passenger/PassengerModel";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";

export enum EngineComputerObjState {
    ON,
    OFF,
}

export default class EngineComputerObj extends BuildObj {

    public state: StateObj<EngineComputerObjState> = null
    
    public onEnter(role: PassengerModel) {
        super.onEnter(role)
        this.setState(EngineComputerObjState.ON)
    }

    public onExit(role: PassengerModel): void {
        super.onExit(role)
        this.setState(EngineComputerObjState.OFF)
    }

    private setState(type?, data?) {
        this.state = new StateObj<EngineComputerObjState>().init(type, data)
    }

}