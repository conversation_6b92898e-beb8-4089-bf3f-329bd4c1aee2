import { PassengerAction } from "../../../common/constant/Enums";
import PassengerModel from "../../passenger/PassengerModel";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";

export enum EngineTreadmillObjState {
    ON,
    OFF,
}

export default class EngineTreadmillObj extends BuildObj {

    public state: StateObj<EngineTreadmillObjState> = null

    
    public onEnter(role: PassengerModel) {
        super.onEnter(role)
        if (this.roles.length > 0 && (!this.state || this.state?.type == EngineTreadmillObjState.OFF)) {
            this.setState(EngineTreadmillObjState.ON)
        }
    }

    public onExit(role: PassengerModel): void {
        super.onExit(role)
        if (this.roles.length <= 0 && this.state?.type == EngineTreadmillObjState.ON) {
            this.setState(EngineTreadmillObjState.OFF)
        }
    }

    public isOn () {
        return this.state?.type == EngineTreadmillObjState.ON
    }

    private setState(type?, data?) {
        this.state = new StateObj<EngineTreadmillObjState>().init(type, data)
    }

}