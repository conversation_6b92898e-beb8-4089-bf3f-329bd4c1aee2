import CarriageModel from "../common/CarriageModel";
import { EngineBuildType } from "../../../common/constant/Enums";
import EngineTreadmillObj from "./EngineTreadmillObj";
import EngineWaterBoxObj from "./EngineWaterBoxObj";
import EnginePullerObj from "./EnginePullerObj";
import EnginePowerObj from "./EnginePowerObj";
import EngineEnergyObj from "./EngineEnergyObj";
import EngineComputerObj from "./EngineComputerObj";

/**
 * 动力室
 */
export default class EngineModel extends CarriageModel {
    
    public init(data) {
        super.init(data)
        return this
    }

    public getEnergy() {
        return this.getBuildByOrder(EngineBuildType.Energy) as EngineEnergyObj
    }

    public getPower() {
        return this.getBuildByOrder(EngineBuildType.POWER) as EnginePowerObj
    }

    public getTreadmill() {
        return this.getBuildByOrder(EngineBuildType.TREADMILL) as EngineTreadmillObj
    }

    public getChair() {
        return this.getBuildByOrder(EngineBuildType.CHAIR)
    }

    public getWaterBox() {
        return this.getBuildByOrder(EngineBuildType.WATER_BOX) as EngineWaterBoxObj
    }

    public getPuller() {
        return this.getBuildByOrder(EngineBuildType.PULLER) as EnginePullerObj
    }

    public getComputer() {
        return this.getBuildByOrder(EngineBuildType.COMPUTER)
    }

    public newBuildObj(type: EngineBuildType) {
        switch (type) {
            case EngineBuildType.TREADMILL: return new EngineTreadmillObj()
            case EngineBuildType.POWER: return new EnginePowerObj()
            case EngineBuildType.WATER_BOX: return new EngineWaterBoxObj()
            case EngineBuildType.PULLER: return new EnginePullerObj()
            case EngineBuildType.Energy: return new EngineEnergyObj()
            case EngineBuildType.COMPUTER: return new EngineComputerObj()
            default:
                return super.newBuildObj(type)
        }
    }
}