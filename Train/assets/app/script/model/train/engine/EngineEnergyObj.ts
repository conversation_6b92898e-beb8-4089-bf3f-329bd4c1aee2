import PassengerModel from "../../passenger/PassengerModel";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";
import EngineModel from "./EngineModel";

export enum EngineEnergyState {
    ON,
    OFF,
}
export default class EngineEnergyObj extends BuildObj {

    public state: StateObj<EngineEnergyState> = null

    
    public onEnter(role: PassengerModel) {
        super.onEnter(role)
        if (this.roles.length > 0 && (!this.state || this.state?.type == EngineEnergyState.OFF)) {
            this.setState(EngineEnergyState.ON, { time: Date.now()})
        }
    }

    public onExit(role: PassengerModel): void {
        super.onExit(role)
        if (this.roles.length <= 0 && this.state?.type == EngineEnergyState.ON) {
            this.setState(EngineEnergyState.OFF, { time: Date.now()})
        }
    }

    public isOn () {
        return this.state?.type == EngineEnergyState.ON
    }

    private setState(type?, data?) {
        this.state = new StateObj<EngineEnergyState>().init(type, data)
    }
}
