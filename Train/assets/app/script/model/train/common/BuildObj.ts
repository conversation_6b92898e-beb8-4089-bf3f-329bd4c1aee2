import { DIR_POINTS_8, MAX_ZINDEX } from "../../../common/constant/Constant"
import { BuildCfg, CarriageUsePosInfo } from "../../../common/constant/DataType"
import { cfgHelper } from "../../../common/helper/CfgHelper"
import { gameHelper } from "../../../common/helper/GameHelper"
import PassengerModel from "../../passenger/PassengerModel"
import ConditionObj from "../../common/ConditionObj"
import CarriageMap from "./CarriageMap"
import CarriageModel from "./CarriageModel"
import ActionTree from "../../passenger/ActionTree"
import EventType from "../../../common/event/EventType"
import { BuildAttr, CarriageID, CarriageUsePosType } from "../../../common/constant/Enums"
import { viewHelper } from "../../../common/helper/ViewHelper"
import { Msg } from "../../../../proto/msg-define"

// 建筑设施
export default class BuildObj {

    public id: string = null
    public order: number = 0
    public skin: number = 1
    public carriageId: number = 0
    public lv: number = 1

    private attr: { [key: string]: number } = {}

    public json: BuildCfg = null //当前的json数据
    public points: cc.Vec2[] = [] //在地图中的站位点

    public position: cc.Vec2 = cc.v2()

    public zIndex: number = 0

    public centerPoint: cc.Vec2 = null

    public roles: PassengerModel[] = []

    public roleIds: any[] = []

    private useLock: number = 0

    private usePosList: CarriageUsePosInfo[] = []

    public isBuilding: boolean = false

    protected _actionTree: ActionTree = null
    protected get actionTree() {
        if (!this._actionTree) {
            this._actionTree = new ActionTree().init(this)
        }
        return this._actionTree
    }

    public init(order: number, carriageId: number, needBuild = true) {
        this.order = order
        this.carriageId = carriageId
        this.isBuilding = needBuild
        this.initJson()
        this.initObj()
        return this
    }

    public setUseLock(lock: boolean, index: number = 0, roleId?: number | string) {
        if (lock) {
            this.useLock |= (1 << index)
        }
        else {
            this.useLock &= (~(1 << index))
        }
        roleId && this.recordAction(lock, roleId)
    }

    public setMultipleUseLock(lock: boolean, indexs: Array<number>, roleId: number) {
        for (let i = 0; i < indexs.length; ++i)
            this.setUseLock(lock, indexs[i])
        this.recordAction(lock, roleId)
    }

    public isEmpty(index: number = 0) {
        return !((this.useLock >> index) & 1)
    }

    public canUse(index: number = 0) {
        if (!this.isEmpty(index)) return false
        if (this.isBuilding) return false
        return true
    }

    public canQueue() {
        if (this.isBuilding) return false
        return true
    }

    public onEnter(role: PassengerModel) {
        this.roles.push(role)
    }

    public onExit(role: PassengerModel) {
        this.roles.remove(role)
    }

    public recordAction(bol: boolean, roleId: number | string) {
        if (bol)
            this.roleIds.push(roleId)
        else
            this.roleIds.remove(roleId)
    }

    public fromDB(data: proto.ITrainItemInfo, carriageId: number) {
        this.lv = data.lv
        this.skin = data.skin
        return this.init(data.order, carriageId, false)
    }

    public initObj() { }

    public getCarriageOrder() {
        return `${this.carriageId}-${this.order}`
    }

    private initJson() {
        this.id = `${this.carriageId}-${this.skin}-${this.order}`
        this.json = cfgHelper.getBuildById(this.id)

        // 网格
        this.initGridJson()
        this.updateAttr()
    }

    public getNextSkinJson() {
        let id = `${this.carriageId}-${this.getAttr(BuildAttr.SKIN) + 1}-${this.order}`
        return cfgHelper.getBuildById(id)
    }

    public levelUp() {
        this.lv++
        this.updateAttr()
        eventCenter.emit(EventType.LEVEL_UP_BUILD, this)
    }

    private initGridJson() {
        let data = cfgHelper.getThemeMapJsonData(this.carriageId)
        const baseJson = (data.baseBuilds || {})[this.order]
        const buildJson = data.builds[this.id]

        let merge = (build, base) => {
            if (!build) return base
            if (!base) return build
            let json: any = ut.setValue("x|y", build)
            json.colliders = build.colliders || base.colliders
            json.zIndex = build.zIndex !== undefined ? build.zIndex : base.zIndex
            json.usePosList = mergeUsePosList(build.usePosList, base.usePosList)
            return json
        }
        let mergeUsePosList = (usePosList = [], baseUsePosList = []) => {
            let res = usePosList.slice()
            for (let baseInfo of baseUsePosList) {
                let hasSame = usePosList.some(info => {
                    return info.id == baseInfo.id && info.type == baseInfo.type
                })
                if (!hasSame) {
                    res.push(baseInfo)
                }
            }
            return res
        }

        const json = merge(buildJson, baseJson)
        if (json) {
            this.position = cc.v2(json.x, json.y)
            if (json.colliders) {
                this.initPoints(json.colliders)
                this.initCenterPoint()
            }
            if (json.zIndex) {
                this.zIndex = json.zIndex
            }
            if (json.usePosList) {
                this.usePosList = cfgHelper.mapCarriageUsePosList(json.usePosList, this.position)
            }
        }
    }

    public getActPoints(add: boolean) {
        return [{ add, points: this.points }]
    }

    public getNearPos(startPos: cc.Vec2) {
        let validPoints = []
        let startPoint = this.map.getActPointByPixel(startPos)
        for (let point of this.points) {
            for (let dir of DIR_POINTS_8) {
                let x = point.x + dir.point.x
                let y = point.y + dir.point.y
                let p = cc.v2(x, y)
                if (this.map.isConnected(p, startPoint)) {
                    validPoints.push(p)
                }
            }
        }
        let p = validPoints.min(p => p.sub(startPoint).magSqr())
        if (p) return this.map.getActPixelByPoint(p)
        return this.position
    }

    public getUsePosList() {
        return this.usePosList
    }

    public getUsePos(index: number = 0) {
        return this.usePosList[index].pos
    }

    public getUseByIndex(index: number) {
        return this.usePosList[index]
    }

    public getUseById(id) {
        return this.usePosList.find(info => info.id == id)
    }

    public getUseIndexById(id) {
        return this.usePosList.findIndex(info => info.id == id)
    }

    public getUsePosListByType(type: CarriageUsePosType) {
        return this.usePosList.filter(info => info.type == type)
    }

    public getCleanPosList() {
        return this.getUsePosListByType(CarriageUsePosType.CLEAN)
    }

    public canClean() {
        let posList = this.getCleanPosList()
        if (posList.length <= 0) return false
        for (let { index } of posList) {
            if (!this.isEmpty(index)) return false
        }
        return true
    }

    protected initPoints(colliders) {
        this.points = []
        let map = new CarriageMap().init() //todo
        for (let collider of colliders) {
            let colliderVertexs = collider.map(v => {
                return cc.v2(this.position.x + v.x, this.position.y + v.y)
            })
            let points = map.getPointsByPolygon(colliderVertexs)
            this.points.pushArr(points)
        }
        //todo去重?
    }

    public splitLastCollider(colliders) {
        let aryLast = []
        this.points = []
        let map = new CarriageMap().init()
        let max = colliders.length
        for (let i = 0; i < max; i++) {
            const collider = colliders[i];
            let colliderVertexs = collider.map(v => {
                return cc.v2(this.position.x + v.x, this.position.y + v.y)
            })
            let points = map.getPointsByPolygon(colliderVertexs)
            if (i == max - 1) {
                aryLast.pushArr(points)
            } else {
                this.points.pushArr(points)
            }
        }
        return aryLast
    }

    private initCenterPoint() {
        let maxY = 0
        let minY = MAX_ZINDEX
        let maxX = 0
        let minX = MAX_ZINDEX
        if (!this.points.length) return;
        for (let point of this.points) {
            if (maxY < point.y) {
                maxY = point.y
            }
            if (minY > point.y) {
                minY = point.y
            }
            if (maxX < point.x) {
                maxX = point.x
            }
            if (minX > point.x) {
                minX = point.x
            }
        }
        this.centerPoint = cc.v2((maxX + minX) * 0.5, (maxY + minY) * 0.5) //todo
    }

    public getZIndex() {
        if (this.zIndex) return this.zIndex
        if (this.centerPoint) {
            let map = this.map
            this.zIndex = MAX_ZINDEX - map.getActPixelByPoint(this.centerPoint).y
            return this.zIndex
        }
        return MAX_ZINDEX
    }

    public updateZIndex(zIndex: number) {
        this.zIndex = zIndex
    }

    public get buildType() { return this.json?.type }//设施类型
    public get isShow() { return this.json?.show }
    public get type() { return this.order }
    public get carriage(): CarriageModel { return gameHelper.train.getCarriageById(this.carriageId) }
    public get map(): CarriageMap { return this.carriage?.getMap() }
    public get prefab(): string { return this.json?.prefab }
    public get sortId() { return this.json?.sortId || 100 }
    public get outputPoints() { return this.json?.point || [] }

    public getAttr(type: BuildAttr) {
        let val = this.attr[type] || 0
        if (type == BuildAttr.SKIN) {
            val++
        }
        return val
    }

    public getAttrByCond(cond: ConditionObj) {
        let attr = gameHelper.transCondToBuildAttr(cond)
        return this.getAttr(attr)
    }

    private updateAttr() {
        let attr = {}
        for (let lv = 1; lv <= this.lv; lv++) {
            let cfg = cfgHelper.checkBuildLvCfg(this.carriageId, this.order, lv)
            if (!cfg?.add) continue
            for (let key in cfg.add) {
                if (!attr[key]) attr[key] = 0
                attr[key] += cfg.add[key]
            }
        }
        this.attr = attr
    }

    update(dt) {
        this.actionTree && this.actionTree.update(dt)
    }

    public getAnimTime(name) {
        return cfgHelper.getBuildAnimTime(this.id, name)
    }

    public getAnimsTime(anims) {
        return cfgHelper.getBuildAnimsTime(this.id, anims)
    }

    public getAnimEventTime(name, eventName = "effect") {
        return cfgHelper.getBuildAnimEventTime(this.id, name, eventName)
    }

    //在完整播完一次的前提下，随机一个播放时长
    protected randomLoopAnimTime(data, name: string) {
        let animTime = this.getAnimTime(name)
        let time = ut.randomRange(data[0], data[1])
        return Math.ceil(time / animTime) * animTime //要完整播完
    }

    public isFloor() {
        return this.buildType == 2
    }

    public isWall() {
        return this.buildType == 1
    }

    public async changeSkin(skin: number) {
        let msg = new proto.C2S_ChangeBuildSkinMessage({ carriageId: this.carriageId, order: this.order, skin })
        let res = await gameHelper.net.request(Msg.C2S_ChangeBuildSkinMessage, msg, true)
        let { code } = proto.S2C_ChangeBuildSkinMessage.decode(res)
        if (code == 0) {
            this.onChangeSkin(skin)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public onChangeSkin(skin: number, first: boolean = false) {
        this.skin = skin
        this.map?.updatePointsByBuild(this, false)
        this.initJson()
        this.map?.updatePointsByBuild(this, true)
        eventCenter.emit(EventType.CHANGE_BUILD, this, first)

        this.resumeBuild()
    }

    public resumeBuild() {
        let ary = this.roleIds.filter(m => m)
        this.roleIds = []
        ary.forEach((id) => {
            let passenger = gameHelper.passenger.getPassenger(id)
            if (passenger) {
                gameHelper.passenger.restartRole(id)
            }
            else if (this.carriage.getID() == CarriageID.WATER) {
                //@ts-ignore
                let cloud = this.carriage.getCloud(id)
                cloud && cloud.restart()
            }
        })
    }

    public buildingStart() {
        this.isBuilding = true
    }

    public async buildingEnd(delay: number) {
        await ut.wait(delay)//为避免晚上乘客在设施升级结束还未变暗时使用设施
        this.isBuilding = false
        // this.resumeBuild()
        eventCenter.emit(EventType.BUILD_OVER_TRAIN_ITEM, this)
    }

    public getSitPos(roleId: number): cc.Vec2 {
        return null
    }

    public isMaxLv(max: number) {
        return this.lv >= max
    }

    public getPreMaxLv() {
        let carriage = this.carriage
        let theme = cfgHelper.getThemes(this.carriageId).find(d => d.order == carriage.getThemeLv() - 1)
        if (!theme) return 1
        return theme.unlockLevel
    }

    public getMaxLv() {
        let carriage = this.carriage
        let theme = cfgHelper.getThemes(this.carriageId).find(d => d.order == carriage.getThemeLv())
        return theme.unlockLevel
    }

    public reset() {
        this.actionTree.terminate()
    }
}
