import { gameHelper } from "../../../common/helper/GameHelper"
import CarriageOutputObj from "./CarriageOutputObj"

// 车厢水电产出模块
export default class CarriageEnergyOutputObj extends CarriageOutputObj {

    private output2: number = 0 //演绎产出累计值

    private _lockOutput: number = 0 //配合演绎，已被锁定的产出

    public getOutput() {
        return this.output + this.output2
    }

    public setOutput(output: number = 0) {
        this.output2 = 0
        super.setOutput(output)
    }

    protected addOutput(output) {
        this.output += output * 0.9
        this.output2 += output * 0.1
    }

    public checkOutputByPlay() {
        let offset = 2 * ut.Time.Second
        return gameHelper.world.time % ut.Time.Hour
    }

    public getOutputByPlay() {
        return Math.floor(this.output2)
    }

    public genOutputByPlay(output?: number) {
        output = output || this.getOutputByPlay()
        this.output2 -= output
        this.genTime = gameHelper.world.time
        return output
    }

    public lockOutput(output) {
        this._lockOutput += output
    }

    public unlockOutput(output) {
        this._lockOutput -= output
    }

    public getUnlockOutput() {
        return this.getOutputByPlay() - this._lockOutput
    }
}
