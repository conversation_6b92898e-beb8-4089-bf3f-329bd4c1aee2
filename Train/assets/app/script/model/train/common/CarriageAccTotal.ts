import { CarriageID, CarriageType } from "../../../common/constant/Enums"
import PassengerModel from "../../passenger/PassengerModel"
import { StateType } from "../../passenger/StateEnum"
import CarriageModel from "./CarriageModel"

const UPDATE_TIME = 10 //x秒更新一次

//统计车厢使用信息
export default class CarriageAccTotal {
    public roles: number[] //使用过的角色id,
    public useNum: number //累计使用次数/使用时间（单位小时）
    private time: number = 0

    private carriage: CarriageModel = null

    public init(carriage: CarriageModel, data) {
        this.carriage = carriage
        this.roles = data?.roles || []
        this.useNum = data?.useNum || 0
        return this
    }

    public toDB() {
        return {
            roles: this.roles,
            useNum: this.useNum
        }
    }

    public add(role: PassengerModel, num: number = 1) {
        let roles = this.roles
        if (!roles.has(role.id)) {
            roles.push(role.id)
        }

        this.useNum += num
    }

    update(dt) {
        this.time += dt
        if (this.time < UPDATE_TIME) return
        this.time = 0

        let carriage = this.carriage
        if (carriage.getID() == CarriageID.ENGINE) {
            let workers = this.carriage.getWorkers()
            for (let worker of workers) {
                this.add(worker, (UPDATE_TIME * ut.Time.Second) / ut.Time.Hour)
            }
        }
    }
}