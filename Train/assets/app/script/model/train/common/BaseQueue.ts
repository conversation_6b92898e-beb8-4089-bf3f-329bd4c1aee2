import { StateType } from "../../passenger/StateEnum"
import BuildObj from "./BuildObj"
import PassengerModel from "../../passenger/PassengerModel"

export default abstract class BaseQueue {
    private timeCur: number = 0
    protected timeMax: number = 1

    public abstract updateOnce(): void
    public abstract calculateQueueRobPos(stateQueue: StateType, i: number): cc.Vec2
    public getDoPos(build: BuildObj) {
        return build.getUsePos(0)
    }
    protected getIndexByEmpty(build: BuildObj, i: number) {
        if (i == 0 && !build.isEmpty())
            return 1
        return i
    }
    public update(dt) {
        this.timeCur += dt
        if (this.timeCur > this.timeMax) {
            this.updateOnce()
        }
    }
    protected updateQueue(ary: PassengerModel[], stateQueue: StateType) {
        this.timeCur = 0
        let max = ary.length
        if (max == 1) return
        let i = this.getRobStart(ary, stateQueue)
        if (i == null) return
        if (i + 1 == max) return
        let pos = this.calculateQueueRobPos(stateQueue, i)
        this.robQueue(ary, i, pos)
    }
    private getRobStart(ary: PassengerModel[], type: StateType) {
        for (let i = 0; i < ary.length; i++) {
            let agnt = ary[i].actionAgent
            if (!(agnt && agnt.getState(type))) {
                return i
            }
        }
    }
    private robQueue(ary: PassengerModel[], start: number, pos: cc.Vec2) {
        let list = []
        for (let i = start; i < ary.length; i++) {
            let role = ary[i]
            let len = role.getPosition().sub(pos).magSqr()
            list.push({ role, len })
        }
        list.sort((a, b) => { return a.len - b.len })
        for (let i = 0; i < list.length; i++) {
            ary[start + i] = list[i].role
        }
    }
    protected calculateOffPos(i: number = 0) {
        let x = 50 * (i + 1)
        let y = 30 * i
        return cc.v2(x, y)
    }
}

