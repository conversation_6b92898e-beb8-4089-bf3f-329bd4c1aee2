import { CACHE_UPDATE_TIME, SPEED_UP_RANDOM } from "../../../common/constant/Constant"
import { ConditionType, ItemID, LifeSkillEffectType, PassengerAttr, SpeedUpType, UIFunctionType } from "../../../common/constant/Enums"
import EventType from "../../../common/event/EventType"
import { cfgHelper } from "../../../common/helper/CfgHelper"
import { gameHelper } from "../../../common/helper/GameHelper"
import { unlockHelper } from "../../../common/helper/UnlockHelper"
import ConditionObj from "../../common/ConditionObj"

@mc.addmodel('heartOutput')
export default class HeartOutputModel extends mc.BaseModel {

    protected output: number = 0 //产出累计值
    protected outputTime: number = 0 //计算产出累计时间，单位s
    protected genTime: number = 0 //下次产出的剩余时间(只是表现), 单位s
    protected debugTime: number = 0

    protected item: ConditionObj = null

    public data: number = 0

    public init() {
        let output = this.data || 0
        this.item = new ConditionObj().init(ConditionType.HEART)
        this.updateInfo(output)
        this.reset()
        return this
    }

    public updateInfo(output: number = 0) {
        this.updateOfflineOutput(output)
        this.outputTime = gameHelper.train.getCarriages()[0].outputObj?.getOutputTime() || 0
    }

    public update(dt) {
        if (!this.canOutput()) return

        let world = gameHelper.world

        this.outputTime += world.transDT(dt, SpeedUpType.S3)

        this.updateOutput()

        if (this.checkOutput()) {
            let succ = this.genOutput()
            this.checkReset(succ)
        }
    }

    private canOutput() {
        if (!unlockHelper.canOutput()) return false
        return true
    }

    public setOutput(output: number = 0) {
        this.output = 0
        this.addOutput(output)
        this.resetOutputTime()
        this.debugTime = 0
    }

    protected addOutput(output) {
        this.output += output
    }

    public getOutput() {
        return this.output
    }

    public updateOfflineOutput(output: number = 0) {
        let passengers = gameHelper.passenger.getPassengers().filter(p => p.isCheckIn())

        let moneysVal: number = 0
        passengers.forEach((p) => {
            moneysVal += p.getHeart()
        })
        let offlineOuput = output - moneysVal
        if (offlineOuput > 0) {
            twlog.info(`[$爱心离线恢复产出: ${offlineOuput}, 服务器下发: ${output}, 客户端已有: ${moneysVal}`)
            let outputRate = this.getOutputRate()
            let oneNum = outputRate || 1
            let num = Math.floor(offlineOuput / oneNum)
            num = Math.min(num, passengers.length)
            if (num > 0) {
                passengers = ut.randomArray(passengers)
                ut.numAvgSplit(offlineOuput, num).forEach((val, i) => {
                    passengers[i].setHeart(val)
                })
                this.setOutput(0)
            }
            else {
                this.setOutput(offlineOuput)
            }
        }
        else {
            this.setOutput(offlineOuput)
        }
    }

    public updateOutput() {
        let time = this.outputTime
        this.debugTime += time
        let unitTime = gameHelper.world.toRealSecond(ut.Time.Hour)
        let count = Math.floor(time / unitTime)
        if (count <= 0) return
        let output = Math.round(count * this.getOutputRate())
        twlog.info(`${gameHelper.world.formatWorldTime()} 列车产出${output}爱心`)
        this.addOutput(output)
        this.outputTime -= count * unitTime
    }

    private resetOutputTime() {
        this.outputTime = 0
    }

    public getOutputRate() {
        return gameHelper.train.getAttr(this.item)
    }

    public genOutput() {
        let output = Math.floor(this.output)
        if (output <= 0) return
        let passenger = gameHelper.passenger.getPassengers().filter(p => p.isCheckIn()).minList(p => p.heart).random()
        if (!passenger) return
        this.debugTime = 0
        passenger.addHeart(output)
        this.output -= output
        return true
    }

    private checkOutput() {
        let intervalTime = ut.Time.Hour
        return Math.floor(gameHelper.world.getTime() / intervalTime) - Math.floor(this.genTime / intervalTime) > 0
    }

    public checkReset(succ) {
        let intervalTime = ut.Time.Hour
        let now = gameHelper.world.getTime()
        if (succ || now % intervalTime >= ut.Time.Minute) this.reset()
    }

    public reset() {
        this.genTime = gameHelper.world.getTime()
    }

    public addOutputTime(time) {
        this.outputTime += time
    }
}
