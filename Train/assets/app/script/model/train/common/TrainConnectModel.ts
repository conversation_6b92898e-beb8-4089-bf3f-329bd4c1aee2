import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import PassengerModel from "../../passenger/PassengerModel";
import CarriageModel from "./CarriageModel";

/**
 * 车厢连接过道
 */
export default class TrainConnectModel {

    public left: CarriageModel = null
    public right: CarriageModel = null

    private roles: PassengerModel[] = []

    public init(left: CarriageModel, right: CarriageModel) {
        this.left = left
        this.right = right
        return this
    }

    public onEnter(role: PassengerModel) {
        this.roles.push(role)
        eventCenter.emit(EventType.PASSENGER_ENTER_CONNECT, this, role)
    }

    public onExit(role: PassengerModel) {
        this.roles.remove(role)
        eventCenter.emit(EventType.PASSENGER_EXIT_CONNECT, this, role)
    }

    public getRoles() {
        return this.roles
    }

    public getIndex() {
        let connects = gameHelper.train.connects
        return connects.findIndex(c => c == this)
    }
}