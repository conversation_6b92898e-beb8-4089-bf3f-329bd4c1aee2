import { TrainGoodsCfg, TrainGoodsLevelCfg, TrainGoodsLevelEffect } from "../../../common/constant/DataType"
import { cfgHelper } from "../../../common/helper/CfgHelper"

export default class GoodsObj {
    public id: string
    public lv: number
    private cfg: TrainGoodsCfg = null
    private cfgLv: TrainGoodsLevelCfg = null
    private _effects: { [lv: number]: TrainGoodsLevelEffect[] } = {}
    public get type() { return this.cfg?.type }
    public get buyCost() { return this.cfgLv?.buyCost || [] }
    public get objects() { return this.cfg?.object || [] }
    public get attrValue() { return this.cfgLv?.attrValue || 0 }

    //食物部分
    public get foodItem() { return this.cfg?.foodItem || [] }
    public get handsFood() { return this.cfg?.handsFood }
    public get drop() { return this.cfg?.drop }
    public get icon() { return this.cfg?.icon }
    public get takeIcon() { return "take_" + this.cfg?.icon }
    public get drinkAnim() { return this.cfg?.drinkAnim || "juiceBlue" }

    public get trainId()  { return this.cfg?.trainId }

    public init(id: string, lv: number) {
        this.id = id
        this.lv = lv
        this.cfg = assetsMgr.getJsonData<TrainGoodsCfg>("TrainGoods", id)
        this.cfgLv = cfgHelper.getTrainGoodsLevel(id, lv)
        return this
    }

    public getAttr() {
        return cfgHelper.getTrainGoodsAttr(this.id, this.lv)
    }
}
