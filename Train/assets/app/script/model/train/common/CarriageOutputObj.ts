import { CACHE_UPDATE_TIME, SPEED_UP_RANDOM } from "../../../common/constant/Constant"
import { ConditionType, ItemID, LifeSkillEffectType, SpeedUpType, UIFunctionType, ValueType } from "../../../common/constant/Enums"
import EventType from "../../../common/event/EventType"
import { cfgHelper } from "../../../common/helper/CfgHelper"
import { gameHelper } from "../../../common/helper/GameHelper"
import { unlockHelper } from "../../../common/helper/UnlockHelper"
import ConditionObj from "../../common/ConditionObj"
import CarriageModel from "./CarriageModel"

// 车厢产出模块
export default class CarriageOutputObj {

    protected carriage: CarriageModel
    protected output: number = 0 //产出累计值
    protected outputTime: number = 0 //计算产出累计时间，单位s
    protected genTime: number = 0 //下次产出的剩余时间(只是表现), 单位s
    protected debugTime: number = 0
    public accTotal: number = 0 //累计总产出

    protected item: ConditionObj = null

    public init(carriage: CarriageModel, item: ConditionObj, data: proto.ICarriageOutput) {
        this.carriage = carriage
        this.item = item
        //每个车厢，初始化丢星尘的等待时间
        this.updateInfo(data)
        this.reset()
        return this
    }

    public updateInfo(data: proto.ICarriageOutput) {
        this.updateOfflineOutput(data?.val)
    }

    public update(dt) {
        if (!this.canOutput()) return

        let world = gameHelper.world

        this.outputTime += world.transDT(dt, SpeedUpType.S3)

        this.updateOutput()

        if (this.checkOutput()) {
            let succ = this.genOutput()
            this.checkReset(succ)
        }
    }

    private canOutput() {
        if (!this.carriage.isBuilt) return
        if (!unlockHelper.canOutput()) return false
        return true
    }

    public checkOutput() {
        let intervalTime = ut.Time.Hour
        let now = gameHelper.world.getTime()
        return Math.floor(now / intervalTime) - Math.floor(this.genTime / intervalTime) > 0
    }

    public checkReset(succ) {
        let intervalTime = ut.Time.Hour
        let now = gameHelper.world.getTime()
        if (succ || now % intervalTime >= ut.Time.Minute) this.reset()
    }

    public setOutput(output: number = 0) {
        this.output = 0
        this.addOutput(output)
        this.outputTime = 0
        this.debugTime = 0
    }

    protected addOutput(output) {
        this.output += output
    }

    public getOutput() {
        return this.output
    }

    public updateOfflineOutput(output: number = 0) {
        let dropMoneys = this.carriage.getAllDrops()
        let moneys = dropMoneys.filter(m => !m.tips && m.item.isSame(this.item))
        let moneysVal: number = 0
        moneys.forEach((money) => {
            let s = money.moneySources.find(s => s.type == ConditionType.CARRIAGE && s.id == this.carriage.getID())
            if (s && s.num) {
                moneysVal += s.num
            }
        })
        let offlineOuput = output - moneysVal
        if (offlineOuput > 0) {
            twlog.info(`[${this.carriage.getID()}] 车厢离线恢复产出: [${this.item.type},${this.item.id}] ${offlineOuput}, 服务器下发: ${output}, 客户端已有: ${moneysVal}`)
            let iconPer = cfgHelper.getMiscData("iconPer")
            let starNum = Math.max(1, Math.floor(offlineOuput / (iconPer[gameHelper.transCondToBuildAttr(this.item)] || offlineOuput)))
            starNum = Math.min(100, starNum)
            if (starNum > 0) {
                ut.numAvgSplit(offlineOuput, starNum).forEach((val) => {
                    if (val > 0) {
                        this.addDrop(val, null, 1)
                    }
                })
                this.setOutput(0)
            }
            else {
                this.setOutput(offlineOuput)
            }
        }
        else {
            this.setOutput(offlineOuput)
        }
    }

    public updateOutput() {
        let time = this.outputTime
        this.debugTime += time
        let unitTime = gameHelper.world.toRealSecond(ut.Time.Hour)
        let count = Math.floor(time / unitTime)
        if (count <= 0) return
        let output = Math.round(count * this.getOutputRate())
        let name = "星尘"
        if (this.item.id == ItemID.ELECTRIC) {
            name = "电力"
        }
        else if (this.item.id == ItemID.WATER) {
            name = "水力"
        }
        else if (this.item.id == ItemID.VITALITY) {
            name = "元气"
        }
        if (output > 0) {
            twlog.info(`${gameHelper.world.formatWorldTime()} 车厢${this.carriage.getID()}, 产出${output}${name}`)
        }

        if (this.item.type == ConditionType.STAR_DUST && output > 0) {
            let carriageOutput = Math.round(this.carriage._getAttr(gameHelper.transCondToBuildAttr(this.item)) / this.getOutputRate() * output)
            let checkInOutput = output - carriageOutput
            gameHelper.passenger.starOutputObj.addOutput(checkInOutput)
            output = carriageOutput
        }
        this.addOutput(output)
        this.outputTime -= count * unitTime
    }

    public getOutputRate() {
        return this.carriage.getAttr(this.item)
    }

    public genOutput() {
        let output = Math.floor(this.output)
        if (output <= 0) return false
   
        this.debugTime = 0
        this.output -= output
        // twlog.info(`genOutput ${gameHelper.world.formatWorldTime()} 车厢${this.carriage.getID()}, 产出${output}`)

        let attr = gameHelper.transCondToBuildAttr(this.item)
        let builds = ut.randomArray(this.carriage.getBuilds().slice())
        let buildAttrSum = builds.reduce((sum, b)=>sum + b.getAttr(attr), 0)
        let inc = output - buildAttrSum
        for (let build of builds) {
            if (output <= 0) break

            let sum = build.getAttr(attr)
            if (sum > 0) {
                sum += inc
                inc = 0
            }

            if (sum <= 0) {
                continue
            }

            sum = Math.round(sum)

            if (sum > output) {
                sum = output
            }
            output -= sum
            this.addDrop(sum, build)
        }
        return true
    }

    public addDrop(output, build?, count?) {
        if (!build) {
            let builds = this.carriage.getBuilds().filter(build => build.getAttrByCond(this.item) > 0)
            if (builds.length <= 0) {
                builds = this.carriage.getBuilds()
            }
            build = builds.random()
        }
        this.carriage.addDropByBuild(new ConditionObj().init(this.item.type, this.item.id, output), build, count)
    }

    public addDropByPos(output, startPos) {
        this.carriage.addDropByPos(new ConditionObj().init(this.item.type, this.item.id, output), startPos)
    }

    public reset() {
        this.genTime = gameHelper.world.getTime()
    }

    public addOutputTime(time) {
        this.outputTime += time
    }

    public getOutputTime() {
        return this.outputTime
    }
}
