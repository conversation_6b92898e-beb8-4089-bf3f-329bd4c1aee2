import { Condition, MoneyAreaInfo } from "../../../common/constant/DataType"
import ConditionObj from "../../common/ConditionObj"

// 掉落
export default class DropItemObj {

    public uid: string = ''
    public areaId: any = null //所在区域ID
    public position: cc.Vec2 = cc.v2()

    public zIndex: number = 0
    public checkZindex: number = 0
    public tag: string = "" //特殊标记
    public isGuide: boolean = false

    public item: ConditionObj = null

    public collected: boolean = false //已领取  

    public init(areaId: any, position: cc.Vec2, item: ConditionObj) {
        this.uid = ut.uid()
        this.areaId = areaId
        this.position.set(position)
        this.item = item
        return this
    }

    public setArea(area: MoneyAreaInfo) {
        this.tag = area.tag
    }

    public getTag() {
        return this.tag || this.areaId
    }

    public getPrefabUrl() { return '' }
}