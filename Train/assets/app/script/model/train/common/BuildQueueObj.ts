import BuildObj from "./BuildObj";

export default class BuildQueueObj extends BuildObj {
    private queueLock: boolean = false
    private timeCD: number = 0

    public isQueueEmpty() {
        if (this.isEmpty()) return true
        return !this.queueLock
    }

    public setQueueLock(bol: boolean) {
        this.queueLock = bol
    }

    public setCD(time: number) {
        this.timeCD = time
    }

    private isCD() {
        return this.timeCD > 0
    }

    public setUseLock(lock: boolean, index: number = 0, roleId?: number) {
        super.setUseLock(lock, index, roleId)
        this.setQueueLock(lock)
    }

    public isEmpty(index: number = 0) {
        return super.isEmpty(index) && !this.isCD()
    }

    update(dt: number) {
        if (this.isCD()) {
            this.timeCD -= dt
        }
    }
}