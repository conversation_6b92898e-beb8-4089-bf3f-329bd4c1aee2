import { MoneyAreaInfo } from "../../../common/constant/DataType"
import { ConditionType, ItemID } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"
import ConditionObj from "../../common/ConditionObj"
import DropItemObj from "./DropItemObj"

// 掉落钱
export default class DropMoneyObj extends DropItemObj {

    public up: boolean = false //是否飘在上方 

    public tips: boolean = false //是否是小费

    public moneySources: ConditionObj[] = [] //来源

    public carriageId: number = 0

    public init(areaId: any, position: cc.Vec2, item: ConditionObj) {
        super.init(areaId, position, item)
        return this
    }

    public setArea(area: MoneyAreaInfo) {
        super.setArea(area)
        this.up = area.up
        this.tips = area.tips
    }

    public setCarriageId(id: number) {
        this.carriageId = id
    }

    public addMoney(source: ConditionObj) {
        let s = this.moneySources.find(s => s.type == source.type && s.id == source.id)
        if (s) {
            s.num += source.num
        }
        else {
            this.moneySources.push(source)
        }
    }

    public setMoney(source: ConditionObj) {
        let s = this.moneySources.find(s => s.type == source.type && s.id == source.id)
        if (s) {
            s.num = source.num
        }
        else {
            this.moneySources.push(source)
        }
    }

    public getNum() {
        let num = 0
        for (let source of this.moneySources) {
            num += source.num
        }
        return num
    }

    public getPrefabUrl() {
        let type = this.item.type
        if (type == ConditionType.STAR_DUST) {
            if (this.tips) {
                return 'drop/drop_tips'
            }
            return 'drop/DROP_STARDUST'
        }
        else if (type == ConditionType.PROP) {
            let id = this.item.id
            if (id == ItemID.ELECTRIC) {
                return 'drop/drop_electric'
            }
            else if (id == ItemID.WATER) {
                return 'drop/drop_water'
            }
            else if (id == ItemID.VITALITY) {
                return 'drop/drop_vitality'
            }
        }
     
    }
}