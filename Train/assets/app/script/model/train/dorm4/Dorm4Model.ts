
import { Dorm4BuildType } from "../../../common/constant/Enums";
import CarriageModel from "../common/CarriageModel";

/**
 * 四号宿舍
 */
export default class Dorm4Model extends CarriageModel {

    public getChairs() {
        return [this.getBuildByOrder(Dorm4BuildType.CHAIR_3), this.getBuildByOrder(Dorm4BuildType.CHAIR_7)]
    }

    public getBedLeft() {
        return this.getBuildByOrder(Dorm4BuildType.BED_1)
    }

    public getBedCenter() {
        return this.getBuildByOrder(Dorm4BuildType.BED_4)
    }

    public newBuildObj(type: Dorm4BuildType) {
        switch (type) {
            default:
                return super.newBuildObj(type)
        }
    }
}