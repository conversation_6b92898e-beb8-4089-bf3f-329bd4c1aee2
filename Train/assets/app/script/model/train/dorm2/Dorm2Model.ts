
import { Dorm2BuildType } from "../../../common/constant/Enums";
import BuildObj from "../common/BuildObj";
import CarriageModel from "../common/CarriageModel";
import Dorm2HearthObj from "./Dorm2HearthObj";
import Dorm2TableObj from "./Dorm2TableObj";
import Dorm2TVObj from "./Dorm2TVObj";

/**
 * 二号宿舍
 */
export default class Dorm2Model extends CarriageModel {

    public getBed() {
        return this.getBuildByOrder(Dorm2BuildType.BED)
    }

    public getHearth() {
        return this.getBuildByOrder(Dorm2BuildType.HEARTH) as Dorm2HearthObj
    }

    public getChairs() {
        return [Dorm2BuildType.CHAIR_1, Dorm2BuildType.CHAIR_2, Dorm2BuildType.CHAIR_3].map((type)=>{
            return this.getBuildByOrder(type)
        }).filter(build => !!build)
    }

    public getTable() {
        return this.getBuildByOrder(Dorm2BuildType.TABLE) as Dorm2TableObj
    }

    public getTV() {
        return this.getBuildByOrder(Dorm2BuildType.TV) as Dorm2TVObj
    }

    public newBuildObj(type: Dorm2BuildType) {
        switch (type) {
            case Dorm2BuildType.HEARTH: return new Dorm2HearthObj()
            case Dorm2BuildType.TV: return new Dorm2TVObj()
            case Dorm2BuildType.TABLE: return new Dorm2TableObj()
            default:
                return super.newBuildObj(type)
        }
    }

}