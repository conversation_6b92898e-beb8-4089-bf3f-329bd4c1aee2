
import { res<PERSON>elper } from "../../../common/helper/ResHelper"
import PassengerModel from "../../passenger/PassengerModel"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

export enum Dorm2TableObjState {
    INTO, //显示零食
    OUT, //消失
}

// 桌子
export default class Dorm2TableObj extends BuildObj {

    public state: StateObj<Dorm2TableObjState> = null
    public foods: string[] = [null, null]

    public addFood(index, url) {
        this.foods[index] = "food/" + url
        if (!this.state || this.state?.type == Dorm2TableObjState.INTO) {
            this.setState(Dorm2TableObjState.OUT, { time: Date.now() })
        }
    }

    public removeFood(index) {
        this.foods[index] = null
        let count = this.foods.filter(f => !!f).length
        if (count <= 0 && this.state?.type == Dorm2TableObjState.OUT) {
            this.setState(Dorm2TableObjState.INTO, { time: Date.now() })
        }
    }

    private setState(type?, data?) {
        this.state = new StateObj<Dorm2TableObjState>().init(type, data)
    }
}