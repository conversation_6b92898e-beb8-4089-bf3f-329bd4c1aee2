
import { res<PERSON>elper } from "../../../common/helper/ResHelper"
import PassengerModel from "../../passenger/PassengerModel"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

export enum Dorm2TVObjState {
    ON,
    OFF,
}

// 电视
export default class Dorm2TVObj extends BuildObj {

    public state: StateObj<Dorm2TVObjState> = null

    public onEnter(role: PassengerModel) {
        super.onEnter(role)
        if (this.roles.length > 0 && (!this.state || this.state?.type == Dorm2TVObjState.OFF)) {
            this.setState(Dorm2TVObjState.ON)
        }
    }

    public onExit(role: PassengerModel): void {
        super.onExit(role)
        if (this.roles.length <= 0 && this.state?.type == Dorm2TVObjState.ON) {
            this.setState(Dorm2TVObjState.OFF)
        }
    }

    public isOn () {
        return this.state?.type == Dorm2TVObjState.ON
    }

    private setState(type?, data?) {
        this.state = new StateObj<Dorm2TVObjState>().init(type, data)
    }
}