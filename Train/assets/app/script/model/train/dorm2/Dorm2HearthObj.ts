
import { res<PERSON><PERSON><PERSON> } from "../../../common/helper/ResHelper"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

export enum Dorm2HearthState {
    READY_FOOD,
    CUT_FOOD,
    WAIT_COOK,
    COOK,
    DONE,
}

// 灶台
export default class Dorm2HearthObj extends BuildObj {

    public state: StateObj<Dorm2HearthState> = null

    private foodCount: number = 0
    private lockFoodCount: number = 0
    public foodIndex: number = 0

    public async readyFood(action) {
        this.randomFood()
        action.onTerminate = () => {
            this.setState()
        }
        let time = 0.666 //food animation
        let timeData = new TimeStateData().init(time)
        this.setState(Dorm2HearthState.READY_FOOD, { timeData })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    public async cutFood(action) {
        action.onTerminate = () => {
            this.setState()
        }
        let time = this.getAnimsTime(["animation2"])
        let timeData = new TimeStateData().init(time)
        this.setState(Dorm2HearthState.CUT_FOOD, { timeData })
        await action.wait(timeData)
        action.onTerminate()
        this.setState(Dorm2HearthState.WAIT_COOK)
        action.ok()
    }

    public async cook(action) {
        let { time } = action.params
        action.onTerminate = () => {
            this.setState()
        }
        let timeData = new TimeStateData().init(time)
        this.setState(Dorm2HearthState.COOK, { timeData })
        await action.wait(timeData)
        this.cookDone()
        action.ok()
    }

    public cookDone() {
        this.foodCount += 1
        this.setState(Dorm2HearthState.DONE)
    }

    public checkReset() {
        if (this.state?.type != Dorm2HearthState.DONE) {
            this.setState()
        }
    }

    public lockFood() {
        this.lockFoodCount++
    }

    public unlockFood() {
        this.lockFoodCount--
    }

    public takeFood() {
        this.foodCount--
        this.lockFoodCount--
        if (this.foodCount <= 0) {
            this.setState()
        }
    }

    public hasFood() {
        return this.foodCount > this.lockFoodCount
    }

    private setState(type?, data?) {
        if (type == null) {
            this.state = null
        }
        else {
            this.state = new StateObj<Dorm2HearthState>().init(type, data)
        }
    }

    public randomFood() {
        this.foodIndex = ut.random(1, 3)
    }

}