import CarriageModel from "../common/CarriageModel";
import { EngineBuildType, SpeedUpType, WaterBuildType } from "../../../common/constant/Enums";
import WaterTableObj from "./WaterTableObj";
import WaterAirObj from "./WaterAirObj";
import WaterRunObj from "./WaterRunObj";
import WaterSaunaObj from "./WaterSaunaObj";
import WaterGameObj from "./WaterGameObj";
import WaterCloudObj from "./WaterCloudObj";
import WaterSaunaShellObj from "./WaterSaunaShellObj";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";

/**
 * 造水间
 */
export default class WaterModel extends CarriageModel {

    public clouds: WaterCloudObj[] = []

    public getTable() {
        return this.getBuildByOrder(WaterBuildType.TABLE) as WaterTableObj
    }

    public getAir() {
        return this.getBuildByOrder(WaterBuildType.AIR) as WaterAirObj
    }

    public getRun() {
        return this.getBuildByOrder(WaterBuildType.RUN) as WaterRunObj
    }

    public getSauna() {
        return this.getBuildByOrder(WaterBuildType.SAUNA) as WaterSaunaObj
    }

    public getSaunaShell() {
        return this.getBuildByOrder(WaterBuildType.SAUNA_SHELL) as WaterSaunaShellObj
    }

    public getGame() {
        return this.getBuildByOrder(WaterBuildType.GAME) as WaterGameObj
    }

    public newBuildObj(type: WaterBuildType) {
        switch (type) {
            case WaterBuildType.TABLE: return new WaterTableObj()
            case WaterBuildType.AIR: return new WaterAirObj()
            case WaterBuildType.RUN: return new WaterRunObj()
            case WaterBuildType.SAUNA: return new WaterSaunaObj()
            case WaterBuildType.SAUNA_SHELL: return new WaterSaunaShellObj()
            case WaterBuildType.GAME: return new WaterGameObj()
            default:
                return super.newBuildObj(type)
        }
    }

    public addCloud() {
        let cloud = new WaterCloudObj().init(this)
        this.clouds.push(cloud)
        eventCenter.emit(EventType.ADD_CLOUD, cloud)
        return cloud
    }

    public removeCloud(id) {
        this.clouds.remove("id", id)
        eventCenter.emit(EventType.REMOVE_CLOUD, id)
    }

    public getCloud(id) {
        return this.clouds.find(c => c.id == id)
    }

    protected updateByWorldDt(dt) {
        super.updateByWorldDt(dt)
        for (let cloud of this.clouds) {
            cloud.update(dt)
        }
    }

}