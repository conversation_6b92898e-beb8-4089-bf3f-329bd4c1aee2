import { BuildAnimation } from "../../../common/constant/Enums"
import PassengerModel from "../../passenger/PassengerModel"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

/**
 * 游戏机
 */

export enum WaterGameObjState {
    USE,
}
export default class WaterGameObj extends BuildObj {

    public state: StateObj<WaterGameObjState> = null

    public use(): void {
        this.reset()
        this.actionTree.start(this.onUse)
    }

    private async onUse(action) {
        let anim = BuildAnimation.USE
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        action.onTerminate = ()=>{
            this.setState()
        }
        this.setState(WaterGameObjState.USE, {anim, timeData})
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private setState(type?, data?) {
        this.state = new StateObj<WaterGameObjState>().init(type, data)
    }
}