import { ActionNode } from "../../passenger/ActionTree"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"
import CarriageModel from "../common/CarriageModel"
import WaterModel from "./WaterModel"

/**
 * 实验台
 */

export enum WaterTableObjState {
    USE,
    MAKE,
}

export default class WaterTableObj extends BuildObj {

    public state: StateObj<WaterTableObjState> = null

    public use() {
        this.reset()
        this.actionTree.start(async(action)=>{
            await action.run(this.onUse)
            await action.wait(1)
            this.setState()
            action.ok()
        })
    }

    public async onUse(action: ActionNode) {
        let anim = "aniUse"
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(WaterTableObjState.USE, { timeData })
        action.onTerminate = ()=>{
            this.setState()
        }
        let eventTime = this.getAnimEventTime(anim)
        action.wait(eventTime).then(()=>{
            this.makeCloud()
        })
        await action.wait(timeData)
        action.ok()
    }

    public canUse(index) {
        if (!super.canUse(index)) return false
        if (this.state != null) return false
        return true
    }

    private makeCloud() {
        let carriage = this.carriage as WaterModel
        let cloud = carriage.addCloud()
        cloud.startByTable()
        let pos = this.getUseById('birth').pos
        cloud.setPosition(pos)
    }

    private setState(type?, data?) {
        this.state = new StateObj<WaterTableObjState>().init(type, data)
    }

}