import { ActionNode } from "../../passenger/ActionTree"
import PassengerModel from "../../passenger/PassengerModel"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

/**
 *  桑拿机外壳
 */

export enum WaterSaunaShellObjState {
    USE_START,
    USE,
    USE_END,
}

export default class WaterSaunaShellObj extends BuildObj {
    public state: StateObj<WaterSaunaShellObjState> = null

    public onEnter(): void {
        this.reset()
        this.actionTree.start(this.onUseStart)
    }

    public onExit(): void {
        this.reset()
        this.actionTree.start(this.onUseEnd)
    }

    private async onUseStart(action: ActionNode) {
        let anim = "aniUse"
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(WaterSaunaShellObjState.USE_START, { timeData, anim })
        action.onTerminate = ()=>{
            this.setState()
        }
        await action.wait(timeData)
        this.setState(WaterSaunaShellObjState.USE, {anim: "aniUse2", loop: true})
        action.ok()
    }

    private async onUseEnd(action: ActionNode) {
        let anim = "aniUse3"
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(WaterSaunaShellObjState.USE_END, { timeData, anim })
        action.onTerminate = ()=>{
            this.setState()
        }
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private setState(type?, data?) {
        this.state = new StateObj<WaterSaunaShellObjState>().init(type, data)
    }
}