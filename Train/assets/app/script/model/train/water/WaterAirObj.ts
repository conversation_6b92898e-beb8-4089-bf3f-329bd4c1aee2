import { BuildAnimation } from "../../../common/constant/Enums"
import PassengerModel from "../../passenger/PassengerModel"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"
import WaterModel from "./WaterModel"

/**
 * 打气机
 */

export enum WaterAirObjState {
    USE,
}

export default class WaterAirObj extends BuildObj {

    public state: StateObj<WaterAirObjState> = null

    public onEnter(role: PassengerModel): void {
        this.setState(WaterAirObjState.USE, {anim: BuildAnimation.USE, loop: true})
    }

    public onExit(role: PassengerModel): void {
        this.setState()
    }

    private setState(type?, data?) {
        this.state = new StateObj<WaterAirObjState>().init(type, data)
    }
}