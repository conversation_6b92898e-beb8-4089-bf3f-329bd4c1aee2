import { BuildAnimation } from "../../../common/constant/Enums"
import { ActionNode } from "../../passenger/ActionTree"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

/**
 *  桑拿机
 */

export enum WaterSaunaObjState {
    ADD,
    COAL,
    USE,
}

export default class WaterSaunaObj extends BuildObj {

    public state: StateObj<WaterSaunaObjState> = null

    public add() {
        this.reset()
        this.actionTree.start(async(action)=>{
            await action.run(this.onAdd)
            action.ok()
        })
    }

    private async onAdd(action: ActionNode) {
        let addAnim = "aniAdd"
        let time = this.getAnimTime(addAnim)
        let timeData = new TimeStateData().init(time)
        this.setState(WaterSaunaObjState.ADD, { timeData, anim: addAnim })
        action.onTerminate = ()=>{
            this.setState()
        }
        await action.wait(timeData)
        this.setState(WaterSaunaObjState.COAL, {anim: "jingzhi2", loop: true})
        action.ok()
    }

    public onEnter() {
        this.reset()
        this.setState(WaterSaunaObjState.USE, {anim: BuildAnimation.USE, loop: true})
    }

    public onExit() {
        this.reset()
        this.setState()
    }

    public canUse(index) {
        if (!super.canUse(index)) return false
        let info = this.getUseByIndex(index)
        if (info.id == "add") {
            return this.state == null
        }
        else if (info.id == "use") {
            return this.state != null
        }
        return true
    }

    private setState(type?, data?) {
        this.state = new StateObj<WaterSaunaObjState>().init(type, data)
    }

}