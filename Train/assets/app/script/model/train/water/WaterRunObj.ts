import { BuildAnimation } from "../../../common/constant/Enums"
import PassengerModel from "../../passenger/PassengerModel"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

/**
 * 跑步机
 */

export enum WaterRunObjState {
    USE,
}

export default class WaterRunObj extends BuildObj {

    public state: StateObj<WaterRunObjState> = null

    public onEnter(role: PassengerModel): void {
        this.setState(WaterRunObjState.USE)
    }

    public onExit(role: PassengerModel): void {
        this.setState()
    }

    private setState(type?, data?) {
        this.state = new StateObj<WaterRunObjState>().init(type, data)
    }
}