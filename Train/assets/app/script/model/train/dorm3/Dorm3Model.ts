
import { StateType } from "../../passenger/StateEnum";
import { Dorm3BuildType } from "../../../common/constant/Enums";
import PassengerModel from "../../passenger/PassengerModel";
import CarriageModel from "../common/CarriageModel";
import BuildObj from "../common/BuildObj";
import BaseQueue from "../common/BaseQueue";
import Dorm3ShedObj from "./Dorm3ShedObj";

class Dorm3Queue extends BaseQueue {
    private model: Dorm3Model = null
    private aryQueueWC: PassengerModel[] = []
    constructor(model: Dorm3Model) {
        super()
        this.model = model
    }
    public updateOnce() {
        this.updateWCQueue()
    }
    public calculateQueueRobPos(stateQueue: StateType, i: number) {
        let build = this.model.getToilet()
        return this.getQueueWCPos(build, this.getIndexByEmpty(build, i))
    }
    private updateWCQueue() {
        this.updateQueue(this.aryQueueWC, StateType.WC_QUEUE)
    }
    public getQueueIndex(role: PassengerModel) {
        let ary = this.aryQueueWC
        return ary.findIndex(r => r == role)
    }
    public getCloseWCPos(role: PassengerModel) {
        let build = this.model.getToilet()
        let index = this.getQueueIndex(role)
        if (index == 0 && build.isEmpty()) {
            return build.getUsePos(1)//直接去厕所
        }
        return this.getQueueWCPos(build, index)//去排队
    }
    public getQueueWCPos(build: BuildObj, i: number) {
        return this.calculateQueuePosWC(build.getUsePos(0), i)
    }
    public crossWCLine(role: PassengerModel) {
        let rolePos = role.getPosition()
        let build = this.model.getToilet()
        let pos = build.getUsePos(0)
        if (rolePos.y < pos.y) return false
        let xr = rolePos.x
        let xb = pos.x
        return xr >= xb - 220 && xr <= xb + 220
    }
    public popQueueWC(role: PassengerModel) {
        this.aryQueueWC.remove(role)
    }
    public pushWCQueue(role: PassengerModel) {
        this.aryQueueWC.push(role)
        this.updateWCQueue()
    }
    private calculateQueuePosWC(pos: cc.Vec2, i: number = 0) {
        if (i == 0) {
            return cc.v2(pos.x + 330, pos.y - 75)
        } else if (i == 1) {
            return cc.v2(pos.x + 470, pos.y - 40)
        } else if (i >= 2) {
            let j = i - 2
            return cc.v2(pos.x + 600 + 150 * j, pos.y - 15)
        }
    }
}
/**
 * 三号宿舍
 */
export default class Dorm3Model extends CarriageModel {
    public queue: Dorm3Queue = null

    public init(data) {
        this.queue = new Dorm3Queue(this)
        return super.init(data)
    }

    protected updateByWorldDt(dt) {
        super.updateByWorldDt(dt)
        this.queue.update(dt)
    }

    public getShed() {
        return this.getBuildByOrder(Dorm3BuildType.SHED) as Dorm3ShedObj
    }

    public getBedCenter() {
        return this.getBuildByOrder(Dorm3BuildType.BED_1)
    }

    public getBedRight() {
        return this.getBuildByOrder(Dorm3BuildType.BED_6)
    }

    public getToilet() {
        return this.getBuildByOrder(Dorm3BuildType.TOILET)
    }

    public getChairs() {
        return [this.getBuildByOrder(Dorm3BuildType.CHAIR_3), this.getBuildByOrder(Dorm3BuildType.CHAIR_7)]
    }

    public getOutPoint() {
        let start: cc.Vec2, end: cc.Vec2
        let build = this.getBuildByOrder(Dorm3BuildType.WASHSTAND)
        if (build) {
            start = build.getUsePos(0), end = build.getUsePos(1)
        } else {
            build = this.getToilet()
            start = build.getUsePos(1), end = build.getUsePos(2)
        }
        return { start, end }
    }

    public newBuildObj(type: Dorm3BuildType) {
        switch (type) {
            case Dorm3BuildType.SHED: return new Dorm3ShedObj()
            default: return super.newBuildObj(type)
        }
    }
}