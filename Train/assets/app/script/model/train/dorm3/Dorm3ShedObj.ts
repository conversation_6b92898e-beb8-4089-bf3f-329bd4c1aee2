import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";

export enum Dorm3ShedObjState {
    OFF,
    KAI,
    GUAN,
}

export default class Dorm3ShedObj extends BuildObj {
    public state: StateObj<Dorm3ShedObjState> = null
    private pointGuan: cc.Vec2[] = []

    public onGuan(data) {
        this.updateShedPoints(true)
        this.setState(Dorm3ShedObjState.GUAN, data)
    }

    public onKai(data) {
        this.updateShedPoints(false)
        this.setState(Dorm3ShedObjState.KAI, data)
    }

    public onOff() {
        this.setState(Dorm3ShedObjState.OFF)
    }

    public onTerminate() {
        if (this.state.type == Dorm3ShedObjState.GUAN) {
            this.updateShedPoints(false)
        }
        this.onOff()
    }

    private setState(type?, data?) {
        this.state = new StateObj<Dorm3ShedObjState>().init(type, data)
    }

    public random() {
        return ut.random(10, 30)
    }

    //--------------动态阻塞--------------
    private updateShedPoints(add: boolean) {
        this.map?.updatePointsByPoints(this.pointGuan, add)
    }
    public getActPoints(add: boolean) {
        let ary = [{ add, points: this.points }]
        if (this.state?.type == Dorm3ShedObjState.GUAN) {
            ary.push({ add, points: this.pointGuan })
        }
        return ary
    }
    protected initPoints(colliders) {
        this.pointGuan = this.splitLastCollider(colliders)
    }
}
