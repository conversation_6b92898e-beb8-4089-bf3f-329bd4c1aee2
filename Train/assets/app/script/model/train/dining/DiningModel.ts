import BuildObj from "../common/BuildObj";
import BaseQueue from "../common/BaseQueue";
import BuildQueueObj from "../common/BuildQueueObj";
import CarriageModel from "../common/CarriageModel";
import PassengerModel from "../../passenger/PassengerModel";
import DiningOrderFoodObj from "./DiningOrderFoodObj";
import DiningOrderDrinkObj from "./DiningOrderDrinkObj";
import DiningOrderFoodBakingObj from "./DiningOrderFoodBakingObj";
import DiningChair12Obj from "./DiningChair12Obj";
import DiningTableObj from "./DiningTableObj";
import { DiningBuildType, GoodsType } from "../../../common/constant/Enums";
import { StateType } from "../../passenger/StateEnum";

class DiningQueue extends BaseQueue {
    protected timeMax: number = 3
    private model: DiningModel = null
    private aryQueueFood: PassengerModel[] = []
    private aryQueueDrink: PassengerModel[] = []
    constructor(model: DiningModel) {
        super()
        this.model = model
    }

    public updateOnce() {
        this.updateFoodQueue()
        this.updateDrinkQueue()
    }

    public calculateQueueRobPos(stateQueue: StateType, i: number) {
        let build = stateQueue == StateType.DINING_FOOD_QUEUE ? this.model.getOrderFood() : this.model.getOrderDrink()
        return this.getDoPos(build)
    }

    public getQueueIndex(role: PassengerModel, type: GoodsType) {
        let bol = type == GoodsType.FOOD
        let ary = bol ? this.aryQueueFood : this.aryQueueDrink
        return ary.findIndex(r => r == role)
    }

    public getQueuePos(role: PassengerModel, build: BuildQueueObj, doPos: cc.Vec2, type: GoodsType) {
        let bol = type == GoodsType.FOOD
        let i = this.getQueueIndex(role, type)
        if (!build.isQueueEmpty()) i++
        if (bol) return this.calculateQueuePosFood(doPos, i)
        return this.calculateQueuePosDrink(doPos, i)
    }

    public getQueueFoodPos(role: PassengerModel) {
        let build = this.model.getOrderFood()
        return this.getQueuePos(role, build, this.getDoPos(build), GoodsType.FOOD)
    }

    public getQueueDrinkPos(role: PassengerModel) {
        let build = this.model.getOrderDrink()
        return this.getQueuePos(role, build, this.getDoPos(build), GoodsType.DRINK)
    }

    public popQueueFood(role: PassengerModel) {
        this.aryQueueFood.remove(role)
    }

    public popQueueDrink(role: PassengerModel) {
        this.aryQueueDrink.remove(role)
    }

    public pushFoodQueue(role: PassengerModel) {
        this.aryQueueFood.push(role)
        this.updateFoodQueue()
    }

    public pushDrinkQueue(role: PassengerModel) {
        this.aryQueueDrink.push(role)
        this.updateDrinkQueue()
    }

    private updateFoodQueue() {
        this.updateQueue(this.aryQueueFood, StateType.DINING_FOOD_QUEUE)
    }

    private updateDrinkQueue() {
        this.updateQueue(this.aryQueueDrink, StateType.DINING_DRINK_QUEUE)
    }

    private calculateQueuePosFood(pos: cc.Vec2, i: number = 0) {
        if (i == 0) return pos
        let off = this.calculateOffPos(i)
        return cc.v2(pos.x + off.x, pos.y - off.y)
    }

    private calculateQueuePosDrink(pos: cc.Vec2, i: number = 0) {
        if (i == 0) return pos
        let off = this.calculateOffPos(i)
        return cc.v2(pos.x - off.x, pos.y - off.y)
    }
}

/**
 * 餐厅
 */
export default class DiningModel extends CarriageModel {
    public queue: DiningQueue
    private mapTableChairs = {
        [DiningBuildType.TABLE_3]: [DiningBuildType.CHAIR_2, DiningBuildType.CHAIR_5, DiningBuildType.CHAIR_6],
        [DiningBuildType.TABLE_10]: [DiningBuildType.CHAIR_9, DiningBuildType.CHAIR_12],
    }

    public init(data) {
        this.queue = new DiningQueue(this)
        super.init(data)
        this.map.setMapScaleRange(0.9, 0.82)
        return this
    }

    protected updateByWorldDt(dt) {
        super.updateByWorldDt(dt)
        this.queue.update(dt)
    }

    public canOrderFood() {
        if (this.getGoodsByType(GoodsType.FOOD).length <= 0) return false
        return this.checkBuildQueue(this.getOrderFood())
    }

    protected checkBuildQueue(build: BuildObj) {
        if (!build) return false
        if (!build.canQueue()) return false
        return true
    }

    public getOrderFood() {
        return this.getBuildByOrder(DiningBuildType.ORDER_FOOD) as DiningOrderFoodObj
    }

    public canOrderDrink() {
        if (this.getGoodsByType(GoodsType.DRINK).length <= 0) return false
        return this.checkBuildQueue(this.getOrderDrink())
    }

    public getOrderDrink() {
        return this.getBuildByOrder(DiningBuildType.ORDER_DRINK) as DiningOrderDrinkObj
    }

    public getBakingMachine() {
        return this.getBuildByOrder(DiningBuildType.BAKING_MACHINE) as DiningOrderFoodBakingObj
    }

    public getPutIndex(tableType: number, chairType: number) {
        let index = 0
        if (tableType == DiningBuildType.TABLE_3) {
            if (chairType == DiningBuildType.CHAIR_5) index = 1
            else if (chairType == DiningBuildType.CHAIR_6) index = 2
        } else if (tableType == DiningBuildType.TABLE_10) {
            if (chairType == DiningBuildType.CHAIR_9) index = 1
        }
        return index
    }

    public getChairs() {
        let tmp = []
        let dic = this.mapTableChairs
        for (const key in dic) {
            const ary = dic[key]
            for (const type of ary) {
                let build = this.getBuildByOrder(type)
                if (build != null) {
                    tmp.push(build)
                }
            }
        }
        return tmp
    }

    public getTableByChair(type: number) {
        let key = this.getTypeTable(type)
        if (key) {
            return this.getBuildByOrder(key) as DiningTableObj
        }
    }

    public haveTable(chairType: number) {
        return this.getTableByChair(chairType) != null
    }

    public getChairPaths(build: BuildObj, role: PassengerModel, fromType?: DiningBuildType) {
        let type = build.type
        let ary = null
        let force = true
        if (type == DiningBuildType.CHAIR_9) {
            if (fromType == null) {
                if (this.haveTable(type)) {
                    ary = [3, 4]
                    let index = ary.min(i => role.getPosition().sub(build.getUsePos(i)).magSqr())
                    if (index == 3) {
                        return [{ index }, { index: 1, force }, { index: 0, force }]
                    } else {
                        return [{ index }, { index: 0, force }]
                    }
                } else {
                    ary = [1, 2]
                }
            } else {
                if (this.isShortFood(fromType)) {
                    return [{ index: 6, force }, { index: 0, force }]
                }
                return [{ index: 5 }, { index: 1, force }, { index: 0, force }]
            }
        } else if (type == DiningBuildType.CHAIR_12) {
            if (fromType == null) {
                ary = [1, 2]
            } else {
                if (this.isShortFood(fromType)) {
                    return [{ index: 0, force }]
                }
                return [{ index: 3 }, { index: 0, force }]
            }
        } else {
            if (this.haveTable(type)) {
                ary = [3, 4]
            } else {
                ary = [1, 2]
            }
        }
        if (!ary) {
            return [{ index: 0 }]
        }
        let index = ary.min(i => role.getPosition().sub(build.getUsePos(i)).magSqr())
        return [{ index }, { index: 0, force }]
    }

    public getChairOutPaths(type: number) {
        let index = null
        if (type == DiningBuildType.CHAIR_9) {
            index = this.haveTable(type) ? 4 : 2
        } else if (type == DiningBuildType.CHAIR_12) {
            index = 2
        } else {
            if (this.haveTable(type)) {
                index = [3, 4].random()
            }
        }
        if (index != null) {
            return [{ index }]
        }
    }

    private isShortFood(fromType: DiningBuildType) {
        return fromType == DiningBuildType.ORDER_FOOD && !this.getBakingMachine()
    }

    private getTypeTable(typeChair: number) {
        let dic = this.mapTableChairs
        for (const key in dic) {
            const ary = dic[key] as Array<number>
            if (ary.has(typeChair)) {
                return key
            }
        }
    }

    public newBuildObj(type: DiningBuildType) {
        switch (type) {
            case DiningBuildType.ORDER_FOOD: return new DiningOrderFoodObj()
            case DiningBuildType.ORDER_DRINK: return new DiningOrderDrinkObj()
            case DiningBuildType.BAKING_MACHINE: return new DiningOrderFoodBakingObj()
            case DiningBuildType.TABLE_3: return new DiningTableObj()
            case DiningBuildType.TABLE_10: return new DiningTableObj()
            case DiningBuildType.CHAIR_12: return new DiningChair12Obj()
            default: return super.newBuildObj(type)
        }
    }
}