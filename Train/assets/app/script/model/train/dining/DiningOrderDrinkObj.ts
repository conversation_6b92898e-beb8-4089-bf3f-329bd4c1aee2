import { GoodsType } from "../../../common/constant/Enums";
import StateObj from "../../passenger/StateObj";
import BuildQueueObj from "../common/BuildQueueObj";
import GoodsObj from "../common/GoodsObj";

export enum DiningOrderDrinkObjState {
    ON,
    OFF,
}

export default class DiningOrderDrinkObj extends BuildQueueObj {
    public juiceId: number = 0
    public drink: GoodsObj = null
    public state: StateObj<DiningOrderDrinkObjState> = null

    public onMake(data) {
        this.setState(DiningOrderDrinkObjState.ON, data)
    }

    public onOff() {
        this.setState(DiningOrderDrinkObjState.OFF)
    }

    public onTerminate() {
        this.onOff()
    }

    private setState(type?, data?) {
        this.state = new StateObj<DiningOrderDrinkObjState>().init(type, data)
    }

    public random(roleId: number) {
        this.drink = this.carriage.randomGoodsByRole(GoodsType.DRINK, roleId)
        return this.drink
    }

    public getUrlById() {
        return `take/${this.drink.takeIcon}`
    }
}
