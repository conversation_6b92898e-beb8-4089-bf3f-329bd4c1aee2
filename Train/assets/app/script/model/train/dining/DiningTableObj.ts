import BuildObj from "../common/BuildObj";

export default class DiningTableObj extends BuildObj {
    public foods: string[] = []

    public addFood(index, url) {
        this.foods[index] = "food/icon/" + url
    }

    public removeFood(index) {
        this.foods[index] = null
    }

    public getFoodCount() {
        let count = 0
        this.foods.forEach(f => { if (f) count++ })
        return count
    }
}
