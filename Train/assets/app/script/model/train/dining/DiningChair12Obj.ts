import BuildObj from "../common/BuildObj";


export default class DiningChair12Obj extends BuildObj {

    public getSitPos(roleId: number): cc.Vec2 {
        switch (roleId) {
            case 1002:
                return cc.v2(-20, -6)
            case 1003:
                return cc.v2(-10, -7)
            case 1004:
                return cc.v2(-8, -7)
            case 1006:
                return cc.v2(0, -12)
            case 1007:
                return cc.v2(0, -14)
            case 1009:
                return cc.v2(-23, -20)
            case 1010:
                return cc.v2(-15, 0)
            case 1015:
                return cc.v2(-18, 0)
            default:
                return cc.v2(0, 0)
        }
    }

}
