import { CarriageID } from "../../../common/constant/Enums";
import { gameHelper } from "../../../common/helper/GameHelper";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj"
import GoodsObj from "../common/GoodsObj";
import DiningModel from "./DiningModel";

export enum DiningOrderFoodBakingObjState {
    SCREEN_START,
    SCREEN_END,
    MAKE,
    MAKE_END,
    OFF,
}

export default class DiningOrderFoodBakingObj extends BuildObj {
    public food: GoodsObj = null
    public state: StateObj<DiningOrderFoodBakingObjState> = null

    public buildingStart() {
        super.buildingStart()
        let obj = this.getFood()
        if (obj) {
            obj.buildBakingStart()
        }
    }

    public async buildingEnd(delay: number) {
        await super.buildingEnd(delay)
        this.getFood()?.buildBakingEnd()
    }

    public onScreenStart(data) {
        this.setState(DiningOrderFoodBakingObjState.SCREEN_START, data)
    }

    public onScreenEnd(data) {
        this.setState(DiningOrderFoodBakingObjState.SCREEN_END, data)
    }

    public onMake(data) {
        this.setState(DiningOrderFoodBakingObjState.MAKE, data)
    }

    public onMakeEnd(data) {
        this.setState(DiningOrderFoodBakingObjState.MAKE_END, data)
    }

    public onOff() {
        this.setState(DiningOrderFoodBakingObjState.OFF)
    }

    private setState(type?, data?) {
        this.state = new StateObj<DiningOrderFoodBakingObjState>().init(type, data)
    }

    private getFood() {
        let carriage = gameHelper.train.getCarriageById(CarriageID.DINING) as DiningModel
        return carriage.getOrderFood()
    }
}
