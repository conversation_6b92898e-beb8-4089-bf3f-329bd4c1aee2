import { CarriageID, GoodsType } from "../../../common/constant/Enums";
import { gameHelper } from "../../../common/helper/GameHelper";
import StateObj from "../../passenger/StateObj";
import BuildQueueObj from "../common/BuildQueueObj";
import GoodsObj from "../common/GoodsObj";
import DiningModel from "./DiningModel";

export enum DiningOrderFoodObjState {
    START,
    SCREEN_START,
    SCREEN_END,
    MAKE,
    MAKE_END,
    OFF,
    BUILD_BAKING,
}

export default class DiningOrderFoodObj extends BuildQueueObj {
    public food: GoodsObj = null
    public state: StateObj<DiningOrderFoodObjState> = null

    public onStart(data) {
        this.setState(DiningOrderFoodObjState.START, data)
    }

    public onScreenStart(data) {
        this.setState(DiningOrderFoodObjState.SCREEN_START, data)
        this.getBaking()?.onScreenStart(data)
    }

    public onScreenEnd(data) {
        this.setState(DiningOrderFoodObjState.SCREEN_END, data)
        this.getBaking()?.onScreenEnd(data)
    }

    public onMake(data) {
        this.setState(DiningOrderFoodObjState.MAKE, data)
        this.getBaking()?.onMake(data)
    }

    public onOff() {
        this.setState(DiningOrderFoodObjState.OFF)
        this.getBaking()?.onOff()
    }

    public onTerminate() {
        this.onOff()
    }

    public buildBakingStart() {
        this.isBuilding = true
        this.setState(DiningOrderFoodObjState.BUILD_BAKING)
    }

    public buildBakingEnd() {
        this.isBuilding = false
        this.setState(DiningOrderFoodObjState.OFF)
    }

    private setState(type?, data?) {
        this.state = new StateObj<DiningOrderFoodObjState>().init(type, data)
    }

    public getBaking() {
        let carriage = gameHelper.train.getCarriageById(CarriageID.DINING) as DiningModel
        return carriage.getBakingMachine()
    }

    private haveBaking() {
        return this.getBaking() != null
    }

    public getSkin() {
        if (this.skin == 1) {
            let pre = 'chucankou'
            return this.haveBaking() ? pre + 2 : pre + 1
        }
        return "default"
    }

    public random(roleId: number) {
        this.food = this.carriage.randomGoodsByRole(GoodsType.FOOD, roleId)
        let build = this.getBaking()
        if (build) build.food = this.food
    }

    public havePanzi() {
        return !this.food.handsFood
    }

    public getUrlById() {
        return `take/${this.food.takeIcon}`
    }
}
