import EventType from "../../common/event/EventType"
import { BuildCfg } from "../../common/constant/DataType"
import { CarriageID } from "../../common/constant/Enums"
import { ActType } from "../passenger/themeActions/ActionCfg"
import CarriageModel from "./common/CarriageModel"
import CarriageMap from "./common/CarriageMap"
import { cfgHelper } from "../../common/helper/CfgHelper"
import UnioinFind from "../map/UnioinFind"
import StateObj from "../passenger/StateObj"

class BuildHeadObj {
    public id: string
    public idx: number//设施序号
    public tIdx: number//主题序号

    public init(idx: number, themeIdx: number) {
        this.idx = idx
        this.tIdx = themeIdx
        this.id = this.getBuildId()
        return this
    }
    public initCfg(cfg: BuildCfg) {
        this.id = cfg.id
        this.idx = cfg.order
        this.tIdx = cfg.themeIndex
        return this
    }
    public toDB() {
        return {
            idx: this.idx
        }
    }
    public getBuildId(): string {
        return `${CarriageID.HEAD}-${this.tIdx}-${this.idx}`
    }
}

class ThemeHeadModel {
    public idx: number = null;//主题序号
    public builds: BuildHeadObj[] = []; //车头设施

    public init(data: any) {
        this.idx = data.idx
        let ary = data.builds
        if (ary) {
            for (let info of ary) {
                this.builds.push(new BuildHeadObj().init(info.idx, this.idx))
            }
        }
        return this
    }
    public toDB() {
        return {
            idx: this.idx,
            builds: this.builds.map(build => build.toDB()),
        }
    }
    public unlockBuild(buildId: string, fromBuy: boolean = false, delay: number = 0) {
        let cfg = cfgHelper.getBuildById(buildId)
        if (!cfg) {
            return twlog.error('ThemeHead unlockBuild fail.no cfg:', buildId)
        }
        let obj = new BuildHeadObj().initCfg(cfg)
        this.builds.push(obj);
        eventCenter.emit(EventType.UNLOCK_HEAD_BUILD, obj, fromBuy, delay)
    }
    public getBuildById(id: string) {
        return this.builds.find(m => m.id === id)
    }
}

export class TrainHeadMap extends CarriageMap {

    protected scaleRange: number[] = [0.91, 0.91]

    init(carriageId?: number) {
        this.carriageId = carriageId
        this.gridSize = 20
        this.size = cc.size(42, 1)
        this.setBasePoint(cc.v2(-140, 0))
        this.unioinFind = new UnioinFind().init(this)
        return this
    }

    public getTransPoint(left: boolean) {
        return this.getActPointByPixel(cc.v2(0, 0))
    }

    // 根据网格点获取像素点
    public getActPixelByPoint(point: cc.Vec2, out?: cc.Vec2) {
        out = out || cc.v2()
        out.x = (point.x + 0.5) * this.gridSize
        out.y = 0
        out.addSelf(this.basePoint)
        return out
    }

    // 根据像素点获取网格点
    public getActPointByPixel(pos: cc.Vec2, out?: cc.Vec2) {
        out = out || cc.v2()
        pos = pos.sub(this.basePoint)
        out.x = Math.floor(pos.x / this.gridSize)
        out.y = 0
        return out
    }
}

export enum TrainHeadState {
    STARGAZE,
}

/**
 * 车头数据模型相关
 */
class TrainHeadModel extends CarriageModel {
    public states: StateObj<TrainHeadState>[] = []

    public init(data: any): this {
        super.init(data)
        return this
    }

    protected initMap() {
        this.map = new TrainHeadMap().init();
    }

    public getChair() {
        return this.getBuildByOrder(1)
    }

    public stargazeStart() {
        this.pushState(TrainHeadState.STARGAZE)
    }

    public stargazeEnd() {
        this.popState(TrainHeadState.STARGAZE)
    }

    public getState(type: TrainHeadState) {
        return this.states.find(t => t.type == type)
    }

    private pushState(type, data?) {
        let state = new StateObj<TrainHeadState>().init(type, data)
        this.states.push(state)
    }

    private popState(type) {
        this.states.remove("type", type)
    }

    public update(dt: any): void {
        this.updateLight()
    }
}

export {
    BuildHeadObj,
    ThemeHeadModel,
    TrainHeadModel,
}