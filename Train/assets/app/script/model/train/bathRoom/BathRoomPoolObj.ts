import BuildObj from "../common/BuildObj";

export default class BathRoomPoolObj extends BuildObj {
    protected max: number = 3

    public canBath() {
        if (this.isBuilding) return false
        for (let i = 0; i < this.max; i++) {
            if (this.isEmpty(i)) return true
        }
        return false
    }
    public randomIndex() {
        let ary: number[] = []
        for (let i = 0; i < this.max; i++) {
            if (this.isEmpty(i)) {
                ary.push(i)
            }
        }
        return ary.random()
    }
    public randomTime() {
        return ut.random(15, 30)
    }
}
