
import { CarriageID, GoodsObjectType, GoodsType } from "../../../common/constant/Enums";
import { gameHelper } from "../../../common/helper/GameHelper";
import StateObj from "../../passenger/StateObj";
import BuildObj from "../common/BuildObj";
import GoodsObj from "../common/GoodsObj";

export enum BathRoomFrogObjState {
    OFF,
    TAKE,
    WAIT,
}

export default class BathRoomFrogObj extends BuildObj {
    public state: StateObj<BathRoomFrogObjState> = null
    private teaObj: GoodsObj = null

    public onTake(data) {
        this.randomTea(data.id)
        this.setState(BathRoomFrogObjState.TAKE, data)
    }
    public onWait(data) {
        this.setState(BathRoomFrogObjState.WAIT, data)
    }
    public onOff() {
        this.setState(BathRoomFrogObjState.OFF)
    }
    public onTerminate() {
        this.onOff()
    }

    private setState(type?, data?) {
        this.state = new StateObj<BathRoomFrogObjState>().init(type, data)
    }

    private randomTea(roleId: number) {
        this.teaObj = this.carriage.randomGoodsByRole(GoodsType.TEA, roleId)
        // 有专属的用专属 没有专属的用通用的
        // let one = ary.find(obj => obj.objects.find(m => m.type == GoodsObjectType.CHARACTER && m.id == roleId) != null)
        // if (one) {
        //     this.teaObj = one
        //     return
        // }
        // let list = ary.filter(m => m.objects.length == 0)
        // this.teaObj = list.random()
    }
    public getTeaUrl() {
        return `frog/frog_${this.teaObj?.icon}`
    }
    public getPaopaoUrl() {
        return `drop/${this.teaObj?.drop}`
    }
}