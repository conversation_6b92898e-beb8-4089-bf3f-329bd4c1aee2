import { BathRoomType } from "../../../common/constant/Enums";
import { StateType } from "../../passenger/StateEnum";
import BuildObj from "../common/BuildObj";
import BaseQueue from "../common/BaseQueue";
import CarriageModel from "../common/CarriageModel";
import PassengerModel from "../../passenger/PassengerModel";
import BathRoomFrogObj from "./BathRoomFrogObj";
import BathRoomPoolObj from "./BathRoomPoolObj";
import BathRoomBoardObj from "./BathRoomBoardObj";

class BathRoomQueue extends BaseQueue {
    private model: BathRoomModel = null
    private aryQueueFrog: PassengerModel[] = []
    constructor(model: BathRoomModel) {
        super()
        this.model = model
    }
    public updateOnce() {
        this.updateQueue(this.aryQueueFrog, StateType.FROG_QUEUE)
    }
    public calculateQueueRobPos(stateQueue: StateType, i: number) {
        let build = this.model.getFrog()
        return this.getQueueTeaPos(build, this.getIndexByEmpty(build, i))
    }
    public popQueueTea(role: PassengerModel) {
        this.aryQueueFrog.remove(role)
    }
    public pushQueueTea(role: PassengerModel) {
        this.aryQueueFrog.push(role)
        this.updateOnce()
    }
    public getQueueIndex(role: PassengerModel) {
        let ary = this.aryQueueFrog
        return ary.findIndex(r => r == role)
    }
    public getCloseTeaPos(role: PassengerModel) {
        let build = this.model.getFrog()
        let index = this.getQueueIndex(role)
        return this.getQueueTeaPos(build, this.getIndexByEmpty(build, index))
    }
    private getQueueTeaPos(build: BuildObj, i: number) {
        return this.calculateQueuePosFrog(build.getUsePos(0), i)
    }
    private calculateQueuePosFrog(pos: cc.Vec2, i: number = 0) {
        if (i == 0) return pos
        let off = this.calculateOffPos(i)
        return cc.v2(pos.x + off.x, pos.y - off.y)
    }
}
/** 浴室 */
export default class BathRoomModel extends CarriageModel {
    public queue: BathRoomQueue = null

    public init(data) {
        this.queue = new BathRoomQueue(this)
        return super.init(data)
    }

    public canBath() {
        let left = this.getBuildByOrder(BathRoomType.POOL_LEFT) as BathRoomPoolObj
        if (left && left.canBath()) return true
        let right = this.getBuildByOrder(BathRoomType.POOL_RIGHT) as BathRoomPoolObj
        return right && right.canBath()
    }

    public randomBath() {
        let ary: BathRoomPoolObj[] = []
        let left = this.getBuildByOrder(BathRoomType.POOL_LEFT) as BathRoomPoolObj
        if (left && left.canBath())
            ary.push(left)
        let right = this.getBuildByOrder(BathRoomType.POOL_RIGHT) as BathRoomPoolObj
        if (right && right.canBath())
            ary.push(right)
        let build = ary.random()
        return { build, index: build.randomIndex() }
    }

    public getPoolPaths(type: BathRoomType) {
        let ary = [{ index: 0 }]
        if (type == BathRoomType.POOL_LEFT)
            ary.push({ index: 1 })
        return ary
    }

    public getFrog() {
        return this.getBuildByOrder(BathRoomType.FROG) as BathRoomFrogObj
    }

    public newBuildObj(type: BathRoomType) {
        switch (type) {
            case BathRoomType.FROG: return new BathRoomFrogObj()
            case BathRoomType.POOL_LEFT: return new BathRoomPoolObj()
            case BathRoomType.POOL_RIGHT: return new BathRoomPoolObj()
            case BathRoomType.BOARD_LEFT: return new BathRoomBoardObj()
            case BathRoomType.BOARD_RIGHT: return new BathRoomBoardObj()
            default:
                return super.newBuildObj(type)
        }
    }
}