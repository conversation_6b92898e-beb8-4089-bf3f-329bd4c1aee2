import { CarriageUsePosType, RoleDir } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"
import { StateType } from "../../passenger/StateEnum"
import StateObj from "../../passenger/StateObj"
import DormBirdAction from "../../passenger/themeActions/DormBirdAction"


export default class DormBirdModel {

    public position: cc.Vec2 = null
    public action: DormBirdAction = null

    private _flyIn: boolean = false
    private _states: StateObj<StateType>[] = []
    private _carriageId: number = 1013
    private _skin: string
    private _dir: RoleDir = RoleDir.RIGHT
    private _isMoving: boolean = false

    get flyIn() { return this._flyIn }
    get skin() { return this._skin }
    get carriage() { return gameHelper.train.getCarriageById(this._carriageId) }
    get dir() { return this._dir }
    get isMoving() { return this._isMoving }
    set isMoving(v) { this._isMoving = v }
    get state() { return this._states.last() }

    public static create(skin: string) {
        const model = new DormBirdModel()
        model._skin = skin
        model.initPos()
        const action = new DormBirdAction()
        action.setBy(model)
        model.action = action

        console.warn(`skin: ${skin}, pos: ${model.position}`)
        return model
    }


    public getPosition() { return this.position }

    protected initPos() {
        let posList = this.carriage.getUsePosListByType(CarriageUsePosType.BIRD_IN)
        this.position = posList.random().pos
    }

    public pushState(type: StateType, data = null) {
        let state = this._states.find(s => s.type == type)
        if (state) {
            twlog.error("pushState重复", this._skin, type)
            return
        }
        state = new StateObj<StateType>().init(type, data)
        this._states.push(state);
    }

    public popState(type: StateType) { this._states.remove("type", type) }

    update(dt: number) {
        this.action?.update(dt);
    }

}