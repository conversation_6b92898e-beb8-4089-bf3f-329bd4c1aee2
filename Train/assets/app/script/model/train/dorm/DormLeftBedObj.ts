
import { res<PERSON>elper } from "../../../common/helper/ResHelper"
import ActionTree, { ActionNode } from "../../passenger/ActionTree"
import PassengerModel from "../../passenger/PassengerModel"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

export enum DormLeftBedObjState {
    SWING, // 摇摇椅
    SING_ENTER,
    SING_IDLE,
    SING_EXIT,
}

// 左边床
export default class DormLeftBedObj extends BuildObj {

    public state: StateObj<DormLeftBedObjState> = null

    public toSwing() {
        this.reset()
        return this.actionTree.start(this.swing)
    }

    protected async baseHandle(action: ActionNode, anim: string, state: DormLeftBedObjState) {
        action.onTerminate = () => this.setState()
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(state, { timeData })
        await action.wait(timeData)
        action.ok()
    }

    public async swing(action: ActionNode) {
        await this.baseHandle(action, "ani_swing", DormLeftBedObjState.SWING)
    }

    public async sing(action: ActionNode) {
        const anims = [
            { str: "sing_enter", state: DormLeftBedObjState.SING_ENTER },
            { str: "sing_idle", state: DormLeftBedObjState.SING_IDLE },
            { str: "sing_exit", state: DormLeftBedObjState.SING_EXIT },
        ]
        action.onTerminate = () => this.setState()
        for (let anim of anims) {
            let time = this.getAnimTime(anim.str)
            let timeData = new TimeStateData().init(time)
            this.setState(anim.state, { timeData })
            await action.wait(timeData)
        }
        action.ok()
    }

    private setState(type?, data?) {
        this.state = new StateObj<DormLeftBedObjState>().init(type, data)
    }

    public reset() {
        this.actionTree.terminate()
    }

    update(dt) {
        this.actionTree && this.actionTree.update(dt)
    }
}