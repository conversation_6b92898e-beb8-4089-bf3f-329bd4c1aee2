
import { res<PERSON>elper } from "../../../common/helper/ResHelper"
import ActionTree, { ActionNode } from "../../passenger/ActionTree"
import PassengerModel from "../../passenger/PassengerModel"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

export enum DormRightBedObjState {
    UP,
    DOWN,
    SWING,
    BIRD_EGG
}

// 右边床
export default class DormRightBedObj extends BuildObj {

    public state: StateObj<DormRightBedObjState> = null

    public toUp() {
        this.reset()
        return this.actionTree.start(this.up)
    }

    public toDown() {
        this.reset()
        return this.actionTree.start(this.down)
    }

    public toSwing() {
        this.reset()
        return this.actionTree.start(this.swing)
    }

    public toBirdEgg() {
        this.reset()
        return this.actionTree.start(this.birdEgg)
    }

    protected async baseHandle(action: ActionNode, anim: string, state: DormRightBedObjState) {
        action.onTerminate = () => this.setState()
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(state, { timeData })
        await action.wait(timeData)
        action.ok()
    }

    public async up(action: ActionNode) {
        await this.baseHandle(action, "aniUp", DormRightBedObjState.UP)
    }

    public async down(action: ActionNode) {
        await this.baseHandle(action, "aniDown", DormRightBedObjState.DOWN)
    }

    public async swing(action: ActionNode) {
        await this.baseHandle(action, "ani_swing", DormRightBedObjState.SWING)
    }

    public async birdEgg(action: ActionNode) {
        await this.baseHandle(action, "ani_bird", DormRightBedObjState.BIRD_EGG)
    }

    public isDown() {
        return this.state?.type == DormRightBedObjState.DOWN
    }

    private setState(type?, data?) {
        this.state = new StateObj<DormRightBedObjState>().init(type, data)
    }

    public reset() {
        this.actionTree.terminate()
    }

    update(dt) {
        this.actionTree && this.actionTree.update(dt)
    }
}