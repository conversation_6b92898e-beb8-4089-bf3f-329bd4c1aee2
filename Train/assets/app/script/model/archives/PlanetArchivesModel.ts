import { Msg } from "../../../proto/msg-define";
import { PlanetProfileCfg } from "../../common/constant/DataType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

@mc.addmodel('planetArchives')
export default class PlanetArchivesModel extends mc.BaseModel {

    public data: number[]

    private profileList: PlanetProfileCfg[] = []

    public init() {
        this.profileList = this.data.map(id => cfgHelper.getPlanetProfileById(id))
        gameHelper.bag.loadPlanetProfile()
    }

    public getList() { return this.profileList }
    public getListByTypes(types: number[]) {
        return this.profileList.filter(t => types.has(t.type))
    }

    //这里只支持num == 1或-1
    public addProfile(id: number, num: number) {
        let data = cfgHelper.getPlanetProfileById(id)
        if (num > 0) {
            this.profileList.push(data)
        }
        else {
            this.profileList.splice(this.profileList.findIndex(t => t.id == id), 1)
        }
        gameHelper.bag.loadPlanetProfile()
    }

    public async unlock(profileId: number, planetId: number, index: number = 1) {
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_UnlockProfileMessage, { planetId, id: profileId, index })
        if (r.code == 0) {
            let planet = gameHelper.planet.getPlanet(planetId)
            planet.unlockProfile(profileId, index)
            this.addProfile(profileId, -1)
            return true
        }
        viewHelper.showNetError(r.code)
        return false
    }

    public getProgressByPlanetId(planetId: number, area?: number): { cur: number, total: number } {
        const ary = this.profileList.filter(t => t.planetId == planetId && (area == undefined || t.area == area))
        const totalAry = cfgHelper.getPlanetProfileByPlanetId(planetId)
        let activeNum = 0
        const planet = gameHelper.planet.getPlanet(planetId)
        if (planet) {
            activeNum = planet.getProfileActiveProgress(area).cur
        }
        return { cur: ary.length + activeNum, total: totalAry.length }
    }

    @ut.addLock
    public async changeSort(sort: { [id: number]: number }, planetId: number) {
        const planet = gameHelper.planet.getPlanet(planetId)
        for (const key in sort) {
            if (!planet.isUnlockProfile(+key)) delete sort[key]
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_PlanetProfileSortChangeMessage, { planetId, sort })
        if (r.code != 0) {
            return void viewHelper.showNetError(r.code)
        }

        const profiles = planet.getProfiles()
        for (const key in sort) {
            profiles[key] = sort[key]
        }
        return true
    }


}
