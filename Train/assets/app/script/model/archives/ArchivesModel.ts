import { Msg } from "../../../proto/msg-define";
import { CharacterProfileCfg } from "../../common/constant/DataType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import PropObj from "../bag/PropObj";
import { ItemID } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";

@mc.addmodel('archives')
export default class ArchivesModel extends mc.BaseModel {

    public data: number[]

    private profileList: CharacterProfileCfg[] = []

    public init() {
        this.profileList = this.data.map(id => cfgHelper.getCharacterProfileById(id))
        gameHelper.bag.loadPassengerProfile()
    }

    public getList() { return this.profileList }
    public getListByTypes(types: number[]) {
        return this.profileList.filter(t => types.has(t.type))
    }

    //这里只支持num == 1或-1
    public addProfile(id: number, num: number) {
        let data = cfgHelper.getCharacterProfileById(id)
        if (num > 0) {
            this.profileList.push(data)
        }
        else {
            this.profileList.splice(this.profileList.findIndex(t => t.id == id), 1)
        }
        gameHelper.bag.loadPlanetProfile()
    }

    public async unlock(profileId: number, passengerId: number, position: number) {
        let { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_PassengerUnlockProfileMessage, { profileId, passengerId, position })
        if (code == 0) {
            let role = gameHelper.passenger.getPassenger(passengerId)
            let profile = cfgHelper.getCharacterProfileById(profileId)
            role.setProfile(profile, position)
            this.addProfile(profileId, -1)
            eventCenter.emit(EventType.PASSENGER_PROFILE_UNLOCK, passengerId)
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    @ut.addLock
    public async changeSort(sortData: { [key: number]: number }, passengerId: number) {
        const character = gameHelper.passenger.getPassenger(passengerId)
        const sort = sortData
        for (const key in sort) {
            if (sort[key] == -1) {
                delete sort[key]
            }
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_PassengerProfileSortChangeMessage, { sort, passengerId })
        if (r.code == 0) {
            for (const type in sort) {
                character.changeProfile(+type, sort[type])
            }
            return true
        } else {
            viewHelper.showNetError(r.code)
        }
        return false
    }

    public checkItemCouldUse(item: CharacterProfileCfg, characterId: number) {
        if (item.characterId == characterId) return { yes: true, data: item }
        let data1 = cfgHelper.getCharacter(characterId)
        let data2 = cfgHelper.getCharacter(item.characterId)


        let newItem = null
        // 星座和年龄以及羁绊需要特殊处理 通用的
        if (item.type == 1 && data1.profile.age == data2.profile.age) {
            newItem = cfgHelper.getCharacterProfileByCharacterIdAndType(characterId, item.type)
        }
        else if (item.type == 2 && data1.profile.sign == data2.profile.sign) {
            newItem = cfgHelper.getCharacterProfileByCharacterIdAndType(characterId, item.type)
        }
        else if ((item.type == 9 || item.type == 11) && data1.profile.friends.has(item.characterId)) {
            newItem = cfgHelper.getCharacterProfileByCharacterIdAndType(characterId, item.type)
        }

        if (newItem) {
            // 判断是否解锁
            const passenger = gameHelper.passenger.getPassenger(newItem.characterId)
            if (!passenger) return { yes: false, data: null }
            const pos = passenger.getProfile(newItem.type + "")
            if (pos) return { yes: false, data: null }

            return { yes: true, data: newItem }
        }

        return { yes: false, data: null }
    }
}
