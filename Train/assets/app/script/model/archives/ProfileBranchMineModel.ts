import { ChapterPlanetMineCfg, Condition, PlanetMineCfg } from "../../common/constant/DataType"
import {  PlanetNodeType } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import PlanetMineModel from "../planet/PlanetMineModel"

export default class ProfileBranchMineModel extends PlanetMineModel {

    public init(id: string, mineId: number) {
        this.id = id
        this.mineId = mineId
        this.initBaseJson()

        let lv = gameHelper.tool.getToolByType(this.type)?.getLv() || 1
        let json = assetsMgr.getJson<ChapterPlanetMineCfg>("ChapterPlanetMine").datas.find(d => {
            if (d.lv == lv) {
                let [planetId, mapId, index] = d.id.split("-")
                let nodes: any[] = cfgHelper.getPlanetJsonData(Number(planetId), "PlanetMap", Number(mapId)).node
                let node = nodes.find(n => n.type == PlanetNodeType.MINE && n.id == Number(index))
                let cfg = assetsMgr.getJsonData<PlanetMineCfg>("PlanetMine", node.mineId)
                if (cfg.type == this.type) {
                    return true
                }
            }
        })
        if (!json) {
            twlog.error("ProfileBranchMineModel not found", mineId, lv)
            return
        }

        this.json = json
        this.maxHp = json.hp
        this.hp = this.maxHp
        return this
    }

    public async syncDie() {
        return gameHelper.profileBranch.passNode(this.index)
    }
}