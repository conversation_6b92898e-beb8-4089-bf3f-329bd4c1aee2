import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { Msg } from "../../../proto/msg-define";
import { Condition } from "../../common/constant/DataType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import EventType from "../../common/event/EventType";
import { ConditionType, GuideStepMark } from "../../common/constant/Enums";
import ConditionObj from "../common/ConditionObj";

export class ExploreInfo {
    constructor(
        public roles: number[],
        public endTime: number,
        public rewards: Condition[]
    ) {
        this.endTime += gameHelper.now()
    }

    setEndTime(endTime: number) {
        this.endTime = endTime + gameHelper.now()
    }

    getSurplusTime(): number {
        return this.endTime - gameHelper.now()
    }

    getTimeStr(): string {
        return ut.millisecondFormat(this.getSurplusTime(), "hh:mm:ss")
    }

    isFinish(): boolean {
        return this.getSurplusTime() <= 0
    }

}

@mc.addmodel('deepExplore')
export default class DeepExploreModel extends mc.BaseModel {
    public data: proto.IExplore
    private exploreMap: Map<number, ExploreInfo> = new Map()
    private _maxTeamCnt: number = 0
    private _area: { [key: number]: number }

    get maxTeamCnt() { return this._maxTeamCnt }
    get usedTeamCnt() { return this.exploreMap.size }

    private guideExplore: { planetId: number, info: ExploreInfo } = null

    public init() {
        let data = this.data
        if (data) {
            data.teams.forEach(team => {
                this.exploreMap.set(team.planetId, new ExploreInfo(
                    team.roles || [],
                    team.surplusTime,
                    gameHelper.toConditions(team.rewards)
                ))
            })
            this._maxTeamCnt = data.maxTeamCount
        }
        this._area = {}
    }

    public updateInfo(data: proto.IExplore) {
        data.teams.forEach(team => {
            let info = this.exploreMap.get(team.planetId)
            info.setEndTime(team.surplusTime)
        })
    }

    // 获取星球要进行探索的区域(服务器已经生成)
    public async getArea(planetId: number) {
        let id = this._area[planetId]
        if (id && id != -1) return id
        const res = await gameHelper.net.requestWithData(Msg.C2S_GetExploreAreaMessage, { planetId })
        if (res.code == 0) {
            this._area[planetId] = res.area
            return res.area
        }
        return -1
    }

    public getRoles() {
        let roles = new Map<number, boolean>()
        this.exploreMap.forEach(info => {
            info.roles.forEach(roleId => {
                roles.set(roleId, true)
            })
        })
        return roles
    }

    public startExplore(planetId: number, roles: number[], surplusTime: number, rewards: Condition[]) {
        let newExploreInfo = new ExploreInfo(roles, surplusTime, rewards)
        this.exploreMap.set(planetId, newExploreInfo)
        if (!gameHelper.guide.isStepEnd(GuideStepMark.DEEP_EXPLORE_START)) {
            this.guideExplore = { planetId, info: newExploreInfo }
        }
        return newExploreInfo
    }

    public isPlanetExplore(planetId: number): boolean {
        let info = this.exploreMap.get(planetId)
        return info && !info.isFinish()
    }

    public getExploreInfo(planetId: number): ExploreInfo {
        return this.exploreMap.get(planetId)
    }

    public async claimExplore(planetId: number): Promise<boolean> {
        let info = this.exploreMap.get(planetId)
        if (info) {
            // 发送领取奖励的协议
            const res = await gameHelper.net.requestWithDataWait(Msg.C2S_ClaimExploreRewardMessage, { planetId })
            const { code, surplusTime } = res
            if (code == 0) {
                if (!gameHelper.guide.isStepEnd(GuideStepMark.DEEP_EXPLORE_END)) {
                    this.guideExplore = { planetId, info }
                }
                gameHelper.grantRewardAndShowUI(gameHelper.toConditions(info.rewards))
                this.exploreMap.delete(planetId)
                this._area[planetId] = null
                return true
            } else {
                console.log("claimExplore", surplusTime, code)
                if (surplusTime) {
                    info.setEndTime(Number(surplusTime))
                }
                viewHelper.showNetError(code)
                return false
            }
        }
        return false
    }

    public async buyShip() {
        const costAry = cfgHelper.getMiscData("deepExplore").spaceshipCost
        const cost = costAry[this.maxTeamCnt - 1]
        const cond = new ConditionObj().init(ConditionType.DIAMOND, -1, cost)
        if (gameHelper.getDiamond() < cost) {
            return void viewHelper.showAlert("title_jumpTips_4")
        }
        const max = cfgHelper.getMiscData("deepExplore").spaceshipMaxNum
        if (this.maxTeamCnt >= max) return void viewHelper.showAlert("ui_deepExplore_tips_4")
        const res = await gameHelper.net.requestWithDataWait(Msg.C2S_BuySpaceshipMessage)
        if (res.code != 0) {
            return viewHelper.showNetError(res.code)
        }
        gameHelper.deductCondition(cond)
        this._maxTeamCnt += 1
        eventCenter.emit(EventType.PLANET_DEEP_EXPLORE_SHIP_CHANGED)
        return true
    }

    public hasFreeShip(): boolean {
        return this.maxTeamCnt > this.exploreMap.size
    }

    public getGuideExplore() {
        return this.guideExplore
    }

}
