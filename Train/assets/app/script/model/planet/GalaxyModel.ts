import { GalaxyCfg } from "../../common/constant/DataType"

export default class GalaxyModel {
    public id: number = 0
    public json: GalaxyCfg = null
    public reachAnim: boolean = false//是否已播放抵达星系动画

    public init(id: number) {
        this.id = id
        this.initJson()
        return this
    }
    public toDB() {
        return {
            id: this.id,
            reachAnim: this.reachAnim,
        }
    }
    public fromDB(data) {
        let { id, reachAnim } = data
        this.id = id
        this.reachAnim = reachAnim
        this.initJson()
        return this
    }
    private initJson() {
        let json = assetsMgr.getJsonData<GalaxyCfg>("Galaxy", this.id)
        this.json = json
    }

}