
import { game<PERSON><PERSON><PERSON> } from "../../common/helper/GameHelper"
import { PlanetAreaCfg } from "../../common/constant/DataType"
import PlanetMap from "./PlanetMap"

/**
 * 星球地图
 */
export default class PlanetArea {
    public id: string = null

    private json: PlanetAreaCfg = null

    private _maps: PlanetMap[] = null

    init(id: string) {
        this.id = id
        this.json = assetsMgr.getJsonData<PlanetAreaCfg>("PlanetArea", id)
        return this
    }


    public get planetId() { return this.json.planetId }
    public get name() { return this.json.name }
    public get windName() { return this.json.windName }
    public get index() { return this.json.index }

    public getPlanet() {
        return gameHelper.planet.getPlanet(this.planetId)
    }

    public getMaps() {
        if (!this._maps) {
            let planet = this.getPlanet()
            this._maps = planet.getMaps().filter(m => this.json.maps.has(m.getId()))
        }
        return this._maps
    }

    public getProgress() { //todo优化
        let tot = 0
        for (let map of this.getMaps()) tot += map.getProgress()
        return tot
    }

    public getNodeCount() {
        let count = 0
        for (let map of this.getMaps()) {
            count += map.getNodeCount()
        }
        return count
    }
    
    public getNodes() {
        let nodes = []
        for (let map of this.getMaps()) {
            nodes.pushArr(map.getNodes())
        }
        return nodes
    }

    public getPercent() {
        let completeCnt = this.getProgress()
        let tot = this.getNodeCount()
        return completeCnt / tot
    }

    public isDone() {
        return this.getPercent() >= 1
    }

    public isUnlock() {
        if (this.isDone()) return true
        let planet = this.getPlanet()
        let curArea = planet.getCurArea()
        return curArea == this || (curArea.isDone() && curArea.index + 1 == this.index)
    }

}