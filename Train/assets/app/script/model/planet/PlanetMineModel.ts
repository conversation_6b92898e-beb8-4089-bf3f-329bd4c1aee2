/**
 * 采集物
 */

import { ChapterPlanetMineCfg, Condition, PlanetMineCfg } from "../../common/constant/DataType"
import { ConditionType, ItemID, PlanetMineGameType, PlanetMineType } from "../../common/constant/Enums"
import { gameHelper } from "../../common/helper/GameHelper"
import EventType from "../../common/event/EventType"
import PlanetNodeModel from "./PlanetNodeModel"

export default class PlanetMineModel extends PlanetNodeModel {

    public hp: number = 0
    public damageMul: number = 1
    public mineId: number = null
    public type: PlanetMineType = PlanetMineType.TREE

    public collecting: boolean = false

    public maxHp: number = 0

    protected baseJson: PlanetMineCfg = null

    public centerOffset: cc.Vec2 = cc.v2()

    public qteSucc: number = 0

    public restoreTime: number = 0
    protected amp: number = 0

    public init(id: string, mineId: number) {
        this.id = id
        this.mineId = mineId
        this.initJson()
        if (!this.json || !mineId) return
        let { hp } = this.json
        this.initBaseJson()
        this.initReawrds()
        this.maxHp = hp
        this.hp = this.maxHp
        return this
    }

    protected initJson() {
        let name = "ChapterPlanetMine"
        if (this.map.getBranch()) {
            name = "BranchPlanetMine"
        }
        this.json = assetsMgr.getJsonData<ChapterPlanetMineCfg>(name, this.id)
    }

    protected initBaseJson() {
        let json = assetsMgr.getJsonData<PlanetMineCfg>("PlanetMine", this.mineId)
        this.type = json.type
        this.baseJson = json
        this.reachOffset = cc.v2(json.reachOffset)
        if (json.centerOffset) {
            this.centerOffset.x = json.centerOffset.x || 0
            this.centerOffset.y = json.centerOffset.y || 0
        }
    }

    public toDB() {
        return {
            id: this.id,
            progress: this.progress,
            qteSucc: this.qteId ? this.qteSucc : null
        }
    }

    public fromDB(data: any) {
        this.progress = this.progress || data.progress || 0
        this.qteSucc = data.qteSucc || 0

        this.hp = this.maxHp - this.progress
        if (this.hp <= 0) { //不好处理死了但没通过的情况，这里设成1，继续走后面的流程
            this.hp = 1
        }
    }

    public get name() { return this.baseJson?.name || "" }
    public get icon() { return this.baseJson?.icon || "" }
    public get gameType() {
        return this.json?.gameType || PlanetMineGameType.CLICK
    }
    public get qteId() { return this.json?.qteId }
    public get dodge() { return this.json?.dodge || 0 } //闪避
    public get restore() { return this.json?.restore || 0 } //每秒恢复hp
    public get defense() { return this.json?.defense || 0 } //防御
    public get tenacity() { return this.json?.tenacity || 0 } //坚韧
    public get lv() { return this.json?.lv || 0 }

    get centerPos() { return this.position.add(this.centerOffset) }
    get collectEffect() { return this.baseJson?.collectEffect }

    public setAmp(amp) {
        this.amp = amp
    }

    public update(dt) {
        if (this.dead) return

        if (this.hp < this.maxHp) {
            this.restoreTime += dt
            let speed = this.getRestoreSpeed()
            while (this.restoreTime >= speed) {
                this.restoreTime -= speed
                let restore = this.getRestore()
                if (restore > 0) {
                    this.changeHp(restore)
                }
            }
        }
    }

    public getRestore() {
        if (this.qteId || this.isFirst()) return 0
        return gameHelper.hero.getAttack()
    }

    //每次恢复间隔时间
    public getRestoreSpeed() {
        let count = Math.max(0, this.restore - this.amp)
        if (count <= 0) return 10000
        return 1 / (count * 2)
    }


    public hit(damage, damageMul: number = 1, isAuto: boolean = false) {
        if (this.dead) return
        if (this.hp <= 0) return
        this.changeHp(-damage, damageMul, isAuto)
        this.progress = this.maxHp - this.hp
        if (this.hp <= 0) {
            this.die()
        }
    }

    protected changeHp(hp, damageMul = 1, isAuto: boolean = false) {
        this.hp = cc.misc.clampf(this.hp + hp, 0, this.maxHp)
        eventCenter.emit(EventType.PLANET_MINE_HP_CHANGE, this, hp, damageMul, isAuto)
    }

    public async die() {
        if (this.dead) return true
        this.dead = true

        this.map.nextNode()
        let succ = true
        if (!gameHelper.hero.isRageMode()) {
            succ = await this.syncDie()
        }
        if (succ) {
            if (gameHelper.tool.isBless()) {
                gameHelper.tool.changeBlessCount(-1)
            }
            if (gameHelper.hero.isRageMode()) {
                if (gameHelper.hero.rageMode.to <= this.index) {
                    gameHelper.hero.exitRageMode()
                }
            }
            if (gameHelper.hero.isSkiMode()) {
                if (this.index >= gameHelper.hero.skiMode.to) {
                    gameHelper.hero.exitSkiMode()
                }
            }
            gameHelper.grantRewards(this.rewards)
            this.end()
        }
        return succ
    }

    public setSize(size) {
        super.setSize(size)

        let width = size.width * 0.5
        let dis = Math.abs(this.reachOffset.x) - width
        let newX = (dis + width * Math.abs(this.scale)) * ut.normalizeNumber(this.reachOffset.x)
        // this.reachPosition = this.position.add(cc.v2(newX, this.reachOffset.y))
    }

    public addQteSucc() {
        this.qteSucc++
    }

    public isFirst() {
        return this.planet?.getId() == 1001 && this.index == 1
    }
}