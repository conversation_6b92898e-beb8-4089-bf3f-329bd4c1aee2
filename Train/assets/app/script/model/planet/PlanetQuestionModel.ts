/**
 * 问答节点
 */
import { QuestionCfg } from "../../common/constant/DataType"
import { gameHelper } from "../../common/helper/GameHelper"
import PlanetNodeModel from "./PlanetNodeModel"


export class Question {
    public id: string
    private json: QuestionCfg = null

    public init(id: string) {
        this.id = id
        this.json = assetsMgr.getJsonData<QuestionCfg>("Question", id)
        return this
    }

    get group() {
        return this.json.group
    }
    get characterId() {
        return this.json.characterId
    }
    get content() {
        return this.json.content
    }
    get opts() {
        return this.json.opts
    }
    get answer() {
        return this.json.answer
    }

    public isAnswer(answer: string) {
        return this.opts[this.answer] == answer
    }

}

export default class PlanetQuestionModel extends PlanetNodeModel {
    protected json: any = null
    
    public questions: Question[] = []

    public init(nodeId, ...params) {
        super.init(nodeId)
        this.reachOffset = cc.v2(-280, -30)
        return this
    }

    public setQuestions(questions: string[]) {
        this.questions = questions.map(id => {
            return new Question().init(id)
        })
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        this.map.nextNode()
        return true
    }

    public async syncDie() {
        let succ = await gameHelper.profileBranch.passNode(this.index)
        if (succ) {
            gameHelper.grantRewards(this.rewards)
        }
        return succ
    }
}
