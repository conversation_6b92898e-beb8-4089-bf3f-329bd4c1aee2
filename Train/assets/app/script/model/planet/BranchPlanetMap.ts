import { cfg<PERSON><PERSON><PERSON> } from "../../common/helper/CfgHelper"
import EventType from "../../common/event/EventType"
import PlanetModel from "./PlanetModel"
import PlanetMap from "./PlanetMap"
import PlanetBranch from "./PlanetBranch"

/**
 * 星球支线地图
 */
export default class BranchPlanetMap extends PlanetMap {

    protected branch: PlanetBranch = null

    get name() {
        return this.json?.name
    }

    public init(planet: PlanetModel, id: number, branch?: PlanetBranch) {
        this.branch = branch
        return super.init(planet, id)
    }

    protected initJson() {
        this.json = cfgHelper.getPlanetJsonData(this.planetId, "BranchPlanetMap", this.id)
    }

    public setProgress(progress: number) {
        this.progress = progress
        this.branch.curNode = this.getCurNode()
        if (progress > 0) {
            eventCenter.emit(EventType.PLANET_NODE_COMPLETE, this.planetId, this.getPreNode())
        }
    }

    public getBranch() {
        return this.branch
    }

    public getArea() {
        return null
    }
}