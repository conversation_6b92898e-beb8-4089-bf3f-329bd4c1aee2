import BaseMap from "../map/BaseMap";
import UnioinFind from "../map/UnioinFind";

/**
 * 星球寻路用的地图
 */
export default class PlanetMoveMap extends BaseMap {

    private unioinFind: UnioinFind = null

    init(width) {
        this.gridSize = 30
        let widthCount = Math.floor(width / this.gridSize)
        this.size = cc.size(widthCount, 10)
        this.setBasePoint(cc.v2(0, 0)) //暂定
        this.unioinFind = new UnioinFind().init(this)
        return this
    }

    update() {
        this.unioinFind && this.unioinFind.update()
    }

    public updatePoints(points: cc.Vec2[], add: boolean) {
        let val = add ? 1 : -1
        for (let point of points) {
            let { x, y } = point
            if (!this.matrix[x]) this.matrix[x] = {}
            if (!this.matrix[x][y]) this.matrix[x][y] = 0
            this.matrix[x][y] += val //这里只设置0，1，防止被多个同时占用的问题
        }
        this.unioinFind.init(this)
    }

    public getConnectPoints(point: cc.Vec2): cc.Vec2[] {
        return this.unioinFind.getConnectPoints(point)
    }

    public isConnected(p1: cc.Vec2, p2: cc.Vec2) {
        return this.unioinFind.isConnected(p1, p2)
    }

    public getNearestEmptyPoint(point: cc.Vec2) {

    }
}