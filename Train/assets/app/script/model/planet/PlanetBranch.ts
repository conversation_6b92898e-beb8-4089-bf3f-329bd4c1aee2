
import { PlanetAreaCfg } from "../../common/constant/DataType"
import PlanetMap from "./PlanetMap"
import BranchPlanetMap from "./BranchPlanetMap"
import PlanetModel from "./PlanetModel"
import PlanetEmptyNode from "./PlanetEmptyNode"
import PlanetNodeModel from "./PlanetNodeModel"

/**
 * 星球支线
 */
export default class PlanetBranch {
    public id: string = null

    protected json: PlanetAreaCfg = null

    protected maps: BranchPlanetMap[] = null

    protected mapId: number = 1

    protected planet: PlanetModel = null

    public curNode: PlanetNodeModel = null //当前节点数据，存本地

    public init(planet: PlanetModel, data) {
        let id = data.id
        this.planet = planet
        this.id = id
        this.initJson()
        this.mapId = data.mapId || 1
        let nodeId = data.nodeId || 1
        this.initMaps()
        let curMapId = this.mapId
        for (let map of this.maps) {
            if (map.getId() < curMapId || curMapId == -1) {
                map.setDone()
            }
        }
        if (curMapId >= 1) {
            let curMap = this.getCurMap()
            // curMap.initNodes()
            curMap.setProgress(nodeId - 1)
            curMap.initNodeEnd()
        }
        else {
            this.mapId = this.maps.last().getId()
        }
        let node = this.getCurMap().getCurNode()
        this.curNode = node
        if (node) {
            if (node instanceof PlanetEmptyNode) {
                node.nodeRewards = data.nodeRewards
            }
        }
    
        return this
    }

    protected initJson() {
        this.json = assetsMgr.getJsonData<PlanetAreaCfg>("PlanetBranch", this.id)
    }

    protected initMaps() {
        this.maps = this.json.maps.map(id => {
            return new BranchPlanetMap().init(this.planet, id, this)
        })
    }

    public toDB() {
        return {
            id: this.id,
            // maps: this.maps.map(m => m.toDB()),
            curNode: this.curNode?.toDB(),
        }
    }

    public fromDB(planet: PlanetModel, data, localData?) {
        let oldNode = this.curNode
        this.init(planet, data)
        let node = this.curNode
        if (node) {
            if (localData?.curNode) {
                if (node.getId() == localData.curNode.id) {
                    node.fromDB(localData.curNode)
                }
            }
            else if (oldNode) {
                if (this.curNode.getId() == oldNode.getId()) {
                    node.fromDB(oldNode.toDB())
                }
            }
        }
        return this
    }

    public get curMap(): PlanetMap {
        return this.getMap(this.mapId)
    }

    public get planetId() { return this.json.planetId }
    public get name() { return this.json.name }
    public get windName() { return this.json.windName }
    public get index() { return this.json.index }

    public get battleBg() { return this.json.battleBg }

    public getPlanet() {
        return this.planet
    }

    public getMaps() {
        return this.maps
    }

    public getMap(id: number) { return this.maps.find(m => m.getId() == id) }

    public getCurMap() { return this.getMap(this.mapId) }

    public getProgress() {
        let tot = 0
        for (let map of this.getMaps()) tot += map.getProgress()
        return tot
    }

    public getNodeCount() {
        let count = 0
        for (let map of this.getMaps()) {
            count += map.getNodeCount()
        }
        return count
    }

    public getPercent() {
        let completeCnt = this.getProgress()
        let tot = this.getNodeCount()
        return completeCnt / tot
    }

    public isDone() {
        return this.getPercent() >= 1
    }

    public getNodes() {
        let nodes = []
        for (let map of this.maps) {
            nodes.pushArr(map.getNodes())
        }
        return nodes
    }

    public isUnlock() {
        return true
    }

    public update(dt) {
        this.maps.forEach(m => m.update(dt))
    }
}