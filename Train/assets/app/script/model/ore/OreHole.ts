import { ConditionType, OreLandType } from "../../common/constant/Enums"
import ConditionObj from "../common/ConditionObj"
import { OreLandNode } from "./OreLandNode"

const LINECAP = 8 //视图显示(0 - LINECAP)行,总共LINECAP + 1行
const ROW_VIEW = 6

export class OreHole {
    private oreLands: OreLandNode[][] = []
    private rowMin: number = 0
    private deep: number = null
    private realDeep: number = null //当前最深位置
    private level: number = null
    public startNode: { x: number, y: number } = null

    public init(data: proto.IOreLevelData) {
        this.level = data.level
        this.realDeep = data.depth
        this.deep = data.depth - (data.depth % (LINECAP + 1))
        this.rowMin = data.isSpecialArea ? ROW_VIEW : 0

        this.oreLands = data.data.map((line, y) => {
            let afterDeep = this.deep
            if (y > LINECAP) {
                afterDeep += LINECAP + 1
            }
            return line.data.map((e, x) => {
                return new OreLandNode().init(e, y + this.deep, x, afterDeep, this)
            })
        })
        this.resetStartNode()
        return this
    }

    public copy(data: OreHole) {
        this.level = data.getLevel()
        this.realDeep = data.getRealDeep()
        this.deep = data.getDeep()
        this.rowMin = data.getRowMin()
        this.startNode = data.startNode
        this.oreLands = data.getOreMap().map((line) => {
            return line.map((e) => {
                return new OreLandNode().copy(e, this)
            })
        })
        return this
    }

    public getOreMap() { return this.oreLands }
    public getDeep() { return this.deep }
    public getRealDeep() { return this.realDeep }
    public getLevel() { return this.level }
    public getRowMin() { return this.rowMin }

    //todo 这里在矿坑里row > 8 不算深度增加
    public refreshRealDeep() {
        this.realDeep = Math.max(this.realDeep, this.getMaxDeep())
    }

    public moveRow(isUp: Boolean = false) {
        this.rowMin += ROW_VIEW * (isUp ? -1 : 1)
        this.resetStartNode()
    }

    //在当前视图里才会有值
    public getOreNodeByPos(x: number, y: number) {
        let rowMax = this.rowMin + ROW_VIEW - 1
        if (y < this.deep || y > this.deep + LINECAP) { return null }
        if (x < this.rowMin || x > rowMax) { return null }
        return this.oreLands[y - this.deep][x]
    }

    private resetStartNode() {
        this.oreLands.forEach(l => {
            l.forEach(e => {
                if ((e.getType() == OreLandType.START || e.getType() == OreLandType.BACK) && !!this.getOreNodeByPos(e.getRow(), e.getLine())) {
                    this.startNode = { x: e.getRow(), y: e.getLine() }
                }
            })
        })
    }

    public updateLands(data: proto.IOreRowData[]) {
        if (!!data && data.length > 0 && !!data[0]) {
            let moveLength = data.length
            for (let i = 0; i < this.oreLands.length - moveLength; i++) {
                this.oreLands[i] = this.oreLands[i + moveLength].slice(0)
            }
            this.deep += moveLength
            this.resetStartNode()
            this.realDeep = this.deep
            this.oreLands.splice(0, moveLength)
            const afterDeep = this.deep + this.oreLands.length

            let newLines = data.map((line, y) => {
                return line.data.map((e, x) => {
                    // 这里的追加是在当前页的后面 所以deep要增加
                    return new OreLandNode().init(e, y + afterDeep, x, afterDeep, this)
                })
            })
            newLines.forEach(line => {
                this.oreLands.push(line)
            })
            this.refreshRealDeep()
        }
    }

    public canReach(tx: number, ty: number) {
        ty -= this.deep
        ty = Math.max(ty, 0)
        // let res: boolean = false
        // let vis: number[][] = []
        // let que: { x: number, y: number }[] = []//还没用过的
        // let node = this.getOreNodeByPos(x, y)
        // if (!!node) {
        //     let brothers = node.getRelation()
        //     if (!!brothers && brothers.length == 1) {
        //         node = this.getOreNodeByPos(brothers[0].row, brothers[0].line)
        //     }
        // }
        // if (!!node && node.getType() == OreLandType.BOSS) {
        //     node.getRelation().forEach(m => {
        //         que.push({ x: m.row, y: m.line })
        //     })
        // }

        try {
            const isSpecial = tx >= ROW_VIEW || ty > LINECAP
            const { data, x, y } = this.getData(true, isSpecial)
            const startPos = { x, y }
            tx >= ROW_VIEW && (tx -= ROW_VIEW)
            ty > LINECAP && (ty -= (LINECAP + 1))
            const row = data[ty]
            let ceil = row[tx]
            let ceilX = tx
            let ceilY = ty
            if (ceil) {
                const ref = ceil.getRelation()
                if (ref && ref.length) {
                    if (ref.length == 1) {
                        ceil = this.getOreNodeByPos(ref[0].row, ref[0].line)
                    }
                    if (ceil) {
                        for (const r of ceil.getRef()) {
                            let gx = r.x
                            let gy = r.y
                            const grid = this.toGridArray(true, isSpecial, (x: number, y: number) => {
                                return gx == x && gy == y
                            })
                            // 只要有一个可达，那就全部可达
                            if (BFS.isReachableWithStart(grid, startPos, { x: gx, y: gy })) {
                                return true
                            }
                        }
                    }
                }
            }
            const grid = this.toGridArray(true, isSpecial, (x: number, y: number) => {
                return ceilX == x && ceilY == y
            })
            return BFS.isReachableWithStart(grid, startPos, { x: tx, y: ty })
        } catch (e) {
        }
    }

    public getMaxDeep() {
        const grid = this.toGridArray(true, false)
        const [x, y] = BFS.findDeepestPoint(grid)
        if (x == -1 || y == -1) {
            twlog.error("计算当前深度时出错:", x, y)
            return this.deep
        }
        return this.deep + y + 1
    }

    //处理boss节点
    public getBossNode(node: OreLandNode) {
        let boss = node.getRelation()
        if (!!boss && boss.length == 1) {
            let pos = boss[0]
            return this.getOreNodeByPos(pos.row, pos.line)
        }
        return node
    }

    public getLayerRewards() {
        let datas = assetsMgr.getJson<any>("OreLayer").datas.filter(c => c.level == this.getLevel())
        let index = datas.findIndex(c => c.layer > this.deep)
        if (index == -1) {
            index = datas.length
        }
        index = cc.misc.clampf(index - 1, 0, datas.length - 1)
        let cfg = datas[index]
        let rewards = []
        for (let reward of cfg.rewardRandom) {
            if (reward.type == ConditionType.ORE_ITEM) {
                rewards.pushArr(cfg.ore.map(o => new ConditionObj().init(ConditionType.ORE_ITEM, o.id)))
            }
            else {
                rewards.push(new ConditionObj().init(reward.type, reward.id))
            }
        }
        return rewards
    }

    /**
     * 获取矿洞格子数据
     * @param curPage 只获取当前页
     * @param isSpecial 是不是特殊区域
     * @returns  ={data: 格子数据, x: 起点x, y: 起点y}
     */
    public getData(curPage: boolean, isSpecial: boolean) {
        let data: OreLandNode[][] = this.oreLands
        if (curPage) {
            data = this.oreLands.slice(0, LINECAP + 1)
        }
        data = data.map((tmp: OreLandNode[], index: number) => {
            if (isSpecial) {
                return tmp.slice(ROW_VIEW)
            }
            return tmp.slice(0, ROW_VIEW)
        })
        let startX = -1
        let startY = -1
        for (let y = 0; y < data.length; y++) {
            const col = data[y]
            for (let x = 0; x < col.length; x++) {
                const item = col[x]
                if (isSpecial && item.getType() == OreLandType.BACK) {
                    startY = y
                    startX = x
                    break
                }
                if (item.getType() == OreLandType.START) {
                    startY = y
                    startX = x
                }
            }
        }

        return {
            data, x: startX, y: startY
        }
    }

    public toGridArray(curPage: boolean, isSpecial: boolean, cond?: (x: number, y: number) => boolean) {
        const { data, x, y } = this.getData(curPage, isSpecial)
        const grid: number[][] = []
        for (let i = 0; i < data.length; i++) {
            const row = data[i]
            grid[i] = new Array(row.length)
            for (let j = 0; j < row.length; j++) {
                const ceil = row[j]
                let type = 1
                if (cond && cond(j, i)) {
                    type = 0
                } else if (ceil.typeCanPass()) {
                    type = 0
                }
                grid[i][j] = type
            }
        }
        return grid
    }

}