import { ConditionType, OreLandType, OreOperateType } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import Monster from "../battle/Monster"
import ConditionObj from "../common/ConditionObj"
import { OreHole } from "./OreHole"

const BOOMSTEP = 2 //炸弹范围（用于bfs搜索）
const CANTDAOJULIST = [ //不会被炸弹/钻头摧毁的类型
    OreLandType.BACK,
    OreLandType.BOSS,
    OreLandType.PURPLE,
    OreLandType.NEXT,
    OreLandType.SPECIAL,
    OreLandType.START,
]

export class OreLandNode {
    //todo 其实可以把行改成当前页面的行，维护一个当前页第一行的deep就行了。逻辑会简单很多
    private line: number = 0 //第几行（整个游戏里的真实行数，和列组合是唯一的）
    private row: number = 0 //第几列
    private type: OreLandType = OreLandType.NONE
    private monsters: Monster[] = []
    private rewards: ConditionObj[] = []
    // 这个是计算后的引用数据
    private relation: { line, row }[] = []
    // 这个是原始引用数据
    private ref: proto.IPoint[] = []
    private nextType: proto.OrePageNextType = null
    private hole: OreHole = null
    private direction: proto.OreBlockItemDrillDirection = null

    public init(data: proto.IOreCeilData, line: number, row: number, deep: number, hole: OreHole) {
        this.type = Number(data.type)
        this.line = line
        this.row = row
        this.nextType = data.nextType
        this.direction = data.direction
        this.hole = hole
        this.ref.length = 0
        data.ref.forEach(m => {
            this.relation.push({ line: m.x + deep, row: m.y })
            this.ref.push({ x: m.x, y: m.y })
        })
        data.monsters.forEach(m => this.monsters.push(new Monster().init(m.id, m.lv, m.starLv)))
        this.rewards = gameHelper.toConditions(data.oreExtra)
        return this
    }

    public copy(data: OreLandNode, hole: OreHole) {
        this.type = data.getType()
        this.line = data.getLine()
        this.row = data.getRow()
        this.nextType = data.getNextType()
        this.direction = data.getDirection()
        this.hole = hole
        this.relation = data.getRelation().map(m => {
            return { line: m.line, row: m.row }
        })
        this.ref = data.getRef().map(m => {
            return { x: m.x, y: m.y }
        })
        this.monsters = data.getMonsters().map(m => {
            return new Monster().init(m.id, m.lv, m.getStarLv())
        })
        this.rewards = gameHelper.toConditions(data.getRewards())
        return this
    }

    public getHole() { return this.hole }
    public getLine() { return this.line }
    public getRow() { return this.row }
    public getType() { return this.type }
    public getNextType() { return this.nextType }
    public getDirection() { return this.direction }
    public getMonsterId() {
        if (this.isMonster()) {
            if (this.type == OreLandType.RUNAWAY_PURPLE) {
                return 2101
            }
            if (!!this.monsters && !!this.monsters[0]) {
                return this.monsters[0].id
            }
        }
        return null
    }
    public hasReward() {
        if (!this.isMonster()) {
            return this.rewards.length > 0
        }
        return false
    }
    public getMonsters() { return this.monsters }
    public getRelation() { return this.relation }
    public getRewards() { return this.rewards }
    public getRef() { return this.ref }

    //格子是否为空
    public isNone() {
        return (this.type == OreLandType.NONE || this.type == OreLandType.START) && !this.hasReward()
    }
    public isMonster() {
        return this.type == OreLandType.PURPLE || this.type == OreLandType.RUNAWAY_PURPLE || this.type == OreLandType.BOSS
    }

    //搜索受炸弹/钻头影响的节点。这里加上连锁反应，加一个step，表现上只用播放就行了
    public getDaojuNodes(type?: OreLandType) {
        let useType = !!type ? type : this.type
        if (useType == OreLandType.BOOM) {
            let vis: number[][] = []
            let que: { x: number, y: number }[] = []//还没用过的
            que.push({ x: this.row, y: this.line })
            if (!vis[this.row]) vis[this.row] = []
            vis[this.row][this.line] = 1

            let index = 0
            while (index < que.length) {
                let now = que[index]
                index++
                for (let i = -1; i <= 1; i++) {
                    for (let j = -1; j <= 1; j++) {
                        if (i * j == 0) {
                            let posX = now.x + i, posY = now.y + j
                            let node = this.hole.getOreNodeByPos(posX, posY)
                            if (!!node && (!vis[posX] || !vis[posX][posY]) && !node.isMonster() && Math.abs(posX - this.row) + Math.abs(posY - this.line) <= BOOMSTEP) {
                                que.push({ x: posX, y: posY })
                                if (!vis[posX]) vis[posX] = []
                                vis[posX][posY] = 1
                            }
                        }
                    }
                }
            }
            return que
        }
        if (useType == OreLandType.DRILL) {
            let vis: number[][] = []
            let que: { x: number, y: number }[] = []//还没用过的
            que.push({ x: this.row, y: this.line })
            if (!vis[this.row]) vis[this.row] = []
            vis[this.row][this.line] = 1

            let index = 0
            while (index < que.length) {
                let nowX = que[index].x, nowY = que[index].y
                index++
                let dir
                if (this.direction == proto.OreBlockItemDrillDirection.Right) dir = { x: 0, y: 1 }
                else if (this.direction == proto.OreBlockItemDrillDirection.Left) dir = { x: 0, y: -1 }
                else if (this.direction == proto.OreBlockItemDrillDirection.Top) dir = { x: -1, y: 0 }
                else if (this.direction == proto.OreBlockItemDrillDirection.Bottom) dir = { x: 1, y: 0 }
                let posX = nowX + dir.x, posY = nowY + dir.y
                let node = this.hole.getOreNodeByPos(posX, posY)
                if (!!node && (!vis[posX] || !vis[posX][posY]) && !node.isMonster()) {
                    que.push({ x: posX, y: posY })
                    if (!vis[posX]) vis[posX] = []
                    vis[posX][posY] = 1
                }
            }
            return que
        }
        return null
    }

    public operate(opType: OreOperateType) {
        let needShowEffect: boolean = false
        let needGetReward: boolean = false
        if (opType == OreOperateType.ONE || opType == OreOperateType.TWICE) {
            if (this.type == OreLandType.GRAY || this.type == OreLandType.BREAK || this.type == OreLandType.NONE) {
                if (!!this.rewards && this.rewards.length > 0) { needGetReward = true }
                this.type = OreLandType.NONE
                needShowEffect = true
            }
            else if (this.type == OreLandType.BLUE) {
                this.type = OreLandType.BREAK
            }
        }
        else if (opType == OreOperateType.BATTLE) {
            if (this.type == OreLandType.PURPLE) {
                if (!!this.rewards && this.rewards.length > 0) { needGetReward = true }
            }
            //点击boss其他格子也只会传操作boss这个格子
            else if (this.type == OreLandType.BOSS) {
                this.relation.forEach(m => {
                    let node = this.hole.getOreNodeByPos(m.row, m.line)
                    node.operate(opType)
                })
                if (!!this.rewards && this.rewards.length > 0) { needGetReward = true }
            }
            else if (this.type == OreLandType.RUNAWAY_PURPLE) {
                this.rewards = []
            }
            this.type = OreLandType.NONE
        }
        //这里会引起连锁反应
        else if (opType == OreOperateType.BOOM || opType == OreOperateType.DRILL) {
            if (this.type == OreLandType.BOOM || this.type == OreLandType.DRILL) {
                //这里相当于dfs提前打好标记
                let preType = this.type
                this.type = OreLandType.NONE
                this.getDaojuNodes(preType).forEach(m => {
                    let node = this.hole.getOreNodeByPos(m.x, m.y)
                    if (m.x != this.row || m.y != this.line) {
                        node.operate(OreOperateType.BOOM)
                    }
                })
            }
            else if (!CANTDAOJULIST.has(this.type)) {
                if (!!this.rewards && this.rewards.length > 0) { needGetReward = true }
                this.type = OreLandType.NONE
            }
        }
        if (needGetReward) {
            gameHelper.grantRewards(this.rewards)
            this.rewards = []
        }
    }

    public typeCanPass(): boolean {
        // 空地
        // 起点
        // 返回
        // 下一层
        // 传送门
        return this.type == OreLandType.NONE || this.type == OreLandType.START || this.type == OreLandType.BACK || this.type == OreLandType.NEXT || this.type == OreLandType.SPECIAL
    }

}