import { Msg } from "../../../proto/msg-define"
import { SpaceStoneCfg } from "../../common/constant/DataType"
import { ConditionType, ItemID } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ConditionObj from "../common/ConditionObj"

@mc.addmodel('spaceStone')
export default class SpaceStoneModel extends mc.BaseModel {
    private marks: number[] = []
    private lv: number = 0
    private energy: number = 0

    public data: proto.ISpaceStone

    public getLv() { return this.lv }
    public getEnergy() { return this.energy }

    public init() {
        this.updateInfo(this.data)
    }

    public updateInfo(data: proto.ISpaceStone) {
        this.marks = this.data?.marks
        this.lv = this.data.lv || 0
        this.energy = this.data.energy || 0
    }

    public async mark(id: number, index: number) {
        let removeId = this.getMarkByIndex(index)
        let { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_MarkSpaceStoneMessage, { id, removeId })
        if (code == 0) {
            //this.marks.remove(removeId)
            //this.marks.push(id)
            this.marks[index] = id
            eventCenter.emit(EventType.SPACESTONE_MARK_CHANGE)
            return true
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public async transfer(id: number) {
        let { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_UseSpaceStoneMessage, { id })
        if (code == 0) {
            this.energy -= this.getTransferCost()
            gameHelper.planet.reach(id)
            viewHelper.gotoPlanetEntry()
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public async lvUp() {
        let { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_SpaceStoneLvUpMessage)
        if (code == 0) {
            gameHelper.deductConditions(gameHelper.toConditions(this.getLvUpCost()))
            if (this.lv == 0) {
                gameHelper.grantReward(new ConditionObj().init(ConditionType.PROP, ItemID.SPACE_STONE, 1))
            }
            let preMaxEnergy = this.getJson()?.energy || 0
            this.lv++
            if (this.lv == 1) {
                this.energy = this.getJson().energy
            }
            else {
                this.energy += this.getJson().energy - preMaxEnergy
            }
            eventCenter.emit(EventType.SPACESTONE_LV_UP)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public getMarkByIndex(index: number) {
        return this.marks[index] || null
    }

    public getJson() {
        return assetsMgr.getJsonData<SpaceStoneCfg>("SpaceStone", this.lv)
    }

    public getLvUpCost() {
        return assetsMgr.getJsonData<SpaceStoneCfg>("SpaceStone", this.lv + 1)?.buyCost
    }

    public getTransferCost() {
        return cfgHelper.getMiscData('spaceStone').cost
    }

    public getMaxMarkCnt() {
        return this.getJson()?.markCnt || 0
    }

    public getMaxMarkNumOnMaxLv() {
        let datas = assetsMgr.getJson<SpaceStoneCfg>("SpaceStone").datas
        return datas[datas.length - 1].markCnt
    }

    public isFull() {
        return this.marks.length >= this.getMaxMarkCnt()
    }

    public getMarks() {
        return this.marks
    }

    public hasMark(id: number) {
        return this.marks.has(id)
    }
}