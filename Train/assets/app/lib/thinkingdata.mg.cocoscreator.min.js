"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_toPropertyKey(n.key),n)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPrimitive(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0===i)return("string"===t?String:Number)(e);i=i.call(e,t||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==typeof e?e:String(e)}var Config={LIB_VERSION:"2.2.2",LIB_NAME:"MG"},_={},ArrayProto=Array.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,nativeToString=ObjProto.toString,nativeHasOwnProperty=Object.prototype.hasOwnProperty,nativeForEach=ArrayProto.forEach,nativeIsArray=Array.isArray,breaker={},utmTypes=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],logger$1=(_.each=function(e,t,i){if(null==e)return!1;if(nativeForEach&&e.forEach===nativeForEach)e.forEach(t,i);else if(e.length===+e.length){for(var n=0,a=e.length;n<a;n++)if(n in e&&t.call(i,e[n],n,e)===breaker)return!1}else for(var r in e)if(nativeHasOwnProperty.call(e,r)&&t.call(i,e[r],r,e)===breaker)return!1},_.extend=function(i){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(i[t]=e[t])}),i},_.extend2Layers=function(i){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(_.isObject(e[t])&&_.isObject(i[t])?_.extend(i[t],e[t]):i[t]=e[t])}),i},_.isArray=nativeIsArray||function(e){return"[object Array]"===nativeToString.call(e)},_.isFunction=function(e){try{return"function"==typeof e}catch(e){return!1}},_.isPromise=function(e){return"[object Promise]"===nativeToString.call(e)&&null!=e},_.isObject=function(e){return"[object Object]"===nativeToString.call(e)&&null!=e},_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(nativeHasOwnProperty.call(e,t))return!1;return!0}return!1},_.isUndefined=function(e){return void 0===e},_.isString=function(e){return"[object String]"===nativeToString.call(e)},_.isDate=function(e){return"[object Date]"===nativeToString.call(e)},_.isBoolean=function(e){return"[object Boolean]"===nativeToString.call(e)},_.isNumber=function(e){return"[object Number]"===nativeToString.call(e)&&/[\d\.]+/.test(String(e))},_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},_.decodeURIComponent=function(t){var i="";try{i=decodeURIComponent(t)}catch(e){i=t}return i},_.encodeURIComponent=function(t){var i="";try{i=encodeURIComponent(t)}catch(e){i=t}return i},_.utf8Encode=function(e){for(var t,i="",n=t=0,a=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<a;r++){var s=e.charCodeAt(r),o=null;s<128?t++:o=127<s&&s<2048?String.fromCharCode(s>>6|192,63&s|128):String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128),null!==o&&(n<t&&(i+=e.substring(n,t)),i+=o,n=t=r+1)}return n<t&&(i+=e.substring(n,e.length)),i},_.base64Encode=function(e){var t,i,n,a,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,o=0,c="",u=[];if(!e)return e;for(e=_.utf8Encode(e);t=(a=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>12&63,i=a>>6&63,n=63&a,u[o++]=r.charAt(a>>18&63)+r.charAt(t)+r.charAt(i)+r.charAt(n),s<e.length;);switch(c=u.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c},_.encodeDates=function(n){return _.each(n,function(e,t){if(_.isDate(e))n[t]=_.formatDate(e);else if(_.isObject(e))n[t]=_.encodeDates(e);else if(_.isArray(e))for(var i=0;i<e.length;i++)_.isDate(e[i])&&(n[t][i]=_.formatDate(e[i]))}),n},_.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+((e=e.getMilliseconds())<100&&9<e?"0"+e:e<10?"00"+e:e)},_.searchObjDate=function(i){try{(_.isObject(i)||_.isArray(i))&&_.each(i,function(e,t){_.isObject(e)||_.isArray(e)?_.searchObjDate(i[t]):_.isDate(e)&&(i[t]=_.formatDate(e))})}catch(e){logger$1.warn(e)}},_.UUID=function(){var e=(new Date).getTime();return String(Math.random()).replace(".","").slice(1,11)+"-"+e},_.UUIDv4=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})},_.setMpPlatform=function(e){_.mpPlatform=e},_.getMpPlatform=function(){return _.mpPlatform},_.createExtraHeaders=function(){return{"TA-Integration-Type":Config.LIB_NAME,"TA-Integration-Version":Config.LIB_VERSION,"TA-Integration-Count":"1","TA-Integration-Extra":_.getMpPlatform()}},_.checkAppId=function(e){return e=e.replace(/\s*/g,"")},_.checkUrl=function(e){return e=e.replace(/\s*/g,""),e=_.url("basic",e)},_.url=function(){function a(){return new RegExp(/(.*?)\.?([^.]*?)\.(com|net|org|biz|ws|in|me|co\.uk|co|org\.uk|ltd\.uk|plc\.uk|me\.uk|edu|mil|br\.com|cn\.com|eu\.com|hu\.com|no\.com|qc\.com|sa\.com|se\.com|se\.net|us\.com|uy\.com|ac|co\.ac|gv\.ac|or\.ac|ac\.ac|af|am|as|at|ac\.at|co\.at|gv\.at|or\.at|asn\.au|com\.au|edu\.au|org\.au|net\.au|id\.au|be|ac\.be|adm\.br|adv\.br|am\.br|arq\.br|art\.br|bio\.br|cng\.br|cnt\.br|com\.br|ecn\.br|eng\.br|esp\.br|etc\.br|eti\.br|fm\.br|fot\.br|fst\.br|g12\.br|gov\.br|ind\.br|inf\.br|jor\.br|lel\.br|med\.br|mil\.br|net\.br|nom\.br|ntr\.br|odo\.br|org\.br|ppg\.br|pro\.br|psc\.br|psi\.br|rec\.br|slg\.br|tmp\.br|tur\.br|tv\.br|vet\.br|zlg\.br|br|ab\.ca|bc\.ca|mb\.ca|nb\.ca|nf\.ca|ns\.ca|nt\.ca|on\.ca|pe\.ca|qc\.ca|sk\.ca|yk\.ca|ca|cc|ac\.cn|net\.cn|com\.cn|edu\.cn|gov\.cn|org\.cn|bj\.cn|sh\.cn|tj\.cn|cq\.cn|he\.cn|nm\.cn|ln\.cn|jl\.cn|hl\.cn|js\.cn|zj\.cn|ah\.cn|gd\.cn|gx\.cn|hi\.cn|sc\.cn|gz\.cn|yn\.cn|xz\.cn|sn\.cn|gs\.cn|qh\.cn|nx\.cn|xj\.cn|tw\.cn|hk\.cn|mo\.cn|cn|cx|cz|de|dk|fo|com\.ec|tm\.fr|com\.fr|asso\.fr|presse\.fr|fr|gf|gs|co\.il|net\.il|ac\.il|k12\.il|gov\.il|muni\.il|ac\.in|co\.in|org\.in|ernet\.in|gov\.in|net\.in|res\.in|is|it|ac\.jp|co\.jp|go\.jp|or\.jp|ne\.jp|ac\.kr|co\.kr|go\.kr|ne\.kr|nm\.kr|or\.kr|li|lt|lu|asso\.mc|tm\.mc|com\.mm|org\.mm|net\.mm|edu\.mm|gov\.mm|ms|nl|no|nu|pl|ro|org\.ro|store\.ro|tm\.ro|firm\.ro|www\.ro|arts\.ro|rec\.ro|info\.ro|nom\.ro|nt\.ro|se|si|com\.sg|org\.sg|net\.sg|gov\.sg|sk|st|tf|ac\.th|co\.th|go\.th|mi\.th|net\.th|or\.th|tm|to|com\.tr|edu\.tr|gov\.tr|k12\.tr|net\.tr|org\.tr|com\.tw|org\.tw|net\.tw|ac\.uk|uk\.com|uk\.net|gb\.com|gb\.net|vg|sh|kz|ch|info|ua|gov|name|pro|ie|hk|com\.hk|org\.hk|net\.hk|edu\.hk|us|tk|cd|by|ad|lv|eu\.lv|bz|es|jp|cl|ag|mobi|eu|co\.nz|org\.nz|net\.nz|maori\.nz|iwi\.nz|io|la|md|sc|sg|vc|tw|travel|my|se|tv|pt|com\.pt|edu\.pt|asia|fi|com\.ve|net\.ve|fi|org\.ve|web\.ve|info\.ve|co\.ve|tel|im|gr|ru|net\.ru|org\.ru|hr|com\.hr|ly|xyz)$/)}function r(e,t){var i=e.charAt(0),t=t.split(i);return i===e?t:t[(e=parseInt(e.substring(1),10))<0?t.length+e:e-1]}function s(e,t){for(var i,n=e.charAt(0),a=t.split("&"),r=[],s={},o=e.substring(1),c=0,u=a.length;c<u;c++)if(""!==(r=(r=a[c].match(/(.*?)=(.*)/))||[a[c],a[c],""])[1].replace(/\s/g,"")){if(r[2]=(i=r[2]||"",_.decodeURIComponent(i.replace(/\+/g," "))),o===r[1])return r[2];(i=r[1].match(/(.*)\[([0-9]+)\]/))?(s[i[1]]=s[i[1]]||[],s[i[1]][i[2]]=r[2]):s[r[1]]=r[2]}return n===e?s:s[o]}return function(e,t){var i,n={};if("tld?"===e)return a();if(t=t||window.location.toString(),!e)return t;if(e=e.toString(),t.match(/^mailto:([^/].+)/))i=t.match(/^mailto:([^/].+)/),n.protocol="mailto",n.email=i[1];else{if((t=t.match(/(.*?)\/#!(.*)/)?(i=t.match(/(.*?)\/#!(.*)/))[1]+i[2]:t).match(/(.*?)#(.*)/)&&(i=t.match(/(.*?)#(.*)/),n.hash=i[2],t=i[1]),n.hash&&e.match(/^#/))return s(e,n.hash);if(t.match(/(.*?)\?(.*)/)&&(i=t.match(/(.*?)\?(.*)/),n.query=i[2],t=i[1]),n.query&&e.match(/^\?/))return s(e,n.query);if(t.match(/(.*?):?\/\/(.*)/)&&(i=t.match(/(.*?):?\/\/(.*)/),n.protocol=i[1].toLowerCase(),t=i[2]),t.match(/(.*?)(\/.*)/)&&(i=t.match(/(.*?)(\/.*)/),n.path=i[2],t=i[1]),n.path=(n.path||"").replace(/^([^/])/,"/$1").replace(/\/$/,""),(e=e.match(/^[-0-9]+$/)?e.replace(/^([^/])/,"/$1"):e).match(/^\//))return r(e,n.path.substring(1));if((i=(i=r("/-1",n.path.substring(1)))&&i.match(/(.*?)\.(.*)/))&&(n.file=i[0],n.filename=i[1],n.fileext=i[2]),t.match(/(.*):([0-9]+)$/)&&(i=t.match(/(.*):([0-9]+)$/),n.port=i[2],t=i[1]),t.match(/(.*?)@(.*)/)&&(i=t.match(/(.*?)@(.*)/),n.auth=i[1],t=i[2]),n.auth&&(i=n.auth.match(/(.*):(.*)/),n.user=i?i[1]:n.auth,n.pass=i?i[2]:void 0),n.hostname=t.toLowerCase(),"."===e.charAt(0))return r(e,n.hostname);a()&&(i=n.hostname.match(a()))&&(n.tld=i[3],n.domain=i[2]?i[2]+"."+i[3]:void 0,n.sub=i[1]||void 0);t=n.port?":"+n.port:"";n.protocol=n.protocol||window.location.protocol.replace(":",""),n.port=n.port||("https"===n.protocol?"443":"80"),n.protocol=n.protocol||("443"===n.port?"https":"http"),n.basic=n.protocol+"://"+n.hostname+t}return e in n?n[e]:"{}"===e?n:""}}(),_.createString=function(e){for(var t=e,i=Math.random().toString(36).substr(2);i.length<t;)i+=Math.random().toString(36).substr(2);return i=i.substr(0,e)},_.createAesKey=function(){return _.createString(16)},_.generateEncryptyData=function(e,t){if(void 0!==t){var i=t.publicKey,t=t.version;if(void 0!==i&&void 0!==t&&"undefined"!=typeof CryptoJS&&"undefined"!=typeof JSEncrypt){var n=_.createAesKey();try{var a=CryptoJS.enc.Utf8.parse(n),r=CryptoJS.enc.Utf8.parse(JSON.stringify(e)),s=_.isUndefined(CryptoJS.pad.Pkcs7)?CryptoJS.pad.PKCS7:CryptoJS.pad.Pkcs7,o=CryptoJS.AES.encrypt(r,a,{mode:CryptoJS.mode.ECB,padding:s}).toString(),c=new JSEncrypt,u=(c.setPublicKey(i),c.encrypt(n));return!1===u?(logger$1.warn("Encryption failed, return the original data"),e):{pkv:t,ekey:u,payload:o}}catch(e){logger$1.warn("Encryption failed, return the original data: "+e)}}}return e},_.getUtm=function(){var i={};return _.each(utmTypes,function(e){try{var t=_.getQueryParam(location.href,e);t.length&&(i[e]=t)}catch(e){logger$1.warn("get utm fail: "+e)}}),JSON.stringify(i)},_.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=_.decodeURIComponent(e);t=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===t||t&&"string"!=typeof t[1]&&t[1].length?"":_.decodeURIComponent(t[1])},_.getUtmFromQuery=function(t){var i={};return _.each(utmTypes,function(e){t[e]&&(i[e]=t[e])}),JSON.stringify(i)},_.indexOf=function(e,t){var i=e.indexOf;if(i)return i.call(e,t);for(var n=0;n<e.length;n++)if(t===e[n])return n;return-1},_.checkCalibration=function(e,t,i){return e},"object"===_typeof(logger$1)?logger$1:{}),PlatformProxy=(logger$1.info=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger$1.enabled)try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}},logger$1.warn=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger$1.enabled)try{return console.warn.apply(console,arguments)}catch(e){console.warn(arguments[0])}},function(){function e(){_classCallCheck(this,e),this.config={persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"}}return _createClass(e,[{key:"getConfig",value:function(){return this.config}},{key:"getStorage",value:function(e,t,i){e=localStorage.getItem(e);if(!t)return _.isJSONString(e)?JSON.parse(e):{};_.isJSONString(e)?i(JSON.parse(e)):i({})}},{key:"setStorage",value:function(e,t){localStorage.setItem(e,t)}},{key:"removeStorage",value:function(e){localStorage.removeItem(e)}},{key:"_setSystemProxy",value:function(e){this._sysCallback=e}},{key:"getSystemInfo",value:function(e){var t={mp_platform:"web",system:this._getOs(),screenWidth:window.screen.width,screenHeight:window.screen.height,systemLanguage:navigator.language};this._sysCallback&&(t=_.extend(t,this._sysCallback(e))),e.success(t),e.complete()}},{key:"_getOs",value:function(){var e=navigator.userAgent;return/Windows/i.test(e)?/Phone/.test(e)||/WPDesktop/.test(e)?"Windows Phone":"Windows":/(iPhone|iPad|iPod)/.test(e)?"iOS":/Android/.test(e)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(e)?"BlackBerry":/Mac/i.test(e)?"MacOS":/Linux/.test(e)?"Linux":/CrOS/.test(e)?"ChromeOS":""}},{key:"getNetworkType",value:function(e){e.complete()}},{key:"onNetworkStatusChange",value:function(){}},{key:"request",value:function(e){var t={},i=new XMLHttpRequest;if(i.open(e.method,e.url),e.header)for(var n in e.header)i.setRequestHeader(n,e.header[n]);return i.onreadystatechange=function(){4===i.readyState&&200===i.status?(t.statusCode=200,_.isJSONString(i.responseText)&&(t.data=JSON.parse(i.responseText)),e.success(t)):200!==i.status&&(t.errMsg="network error",e.fail(t))},i.ontimeout=function(){t.errMsg="timeout",e.fail(t)},i.send(e.data),i}},{key:"initAutoTrackInstance",value:function(e,t){this.instance=e,this.autoTrack=t.autoTrack;var i=this;"onpagehide"in window?window.onpagehide=function(){i.onPageHide(!0)}:window.onbeforeunload=function(){i.onPageHide(!0)},i.onPageShow(),i.autoTrack.appHide&&i.instance.timeEvent("ta_page_hide"),"onvisibilitychange"in document&&(document.onvisibilitychange=function(){document.hidden?i.onPageHide(!1):(i.onPageShow(),i.autoTrack.appHide&&i.instance.timeEvent("ta_page_hide"))})}},{key:"setGlobal",value:function(e,t){window[t]=e}},{key:"getAppOptions",value:function(){}},{key:"showToast",value:function(){}},{key:"onPageShow",value:function(){var e;this.autoTrack.appShow&&(_.extend(e={},this.autoTrack.properties),_.isFunction(this.autoTrack.callback)&&_.extend(e,this.autoTrack.callback("appShow")),this.instance._internalTrack("ta_page_show",e))}},{key:"onPageHide",value:function(e){var t;this.autoTrack.appHide&&(_.extend(t={},this.autoTrack.properties),_.isFunction(this.autoTrack.callback)&&_.extend(t,this.autoTrack.callback("appHide")),this.instance._internalTrack("ta_page_hide",t,new Date,null,e))}}],[{key:"createInstance",value:function(){return new e}}]),e}()),AutoTrackBridge=function(){function i(e,t){_classCallCheck(this,i),this.taInstance=e,this.config=t||{},this.referrer="Directly open",this.config.isPlugin?(e.App=function(){App.apply(this,arguments)},inension(e.Page)):(t=App,App=this._initAppExtention(t),e=Page,Page=this._initPageExtension(e))}return _createClass(i,[{key:"_initPageExtension",value:function(n){var a=this;return function(e){var t=e.onShow,i=e.onShareAppMessage;return e.onShow=function(e){a.onPageShow(),"function"==typeof t&&t.call(this,e)},"function"==typeof i&&(e.onShareAppMessage=function(e){e=i.call(this,e);return a.onPageShare(e)}),n(e)}}},{key:"_initAppExtention",value:function(a){var r=this;return function(e){var t=e.onLaunch,i=e.onShow,n=e.onHide;return e.onLaunch=function(e){r.onAppLaunch(e,this),"function"==typeof t&&t.call(this,e)},e.onShow=function(e){r.onAppShow(e),"function"==typeof i&&i.call(this,e)},e.onHide=function(){r.onAppHide(),"function"==typeof n&&n.call(this)},a(e)}}},{key:"onAppLaunch",value:function(e,t){this._setAutoTrackProperties(e),_.isUndefined(t)||(t[this.taInstance.name]=this.taInstance),this.config.appLaunch&&(t={},e&&e.path&&(t["#url_path"]=this._getPath(e.path)),e&&e.query&&(t["#utm"]=_.getUtmFromQuery(e.query)),this.taInstance._internalTrack("ta_mp_launch",t))}},{key:"onAppShow",value:function(e){var t;this.config.appHide&&this.taInstance.timeEvent("ta_mp_hide"),this._setAutoTrackProperties(e),this.config.appShow&&(t={},e&&e.path&&(t["#url_path"]=this._getPath(e.path)),e&&e.query&&(t["#utm"]=_.getUtmFromQuery(e.query)),_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("appShow")),this.taInstance._internalTrack("ta_mp_show",t))}},{key:"onAppHide",value:function(){var e;this.config.appHide&&(e={"#url_path":this._getCurrentPath()},_.extend(e,this.config.properties),_.isFunction(this.config.callback)&&_.extend(e,this.config.callback("appHide")),this.taInstance._internalTrack("ta_mp_hide",e))}},{key:"_getCurrentPath",value:function(){var e="Not to get";try{var t=getCurrentPages(),e=t[t.length-1].route}catch(e){logger$1.info(e)}return e}},{key:"_setAutoTrackProperties",value:function(e){e={"#scene":e.scene};this.taInstance._setAutoTrackProperties(e)}},{key:"_getPath",value:function(e){return"string"==typeof e?e.replace(/^\//,""):"Abnormal values"}},{key:"onPageShare",value:function(e){return this.config.pageShare&&this.taInstance._internalTrack("ta_mp_share",{"#url_path":this._getCurrentPath()}),_.isObject(e)?e:{}}},{key:"onPageShow",value:function(){var e,t;this.config.pageShow&&(t={"#url_path":(e=this._getCurrentPath())||"The system did not get a value","#referrer":this.referrer},this.referrer=e,this.taInstance._internalTrack("ta_mp_view",t))}}]),i}(),AutoTrackBridge$1=function(){function a(e,t,i){var n=this,e=(_classCallCheck(this,a),this.taInstance=e,this.config=t||{},i.getLaunchOptionsSync());this._onShow(e),this.startTracked=!0,i.onShow(function(e){n._onShow(e)}),i.onHide(function(){var e;n.startTracked=!1,n.config.appHide&&(_.extend(e={},n.config.properties),_.isFunction(n.config.callback)&&_.extend(e,n.config.callback("appHide")),n.taInstance._internalTrack("ta_mg_hide",e))})}return _createClass(a,[{key:"_onShow",value:function(e){this.startTracked||(this.config.appHide&&this.taInstance.timeEvent("ta_mg_hide"),e&&e.scene&&this.taInstance._setAutoTrackProperties({"#scene":e.scene}),this.config.appShow&&(_.extend(e={},this.config.properties),_.isFunction(this.config.callback)&&_.extend(e,this.config.callback("appShow")),this.taInstance._internalTrack("ta_mg_show",e)))}}]),a}(),PlatformProxy$1=function(){function n(e,t,i){_classCallCheck(this,n),this.api=e,this.config=t,this._config=i}return _createClass(n,[{key:"getConfig",value:function(){return this.config}},{key:"getStorage",value:function(e,t,i){if(!t)return"dd_mp"===this._config.platform?(t=this.api.getStorageSync({key:e}),_.isJSONString(t.data)?JSON.parse(t.data):{}):(t=this.api.getStorageSync(e),_.isJSONString(t)?JSON.parse(t):{});this.api.getStorage({key:e,success:function(e){e=_.isJSONString(e.data)?JSON.parse(e.data):{};i(e)},fail:function(){logger$1.warn("getStorage faild"),i({})}})}},{key:"setStorage",value:function(e,t){"wechat_mp"===this._config.platform||"wechat_mg"===this._config.platform?this.api.setStorageSync(e,t):this.api.setStorageSync({key:e,data:t})}},{key:"removeStorage",value:function(e){_.isFunction(this.api.removeStorage)?this.api.removeStorage({key:e}):_.isFunction(this.api.deleteStorage)&&this.api.deleteStorage({key:e})}},{key:"_getPlatform",value:function(){return""}},{key:"getSystemInfo",value:function(t){var i=this._config.mpPlatform;this.api.getSystemInfo({success:function(e){_.isFunction(i)?e.mp_platform=i(e):e.mp_platform=i,t.success(e),"wechat"===i&&t.complete()},complete:function(){t.complete()}})}},{key:"getNetworkType",value:function(t){_.isFunction(this.api.getNetworkType)?this.api.getNetworkType({success:function(e){t.success(e)},complete:function(){t.complete()}}):(t.success({}),t.complete())}},{key:"onNetworkStatusChange",value:function(e){_.isFunction(this.api.onNetworkStatusChange)?this.api.onNetworkStatusChange(e):e({})}},{key:"request",value:function(t){var e;return"ali_mp"===this._config.platform||"dd_mp"===this._config.platform?((e=_.extend({},t)).headers=t.header,e.success=function(e){e.statusCode=e.status,t.success(e)},e.fail=function(e){e.errMsg=e.errorMessage,t.fail(e)},"dd_mp"===this._config.platform?this.api.httpRequest(e):this.api.request(e)):this.api.request(t)}},{key:"initAutoTrackInstance",value:function(e,t){return _.isObject(t.autoTrack)&&(t.autoTrack.isPlugin=t.is_plugin),new(this._config.mp?AutoTrackBridge:AutoTrackBridge$1)(e,t.autoTrack,this.api)}},{key:"setGlobal",value:function(e,t){this._config.mp?logger$1.warn("ThinkingAnalytics: we do not set global name for TA instance when you do not enable auto track."):GameGlobal[t]=e}},{key:"getAppOptions",value:function(e){var t={};try{t=this.api.getLaunchOptionsSync()}catch(e){logger$1.warn("Cannot get launch options.")}if(_.isFunction(e))try{this._config.mp?this.api.onAppShow(e):this.api.onShow(e)}catch(e){logger$1.warn("Cannot register onShow callback.")}return t}},{key:"showToast",value:function(e){var t;_.isFunction(this.api.showToast)&&(t={title:e},"dd_mp"!==this._config.platform&&"ali_mp"!==this._config.platform||(t.content=e),this.api.showToast(t))}}],[{key:"createInstance",value:function(){return this._createInstance("R_CURRENT_PLATFORM")}},{key:"_createInstance",value:function(e){switch(e){case"wechat_mp":return new n(wx,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_wechat"},{mpPlatform:"wechat",mp:!0,platform:e});case"wechat_mg":return new n(wx,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_wechat_game"},{mpPlatform:"wechat",platform:e});case"qq_mp":return new n(qq,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qq"},{mpPlatform:"qq",mp:!0,platform:e});case"qq_mg":return new n(qq,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qq_game"},{mpPlatform:"qq",platform:e});case"baidu_mp":return new n(swan,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_swan"},{mpPlatform:function(e){return e.host},mp:!0,platform:e});case"baidu_mg":return new n(swan,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_swan_game"},{mpPlatform:function(e){return e.host},platform:e});case"tt_mg":return new n(tt,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tt_game"},{mpPlatform:function(e){return e.appName},platform:e});case"tt_mp":return new n(tt,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tt"},{mpPlatform:function(e){return e.appName},mp:!0,platform:e});case"ali_mp":return new n(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_ali"},{mpPlatform:function(e){return e.app},mp:!0,platform:e});case"dd_mp":return new n(dd,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_dd"},{mpPlatform:"dingding",mp:!0,platform:e});case"bl_mg":return new n(bl,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"},{mpPlatform:"bilibili",platform:e});case"kuaishou_mp":return new n(ks,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_kuaishou"},{mpPlatform:"kuaishou",mp:!0,platform:e});case"qh360_mg":return new n(qh,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qh360"},{mpPlatform:"qh360",platform:e});case"tb_mp":return new n(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tb"},{mpPlatform:"tb",mp:!0,platform:e});case"jd_mp":return new n(jd,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_jd"},{mpPlatform:"jd",mp:!0,platform:e});case"qh360_mp":return new n(qh,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qh360"},{mpPlatform:"qh360",mp:!0,platform:e});case"WEB":return new PlatformProxy.createInstance}}}]),n}(),AutoTrackBridge$2=_createClass(function e(t,i){var n=this;_classCallCheck(this,e),this.taInstance=t,this.config=i||{},this.config.appShow&&this.taInstance._internalTrack("ta_mg_show"),this.config.appHide&&this.taInstance.timeEvent("ta_mg_hide"),qg.onShow(function(){var e;n.config.appHide&&n.taInstance.timeEvent("ta_mg_hide"),n.config.appShow&&(_.extend(e={},n.config.properties),_.isFunction(n.config.callback)&&_.extend(e,n.config.callback("appShow")),n.taInstance._internalTrack("ta_mg_show"))}),qg.onHide(function(){var e;n.config.appHide&&(_.extend(e={},n.config.properties),_.isFunction(n.config.callback)&&_.extend(e,n.config.callback("appHide")),n.taInstance._internalTrack("ta_mg_hide"))})}),PlatformProxy$2=function(){function e(){_classCallCheck(this,e),this.config={persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_vivo_game",asyncPersistence:!0}}return _createClass(e,[{key:"getConfig",value:function(){return this.config||{}}},{key:"getStorage",value:function(e,t,i){if(!t)return t=qg.getStorageSync({key:e}),_.isJSONString(t)?JSON.parse(t):{};qg.getStorage({key:e,success:function(e){e=_.isJSONString(e)?JSON.parse(e):{};i(e)},fail:function(){i({})}})}},{key:"setStorage",value:function(e,t){qg.setStorage({key:e,value:t})}},{key:"removeStorage",value:function(e){qg.deleteStorage({key:e})}},{key:"getSystemInfo",value:function(n){qg.getSystemInfo({success:function(e){var t=e,i=[e.osType,e.osVersionName].join(" ");t.brand=e.manufacturer,t.system=i,t.mp_platform="vivo_qg",n.success(t)},complete:function(){n.complete()}})}},{key:"getNetworkType",value:function(i){qg.getNetworkType({success:function(e){var t=e;t.networkType=e.type,i.success(t)},complete:function(){i.complete()}})}},{key:"onNetworkStatusChange",value:function(i){qg.subscribeNetworkStatus({callback:function(e){var t=e;t.networkType=e.type,i(t)}})}},{key:"request",value:function(t){return qg.request({url:t.url,data:t.data,method:t.method,header:t.header,success:function(e){t.success(e)},fail:function(e){t.fail(e)}})}},{key:"initAutoTrackInstance",value:function(e,t){return new AutoTrackBridge$2(e,t.autoTrack)}},{key:"setGlobal",value:function(e,t){globalThis[t]=e}},{key:"getAppOptions",value:function(){return{}}},{key:"showToast",value:function(e){qg.showToast({message:e,duration:0})}}],[{key:"createInstance",value:function(){return new e}}]),e}(),AutoTrackBridge$3=_createClass(function e(t,i,n){var a=this;_classCallCheck(this,e),this.taInstance=t,this.config=i||{},this.config.appShow&&(_.extend(t={},this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("appShow")),this.taInstance._internalTrack("ta_mg_show",t)),this.config.appHide&&this.taInstance.timeEvent("ta_mg_hide"),n.onShow(function(){var e;a.config.appHide&&a.taInstance.timeEvent("ta_mg_hide"),a.config.appShow&&(_.extend(e={},a.config.properties),_.isFunction(a.config.callback)&&_.extend(e,a.config.callback("appShow")),a.taInstance._internalTrack("ta_mg_show",e))}),n.onHide(function(){var e;a.config.appHide&&(_.extend(e={},a.config.properties),_.isFunction(a.config.callback)&&_.extend(e,a.config.callback("appHide")),a.taInstance._internalTrack("ta_mg_hide",e))})}),PlatformProxy$3=function(){function n(e,t,i){_classCallCheck(this,n),this.api=e,this.config=t,this._config=i}return _createClass(n,[{key:"getConfig",value:function(){return this.config||{}}},{key:"getStorage",value:function(e,t,i){e=localStorage.getItem(e);if(!t)return _.isJSONString(e)?JSON.parse(e):{};_.isJSONString(e)?i(JSON.parse(e)):i({})}},{key:"setStorage",value:function(e,t){localStorage.setItem(e,t)}},{key:"removeStorage",value:function(e){localStorage.removeItem(e)}},{key:"getSystemInfo",value:function(t){var i=this._config.mpPlatform;this.api.getSystemInfo({success:function(e){e.mp_platform=i,t.success(e)},complete:function(){t.complete()}})}},{key:"getNetworkType",value:function(t){this.api.getNetworkType({success:function(e){t.success(e)},complete:function(){t.complete()}})}},{key:"onNetworkStatusChange",value:function(t){this.api.onNetworkStatusChange({callback:function(e){t(e)}})}},{key:"request",value:function(e){var t={},i=new XMLHttpRequest;if(i.open(e.method,e.url),e.header)for(var n in e.header)i.setRequestHeader(n,e.header[n]);return i.onreadystatechange=function(){4===i.readyState&&200===i.status?(t.statusCode=200,_.isJSONString(i.responseText)&&(t.data=JSON.parse(i.responseText)),e.success(t)):200!==i.status&&(t.errMsg="network error",e.fail(t))},i.ontimeout=function(){t.errMsg="timeout",e.fail(t)},i.send(e.data),i}},{key:"initAutoTrackInstance",value:function(e,t){return new AutoTrackBridge$3(e,t.autoTrack,this.api)}},{key:"setGlobal",value:function(e,t){globalThis[t]=e}},{key:"getAppOptions",value:function(){return this.api.getLaunchOptionsSync()}},{key:"showToast",value:function(e){this.api.showToast({title:e,icon:"none",duration:2e3})}}],[{key:"createInstance",value:function(){return this._createInstance("R_CURRENT_PLATFORM")}},{key:"_createInstance",value:function(e){switch(e){case"oppo":return new n(qg,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_oppo_game"},{mpPlatform:"oppo_qg"});case"huawei":return new n(hbs,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_huawei_game"},{mpPlatform:"huawei_qg"});case"mz":return new n(qg,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_mz_game"},{mpPlatform:"mz"});case"xiaomi":return new n(qg,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg"},{mpPlatform:"xiaomi"})}}}]),n}(),PlatformProxyCC=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"createInstance",value:function(){var i,e=Object.freeze({WECHAT_GAME:104,QQ_PLAY:105,BAIDU_GAME:107,VIVO_GAME:108,OPPO_GAME:109,HUAWEI_GAME:110,XIAOMI_GAME:111,BYTEDANCE_GAME:117,QTT_GAME:116,LINKSURE:119,WECHAT_MINI_GAME:"WECHAT_GAME",BAIDU_MINI_GAME:"BAIDU_MINI_GAME",XIAOMI_QUICK_GAME:"XIAOMI_QUICK_GAME",OPPO_MINI_GAME:"OPPO_MINI_GAME",VIVO_MINI_GAME:"VIVO_MINI_GAME",HUAWEI_QUICK_GAME:"HUAWEI_QUICK_GAME",BYTEDANCE_MINI_GAME:"BYTEDANCE_MINI_GAME",QTT_MINI_GAME:"QTT_MINI_GAME",LINKSURE_MINI_GAME:"LINKSURE_MINI_GAME"});return cc.sys.platform===e.WECHAT_GAME||cc.sys.platform===e.WECHAT_MINI_GAME?PlatformProxy$1._createInstance("wechat_mg"):cc.sys.platform===e.BAIDU_GAME||cc.sys.platform===e.BAIDU_MIN_GAME?PlatformProxy$1._createInstance("baidu_mg"):cc.sys.platform===e.VIVO_GAME||cc.sys.platform===e.VIVO_MINI_GAME?PlatformProxy$2.createInstance():cc.sys.platform===e.QQ_PLAY?PlatformProxy$1._createInstance("qq_mg"):cc.sys.platform===e.OPPO_GAME||cc.sys.platform===e.OPPO_MINI_GAME?PlatformProxy$3._createInstance("oppo"):cc.sys.platform===e.HUAWEI_GAME||cc.sys.platform===e.HUAWEI_QUICK_GAME?PlatformProxy$3._createInstance("huawei"):cc.sys.platform===e.XIAOMI_GAME||cc.sys.platform===e.XIAOMI_QUICK_GAME?PlatformProxy$3._createInstance("xiaomi"):cc.sys.platform===e.BYTEDANCE_GAME||cc.sys.platform===e.BYTEDANCE_MINI_GAME?PlatformProxy$1._createInstance("tt_mg"):((i=PlatformProxy.createInstance())._sysCallback=function(){return{system:cc.sys.os.replace(" ","")+" "+cc.sys.osVersion}},i.getNetworkType=function(e){var t={};switch(cc.sys.getNetworkType()){case cc.sys.NetworkType.LAN:t.networkType="WIFI";break;case cc.sys.NetworkType.WWAN:t.networkType="WWAN";break;default:t.networkType="NONE"}e.success(t),e.complete()},i.getSystemInfo=function(e){var t={mp_platform:cc.sys.platform.toString(),system:i._getOs(),screenWidth:window.screen.width,screenHeight:window.screen.height};i._sysCallback&&(t=_.extend(t,i._sysCallback(e))),e.success(t),e.complete()},i)}}]),e}(),PlatformAPI=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"_getCurrentPlatform",value:function(){return this.currentPlatform||(this.currentPlatform=PlatformProxyCC.createInstance())}},{key:"getConfig",value:function(){return this._getCurrentPlatform().getConfig()}},{key:"getStorage",value:function(e,t,i){return this._getCurrentPlatform().getStorage(e,t,i)}},{key:"setStorage",value:function(e,t){return this._getCurrentPlatform().setStorage(e,t)}},{key:"removeStorage",value:function(e){return this._getCurrentPlatform().removeStorage(e)}},{key:"getSystemInfo",value:function(e){return this._getCurrentPlatform().getSystemInfo(e)}},{key:"getNetworkType",value:function(e){return this._getCurrentPlatform().getNetworkType(e)}},{key:"onNetworkStatusChange",value:function(e){this._getCurrentPlatform().onNetworkStatusChange(e)}},{key:"request",value:function(e){return this._getCurrentPlatform().request(e)}},{key:"initAutoTrackInstance",value:function(e,t){return this._getCurrentPlatform().initAutoTrackInstance(e,t)}},{key:"setGlobal",value:function(e,t){e&&t&&this._getCurrentPlatform().setGlobal(e,t)}},{key:"getAppOptions",value:function(e){return this._getCurrentPlatform().getAppOptions(e)}},{key:"showDebugToast",value:function(e){this._getCurrentPlatform().showToast(e)}}]),e}(),KEY_NAME_MATCH_REGEX=/^[a-zA-Z][a-zA-Z0-9_]{0,49}$/,PropertyChecker=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"stripProperties",value:function(e){return _.isObject(e)&&_.each(e,function(e,t){_.isString(e)||_.isNumber(e)||_.isDate(e)||_.isBoolean(e)||_.isArray(e)||_.isObject(e)||logger$1.warn("Your data -",t,e,"- format does not meet requirements and may not be stored correctly. Attribute values only support String, Number, Date, Boolean, Array, Object")}),e}},{key:"_checkPropertiesKey",value:function(e){var i=!0;return _.each(e,function(e,t){KEY_NAME_MATCH_REGEX.test(t)||(logger$1.warn("Invalid KEY: "+t),i=!1)}),i}},{key:"event",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger$1.warn("Check the parameter format. The eventName must start with an English letter and contain no more than 50 characters including letters, digits, and underscores: "+e),!1)}},{key:"propertyName",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger$1.warn("Check the parameter format. PropertyName must start with a letter and contain letters, digits, and underscores (_). The value is a string of no more than 50 characters: "+e),!1)}},{key:"properties",value:function(e){return this.stripProperties(e),!(e&&(_.isObject(e)?!this._checkPropertiesKey(e)&&(logger$1.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),1):(logger$1.warn("properties can be none, but it must be an object"),1)))}},{key:"propertiesMust",value:function(e){return this.stripProperties(e),void 0===e||!_.isObject(e)||_.isEmptyObject(e)?(logger$1.warn("properties must be an object with a value"),!1):!!this._checkPropertiesKey(e)||(logger$1.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1)}},{key:"userId",value:function(e){return!(!_.isString(e)||!/^.{1,64}$/.test(e))||(logger$1.warn("The user ID must be a string of less than 64 characters and cannot be null"),!1)}},{key:"userAddProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isNumber(e[t]))return logger$1.warn("The attributes of userAdd need to be Number"),!1;return!0}},{key:"userAppendProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isArray(e[t]))return logger$1.warn("The attribute of userAppend must be Array"),!1;return!0}}]),e}(),HttpTask=function(){function r(e,t,i,n,a){_classCallCheck(this,r),this.data=e,this.serverUrl=t,this.callback=a,this.tryCount=_.isNumber(i)?i:1,this.timeout=_.isNumber(n)?n:3e3,this.taClassName="HttpTask"}return _createClass(r,[{key:"run",value:function(){var t=this,e=_.createExtraHeaders(),i=(e["content-type"]="application/json",PlatformAPI.request({url:this.serverUrl,method:"POST",data:this.data,header:e,success:function(e){t.onSuccess(e)},fail:function(e){t.onFailed(e)}}));setTimeout(function(){(_.isObject(i)||_.isPromise(i))&&_.isFunction(i.abort)&&i.abort()},this.timeout)}},{key:"onSuccess",value:function(e){if(200===e.statusCode){var t;switch(e.data.code){case 0:t="success";break;case-1:t="invalid data";break;case-2:t="invalid APP ID";break;default:t="Unknown return code"}this.callback({code:e.data.code,msg:t})}else this.callback({code:-3,msg:e.statusCode})}},{key:"onFailed",value:function(e){0<--this.tryCount?this.run():this.callback({code:-3,msg:e.errMsg})}}]),r}(),HttpTaskDebug=function(){function o(e,t,i,n,a,r,s){_classCallCheck(this,o),this.data=e,this.serverDebugUrl=t,this.callback=s,this.tryCount=_.isNumber(i)?i:1,this.timeout=_.isNumber(n)?n:3e3,this.dryrun=a,this.deviceId=r,this.taClassName="HttpTaskDebug"}return _createClass(o,[{key:"run",value:function(){var t=this,e="appid="+this.data["#app_id"]+"&source=client&dryRun="+this.dryrun+"&deviceId="+this.deviceId+"&data="+encodeURIComponent(JSON.stringify(this.data.data[0])),i=_.createExtraHeaders(),n=(i["content-type"]="application/x-www-form-urlencoded",PlatformAPI.request({url:this.serverDebugUrl,method:"POST",data:e,header:i,success:function(e){t.onSuccess(e)},fail:function(e){t.onFailed(e)}}));setTimeout(function(){(_.isObject(n)||_.isPromise(n))&&_.isFunction(n.abort)&&n.abort()},this.timeout)}},{key:"onSuccess",value:function(e){if(200===e.statusCode){var t;if(0===e.data.errorLevel)t="Verify data success.";else if(1===e.data.errorLevel){for(var i=e.data.errorProperties,n="",a=0;a<i.length;a++)var r=i[a].errorReason,n=n+" propertyName:"+i[a].propertyName+" errorReasons:"+r+"\n";t="Debug data error. errorLevel:"+e.data.errorLevel+" reason:"+n}else 2!==e.data.errorLevel&&-1!==e.data.errorLevel||(t="Debug data error. errorLevel:"+e.data.errorLevel+" reason:"+e.data.errorReasons);logger$1.info(t),this.callback({code:e.data.errorLevel,msg:t})}else this.callback({code:-3,msg:e.statusCode})}},{key:"onFailed",value:function(e){0<--this.tryCount?this.run():this.callback({code:-3,msg:e.errMsg})}}]),o}(),SenderQueue=function(){function e(){_classCallCheck(this,e),this.items=[],this.isRunning=!1,this.showDebug=!1}return _createClass(e,[{key:"enqueue",value:function(e,t,i){var n=!(3<arguments.length&&void 0!==arguments[3])||arguments[3],a=this,e="debug"===i.debugMode?new HttpTaskDebug(e,t,i.maxRetries,i.sendTimeout,0,i.deviceId,function(e){a.isRunning=!1,_.isFunction(i.callback)&&i.callback(e),a._runNext(),!1!==a.showDebug||0!==e.code&&1!==e.code&&2!==e.code||(a.showDebug=!0,_.isFunction(PlatformAPI.showDebugToast)&&PlatformAPI.showDebugToast("The current mode is Debug"))}):"debugOnly"===i.debugMode?new HttpTaskDebug(e,t,i.maxRetries,i.sendTimeout,1,i.deviceId,function(e){a.isRunning=!1,_.isFunction(i.callback)&&i.callback(e),a._runNext(),!1!==a.showDebug||0!==e.code&&1!==e.code&&2!==e.code||(a.showDebug=!0,_.isFunction(PlatformAPI.showDebugToast)&&PlatformAPI.showDebugToast("The current mode is debugOnly"))}):new HttpTask(JSON.stringify(e),t,i.maxRetries,i.sendTimeout,function(e){a.isRunning=!1,_.isFunction(i.callback)&&i.callback(e),a._runNext()});!0===n?(this.items.push(e),this._runNext()):e.run()}},{key:"_dequeue",value:function(){return this.items.shift()}},{key:"_runNext",value:function(){if(0<this.items.length&&!this.isRunning)if(this.isRunning=!0,"HttpTask"!==this.items[0].taClassName)this._dequeue().run();else{var e=this.items.splice(0,this.items.length),t=e[0],i=JSON.parse(t.data),n=i["#app_id"],a=[];a.push(t.callback);for(var r=1;r<e.length;r++){var s=e[r],o=JSON.parse(s.data);o["#app_id"]===n&&t.serverUrl===s.serverUrl?(i.data=i.data.concat(o.data),a.push(s.callback)):this.items.push(s)}var c=(new Date).getTime();i["#flush_time"]=c,new HttpTask(JSON.stringify(i),t.serverUrl,t.tryCount,t.timeout,function(e){for(var t in a)Object.hasOwnProperty.call(a,t)&&(0,a[t])(e)}).run()}}}]),e}(),senderQueue=new SenderQueue,DEFAULT_CONFIG={name:"thinkingdata",is_plugin:!1,maxRetries:3,sendTimeout:3e3,enablePersistence:!0,asyncPersistence:!1,enableLog:!0,strict:!1,debugMode:"none",enableCalibrationTime:!1},systemInformation={properties:{"#lib":Config.LIB_NAME,"#lib_version":Config.LIB_VERSION},initDeviceId:function(e){_.isString(e)&&(this.properties["#device_id"]=e)},getSystemInfo:function(e){var i=this;PlatformAPI.onNetworkStatusChange(function(e){i.properties["#network_type"]=e.networkType}),PlatformAPI.getNetworkType({success:function(e){i.properties["#network_type"]=e.networkType},complete:function(){PlatformAPI.getSystemInfo({success:function(e){logger$1.info(JSON.stringify(e,null,4));var t=e.system?e.system.replace(/\s+/g," ").split(" "):[],t={"#manufacturer":e.brand,"#device_model":e.model,"#screen_width":Number(e.screenWidth),"#screen_height":Number(e.screenHeight),"#os":t[0],"#os_version":t[1],"#mp_platform":e.mp_platform,"#system_language":e.systemLanguage};_.extend(i.properties,t),_.setMpPlatform(e.mp_platform)},complete:function(){e()}})}})}},ThinkingDataPersistence=function(){function e(t,i){var n=this;_classCallCheck(this,e),this.enabled=t.enablePersistence,this.enabled?(t.isChildInstance?(this.name=t.persistenceName+"_"+t.name,this.nameOld=t.persistenceNameOld+"_"+t.name):(this.name=t.persistenceName,this.nameOld=t.persistenceNameOld),t.asyncPersistence?(this._state={},PlatformAPI.getStorage(this.name,!0,function(e){_.isEmptyObject(e)?PlatformAPI.getStorage(n.nameOld,!0,function(e){n._state=_.extend2Layers({},e,n._state),n._init(t,i),n._save()}):(n._state=_.extend2Layers({},e,n._state),n._init(t,i),n._save())})):(this._state=PlatformAPI.getStorage(this.name)||{},_.isEmptyObject(this._state)&&(this._state=PlatformAPI.getStorage(this.nameOld)||{}),this._init(t,i))):(this._state={},this._init(t,i))}return _createClass(e,[{key:"_init",value:function(e,t){this.getDistinctId()||this.setDistinctId(_.UUID()),e.isChildInstance||(this.getDeviceId()||this._setDeviceId(_.UUID()),systemInformation.initDeviceId(this.getDeviceId())),this.initComplete=!0,"function"==typeof t&&t(),this._save()}},{key:"_save",value:function(){this.enabled&&this.initComplete&&PlatformAPI.setStorage(this.name,JSON.stringify(this._state))}},{key:"_set",value:function(e,t){var i,n=this;"string"==typeof e?(i={})[e]=t:"object"===_typeof(e)&&(i=e),_.each(i,function(e,t){n._state[t]=e}),this._save()}},{key:"_get",value:function(e){return this._state[e]}},{key:"setEventTimer",value:function(e,t){var i=this._state.event_timers||{};i[e]=t,this._set("event_timers",i)}},{key:"removeEventTimer",value:function(e){var t=(this._state.event_timers||{})[e];return _.isUndefined(t)||(delete this._state.event_timers[e],this._save()),t}},{key:"getDeviceId",value:function(){return this._state.device_id}},{key:"_setDeviceId",value:function(e){this.getDeviceId()?logger$1.warn("cannot modify the device id."):this._set("device_id",e)}},{key:"getDistinctId",value:function(){return this._state.distinct_id}},{key:"setDistinctId",value:function(e){this._set("distinct_id",e)}},{key:"getAccountId",value:function(){return this._state.account_id}},{key:"setAccountId",value:function(e){this._set("account_id",e)}},{key:"getSuperProperties",value:function(){return this._state.props||{}}},{key:"setSuperProperties",value:function(e,t){t=t?e:_.extend(this.getSuperProperties(),e);this._set("props",t)}}]),e}(),dataStoragePrefix="tampsdk_",tabStoragePrefix="tab_tampsdk_",BatchConsumer=function(){function i(e,t){_classCallCheck(this,i),this.config=e,this.ta=t,this.timer=null,this.batchConfig=_.extend({size:5,interval:5e3,storageLimit:200},this.config.batchConfig),this.batchConfig.size<1&&(this.batchConfig.size=1),30<this.batchConfig.size&&(this.batchConfig.size=30),this.tabKey=tabStoragePrefix+this.config.appId,this.storageLimit=this.batchConfig.storageLimit}return _createClass(i,[{key:"batchInterval",value:function(){var e=this;e.timer=setTimeout(function(){e.recycle(),e.send(),clearTimeout(e.timer),e.batchInterval()},this.batchConfig.interval)}},{key:"add",value:function(e){var t=dataStoragePrefix+this.config.appId+"_"+String(_.UUID()),i=PlatformAPI.getStorage(this.tabKey);if((i=_.isArray(i)?i:[]).length<=this.storageLimit)i.push(t),PlatformAPI.setStorage(this.tabKey,JSON.stringify(i)),PlatformAPI.setStorage(t,JSON.stringify(e));else{for(var n=i.splice(0,20),i=(console.log("deleted events data:"+n),i.push(t),PlatformAPI.setStorage(this.tabKey,JSON.stringify(i)),PlatformAPI.setStorage(t,JSON.stringify(e)),{}),a=[],r=0;r<n.length;r++){var s=PlatformAPI.getStorage(n[r]);a.push(s)}i.data=a,i["#app_id"]=this.config.appId,this.request(i,n)}}},{key:"flush",value:function(){clearTimeout(this.timer),this.send(),this.batchInterval()}},{key:"send",value:function(){var e=PlatformAPI.getStorage(this.tabKey);if(e&&e.length){for(var t={},i=[],n=[],a=e.length<this.batchConfig.size?e.length:this.batchConfig.size,r=0;r<a;r++){var s=PlatformAPI.getStorage(e[r]);i.push(s),n.push(e[r])}t.data=i,t["#app_id"]=this.config.appId,this.request(t,n)}}},{key:"request",value:function(e,t){var i=this;logger$1.info("flush data: "+JSON.stringify(e)),senderQueue.enqueue(e,this.ta.serverUrl,{maxRetries:this.config.maxRetries,sendTimeout:this.config.sendTimeout,callback:function(e){i.remove(t)},debugMode:this.config.debugMode,deviceId:this.ta.getDeviceId()})}},{key:"remove",value:function(e){var t=PlatformAPI.getStorage(this.tabKey);if(t){for(var i=0;i<e.length;i++){var n=_.indexOf(t,e[i]);-1<n&&t.splice(n,1),PlatformAPI.removeStorage(e[i])}PlatformAPI.setStorage(this.tabKey,JSON.stringify(t))}}},{key:"recycle",value:function(){}}]),i}(),ThinkingDataAPI=function(){function i(e){_classCallCheck(this,i),e.appId=e.appId?_.checkAppId(e.appId):_.checkAppId(e.appid),e.serverUrl=e.serverUrl?_.checkUrl(e.serverUrl):_.checkUrl(e.server_url);var t=_.extend({},DEFAULT_CONFIG,PlatformAPI.getConfig());_.isObject(e)?this.config=_.extend(t,e):this.config=t,this._init(this.config)}return _createClass(i,[{key:"_init",value:function(e){var t=this,i=(this.name=e.name,this.appId=e.appId||e.appid,e.serverUrl||e.server_url);this.serverUrl=i+"/sync_xcx",this.serverDebugUrl=i+"/data_debug",this.configUrl=i+"/config",this.autoTrackProperties={},this._queue=[],this.updateConfig(this.configUrl,this.appId),e.isChildInstance?this._state={}:(logger$1.enabled=e.enableLog,this.instances=[],this._state={getSystemInfo:!1,initComplete:!1},PlatformAPI.setGlobal(this,this.name)),this.store=new ThinkingDataPersistence(e,function(){t.config.asyncPersistence&&_.isFunction(t.config.persistenceComplete)&&t.config.persistenceComplete(t),t._updateState()}),this.enabled=!_.isBoolean(this.store._get("ta_enabled"))||this.store._get("ta_enabled"),this.isOptOut=!!_.isBoolean(this.store._get("ta_isOptOut"))&&this.store._get("ta_isOptOut"),!e.isChildInstance&&e.autoTrack&&(this.autoTrack=PlatformAPI.initAutoTrackInstance(this,e)),void 0!==this.config.enableBatch&&!1!==this.config.enableBatch&&(this.batchConsumer=new BatchConsumer(this.config,this),this.batchConsumer.batchInterval())}},{key:"initSystemInfo",value:function(){var e=this;this.config.isChildInstance||systemInformation.getSystemInfo(function(){e._updateState({getSystemInfo:!0})})}},{key:"updateConfig",value:function(e,t){var i=this,n=_.createExtraHeaders(),a=(n["content-type"]="application/json",PlatformAPI.request({url:e+"?appid="+t,method:"GET",header:n,success:function(e){_.isUndefined(e)||_.isUndefined(e.data)||(logger$1.info("config update success("+t+") :"+JSON.stringify(e.data)),_.isUndefined(e.data.data))||(i.config.syncBatchSize=e.data.data.sync_batch_size,i.config.syncInterval=e.data.data.sync_interval,i.config.disableEventList=e.data.data.disable_event_list,_.isUndefined(e.data.data.secret_key))||(e=e.data.data.secret_key,i.config.secretKey={publicKey:e.key,version:e.version})},fail:function(e){logger$1.info("config update fail("+t+") :"+e.errMsg)}}));setTimeout(function(){(_.isObject(a)||_.isPromise(a))&&_.isFunction(a.abort)&&a.abort()},3e3)}},{key:"initInstance",value:function(e,t){if(!this.config.isChildInstance)return _.isString(e)&&e!==this.name&&_.isUndefined(this[e])?(t=new i(_.extend({},this.config,{enablePersistence:!1,isChildInstance:!0,name:e},t)),this[e]=t,this.instances.push(e),this[e]._state=this._state,t):void logger$1.warn("initInstance() failed due to the name is invalid: "+e);logger$1.warn("initInstance() cannot be called on child instance")}},{key:"lightInstance",value:function(e){return this[e]}},{key:"_setAutoTrackProperties",value:function(e){_.extend(this.autoTrackProperties,e)}},{key:"init",value:function(){if(this.initSystemInfo(),this._state.initComplete)return!1;this._updateState({initComplete:!0}),logger$1.info("Thinking Analytics SDK initialized successfully with mode: "+this.config.debugMode+", APP ID : "+this.config.appId+", server url: "+this.config.serverUrl+", libversion: "+Config.LIB_VERSION)}},{key:"_isReady",value:function(){return this._state.getSystemInfo&&this._state.initComplete&&this.store.initComplete&&this.getDeviceId()}},{key:"_updateState",value:function(e){var t=this;_.isObject(e)&&_.extend(this._state,e),this._onStateChange(),_.each(this.instances,function(e){t[e]._onStateChange()})}},{key:"_onStateChange",value:function(){var t=this;this._isReady()&&this._queue&&0<this._queue.length&&(_.each(this._queue,function(e){t[e[0]].apply(t,slice.call(e[1]))}),this._queue=[])}},{key:"_hasDisabled",value:function(){var e=!this.enabled||this.isOptOut;return e&&logger$1.info("ThinkingData is Pause or Stop!"),e}},{key:"_sendRequest",value:function(e,t,i){var n,a;this._hasDisabled()||(!_.isUndefined(this.config.disableEventList)&&this.config.disableEventList.includes(e.eventName)?logger$1.info("disabled Event : "+e.eventName):(t=_.isDate(t)?t:new Date,n={data:[{"#type":e.type,"#time":_.formatDate(t),"#distinct_id":this.store.getDistinctId()}]},this.store.getAccountId()&&(n.data[0]["#account_id"]=this.store.getAccountId()),"track"===e.type||"track_update"===e.type||"track_overwrite"===e.type?(n.data[0]["#event_name"]=e.eventName,"track_update"===e.type||"track_overwrite"===e.type?n.data[0]["#event_id"]=e.extraId:e.firstCheckId&&(n.data[0]["#first_check_id"]=e.firstCheckId),n.data[0].properties=_.extend({"#zone_offset":0-t.getTimezoneOffset()/60},systemInformation.properties,this.autoTrackProperties,this.store.getSuperProperties(),this.dynamicProperties?this.dynamicProperties():{}),t=this.store.removeEventTimer(e.eventName),_.isUndefined(t)||(t=(new Date).getTime()-t,86400<(t=parseFloat((t/1e3).toFixed(3)))?t=86400:t<0&&(t=0),n.data[0].properties["#duration"]=t)):n.data[0].properties={},_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(n.data[0].properties,e.properties),_.searchObjDate(n.data[0]),1<this.config.maxRetries&&(n.data[0]["#uuid"]=_.UUIDv4()),n["#app_id"]=this.appId,logger$1.info(JSON.stringify(n,null,4)),t="debug"===this.config.debugMode||"debugOnly"===this.config.debugMode?this.serverDebugUrl:this.serverUrl,_.isBoolean(this.config.enableEncrypt)&&!0===this.config.enableEncrypt&&(n.data[0]=_.generateEncryptyData(n.data[0],this.config.secretKey)),this.batchConsumer&&"none"===this.config.debugMode&&!i?(this.batchConsumer.add(n.data[0]),_.isFunction(e.onComplete)&&e.onComplete({code:0,msg:"success"})):i?(i=new FormData,"debug"===this.config.debugMode||"debugOnly"===this.config.debugMode?(i.append("source","client"),i.append("appid",this.appId),i.append("dryRun","debugOnly"===this.config.debugMode?1:0),i.append("deviceId",this.getDeviceId()),i.append("data",JSON.stringify(n.data[0]))):(a=_.base64Encode(JSON.stringify(n)),i.append("data",a)),navigator.sendBeacon(t,i),_.isFunction(e.onComplete)&&e.onComplete({statusCode:200})):senderQueue.enqueue(n,t,{maxRetries:this.config.maxRetries,sendTimeout:this.config.sendTimeout,callback:e.onComplete,debugMode:this.config.debugMode,deviceId:this.getDeviceId()})))}},{key:"_isObjectParams",value:function(e){return _.isObject(e)&&_.isFunction(e.onComplete)}},{key:"track",value:function(e,t,i,n){var a;this._hasDisabled()||(this._isObjectParams(e)&&(e=(a=e).eventName,t=a.properties,i=a.time,n=a.onComplete),PropertyChecker.event(e)&&PropertyChecker.properties(t)||!this.config.strict?this._internalTrack(e,t,i,n):_.isFunction(n)&&n({code:-1,msg:"invalid parameters"}))}},{key:"trackUpdate",value:function(e){var t,i;this._hasDisabled()||(e&&e.eventId&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),i=_.isDate(e.time)?e.time:new Date,this._sendRequest({type:"track_update",eventName:e.eventName,properties:t,onComplete:e.onComplete,extraId:e.eventId},i)):this._queue.push(["trackUpdate",[e]]):(logger$1.warn("Invalide parameter for trackUpdate: you should pass an object contains eventId to trackUpdate()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"trackOverwrite",value:function(e){var t,i;this._hasDisabled()||(e&&e.eventId&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),i=_.isDate(e.time)?e.time:new Date,this._sendRequest({type:"track_overwrite",eventName:e.eventName,properties:t,onComplete:e.onComplete,extraId:e.eventId},i)):this._queue.push(["trackOverwrite",[e]]):(logger$1.warn("Invalide parameter for trackOverwrite: you should pass an object contains eventId to trackOverwrite()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"trackFirstEvent",value:function(e){var t,i;this._hasDisabled()||(e&&e.eventName&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),i=_.isDate(e.time)?e.time:new Date,this._sendRequest({type:"track",eventName:e.eventName,properties:t,onComplete:e.onComplete,firstCheckId:e.firstCheckId||this.getDeviceId()},i)):this._queue.push(["trackFirstEvent",[e]]):(logger$1.warn("Invalide parameter for trackFirstEvent: you should pass an object contains eventName to trackFirstEvent()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"_internalTrack",value:function(e,t,i,n,a){var r;this._hasDisabled()||(r=_.checkCalibration(t,i,this.config.enableCalibrationTime),i=_.isDate(i)?i:new Date,this._isReady()?this._sendRequest({type:"track",eventName:e,properties:r,onComplete:n},i,a):this._queue.push(["_internalTrack",[e,t,i,n]]))}},{key:"userSet",value:function(e,t,i){var n;this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_set",properties:e,onComplete:i},t):this._queue.push(["userSet",[e,t,i]])):(logger$1.warn("calling userSet failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userSetOnce",value:function(e,t,i){var n;this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_setOnce",properties:e,onComplete:i},t):this._queue.push(["userSetOnce",[e,t,i]])):(logger$1.warn("calling userSetOnce failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userUnset",value:function(e,t,i){var n;this._hasDisabled()||(this._isObjectParams(void 0)&&(e=(n=void 0).property,t=n.time,i=n.onComplete),PropertyChecker.propertyName(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?((n={})[e]=0,this._sendRequest({type:"user_unset",properties:n,onComplete:i},t)):this._queue.push(["userUnset",[e,i,t]])):(logger$1.warn("calling userUnset failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userDel",value:function(e,t){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).time,t=i.onComplete),e=_.isDate(e)?e:new Date,this._isReady()?this._sendRequest({type:"user_del",onComplete:t},e):this._queue.push(["userDel",[e,t]]))}},{key:"userAdd",value:function(e,t,i){var n;this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.userAddProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_add",properties:e,onComplete:i},t):this._queue.push(["userAdd",[e,t,i]])):(logger$1.warn("calling userAdd failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userAppend",value:function(e,t,i){var n;this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.userAppendProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_append",properties:e,onComplete:i},t):this._queue.push(["userAppend",[e,t,i]])):(logger$1.warn("calling userAppend failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userUniqAppend",value:function(e,t,i){var n;this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.userAppendProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_uniq_append",properties:e,onComplete:i},t):this._queue.push(["userUniqAppend",[e,t,i]])):(logger$1.warn("calling userAppend failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"flush",value:function(){this.batchConsumer&&"none"===this.config.debugMode&&this.batchConsumer.flush()}},{key:"authorizeOpenID",value:function(e){this.identify(e)}},{key:"identify",value:function(e){if(!this._hasDisabled()){if("number"==typeof e)e=String(e);else if("string"!=typeof e)return!1;this.store.setDistinctId(e)}}},{key:"getDistinctId",value:function(){return this.store.getDistinctId()}},{key:"login",value:function(e){if(!this._hasDisabled()){if("number"==typeof e)e=String(e);else if("string"!=typeof e)return!1;this.store.setAccountId(e)}}},{key:"getAccountId",value:function(){return this.store.getAccountId()}},{key:"logout",value:function(){this._hasDisabled()||this.store.setAccountId(null)}},{key:"setSuperProperties",value:function(e){this._hasDisabled()||(PropertyChecker.propertiesMust(e)||!this.config.strict?this.store.setSuperProperties(e):logger$1.warn("setSuperProperties parameter must be a valid property value"))}},{key:"clearSuperProperties",value:function(){this._hasDisabled()||this.store.setSuperProperties({},!0)}},{key:"unsetSuperProperty",value:function(e){var t;this._hasDisabled()||_.isString(e)&&(delete(t=this.getSuperProperties())[e],this.store.setSuperProperties(t,!0))}},{key:"getSuperProperties",value:function(){return this.store.getSuperProperties()}},{key:"getPresetProperties",value:function(){var e=systemInformation.properties,t={},i=e["#os"],i=(t.os=_.isUndefined(i)?"":i,e["#screen_width"]),i=(t.screenWidth=_.isUndefined(i)?0:i,e["#screen_height"]),i=(t.screenHeight=_.isUndefined(i)?0:i,e["#network_type"]),i=(t.networkType=_.isUndefined(i)?"":i,e["#device_model"]),i=(t.deviceModel=_.isUndefined(i)?"":i,e["#os_version"]),n=(t.osVersion=_.isUndefined(i)?"":i,t.deviceId=this.getDeviceId(),0-(new Date).getTimezoneOffset()/60),i=(t.zoneOffset=n,e["#manufacturer"]);return t.manufacturer=_.isUndefined(i)?"":i,t.toEventPresetProperties=function(){return{"#device_model":t.deviceModel,"#device_id":t.deviceId,"#screen_width":t.screenWidth,"#screen_height":t.screenHeight,"#os":t.os,"#os_version":t.osVersion,"#network_type":t.networkType,"#zone_offset":n,"#manufacturer":t.manufacturer}},t}},{key:"setDynamicSuperProperties",value:function(e){this._hasDisabled()||("function"==typeof e?PropertyChecker.properties(e())||!this.config.strict?this.dynamicProperties=e:logger$1.warn("A dynamic public property must return a valid property value"):logger$1.warn("setDynamicSuperProperties parameter must be a function type"))}},{key:"timeEvent",value:function(e,t){this._hasDisabled()||(t=_.isDate(t)?t:new Date,this._isReady()?PropertyChecker.event(e)||!this.config.strict?this.store.setEventTimer(e,t.getTime()):logger$1.warn("calling timeEvent failed due to invalid eventName: "+e):this._queue.push(["timeEvent",[e,t]]))}},{key:"getDeviceId",value:function(){return systemInformation.properties["#device_id"]}},{key:"enableTracking",value:function(e){this.enabled=e,this.store._set("ta_enabled",e)}},{key:"optOutTracking",value:function(){this.store.setSuperProperties({},!0),this.store.setDistinctId(_.UUID()),this.store.setAccountId(null),this._queue.splice(0,this._queue.length),this.isOptOut=!0,this.store._set("ta_isOptOut",!0)}},{key:"optOutTrackingAndDeleteUser",value:function(){var e=new Date;this._sendRequest({type:"user_del"},e),this.optOutTracking()}},{key:"optInTracking",value:function(){this.isOptOut=!1,this.store._set("ta_isOptOut",!1)}},{key:"setTrackStatus",value:function(e){switch(e){case"PAUSE":this.eventSaveOnly=!1,this.optInTracking(),this.enableTracking(!1);break;case"STOP":this.eventSaveOnly=!1,this.optOutTracking(!0);break;case"SAVE_ONLY":break;default:this.eventSaveOnly=!1,this.optInTracking(),this.enableTracking(!0)}logger$1.info("switch track status:"+e)}}]),i}(),DEFAULT_CONFIG$1={name:"thinkingdata",enableLog:!0,enableNative:!1},ThinkingDataAPIForNative=function(){function i(e){_classCallCheck(this,i),e.appId=e.appId?_.checkAppId(e.appId):_.checkAppId(e.appid),e.serverUrl=e.serverUrl?_.checkUrl(e.serverUrl):_.checkUrl(e.server_url);var t=_.extend({},DEFAULT_CONFIG$1,PlatformAPI.getConfig());_.isObject(e)?this.config=_.extend(t,e):this.config=t,this._init(this.config)}return _createClass(i,[{key:"_isNativePlatform",value:function(){return!(!this._isIOS()&&!this._isAndroid()||!this.config.enableNative)}},{key:"_isIOS",value:function(){return!(!cc.sys.isNative||"iOS"!==cc.sys.os)}},{key:"_isAndroid",value:function(){return!(!cc.sys.isNative||"Android"!==cc.sys.os)}},{key:"_init",value:function(e){this.name=e.name,this.appId=e.appId||e.appid,this._isNativePlatform()?(this.initInstanceForNative(this.name,e,this.appId),this._readStorage(e)):this.taJs=new ThinkingAnalyticsAPIForJS(e)}},{key:"_readStorage",value:function(e){var t=this,i=e.persistenceName,n=e.persistenceNameOld;e.isChildInstance&&(i=e.persistenceName+"_"+e.name,n=e.persistenceNameOld+"_"+e.name),this._state=PlatformAPI.getStorage(i)||{},_.isEmptyObject(this._state)&&(this._state=PlatformAPI.getStorage(n)||{}),_.isEmptyObject(this._state)?PlatformAPI.getStorage(i,!0,function(e){_.isEmptyObject(e)?PlatformAPI.getStorage(n,!0,function(e){t._state=_.extend2Layers({},e,t._state)}):t._state=_.extend2Layers({},e,t._state),t._state.distinct_id&&t.identifyForNative(t._state.distinct_id),t._state.account_id&&t.loginForNative(t._state.account_id)}):(this._state.distinct_id&&this.identifyForNative(this._state.distinct_id),this._state.account_id&&this.loginForNative(this._state.account_id))}},{key:"initInstance",value:function(e,t){return this._isNativePlatform()?_.isUndefined(t)?this[e]=new ThinkingAnalyticsAPI(this.config):this[e]=new ThinkingAnalyticsAPI(t):this[e]=this.taJs.initInstance(e,t),this[e]}},{key:"lightInstance",value:function(e){return this[e]}},{key:"init",value:function(){var e,t;this._isNativePlatform()?(e=window,t=this,e.__autoTrackCallback=function(e){return _.isFunction(t.config.autoTrack.callback)?(e=t.config.autoTrack.callback(e),JSON.stringify(e)):"{}"},this.startThinkingAnalyticsForNative()):this.taJs.init()}},{key:"track",value:function(e,t,i,n){this._isNativePlatform()?this.trackForNative(e,t,i,this.appId):this.taJs.track(e,t,i,n)}},{key:"trackUpdate",value:function(e){this._isNativePlatform()?this.trackUpdateForNative(e,this.appId):this.taJs.trackUpdate(e)}},{key:"trackOverwrite",value:function(e){this._isNativePlatform()?this.trackOverwriteForNative(e,this.appId):this.taJs.trackOverwrite(e)}},{key:"trackFirstEvent",value:function(e){this._isNativePlatform()?this.trackFirstEventForNative(e,this.appId):this.taJs.trackFirstEvent(e)}},{key:"userSet",value:function(e,t,i){this._isNativePlatform()?this.userSetForNative(e,this.appId):this.taJs.userSet(e,t,i)}},{key:"userSetOnce",value:function(e,t,i){this._isNativePlatform()?this.userSetOnceForNative(e,this.appId):this.taJs.userSetOnce(e,t,i)}},{key:"userUnset",value:function(e,t,i){this._isNativePlatform()?this.userUnsetForNative(e,this.appId):this.taJs.userUnset(e,t,i)}},{key:"userDel",value:function(e,t){this._isNativePlatform()?this.userDelForNative(this.appId):this.taJs.userDel(e,t)}},{key:"userAdd",value:function(e,t,i){this._isNativePlatform()?this.userAddForNative(e,this.appId):this.taJs.userAdd(e,t,i)}},{key:"userAppend",value:function(e,t,i){this._isNativePlatform()?this.userAppendForNative(e,this.appId):this.taJs.userAppend(e,t,i)}},{key:"userUniqAppend",value:function(e,t,i){this._isNativePlatform()?this.userUniqAppendForNative(e,this.appId):this.taJs.userUniqAppend(e,t,i)}},{key:"flush",value:function(){this._isNativePlatform()?this.flushForNative(this.appId):this.taJs.flush()}},{key:"authorizeOpenID",value:function(e){this.identify(e)}},{key:"identify",value:function(e){this._isNativePlatform()?this.identifyForNative(e,this.appId):this.taJs.identify(e)}},{key:"getDistinctId",value:function(){return this._isNativePlatform()?this.getDistinctIdForNative(this.appId):this.taJs.getDistinctId()}},{key:"login",value:function(e){this._isNativePlatform()?this.loginForNative(e,this.appId):this.taJs.login(e)}},{key:"getAccountId",value:function(){return this._isNativePlatform()?this.getAccountIdForNative(this.appId):this.taJs.getAccountId()}},{key:"logout",value:function(){this._isNativePlatform()?this.logoutForNative(this.appId):this.taJs.logout()}},{key:"setSuperProperties",value:function(e){this._isNativePlatform()?this.setSuperPropertiesForNative(e,this.appId):this.taJs.setSuperProperties(e)}},{key:"clearSuperProperties",value:function(){this._isNativePlatform()?this.clearSuperPropertiesForNative(this.appId):this.taJs.clearSuperProperties()}},{key:"unsetSuperProperty",value:function(e){this._isNativePlatform()?this.unsetSuperPropertyForNative(e,this.appId):this.taJs.unsetSuperProperty(e)}},{key:"getSuperProperties",value:function(){return this._isNativePlatform()?this.getSuperPropertiesForNative(this.appId):this.taJs.getSuperProperties()}},{key:"getPresetProperties",value:function(){var e,t,i,n;return this._isNativePlatform()?(e=this.getPresetPropertiesForNative(this.appId),t={},n=e["#os"],t.os=_.isUndefined(n)?"":n,n=e["#screen_width"],t.screenWidth=_.isUndefined(n)?0:n,n=e["#screen_height"],t.screenHeight=_.isUndefined(n)?0:n,n=e["#network_type"],t.networkType=_.isUndefined(n)?"":n,n=e["#device_model"],t.deviceModel=_.isUndefined(n)?"":n,n=e["#os_version"],t.osVersion=_.isUndefined(n)?"":n,t.deviceId=this.getDeviceId(),i=0-(new Date).getTimezoneOffset()/60,t.zoneOffset=i,n=e["#manufacturer"],t.manufacturer=_.isUndefined(n)?"":n,t.toEventPresetProperties=function(){return{"#device_model":t.deviceModel,"#device_id":t.deviceId,"#screen_width":t.screenWidth,"#screen_height":t.screenHeight,"#os":t.os,"#os_version":t.osVersion,"#network_type":t.networkType,"#zone_offset":i,"#manufacturer":t.manufacturer}},t):this.taJs.getPresetProperties()}},{key:"setDynamicSuperProperties",value:function(t){this._isNativePlatform()?"function"==typeof t?(this.dynamicProperties=t,window.__dynamicPropertiesForNative=function(e){console.log("__dynamicPropertiesForNative: native msg: ",e);e=t(),e=_.encodeDates(e);return JSON.stringify(e)},this.setDynamicSuperPropertiesForNative("__dynamicPropertiesForNative")):logger.warn("setDynamicSuperProperties parameter must be a function type"):this.taJs.setDynamicSuperProperties(t)}},{key:"timeEvent",value:function(e,t){return this._isNativePlatform()?this.timeEventForNative(e,this.appId):this.taJs.timeEvent(e,t)}},{key:"getDeviceId",value:function(){return this._isNativePlatform()?this.getDeviceIdForNative(this.appId):this.taJs.getDeviceId()}},{key:"enableTracking",value:function(e){this._isNativePlatform()?this.enableTrackingForNative(e,this.appId):this.taJs.enableTracking(e)}},{key:"optOutTracking",value:function(){this._isNativePlatform()?this.optOutTrackingForNative(this.appId):this.taJs.optOutTracking()}},{key:"optOutTrackingAndDeleteUser",value:function(){this._isNativePlatform()?this.optOutTrackingAndDeleteUserForNative(this.appId):this.taJs.optOutTrackingAndDeleteUser()}},{key:"optInTracking",value:function(){this._isNativePlatform()?this.optInTrackingForNative(this.appId):this.taJs.optInTracking()}},{key:"setTrackStatus",value:function(e){this._isNativePlatform()?this.setTrackStatusForNative(e,this.appId):this.taJs.setTrackStatus(e)}},{key:"trackForNative",value:function(e,t,i,n){i=_.isDate(i)?_.formatDate(i):"";_.isUndefined(t)&&(t={}),t=_.extend(t,this.dynamicProperties?this.dynamicProperties():{}),t=_.encodeDates(t),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","track","(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",e,JSON.stringify(t),i,n):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","track:properties:time:appId:",e,JSON.stringify(t),i,n)}},{key:"trackUpdateForNative",value:function(e,t){e.properties=_.extend(_.isUndefined(e.properties)?{}:e.properties,this.dynamicProperties?this.dynamicProperties():{}),e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","trackUpdate","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","trackUpdate:appId:",JSON.stringify(e),t)}},{key:"trackFirstEventForNative",value:function(e,t){e.properties=_.extend(_.isUndefined(e.properties)?{}:e.properties,this.dynamicProperties?this.dynamicProperties():{}),e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","trackFirstEvent","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","trackFirstEvent:appId:",JSON.stringify(e),t)}},{key:"trackOverwriteForNative",value:function(e,t){e.properties=_.extend(_.isUndefined(e.properties)?{}:e.properties,this.dynamicProperties?this.dynamicProperties():{}),e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","trackOverwrite","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","trackOverwrite:appId:",JSON.stringify(e),t)}},{key:"timeEventForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","timeEvent","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","timeEvent:appId:",e,t)}},{key:"loginForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","login","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","login:appId:",e,t)}},{key:"logoutForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","logout","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","logout:",e)}},{key:"setSuperPropertiesForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","setSuperProperties","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setSuperProperties:appId:",JSON.stringify(e),t)}},{key:"getSuperPropertiesForNative",value:function(e){var t="{}";return this._isAndroid()?t=jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","getSuperProperties","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()&&(t=jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getSuperProperties:",e)),JSON.parse(t)}},{key:"unsetSuperPropertyForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","unsetSuperProperty","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","unsetSuperProperty:appId:",e,t)}},{key:"clearSuperPropertiesForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","clearSuperProperties","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","clearSuperProperties:",e)}},{key:"userSetForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","userSet","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userSet:appId:",JSON.stringify(e),t)}},{key:"userSetOnceForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","userSetOnce","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userSetOnce:appId:",JSON.stringify(e),t)}},{key:"userAppendForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","userAppend","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userAppend:appId:",JSON.stringify(e),t)}},{key:"userUniqAppendForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","userUniqAppend","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userUniqAppend:appId:",JSON.stringify(e),t)}},{key:"userAddForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","userAdd","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userAdd:appId:",JSON.stringify(e),t)}},{key:"userUnsetForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","userUnset","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userUnset:appId:",e,t)}},{key:"userDelForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","userDel","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userDel:",e)}},{key:"flushForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","flush","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","flush:",e)}},{key:"authorizeOpenIDForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","authorizeOpenID","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","authorizeOpenID:appId:",e,t)}},{key:"identifyForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","identify","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","identify:appId:",e,t)}},{key:"initInstanceForNative",value:function(e,t,i){this._isAndroid()?(jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","setCustomerLibInfo","(Ljava/lang/String;Ljava/lang/String;)V",Config.LIB_NAME,Config.LIB_VERSION),_.isUndefined(t)?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","initInstanceAppId","(Ljava/lang/String;Ljava/lang/String;)V",e,i):jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","initInstanceConfig","(Ljava/lang/String;Ljava/lang/String;)V",e,JSON.stringify(t))):this._isIOS()&&(jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setCustomerLibInfoWithLibName:libVersion:",Config.LIB_NAME,Config.LIB_VERSION),_.isUndefined(t)?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","initInstance:appId:",e,i):jsb.reflection.callStaticMethod("CocosCreatorProxyApi","initInstance:config:",e,JSON.stringify(t)))}},{key:"lightInstanceForNative",value:function(e,t){return this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","lightInstance","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","lightInstance:appId:",e,t):void 0}},{key:"startThinkingAnalyticsForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","startThinkingAnalytics","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","startThinkingAnalytics:",e)}},{key:"setDynamicSuperPropertiesForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","setDynamicSuperProperties","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setDynamicSuperProperties:appId:",e,t)}},{key:"getDeviceIdForNative",value:function(e){return this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","getDeviceId","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getDeviceId:",e):void 0}},{key:"getDistinctIdForNative",value:function(e){return this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","getDistinctId","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getDistinctId:",e):void 0}},{key:"getAccountIdForNative",value:function(e){return this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","getAccountId","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getAccountId:",e):void 0}},{key:"getPresetPropertiesForNative",value:function(e){var t="{}";return this._isAndroid()?t=jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","getPresetProperties","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()&&(t=jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getPresetProperties:",e)),JSON.parse(t)}},{key:"enableTrackingForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","enableTracking","(Ljava/lang/String;Ljava/lang/String;)V",e.toString(),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","enableTracking:appId:",e.toString(),t)}},{key:"optOutTrackingForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","optOutTracking","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","optOutTracking:",e)}},{key:"optOutTrackingAndDeleteUserForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","optOutTrackingAndDeleteUser","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","optOutTrackingAndDeleteUser:",e)}},{key:"optInTrackingForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","optInTracking","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","optInTracking:",e)}},{key:"setTrackStatusForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("org/cocos2dx/javascript/CocosCreatorProxyApi","setTrackStatus","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setTrackStatus:appId:",e,t)}}]),i}();window.ThinkingAnalyticsAPI=ThinkingDataAPIForNative,window.ThinkingAnalyticsAPIForJS=ThinkingDataAPI,module.exports=ThinkingDataAPIForNative;