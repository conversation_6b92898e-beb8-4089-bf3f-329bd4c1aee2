import { util } from "../core/utils/Utils";
import { appleHelper } from "../script/common/apple/AppleHelper";
import { agreementConfig } from "../script/common/CommunityConfig";
import { Channel, LangCfgName } from "../script/common/constant/Enums";
import EventType from "../script/common/event/EventType";
import { gameHelper } from "../script/common/helper/GameHelper";
import { viewHelper } from "../script/common/helper/ViewHelper";
import { localConfig } from "../script/common/LocalConfig";
import { wxHelper } from "../script/common/wx/WxHelper";
import { wxAppHelper } from "../script/common/wx_app/WxAppHelper";
import App from "./App";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LoginCmpt extends cc.Component {

    @property(cc.Node)
    checkBoxSelect: cc.Node = null

    @property(cc.Node)
    private wechatBtnNode: cc.Node = null
    @property(cc.Node)
    private wxAppBtnNode: cc.Node = null
    @property(cc.Node)
    private appleBtnNode: cc.Node = null
    // @property(cc.Node)
    // private twitterBtnNode: cc.Node = null
    @property(cc.Node)
    private facebookBtnNode: cc.Node = null
    @property(cc.Node)
    private googleBtnNode: cc.Node = null
    @property(cc.Node)
    private guestBtnNode: cc.Node = null
    @property(cc.Node)
    private taptapBtnNode: cc.Node = null
    @property(cc.Node)
    private agreement: cc.Node = null


    private isAgree: boolean = false

    private app: App = null

    onLoad() {
        this.isAgree = gameHelper.isAgreement()
        this.checkBoxSelect.active = this.isAgree
        this.agreement.active = gameHelper.isInland() && ut.isMobile()
        this.updateBtns()
    }

    // 登录按钮显示判断
    private updateBtns() {
        const isInland = gameHelper.isInland(), isGLobal = gameHelper.isGLobal(), isIos = ut.isIos(), isMobile = ut.isMobile()

        this.guestBtnNode.active = CC_DEV

        this.wechatBtnNode.active = ut.isMiniGame()

        // ios是否安装微信：是：显示微信按钮  否：隐藏微信按钮
        this.wxAppBtnNode.active = isMobile && isInland && (!isIos || wxAppHelper.isInstall()) && !localConfig.channel

        // 苹果登录
        this.appleBtnNode.active = isMobile && isIos && appleHelper.isShowAppleButton() && !localConfig.channel

        // this.twitterBtnNode.active = isGLobal
        this.facebookBtnNode.active = isMobile && isGLobal && !localConfig.channel
        this.googleBtnNode.active = isMobile && isGLobal && ut.isAndroid() && !localConfig.channel

        this.taptapBtnNode.active = localConfig.channel == Channel.TAPTAP

        this.initWxAuthorizeBtn()
    }

    private async initWxAuthorizeBtn() {
        if (this.wechatBtnNode.active) {
            if (!await wxHelper.checkAuthorize()) {
                wxHelper.createUserInfoButton(this.wechatBtnNode).onTap(this.wxAuthorize.bind(this))
            }
        }
    }

    public init(app) {
        this.app = app
    }

    public clean() {
        this.closePnl('common/Agreement')
        this.closePnl('common/Certification')
        this.node.destroy()
    }

    private closePnl(key: string) {
        eventCenter.emit(mc.Event.CLOSE_PNL, key)
    }

    // 账号登录
    public async onClickAccountLogin() {
        let succ = await this.check()
        if (!succ) return

        viewHelper.showPnl("login/AccountLoginPnl")

        await eventCenter.wait(EventType.LOGIN_COMPLETE)
        this.loginEnd()
    }

    // 微信登录
    @util.addLock
    public async onClickWxLogin() {
        let succ = await this.check()
        if (!succ) return

        let data = await gameHelper.user.wxLogin()
        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return
        }
        this.loginEnd()
    }

    // 微信App登录
    @util.addLock
    public async onClickWxAppLogin() {
        let succ = await this.check()
        if (!succ) return

        let data = await gameHelper.user.wxAppLogin()
        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return
        }
        this.loginEnd()
    }

    // 游客登录
    @util.addLock
    public async onClickGuestLogin() {
        let inviteCode = ""

        let succ = await this.check()
        if (!succ) return

        let data = await gameHelper.user.guestLogin(inviteCode)
        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return
        }
        this.loginEnd()
    }

    // apple登录
    public async onClickAppleLogin() {
        let succ = await this.check()
        if (!succ) return
        let data = await gameHelper.user.appleLogin()
        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return
        }
        this.loginEnd()
    }

    // facebook登录
    public async onClickFacebookLogin() {
        let succ = await this.check()
        if (!succ) return
        let data = await gameHelper.user.facebookLogin()
        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return
        }
        this.loginEnd()
    }

    /**google登录 */
    public async onClickGoogleLogin() {
        let succ = await this.check()
        if (!succ) return
        let data = await gameHelper.user.googleLogin()
        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return
        }
        this.loginEnd()
    }

    private loginEnd() {
        this.app.nativeLogin(true)
        this.clean()
    }

    public async onClickTaptapLogin() {
        let succ = await this.check()
        if (!succ) return
        let data = await gameHelper.user.taptapLogin()
        if (data.code != 0) {
            viewHelper.showNetError(data.code)  
            return
        }
        this.loginEnd()
    }

    // 协议勾选
    public async onClickToggle() {
        this.setAgreement(!this.isAgree)
    }

    private setAgreement(agree: boolean) {
        this.isAgree = agree
        this.checkBoxSelect.active = this.isAgree
        storageMgr.setOrgItem("__@agreement", this.isAgree ? '1' : '0')

        if (agree) {
            this.app.initSDK()
        }
    }

    // 点击用户协议
    public onClickAgreement1() {
        cc.sys.openURL(agreementConfig.service)
    }

    // 点击隐私政策
    public onClickAgreement2() {
        cc.sys.openURL(agreementConfig.privacy)
    }

    // 登录前检测各种弹窗
    private async check() {
        // 显示用户协议和隐私
        if (this.agreement.active && !this.isAgree) {
            let succ = await new Promise((r) => {
                viewHelper.showPnl("common/Agreement", (agree) => {
                    if (agree) {
                        this.setAgreement(agree)
                    }
                    r(agree)
                })
            })
            if (!succ) return false
        }
        return true
    }

    //微信小程序授权
    private async wxAuthorize({ encryptedData, iv }) {
        let success = await wxHelper.checkAuthorize()

        if (success) {
            wxHelper.destroyButton(this.wechatBtnNode.uuid)
            this.onClickWxLogin()
        } else {
            viewHelper.showAlert("login_tips_13", { lang: LangCfgName.LOAD })
        }
    }
}
