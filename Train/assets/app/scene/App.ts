import { db<PERSON><PERSON><PERSON> } from "../script/common/helper/DatabaseHelper";
import { localConfig } from "../script/common/LocalConfig";
import { resHelper } from "../script/common/helper/ResHelper";
import { viewHelper } from "../script/common/helper/ViewHelper";
import { cfgHelper } from "../script/common/helper/CfgHelper";
import LoadingCmpt from "../../startScene/LoadingCmpt";
import { gameHelper } from '../script/common/helper/GameHelper';
import LoginCmpt from "./LoginCmpt";
import { startHelper } from "../../startScene/StartHelper";
import { taHelper } from "../script/common/helper/TaHelper";
import { wxHelper } from "../script/common/wx/WxHelper";
import version from "../../startScene/version";
import { GuideStepMark, LangCfgName, Platform } from "../script/common/constant/Enums";
import NetEvent from "../script/common/event/NetEvent";
import { unlockHelper } from "../script/common/helper/UnlockHelper";
import CoreEventType from "../core/event/CoreEventType";
import { NET_ERROR_CODE } from "../script/common/constant/Constant";
import { jsbHelper } from "../script/common/helper/JsbHelper";

const { ccclass, property } = cc._decorator;

// 游戏入口
@ccclass
export default class App extends cc.Component {

    private loadingCmpt: LoadingCmpt = null;

    private initedModels: boolean = false;

    private isAccountLogin: boolean = false //当前是不是走账号登录
    private logined: boolean = false //账号登录过了，不走token登录

    private loginStep: number = 0

    private isInitSDK: boolean = false

    onLoad() {
        mc.currScene = "loading"

        cc.macro.ENABLE_MULTI_TOUCH = true
        cc.debug.setDisplayStats(localConfig.showFps)
        ut.setKeepScreenOn(true)  //阻止挂机
        twlog.open = localConfig.openLog

        this.loadingCmpt = cc.find("LoadGame").getComponent(LoadingCmpt)
        this.loadingCmpt.onAppScene(cc.find("Canvas/load"))
    }

    async start() {
        mc.init(version.GAME_NAME_SPACE, this.node, startHelper.getLocalLang())

        await storageMgr.init()

        if (ut.isMobile()) {
            await this.checkUpdate()
        }

        if (gameHelper.isAgreement()) {
            this.initSDK()
        }

        eventCenter.on(NetEvent.NET_DISCONNECT, this.onDisconnect, this)
        await this.connectSever()

        // 开始加载
        this.appLoad()
    }

    public initSDK() {
        if (this.isInitSDK) return
        this.isInitSDK = true
        taHelper.init()
        wxHelper.init()
        jsbHelper.call("INIT_SDK")
    }

    //--------------- 更新 ---------------------------
    // 检测更新
    private async checkUpdate() {
        this.loadingCmpt.setDescKey("update_0")
        this.loadingCmpt.setPercent(0)
        let userModel = gameHelper.user;
        let version = gameHelper.getVersion();
        let platform = gameHelper.getPlatform();

        let count = 5
        let status = ""
        while (count--) {
            let { code, url, packageUrl, downloadUrl, newVer } = await gameHelper.net.get("login/checkVersion", { uid: userModel.getUid(), platform, version }) || {};
            if (code === 0) {
                eventCenter.on(CoreEventType.HOT_UPDATE_EVENT, this.onHotUpdateEvent, this)
                hotUpdateMgr.start(packageUrl, url, newVer)
                return eventCenter.wait(CoreEventType.HOT_UPDATE_SKIP)
            } else if (code === 1) {
                this.onBigVersionUpdate(downloadUrl)
                return new Promise(r => { })
            }
            else if (code == 2 || code == 3) {
                return
            }
            else {
                await ut.wait(2, this)
                status = code
            }
        }
        await new Promise(ok => {
            let msg = startHelper.lang("login_tips_8", `checkVersion:${status}`)
            viewHelper.showMessageBox(msg, ok, null, { lang: LangCfgName.LOAD, lockClose: true, ok: "login_buttonName_11" })
        })
        return this.checkUpdate()
    }

    private async onBigVersionUpdate(downloadUrl?: string) {
        let ok = () => {
            let url = downloadUrl || gameHelper.getAppShopUrl()
            cc.sys.openURL(url);
            this.onBigVersionUpdate(downloadUrl) //因为点了ok之后会自动销毁，但又不想让弹窗销毁，所以重新走一遍
        }
        let msg = "update_must_appstore"
        viewHelper.showMessageBox(msg, ok, null, { lang: LangCfgName.LOAD, lockClose: true, ok: "button_go" })
    }

    // 热更新回调
    private onHotUpdateEvent(event: any, failed?: boolean) {
        if (failed) {
            this.onHotUpdateRetry()
            return
        }
        switch (event.getEventCode()) {
            case jsb.EventAssetsManager.NEW_VERSION_FOUND:// 发现新版本
                this.loadingCmpt.setDescKey("update_1")
                this.loadingCmpt.setPercent(0)
                break
            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:// 已经是最新的版本
                eventCenter.emit(CoreEventType.HOT_UPDATE_SKIP)
                break
            case jsb.EventAssetsManager.UPDATE_PROGRESSION:// 更新进度
                if (event.getPercent()) {
                    let percent = Math.floor(event.getPercent() * 100)
                    this.loadingCmpt.setPercent(percent)
                }
                break
            case jsb.EventAssetsManager.UPDATE_FINISHED:// 更新完成
                ut.wait(0.2, this).then(() => gameHelper.gameRestart())
                break
        }
    }

    private async onHotUpdateRetry() {
        let msg = "update_2"
        let ok = () => {
            hotUpdateMgr.redownload()
        }
        viewHelper.showMessageBox(msg, ok, null, { lang: LangCfgName.LOAD, lockClose: true, ok: "login_buttonName_11" })
    }
    //----------------------------------------------------------------------

    private async connectSever(isReconnect?) {
        twlog.info("连接服务器...")
        let retryCnt = 10
        while (true) {
            const connected = await gameHelper.net.connect(null, isReconnect)
            if (!connected) {
                if (retryCnt > 0) {
                    await ut.wait(2, this)
                    retryCnt--
                }
                else {
                    retryCnt = 5
                    await this.showNetFail("connect", -1)
                }
            }
            else {
                break
            }
        }

        while (true) {
            let data = await gameHelper.user.setSessionVersion()
            if (data.code != 0) {
                if (data.code == NET_ERROR_CODE.DIS_CONNECT) {
                    await new Promise(r => { }) //打断后续流程
                }
                await this.showNetFail("setSessionVersion", data.code)
                continue
            }
            break
        }
    }

    private async onDisconnect() {
        this.loginStep = -1
        await this.connectSever(true)
        this.loginStep = 0
        this.logined = false

        if (this.isAccountLogin) { //当前状态是走三方登录，重发三方登录请求
            gameHelper.net.reSend()
        }
        else {
            gameHelper.net.failAll() //如果不是，走serverLogin流程
        }
    }

    private appLoad() {
        this.loadingCmpt.setDescKey("loading")
        if (!gameHelper.user.getToken() || storageMgr.getOrgItem("__@accountswitch")) {
            this.isAccountLogin = true
            this.initNativeLogin()
        }
        else {
            this.load()
        }
    }

    //------------登录相关 ---------------------
    private async serverLogin() {
        while (true) {
            while (this.loginStep == 0) {
                let data
                if (!this.logined) { //登录界面走过了
                    data = await gameHelper.user.loginByToken()
                    if (data.code != 0) {
                        if (data.code != NET_ERROR_CODE.DIS_CONNECT) {
                            await this.showNetFail("login", data.code)
                        }
                        if (data.code >= 2 && data.code <= 5) {
                            gameHelper.user.updateToken("", true)
                            storageMgr.clear()
                            this.appLoad()
                            return new Promise(r => { }) // 打断后续流程
                        }
                        continue
                    }
                }
                this.loginStep = 1
            }

            while (this.loginStep == 1) {
                this.loginStep = 2
            }

            while (this.loginStep == 2) {
                let data = await gameHelper.user.updateRecord()
                if (data.code != 0) {
                    if (data.code != NET_ERROR_CODE.DIS_CONNECT) {
                        await this.showNetFail("updateRecord", data.code)
                    }
                    continue
                }
                this.loginStep = 3
            }

            if (this.loginStep == 3) break

            if (this.loginStep == -1) { //等重连
                await ut.wait(1, this)
            }
        }
        gameHelper.isLogin = true
        eventCenter.off(NetEvent.NET_DISCONNECT, this.onDisconnect, this)
    }

    //App登录界面
    private async initNativeLogin() {
        localStorage.removeItem("__@accountswitch");
        this.loadingCmpt.hideLoadBar()
        cc.resources.load('tmp/prefab/native/LOGIN', cc.Prefab, (err, login: cc.Prefab) => {
            let loginCmpt = cc.instantiate2(login, this.loadingCmpt.node).Component(LoginCmpt)
            loginCmpt.init(this)
        })
    }

    //App登录成功后回调
    public nativeLogin(logined?: boolean) {
        if (logined) {
            this.logined = true
        }
        this.isAccountLogin = false
        this.loadingCmpt.showLoadBar()
        this.loadingCmpt.reset()
        this.load()
    }

    //-----------------------------------------

    public showNetFail(name: string, status: number): Promise<void> {
        return new Promise(async (resolve) => {
            let msg = startHelper.lang("login_tips_8", `${name}:${status}`)
            viewHelper.showLoadMessageBox(msg, () => {
                resolve()
            }, null, { lockClose: true, ok: "login_buttonName_11" })
        })
    }

    private async load(data?) {

        let startTime = Date.now()

        let login = this.serverLogin()

        await this.loadStep((progress) => {
            return assetsMgr.init(progress)
        }, "assetsMgrInit", 0.6)

        await login

        cfgHelper.init()
        this.initModels()
        resHelper.init()
        unlockHelper.init()

        await gameHelper.user.checkCertification()

        await Promise.all([
            (async () => {
                await this.loadStep((progress) => {
                    return this.loadAllNotice(progress)
                }, "loadAllNotice", 0.1)

                await this.loadStep((progress) => {
                    return viewHelper.preloadWind(this.getWindName(), (done: number, total: number) => {
                        progress(done / total)
                    })
                }, "preloadWind", 0.3)
            })(),
        ])

        console.log("Load cost: ", Date.now() - startTime)
        await gameHelper.user.checkMd5()

        this.go()
    }

    private async loadStep(func: Function, key: string, p: number = 0) {
        let startTime = Date.now()
        let progressFunc = (percent) => {
            this.loadingCmpt.addPercentByProgress(key, p, percent)
        }
        await func(progressFunc)
        let endTime = Date.now()
        console.log(`loadStep ${key}: cost ${endTime - startTime}ms`);
    }

    private async loadAllNotice(progessCallback: (percent: number) => void) {
        return new Promise<void>(resolve => eventCenter.emit(mc.Event.LOAD_ALL_NOTICE, resolve, (done: number, total: number) => progessCallback(done / total)))
    }

    private initModels() {
        if (this.initedModels) return
        mc.modelMgr.initModels()
        this.initedModels = true
    }

    private async go() {
        this.loadingCmpt.setPercent(1)
        await ut.waitNextFrame(0)
        this.loadingCmpt.clean()
        this.loadingCmpt = null
        // 显示第一个wind
        eventCenter.emit(mc.Event.GOTO_WIND, this.getWindName())
    }

    private getWindName() {
        if ((!gameHelper.guide.isStepEnd(GuideStepMark.REPAIR_TRAIN) || !gameHelper.guide.isStepEnd(GuideStepMark.ENTER_TRAIN)))
            return 'repair'
        return 'main'
    }

    update(dt: number) {
        // profiler.start("updateModels")
        this.updateModels(dt)
        // profiler.end("updateModels")

        dbHelper.update(dt)
    }

    updateModels(dt) {
        if (!this.initedModels) return
        if (!gameHelper.isEnterSecne) return
        let models = mc.modelMgr.getModels()
        // console.log("updateModels", dt)
        models.forEach((model, name) => {
            model.update(dt)
        })
    }
}
