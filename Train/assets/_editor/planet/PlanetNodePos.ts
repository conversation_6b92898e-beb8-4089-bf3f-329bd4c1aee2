// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const {ccclass, property, menu, executeInEditMode} = cc._decorator;

@ccclass
@executeInEditMode
export default class PlanetNodePos extends cc.Component {
    @property({visible: false, serializable: true})
    cname: String = "PlanetNodePos";

    @property({readonly: true, serializable: true})
    pos: cc.Vec2 = null

    private transCmpt = null

    init(transCmpt) {
        this.transCmpt = transCmpt
        this.updateTransPos()
    }

    protected onEnable() {
        this.node.on(cc.Node.EventType.POSITION_CHANGED, ()=>{
            this.updateTransPos()
        })
    }

    protected onDisable(): void {
        this.node.off(cc.Node.EventType.POSITION_CHANGED)
    }

    public updateTransPos() {
        if (!this.transCmpt) return
        this.pos = this.transCmpt.transPos(this.getPosition())
    }
}
