const {ccclass, property, menu, executeInEditMode} = cc._decorator;

@ccclass
@executeInEditMode
export default class EternalGardenPosTrans extends cc.Component {
    @property(cc.Node)
    bgNode: cc.Node = null

    @property(cc.Node)
    map: cc.Node = null

    @property(cc.Node)
    debugNode: cc.Node = null

    @property(cc.Node)
    debugNode2: cc.Node = null

    private center: cc.Node

    protected onEnable(): void {
        this.center = this.bgNode.Child("road_trans/center")
        if (!this.map) return
        this.init()
        // this.map.on(cc.Node.EventType.CHILD_ADDED, ()=>{
        //     this.init()
        // })
        // this.map.on(cc.Node.EventType.CHILD_REMOVED, ()=>{
        //     this.init()
        // })
    }

    private init() {
        this.showDebug()
    }

    private showDebug() {
        if (!this.debugNode || !this.debugNode.active) return
        this.debugNode.removeAllChildren()
        for (let block of this.map.children) {
            let block2 = cc.instantiate(block)
            block2.parent = this.debugNode
            let children = [block]
            let children2 = [block2]
            if (!isNaN(Number(block.name))) {
                children = block.children
                children2 = block2.children
            }
            for (let i = 0; i < children.length; i++) {
                let child = children[i]
                let child2 = children2[i]
                child2.removeComponent(cc.Widget)
                let update = ()=>{
                    let {pos, angle} = this.trans2View(child.getPosition())
                    child2.setPosition(pos)
                    child2.angle = angle
                }
                update()
                child.on(cc.Node.EventType.POSITION_CHANGED, ()=>{
                    update()
                })
            }
        }
    }

    transPos(pos) {
        let curX = pos.x
        let startAngle = -90
        let centerNode = this.center
        let center = ut.convertToNodeAR(centerNode, this.map)
        let radis = center.y
        let halfLen = (radis * Math.PI)
        
        if (curX <= center.x ) {
            if (pos.y < center.y) { //down
                return pos
            }
            else { //up
                let x = center.x + halfLen + (center.x - curX)
                let y = center.y + radis - pos.y
                return cc.v2(x, y)
            }
        }
        else {
            let vec = pos.sub(center)
            let dis = vec.mag()
            let offsetY = radis - dis
            let angle = cc.misc.radiansToDegrees(Math.atan2(vec.y, vec.x)) - startAngle
            let offsetX = halfLen * angle / 180
            return cc.v2(center.x + offsetX, offsetY)
        }
    }

    private trans2View(pos: cc.Vec2, out: {pos?: cc.Vec2, angle?: number} = {}) {
        let curX = pos.x
        let centerNode = this.center
        let center = ut.convertToNodeAR(centerNode, this.map)
        let radis = center.y
        let curveLen = (radis * Math.PI)
        let transNode = this.bgNode.Child('road_trans')

        let angle = 0
        let curveStartX = center.x
        let curveEndX = curveStartX + curveLen
        if (curX <= curveStartX) { //down
        }
        else if (curX <= curveEndX) { //curve
            let ratio = (curX - curveStartX) / curveLen
            let angle2 = -90 + 180 * ratio
            let dis = pos.y
            let x = center.x + (radis - dis) * ut.cos(angle2)
            let y = center.y + (radis - dis) * ut.sin(angle2)
            pos = cc.v2(x, y)
            angle = angle2 + 90
        }
        else {  //up
            let x = (2 * center.x + curveLen ) - pos.x
            let y = center.y + radis - pos.y
            angle = 180
            pos = cc.v2(x, y)
        }
        // out.pos = ut.convertToNodeAR(this.map, transNode, pos)
        out.pos = pos
        out.angle = angle
        return out
    }

}
