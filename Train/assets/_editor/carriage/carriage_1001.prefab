[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "carriage_1001", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 42}], "_active": true, "_components": [], "_prefab": {"__id__": 45}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2712, "height": 1153}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "shell", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}, {"__id__": 38}], "_active": true, "_components": [], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2628, "height": 1206}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "sp_connect", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": false, "_components": [{"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 303.82, "height": 632}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1158.892, -351, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "jingzhi", "_preCacheMode": 1, "_cacheMode": 1, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "jingzhi", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "69d0fe62-b7c3-4996-b5ab-0732b2607fa9"}, "_N$_defaultCacheMode": 1, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "0e606799-59b4-48d4-8952-b201f4ec6bd6"}, "fileId": "c2mutXypNHQokTrs0+oxDW", "sync": false}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 7}], "_active": true, "_components": [], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2149, "height": 810.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "themes", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 8}, {"__id__": 10}], "_active": true, "_components": [], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 9}, "_name": "theme_1001_1", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "94c6567e-b611-4cef-91c0-878c0fbe95b5"}, "fileId": "e596jCBqNOvLBVmjkjjDVw", "sync": true}, {"__type__": "cc.Node", "_name": "theme_1001_2", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 11}, {"__id__": 32}], "_active": false, "_components": [], "_prefab": {"__id__": 35}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "root", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [{"__id__": 12}, {"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}], "_active": true, "_components": [{"__id__": 30}], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 13}, "_name": "trainItem_1001_2_1", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 125.734, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 12}, "asset": {"__uuid__": "84d8e07e-47d5-4f7c-9bdb-5688e60bd54f"}, "fileId": "92pEtJTHJILbPOr8aYHboZ", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 15}, "_name": "trainItem_1001_2_2", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -314.886, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 14}, "asset": {"__uuid__": "8de27e6d-91c2-4a37-b6bc-a456efd7baec"}, "fileId": "6ckNIF09FCRaGhp0OVS/gB", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 17}, "_name": "trainItem_1001_2_9", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-817.593, -225.035, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "cbb8d553-7838-42d2-ba27-538df3cdcd12"}, "fileId": "5cLAoN57VN+5g3lGxFUYQp", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 19}, "_name": "trainItem_1001_2_4", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-350.153, -251.351, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 18}, "asset": {"__uuid__": "9d6072a5-adb2-4cca-b7a7-08566141614d"}, "fileId": "e5yqEHof1N5IQGNRdGAbEC", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 21}, "_name": "trainItem_1001_2_8", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-779.105, -251.621, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 20}, "asset": {"__uuid__": "a0742ec4-be58-460a-8283-85a1b96a8780"}, "fileId": "cdHZeoUmxJNbDb8cpiXQyo", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 23}, "_name": "trainItem_1001_2_7", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [815.267, -225.035, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 22}, "asset": {"__uuid__": "496572ff-dad9-41dc-b94c-9f5016ef4993"}, "fileId": "48n0UcBTVE4p/XQNmMT1NI", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 25}, "_name": "trainItem_1001_2_5", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [776.919, -251.621, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 24}, "asset": {"__uuid__": "7cdcb7c1-577c-4d23-8931-a09d5efb2826"}, "fileId": "94oNJj89ZK+aQ5C4YIVV1B", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 27}, "_name": "trainItem_1001_2_6", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [349.993, -251.351, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 26}, "asset": {"__uuid__": "abd17522-62ca-4956-9a13-9706e831accb"}, "fileId": "89MBNeu/1L/7mVcUH84Keh", "sync": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 29}, "_name": "trainItem_1001_2_3", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-2.6, -327.432, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 28}, "asset": {"__uuid__": "24883b3e-8ac0-4997-9974-00f6a47dab03"}, "fileId": "f1sRGuvG5AOatDEKP31zKr", "sync": true}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 10}, "asset": {"__uuid__": "9ef404e4-683c-40f8-bbc3-17e860917b59"}, "fileId": "6f7ISgyqJGjIHy5VxpuJTF", "sync": false}, {"__type__": "cc.Node", "_name": "主控室_蒸汽小恐龙", "_objFlags": 0, "_parent": {"__id__": 10}, "_children": [], "_active": false, "_components": [{"__id__": 33}], "_prefab": {"__id__": 34}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2281, "height": 918}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "df1a8cfc-0cd2-4722-a137-f057def6702c"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 10}, "asset": {"__uuid__": "9ef404e4-683c-40f8-bbc3-17e860917b59"}, "fileId": "a5VpsRpQ5DEam5A7CPt53t", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 10}, "asset": {"__uuid__": "9ef404e4-683c-40f8-bbc3-17e860917b59"}, "fileId": "07+W10mGBHubWIBed5lLlA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "0e606799-59b4-48d4-8952-b201f4ec6bd6"}, "fileId": "9apfJirRVAbJRzHhjbGjGs", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "0e606799-59b4-48d4-8952-b201f4ec6bd6"}, "fileId": "cfOKD01WJMsJiqVItFkRUL", "sync": false}, {"__type__": "cc.Node", "_name": "sp_carriage", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 39}], "_prefab": {"__id__": 40}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2456, "height": 1206}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "jingzhi", "_preCacheMode": 1, "_cacheMode": 1, "loop": false, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "jingzhi", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "fda0bc63-b6ab-461e-8bab-ae0582904738"}, "_N$_defaultCacheMode": 1, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "0e606799-59b4-48d4-8952-b201f4ec6bd6"}, "fileId": "a6Is4RKMpP2IQRlsvfhGkF", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "0e606799-59b4-48d4-8952-b201f4ec6bd6"}, "fileId": "2c8KKwbVxGSpNb0gSLScmQ", "sync": false}, {"__type__": "cc.Node", "_name": "1一号寝室01", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 120, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2688, "height": 1242}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4ae714cc-2906-4c58-bf46-e82101f00aa1"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5b48KYcNRPgbJcOpgWj+J2", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]