[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "theme_1020_2", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 142}], "_active": true, "_components": [], "_prefab": {"__id__": 147}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 20}, {"__id__": 26}, {"__id__": 32}, {"__id__": 38}, {"__id__": 47}, {"__id__": 60}, {"__id__": 69}, {"__id__": 86}, {"__id__": 103}, {"__id__": 118}, {"__id__": 131}], "_active": true, "_components": [{"__id__": 140}], "_prefab": {"__id__": 141}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "地板", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_active": true, "_components": [], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15, -255, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 3}, "_prefab": {"__id__": 5}, "_name": "trainItem_1020_2_7", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "f4a1c857-1f23-437e-802c-f26a89ff3d87"}, "fileId": "d3O/s6DL9FVp4laYnx52b7", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [15, 800, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58REO8glVPToyE5RqyWwur", "sync": false}, {"__type__": "cc.Node", "_name": "colliders", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [{"__id__": 9}, {"__id__": 12}, {"__id__": 15}], "_active": true, "_components": [], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1060, 95, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": -20}, {"__type__": "cc.Vec2", "x": 120, "y": -20}, {"__type__": "cc.Vec2", "x": 120, "y": -40}, {"__type__": "cc.Vec2", "x": 140, "y": -40}, {"__type__": "cc.Vec2", "x": 140, "y": -60}, {"__type__": "cc.Vec2", "x": 160, "y": -60}, {"__type__": "cc.Vec2", "x": 160, "y": -100}, {"__type__": "cc.Vec2", "x": 180, "y": -100}, {"__type__": "cc.Vec2", "x": 180, "y": -120}, {"__type__": "cc.Vec2", "x": 220, "y": -120}, {"__type__": "cc.Vec2", "x": 220, "y": -140}, {"__type__": "cc.Vec2", "x": 460, "y": -140}, {"__type__": "cc.Vec2", "x": 460, "y": -120}, {"__type__": "cc.Vec2", "x": 400, "y": -120}, {"__type__": "cc.Vec2", "x": 400, "y": -100}, {"__type__": "cc.Vec2", "x": 340, "y": -100}, {"__type__": "cc.Vec2", "x": 340, "y": -80}, {"__type__": "cc.Vec2", "x": 300, "y": -80}, {"__type__": "cc.Vec2", "x": 300, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": 0}, {"__type__": "cc.Vec2", "x": 160, "y": 0}, {"__type__": "cc.Vec2", "x": 160, "y": 20}, {"__type__": "cc.Vec2", "x": 0, "y": 20}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33RvsRZClBJIbozVFwbV7C", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-660, -65, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -140, "y": 20}, {"__type__": "cc.Vec2", "x": -140, "y": 0}, {"__type__": "cc.Vec2", "x": -100, "y": 0}, {"__type__": "cc.Vec2", "x": -100, "y": -20}, {"__type__": "cc.Vec2", "x": 0, "y": -20}, {"__type__": "cc.Vec2", "x": 0, "y": -40}, {"__type__": "cc.Vec2", "x": 120, "y": -40}, {"__type__": "cc.Vec2", "x": 120, "y": -60}, {"__type__": "cc.Vec2", "x": 1460, "y": -60}, {"__type__": "cc.Vec2", "x": 1460, "y": -40}, {"__type__": "cc.Vec2", "x": 1520, "y": -40}, {"__type__": "cc.Vec2", "x": 1520, "y": -20}, {"__type__": "cc.Vec2", "x": 1560, "y": -20}, {"__type__": "cc.Vec2", "x": 1560, "y": 0}, {"__type__": "cc.Vec2", "x": 1580, "y": 0}, {"__type__": "cc.Vec2", "x": 1580, "y": 40}, {"__type__": "cc.Vec2", "x": 1600, "y": 40}, {"__type__": "cc.Vec2", "x": 1600, "y": 80}, {"__type__": "cc.Vec2", "x": 1420, "y": 80}, {"__type__": "cc.Vec2", "x": 1420, "y": 40}, {"__type__": "cc.Vec2", "x": 1400, "y": 40}, {"__type__": "cc.Vec2", "x": 1400, "y": 20}, {"__type__": "cc.Vec2", "x": 1380, "y": 20}, {"__type__": "cc.Vec2", "x": 1380, "y": 0}, {"__type__": "cc.Vec2", "x": 1340, "y": 0}, {"__type__": "cc.Vec2", "x": 1340, "y": -20}, {"__type__": "cc.Vec2", "x": 1220, "y": -20}, {"__type__": "cc.Vec2", "x": 1220, "y": -40}, {"__type__": "cc.Vec2", "x": 720, "y": -40}, {"__type__": "cc.Vec2", "x": 720, "y": -20}, {"__type__": "cc.Vec2", "x": 520, "y": -20}, {"__type__": "cc.Vec2", "x": 520, "y": 0}, {"__type__": "cc.Vec2", "x": 180, "y": 0}, {"__type__": "cc.Vec2", "x": 180, "y": 20}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2yvvsUy1KG4jYCL8hkT/D", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [660, 95, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 20, "y": 0}, {"__type__": "cc.Vec2", "x": 20, "y": -20}, {"__type__": "cc.Vec2", "x": 60, "y": -20}, {"__type__": "cc.Vec2", "x": 60, "y": -40}, {"__type__": "cc.Vec2", "x": 80, "y": -40}, {"__type__": "cc.Vec2", "x": 80, "y": -80}, {"__type__": "cc.Vec2", "x": 280, "y": -80}, {"__type__": "cc.Vec2", "x": 280, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": 20}, {"__type__": "cc.Vec2", "x": 0, "y": 20}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1aMsGmoM5DdpLXhtnwNewa", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9bm2j/eUtJxppiuiIoWQhK", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8J9W84tREgK4vTkUsznXI", "sync": false}, {"__type__": "cc.Node", "_name": "墙", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 21}, {"__id__": 23}], "_active": true, "_components": [], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 145.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 20}, "_prefab": {"__id__": 22}, "_name": "trainItem_1020_2_5", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 21}, "asset": {"__uuid__": "5c41c042-72c3-446a-bb6e-22d49707655d"}, "fileId": "cfI+UDsI9Nh52OD6rTEB5Q", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 334, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52rbM1cUNA4JR3s7dh+uIc", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b93OvTQjFKCKhAmVOTZ+VR", "sync": false}, {"__type__": "cc.Node", "_name": "窗", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 27}, {"__id__": 29}], "_active": true, "_components": [], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5, 212, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 26}, "_prefab": {"__id__": 28}, "_name": "trainItem_1020_2_6", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "1f8e857c-6af1-4617-8edb-62c583f8a9c2"}, "fileId": "77DucEuoNL/JdBC7++Yea3", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 30}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5, 204, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0e+KM4CwJB7pv+EpPUIBI1", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84Qr+a5t5HE5Dl5UoNFZ/Y", "sync": false}, {"__type__": "cc.Node", "_name": "盆栽-左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 33}, {"__id__": 35}], "_active": true, "_components": [], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-976, 70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 32}, "_prefab": {"__id__": 34}, "_name": "trainItem_1020_2_11", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "aea30663-b207-4d08-b15f-62fc3313d55c"}, "fileId": "c458yBhoxHOp9pDwi44qO6", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3, 213, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80tXBC+QxLFK7IxOsMFoOS", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33OCtrpzVNc7IZsYXe/wqm", "sync": false}, {"__type__": "cc.Node", "_name": "盆栽-右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 39}, {"__id__": 41}, {"__id__": 43}], "_active": true, "_components": [], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [496, -3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 38}, "_prefab": {"__id__": 40}, "_name": "trainItem_1020_2_12", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 39}, "asset": {"__uuid__": "c3a7bfe4-c672-49a8-b1a8-9b308187abdc"}, "fileId": "c27okiujlOP65XdM4kjMEL", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 42}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, 286, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5dkEo6MVC+5L5ESFl4+jz", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [{"__id__": 44}], "_prefab": {"__id__": 45}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -106, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -92, "y": -50}, {"__type__": "cc.Vec2", "x": 86, "y": -50}, {"__type__": "cc.Vec2", "x": 86, "y": -32}, {"__type__": "cc.Vec2", "x": -92, "y": -32}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61c3S8ELlJ0pxWMYvw8WRG", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59lkPTqZZEjpuUWrere4Cp", "sync": false}, {"__type__": "cc.Node", "_name": "青蛙", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 48}, {"__id__": 50}, {"__id__": 52}, {"__id__": 55}], "_active": true, "_components": [], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [817, 116, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 47}, "_prefab": {"__id__": 49}, "_name": "trainItem_1020_2_3", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 48}, "asset": {"__uuid__": "d8e24fa4-1a58-4748-8a6c-9e874cec513a"}, "fileId": "29DlkCQgdPc76G2p6EMM1P", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cBB3dbQJKwoIRVvWAbilz", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [{"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -225, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -92, "y": -50}, {"__type__": "cc.Vec2", "x": 148, "y": -50}, {"__type__": "cc.Vec2", "x": 148, "y": -32}, {"__type__": "cc.Vec2", "x": -92, "y": -32}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6jQqcfD1Og52Ec+vymH1b", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [{"__id__": 56}], "_active": true, "_components": [], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 57}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [110, -290, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22Q4MPHhVHb6jzcMRKA+iU", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94zR2qaX5CuqJH4gwh6kek", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e56Tsg4I5IuofgY4FGzyPc", "sync": false}, {"__type__": "cc.Node", "_name": "茶壶", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 61}, {"__id__": 63}, {"__id__": 65}], "_active": true, "_components": [], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-758, 112, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 60}, "_prefab": {"__id__": 62}, "_name": "trainItem_1020_2_10", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 61}, "asset": {"__uuid__": "31886a05-1ef0-4195-8560-734d821a0c15"}, "fileId": "70tIrqrA9CAaAUXehsEf/R", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, -2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdiw95x/JPDJLRK0i/qxw9", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 66}], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, -219, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -87, "y": -50}, {"__type__": "cc.Vec2", "x": 73, "y": -50}, {"__type__": "cc.Vec2", "x": 73, "y": -32}, {"__type__": "cc.Vec2", "x": -87, "y": -32}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22N+ty+rdJdaqWHZOfYZvl", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "432ORq4xFAaqu5zr64wRUG", "sync": false}, {"__type__": "cc.Node", "_name": "踏板-右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 70}, {"__id__": 72}, {"__id__": 74}], "_active": true, "_components": [], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [819, -221, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 69}, "_prefab": {"__id__": 71}, "_name": "trainItem_1020_2_9", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 70}, "asset": {"__uuid__": "45705766-911c-4768-872a-fdb05044e9f5"}, "fileId": "36wxpbWXpNl7rb19UmlmOM", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 69}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 73}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8, 112, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0f0ZEa0XpL3YfxTDLz7LE4", "sync": false}, {"__type__": "cc.Node", "_name": "colliders", "_objFlags": 0, "_parent": {"__id__": 69}, "_children": [{"__id__": 75}, {"__id__": 78}, {"__id__": 81}], "_active": true, "_components": [], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -160, "y": -10}, {"__type__": "cc.Vec2", "x": 120, "y": -10}, {"__type__": "cc.Vec2", "x": 120, "y": 10}, {"__type__": "cc.Vec2", "x": -160, "y": 10}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8boHk81dMYp2lLfz98/wr", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [26, -49, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -160, "y": -10}, {"__type__": "cc.Vec2", "x": 140, "y": -10}, {"__type__": "cc.Vec2", "x": 140, "y": 30}, {"__type__": "cc.Vec2", "x": -160, "y": 30}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daM6U77jFLkbHO1n8EhrkI", "sync": false}, {"__type__": "cc.Node", "_name": "collider(可走)", "_objFlags": 0, "_parent": {"__id__": 74}, "_children": [], "_active": true, "_components": [{"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8, -9, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 134, "y": -10}, {"__type__": "cc.Vec2", "x": 134, "y": 30}, {"__type__": "cc.Vec2", "x": -146, "y": 30}, {"__type__": "cc.Vec2", "x": -146, "y": -10}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0fWVfAEi1JLbLFqbpLFbj9", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3aAJQ5u0VOabMPdsFfB3PH", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "189QCV2S9Lv69/Xm+6nro9", "sync": false}, {"__type__": "cc.Node", "_name": "踏板-左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 87}, {"__id__": 89}, {"__id__": 91}], "_active": true, "_components": [], "_prefab": {"__id__": 102}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-772.5, -277, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 86}, "_prefab": {"__id__": 88}, "_name": "trainItem_1020_2_8", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 87}, "asset": {"__uuid__": "345b4350-0362-42c7-b34c-e34235893033"}, "fileId": "deRM5YWShOB5XOxaX8p/ZX", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 90}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, 168, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7d9a38VSBNI6UHNF1IouHq", "sync": false}, {"__type__": "cc.Node", "_name": "colliders", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [{"__id__": 92}, {"__id__": 95}, {"__id__": 98}], "_active": true, "_components": [], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 93}], "_prefab": {"__id__": 94}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-133, 31, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -50, "y": -54}, {"__type__": "cc.Vec2", "x": -28, "y": -54}, {"__type__": "cc.Vec2", "x": 211, "y": 26}, {"__type__": "cc.Vec2", "x": 210, "y": 46}, {"__type__": "cc.Vec2", "x": 190, "y": 46}, {"__type__": "cc.Vec2", "x": 190, "y": 66}, {"__type__": "cc.Vec2", "x": 150.6, "y": 65.5}, {"__type__": "cc.Vec2", "x": 150.6, "y": 26.5}, {"__type__": "cc.Vec2", "x": -9, "y": -27}, {"__type__": "cc.Vec2", "x": -9, "y": 6}, {"__type__": "cc.Vec2", "x": -50, "y": 6}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7O5kpa9NLTq8J0YkynXtI", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 96}], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-25.5, -33, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 15, "y": -50}, {"__type__": "cc.Vec2", "x": 15.5, "y": -38}, {"__type__": "cc.Vec2", "x": 176, "y": 21.5}, {"__type__": "cc.Vec2", "x": 223, "y": 21.5}, {"__type__": "cc.Vec2", "x": 223, "y": 50}, {"__type__": "cc.Vec2", "x": 203, "y": 70.5}, {"__type__": "cc.Vec2", "x": 184, "y": 70.5}, {"__type__": "cc.Vec2", "x": -17, "y": -10.5}, {"__type__": "cc.Vec2", "x": -37, "y": -10.5}, {"__type__": "cc.Vec2", "x": -37, "y": -50}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d91hFhfwRCi6c5tq0PReqG", "sync": false}, {"__type__": "cc.Node", "_name": "collider(可走)", "_objFlags": 0, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 99}], "_prefab": {"__id__": 100}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-12.5, -13, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 98}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -30, "y": -30}, {"__type__": "cc.Vec2", "x": -30, "y": -10}, {"__type__": "cc.Vec2", "x": 50, "y": -10}, {"__type__": "cc.Vec2", "x": 50, "y": 10}, {"__type__": "cc.Vec2", "x": 90, "y": 10}, {"__type__": "cc.Vec2", "x": 90, "y": 30}, {"__type__": "cc.Vec2", "x": 130, "y": 30}, {"__type__": "cc.Vec2", "x": 130, "y": 50}, {"__type__": "cc.Vec2", "x": 30, "y": 50}, {"__type__": "cc.Vec2", "x": 30, "y": 30}, {"__type__": "cc.Vec2", "x": -50, "y": 30}, {"__type__": "cc.Vec2", "x": -50, "y": 10}, {"__type__": "cc.Vec2", "x": -110, "y": 10}, {"__type__": "cc.Vec2", "x": -110, "y": -30}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1cPVIyaotDCY5BUBodh0j8", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6jP+2yPJKe5t7uIflOFQM", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43xhTpqzdE+7wcPobeZ7oH", "sync": false}, {"__type__": "cc.Node", "_name": "浴池-左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 111}], "_active": true, "_components": [], "_prefab": {"__id__": 117}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-293, -59, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 103}, "_prefab": {"__id__": 105}, "_name": "trainItem_1020_2_1", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 104}, "asset": {"__uuid__": "ed1bfada-0c62-4fdc-a282-b18a5ae53458"}, "fileId": "df+LWX8s9Juoem4UkyHyTc", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [33, -18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4YbD7GWhA5J2wC6YAi2yx", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [{"__id__": 109}], "_prefab": {"__id__": 110}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-27, -130, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -155, "y": -70}, {"__type__": "cc.Vec2", "x": -95, "y": -70}, {"__type__": "cc.Vec2", "x": -95, "y": -51}, {"__type__": "cc.Vec2", "x": 285, "y": -50}, {"__type__": "cc.Vec2", "x": 285, "y": -31}, {"__type__": "cc.Vec2", "x": 385, "y": -31}, {"__type__": "cc.Vec2", "x": 385, "y": -11}, {"__type__": "cc.Vec2", "x": 465, "y": -11}, {"__type__": "cc.Vec2", "x": 465, "y": 50}, {"__type__": "cc.Vec2", "x": -175, "y": 50}, {"__type__": "cc.Vec2", "x": -175, "y": -50}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edjy+UlTpPqYv/zDVjQWEA", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [{"__id__": 112}, {"__id__": 114}], "_active": true, "_components": [], "_prefab": {"__id__": 116}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 111}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-330, -165, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89NvaVV7JC3pu3lDFdkfqt", "sync": false}, {"__type__": "cc.Node", "_name": "进入点2", "_objFlags": 0, "_parent": {"__id__": 111}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 115}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-300, -110, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "566SAISptFTrUateivmFwA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99xdhX/ZNIkZ/+YNVMJzis", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a2qOXa9XxOlqQVYuOZwXBl", "sync": false}, {"__type__": "cc.Node", "_name": "浴池-右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 119}, {"__id__": 121}, {"__id__": 123}, {"__id__": 126}], "_active": true, "_components": [], "_prefab": {"__id__": 130}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [258.5, -176, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 118}, "_prefab": {"__id__": 120}, "_name": "trainItem_1020_2_4", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 119}, "asset": {"__uuid__": "167be98c-b9cc-43d3-b73e-ee0ee7d19069"}, "fileId": "4dPbUIYI9E/boVs0nWeik9", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 118}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33, -18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32jfVGu4lIIa5MZorrSeDm", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 118}, "_children": [], "_active": true, "_components": [{"__id__": 124}], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33, -113, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -120, "y": -50}, {"__type__": "cc.Vec2", "x": 240, "y": -50}, {"__type__": "cc.Vec2", "x": 240, "y": -30}, {"__type__": "cc.Vec2", "x": 320, "y": -30}, {"__type__": "cc.Vec2", "x": 320, "y": -10}, {"__type__": "cc.Vec2", "x": 360, "y": -10}, {"__type__": "cc.Vec2", "x": 360, "y": 10}, {"__type__": "cc.Vec2", "x": 400, "y": 10}, {"__type__": "cc.Vec2", "x": 420, "y": 48}, {"__type__": "cc.Vec2", "x": 420, "y": 150}, {"__type__": "cc.Vec2", "x": -400, "y": 150}, {"__type__": "cc.Vec2", "x": -400, "y": 30}, {"__type__": "cc.Vec2", "x": -342, "y": -12}, {"__type__": "cc.Vec2", "x": -202, "y": -40}, {"__type__": "cc.Vec2", "x": -140, "y": -40}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80dFvlasNOQo2zrEK6cZpE", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 118}, "_children": [{"__id__": 127}], "_active": true, "_components": [], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 126}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 128}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [352, -138, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4b0Rn8ZrtBqpg00RzhIaPc", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "398E2LzQVMC5SiGiLMevTS", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46pvzLGL5ClqZubQfPUWdQ", "sync": false}, {"__type__": "cc.Node", "_name": "浴灯", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 132}, {"__id__": 134}, {"__id__": 136}], "_active": true, "_components": [], "_prefab": {"__id__": 139}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-250.5, -183.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 131}, "_prefab": {"__id__": 133}, "_name": "trainItem_1020_2_2", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 132}, "asset": {"__uuid__": "97c92b17-0d37-43f1-a257-ad395651d31f"}, "fileId": "4betdb5eNH6KeINlBbEApL", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 131}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [5.5, -87, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fV9Dre4RDvpn10r88AkyZ", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 131}, "_children": [], "_active": true, "_components": [{"__id__": 137}], "_prefab": {"__id__": 138}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -67, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -65, "y": -30}, {"__type__": "cc.Vec2", "x": 10, "y": -50}, {"__type__": "cc.Vec2", "x": 55, "y": -30}, {"__type__": "cc.Vec2", "x": 55, "y": -10}, {"__type__": "cc.Vec2", "x": -65, "y": -10}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3c9QqLei1JLbT0TDUaOi5U", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4DlucDU5P4pnicms8jTgC", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24vyUqrfBGqKw4Ab6hOHFA", "sync": false}, {"__type__": "cc.Node", "_name": "carriage_grid", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 143}], "_active": true, "_components": [{"__id__": 145}], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 230}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1075, -450, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 142}, "_prefab": {"__id__": 144}, "_name": "map_grid", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 143}, "asset": {"__uuid__": "cbd568b7-c9b6-440c-9afe-1e015ca6575c"}, "fileId": "17I4GwiVFFc49cMBsYwuLD", "sync": true}, {"__type__": "c4129Li+KJLkZEp0lnX9U5G", "_name": "", "_objFlags": 0, "node": {"__id__": 142}, "_enabled": true, "gridSize": 20, "size": {"__type__": "cc.Size", "width": 108, "height": 13}, "basePoint": {"__type__": "cc.Vec2", "x": 0, "y": 50}, "buildNode": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4lpbYn0BJkod/310a/7+c", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]