[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "theme_1014_1", "_objFlags": 512, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 134}, {"__id__": 137}], "_active": true, "_components": [], "_prefab": {"__id__": 142}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "new", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2444, "height": 1146}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -9.528, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ad5c727e-79b6-403d-abb1-01c4b6554abb"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53j+S41WxP3YbSUZgXjxWy", "sync": false}, {"__type__": "cc.Node", "_name": "root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 6}, {"__id__": 11}, {"__id__": 15}, {"__id__": 20}, {"__id__": 24}, {"__id__": 28}, {"__id__": 32}, {"__id__": 39}, {"__id__": 46}, {"__id__": 57}, {"__id__": 61}, {"__id__": 72}, {"__id__": 87}, {"__id__": 104}, {"__id__": 113}], "_active": true, "_components": [{"__id__": 132}], "_prefab": {"__id__": 133}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "地板默认", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 7}], "_active": false, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 285}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -277, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 6}, "_prefab": {"__id__": 8}, "_name": "trainItem_1014_1_14", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "a57509c5-6ec8-46b6-8ef9-87329bbf8df9"}, "fileId": "33zdfaRJlMs7oPGLCo834Z", "sync": true}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": false, "alignMode": 1, "_target": null, "_alignFlags": 4, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0fQAmzSZKfKP/6Jmjcazq", "sync": false}, {"__type__": "cc.Node", "_name": "地板", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 12}], "_active": true, "_components": [], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -278, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 13}, "_name": "trainItem_1014_1_9", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 12}, "asset": {"__uuid__": "bb1e2f73-6e2e-4cc0-a4f5-4cd7461b9f3e"}, "fileId": "0f8c6dDfpHkYQB3D0jucp1", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4Df+jrDdDqoNN10Drahcp", "sync": false}, {"__type__": "cc.Node", "_name": "墙纸默认", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 16}], "_active": false, "_components": [{"__id__": 18}], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2202.75, "height": 550}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 135.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 17}, "_name": "trainItem_1014_1_13", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "7d21cc5b-0e89-447d-a85b-e10aac4b4ada"}, "fileId": "85ZgRSBylOP6w6QAS03j/F", "sync": true}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": false, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5R1oMHe1AX6RELAMzAa61", "sync": false}, {"__type__": "cc.Node", "_name": "墙纸", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 21}], "_active": true, "_components": [], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2202.75, "height": 608}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 147, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 20}, "_prefab": {"__id__": 22}, "_name": "trainItem_1014_1_8", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 21}, "asset": {"__uuid__": "7f595511-e5d2-4c9d-81b0-2261180ab2cc"}, "fileId": "44r37UF4FHaJ1syRk4K3oJ", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50jsLm4PdNN4tUvcSVIcBE", "sync": false}, {"__type__": "cc.Node", "_name": "拱形窗", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 25}], "_active": true, "_components": [], "_prefab": {"__id__": 27}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 201, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 24}, "_prefab": {"__id__": 26}, "_name": "trainItem_1014_1_12", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 25}, "asset": {"__uuid__": "e4fb0f23-83a7-4710-8b73-d71658cb91cd"}, "fileId": "5e81tYaIVBQrkiBB87A7AR", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55hPhrRetIGI8poXOtQGPW", "sync": false}, {"__type__": "cc.Node", "_name": "厨房墙", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 29}], "_active": true, "_components": [], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-775.5, 116, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 28}, "_prefab": {"__id__": 30}, "_name": "trainItem_1014_1_5", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 29}, "asset": {"__uuid__": "864fa5db-2e3c-41c8-baa9-cd0d8cb74694"}, "fileId": "e3l8xz2KNN5JgCippHj1TW", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4d3wyPwqJPKbEFI5a077fH", "sync": false}, {"__type__": "cc.Node", "_name": "电视桌", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 33}, {"__id__": 35}], "_active": true, "_components": [], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 69, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 32}, "_prefab": {"__id__": 34}, "_name": "trainItem_1014_1_15", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "9d620bff-694a-47b4-bba4-ab201ddad20f"}, "fileId": "7f8NXt261O1JP1bGm5EtiE", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -212, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -317, "y": -17}, {"__type__": "cc.Vec2", "x": 317, "y": -17}, {"__type__": "cc.Vec2", "x": 317, "y": 30}, {"__type__": "cc.Vec2", "x": -317, "y": 30}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e70nbkOWxD75ddL0ybEn+6", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61fSd0lxVL/behBNSRRUu/", "sync": false}, {"__type__": "cc.Node", "_name": "电视", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 40}, {"__id__": 42}], "_active": true, "_components": [], "_prefab": {"__id__": 45}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 39}, "_prefab": {"__id__": 41}, "_name": "trainItem_1014_1_6", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 40}, "asset": {"__uuid__": "d336686e-8040-45ee-b636-06213fa2d0d5"}, "fileId": "7f8NXt261O1JP1bGm5EtiE", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -212, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -317, "y": -17}, {"__type__": "cc.Vec2", "x": 317, "y": -17}, {"__type__": "cc.Vec2", "x": 317, "y": 30}, {"__type__": "cc.Vec2", "x": -317, "y": 30}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d4ds1pf/9AO7EPkhKiF77Q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0DEVZCKdOAoJVOXYw7XFf", "sync": false}, {"__type__": "cc.Node", "_name": "婴儿床", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 47}, {"__id__": 49}, {"__id__": 52}], "_active": true, "_components": [], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [723, 29, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 46}, "_prefab": {"__id__": 48}, "_name": "trainItem_1014_1_1", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 47}, "asset": {"__uuid__": "1d53dc19-15c4-4fcc-817b-f2faf1679fbf"}, "fileId": "91OsrZqoVNJ4noWyL8mdlT", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -194, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -162.2, "y": 5.6}, {"__type__": "cc.Vec2", "x": 51.4, "y": -0.3}, {"__type__": "cc.Vec2", "x": 270.9, "y": 7.3}, {"__type__": "cc.Vec2", "x": 280, "y": 36}, {"__type__": "cc.Vec2", "x": -173.9, "y": 31.4}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23ecYIwLdNep2j87HQGUHK", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [{"__id__": 53}], "_active": true, "_components": [], "_prefab": {"__id__": 55}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "上床", "_objFlags": 0, "_parent": {"__id__": 52}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20.88, -207.215, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "481bf0oC5PKIb1nQmI/z2s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56jnTTLSVOWre8bB1uQKQK", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61W10GgFVO/ICBeLt/riI7", "sync": false}, {"__type__": "cc.Node", "_name": "地毯", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 58}], "_active": true, "_components": [], "_prefab": {"__id__": 60}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [387.5, -286.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 57}, "_prefab": {"__id__": 59}, "_name": "trainItem_1014_1_11", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 58}, "asset": {"__uuid__": "bd991f07-6ae0-4f6d-bbe1-438fea9c6ee2"}, "fileId": "08ksCZBPlGJadJrD6W9hDb", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49rT2222xPdIqxaoLPSWWG", "sync": false}, {"__type__": "cc.Node", "_name": "躺椅1", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 62}, {"__id__": 64}, {"__id__": 67}], "_active": true, "_components": [], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [753, -200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 61}, "_prefab": {"__id__": 63}, "_name": "trainItem_1014_1_7", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 62}, "asset": {"__uuid__": "2897cc08-2627-4f80-967d-cde2beaa8581"}, "fileId": "1eyigOi7BHSpMl9YarR1uW", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 61}, "_children": [], "_active": true, "_components": [{"__id__": 65}], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [30, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -68.3, "y": -87.9}, {"__type__": "cc.Vec2", "x": 34.8, "y": -44.9}, {"__type__": "cc.Vec2", "x": 109.1, "y": -35.6}, {"__type__": "cc.Vec2", "x": 109.7, "y": -11.5}, {"__type__": "cc.Vec2", "x": 84.6, "y": 12.7}, {"__type__": "cc.Vec2", "x": -3.5, "y": 19.2}, {"__type__": "cc.Vec2", "x": -110, "y": 12.9}, {"__type__": "cc.Vec2", "x": -182.2, "y": -35.8}, {"__type__": "cc.Vec2", "x": -182.2, "y": -58.5}, {"__type__": "cc.Vec2", "x": -107.4, "y": -63.5}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1042xr3Z5DkKZ2aCiqQkNl", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 61}, "_children": [{"__id__": 68}], "_active": true, "_components": [], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-30, 230, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "坐下", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 69}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-73.751, -345.473, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46raOXNpxD1oMv+avGPrh3", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99OU1Hjd5Nvrb+6JEYSZj+", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b2NuEq4ndIo4lnhv2U99Wl", "sync": false}, {"__type__": "cc.Node", "_name": "躺椅2", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 73}, {"__id__": 75}, {"__id__": 78}], "_active": true, "_components": [], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [526, -159, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 72}, "_prefab": {"__id__": 74}, "_name": "trainItem_1014_1_10", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 73}, "asset": {"__uuid__": "734cacfe-4157-4fd5-b502-44a6483f9735"}, "fileId": "d817fUJ39LFJWpqj4QVzvk", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -42.1, "y": -55.4}, {"__type__": "cc.Vec2", "x": 30.6, "y": -68.8}, {"__type__": "cc.Vec2", "x": 54.7, "y": -31.6}, {"__type__": "cc.Vec2", "x": 105.9, "y": -0.4}, {"__type__": "cc.Vec2", "x": 115.8, "y": 17}, {"__type__": "cc.Vec2", "x": 106.5, "y": 29}, {"__type__": "cc.Vec2", "x": -11, "y": 32.9}, {"__type__": "cc.Vec2", "x": -69.4, "y": 31.9}, {"__type__": "cc.Vec2", "x": -119.2, "y": -49.3}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81qMvHidVGYqROHuLGeQmF", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 79}], "_active": true, "_components": [], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "坐下", "_objFlags": 0, "_parent": {"__id__": 78}, "_children": [{"__id__": 80}], "_active": true, "_components": [], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100.31, -60.657, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1007_1", "_objFlags": 0, "_parent": {"__id__": 79}, "_children": [], "_active": false, "_components": [{"__id__": 81}, {"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "life/ani_cook", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "c6167c41-60e5-4dac-b435-0a8ed884cea6"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "7af8fae0vJHbrAJrCAJ0LNj", "_name": "", "_objFlags": 0, "node": {"__id__": 80}, "_enabled": true, "duration": 1, "_percent": 0, "_play": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2enJfjdtlNd6ZNdZcDsRKh", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5emwnIPgZNsJmzqekIqNpy", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6ji3ygY1OI7/5Uj/kRe1U", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eT7wK7HBHYKPscYwG81t9", "sync": false}, {"__type__": "cc.Node", "_name": "懒人沙发", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 88}, {"__id__": 90}, {"__id__": 93}, {"__id__": 101}], "_active": true, "_components": [], "_prefab": {"__id__": 103}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [165, -244, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 87}, "_prefab": {"__id__": 89}, "_name": "trainItem_1014_1_3", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 88}, "asset": {"__uuid__": "ce5f2215-7615-4691-b974-75f4b3816f76"}, "fileId": "69yFfPBx1DnI44ZT7b2u/v", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 87}, "_children": [], "_active": true, "_components": [{"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -74.7, "y": -5}, {"__type__": "cc.Vec2", "x": -53.2, "y": -20}, {"__type__": "cc.Vec2", "x": 57.1, "y": -20}, {"__type__": "cc.Vec2", "x": 78.2, "y": -5}, {"__type__": "cc.Vec2", "x": 100, "y": 14.6}, {"__type__": "cc.Vec2", "x": 100, "y": 60}, {"__type__": "cc.Vec2", "x": -110, "y": 60}, {"__type__": "cc.Vec2", "x": -110, "y": 17.2}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9iiDj/nRH0oLKZXoBqSpb", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 87}, "_children": [{"__id__": 94}], "_active": true, "_components": [], "_prefab": {"__id__": 100}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "坐下", "_objFlags": 0, "_parent": {"__id__": 93}, "_children": [{"__id__": 95}], "_active": false, "_components": [], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [44.941, -76.375, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1007_1", "_objFlags": 0, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 97}], "_prefab": {"__id__": 98}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "life/sit_idle", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "c6167c41-60e5-4dac-b435-0a8ed884cea6"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "7af8fae0vJHbrAJrCAJ0LNj", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "duration": 2, "_percent": 0, "_play": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37C5q/QCBBD7LQXY0LwnK/", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8mO0lBW9G75UpYE2pNsyr", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3faRXtutNG7IFlUoAleabK", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 87}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 102}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -39.701, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a9eGIzQpVHTrBQH3bTVcFY", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1doVpu0pBLg4qExw8FPlLW", "sync": false}, {"__type__": "cc.Node", "_name": "桌子", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 105}, {"__id__": 107}, {"__id__": 110}], "_active": true, "_components": [], "_prefab": {"__id__": 112}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [387, -241.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 104}, "_prefab": {"__id__": 106}, "_name": "trainItem_1014_1_2", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 105}, "asset": {"__uuid__": "88fe0d7c-59b7-4ff8-9eae-5fa76a714cc8"}, "fileId": "71ZwyoRxVB2ZNEYsAtMv9f", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_components": [{"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -140.4, "y": 12.9}, {"__type__": "cc.Vec2", "x": -80, "y": -15}, {"__type__": "cc.Vec2", "x": 0, "y": -20}, {"__type__": "cc.Vec2", "x": 80, "y": -15}, {"__type__": "cc.Vec2", "x": 131.9, "y": 9.6}, {"__type__": "cc.Vec2", "x": 185, "y": 60}, {"__type__": "cc.Vec2", "x": -1, "y": 86.9}, {"__type__": "cc.Vec2", "x": -185, "y": 60}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcChEpUjpP6q7WOAYgllRi", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 111}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -64.133, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "955U0n1zJHvo3m7qjs3MGT", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05gnpmDwdK5Zy+mO2FoFj7", "sync": false}, {"__type__": "cc.Node", "_name": "灶台", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [{"__id__": 114}, {"__id__": 126}, {"__id__": 128}], "_active": true, "_components": [], "_prefab": {"__id__": 131}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-646, -195, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [{"__id__": 115}, {"__id__": 120}], "_active": true, "_components": [], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "切菜", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 116}], "_active": true, "_components": [], "_prefab": {"__id__": 119}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-179.09, -46.649, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1007_1", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [], "_active": true, "_components": [{"__id__": 117}], "_prefab": {"__id__": 118}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "c6167c41-60e5-4dac-b435-0a8ed884cea6"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6d3He1WBpIYZbMEVKuenQp", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6cIXPuAGxF5oFURMcLWY7Z", "sync": false}, {"__type__": "cc.Node", "_name": "炒菜", "_objFlags": 0, "_parent": {"__id__": 114}, "_children": [{"__id__": 121}], "_active": true, "_components": [], "_prefab": {"__id__": 124}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [40.754, -40.694, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1007_1", "_objFlags": 0, "_parent": {"__id__": 120}, "_children": [], "_active": true, "_components": [{"__id__": 122}], "_prefab": {"__id__": 123}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 326.66, "height": 319.94}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "c6167c41-60e5-4dac-b435-0a8ed884cea6"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11MBX4YWZKsbUOBrZUKCq4", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4/soH2JNCL58uEFf4NkXA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3eTojzz5VNJZhaFSVp9Umu", "sync": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 113}, "_prefab": {"__id__": 127}, "_name": "trainItem_1014_1_4", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 126}, "asset": {"__uuid__": "e32fb3c9-0594-43a1-8872-9e98082ab26f"}, "fileId": "17f2lzuF9DP6owFzwZTbbn", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 113}, "_children": [], "_active": true, "_components": [{"__id__": 129}], "_prefab": {"__id__": 130}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -80.51, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -278, "y": -68.3}, {"__type__": "cc.Vec2", "x": 303.7, "y": -69.7}, {"__type__": "cc.Vec2", "x": 324.7, "y": 33}, {"__type__": "cc.Vec2", "x": -336.2, "y": 31.1}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1d1zzx+/pKnbKbUyQkmExA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5KvSNoVhAkbMjNAyGBffv", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": false, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbdG4osDNGn5cuIjff8DrO", "sync": false}, {"__type__": "cc.Node", "_name": "sp_carriage", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 135}], "_prefab": {"__id__": 136}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2421, "height": 1206}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "jingzhi", "_preCacheMode": 1, "_cacheMode": 1, "loop": false, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "jingzhi", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "fda0bc63-b6ab-461e-8bab-ae0582904738"}, "_N$_defaultCacheMode": 1, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01ebBSRk1Hdb4QH36AMSTw", "sync": false}, {"__type__": "cc.Node", "_name": "carriage_grid", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 138}], "_active": false, "_components": [{"__id__": 140}], "_prefab": {"__id__": 141}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 230}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1075, -450, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 137}, "_prefab": {"__id__": 139}, "_name": "map_grid", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 138}, "asset": {"__uuid__": "cbd568b7-c9b6-440c-9afe-1e015ca6575c"}, "fileId": "17I4GwiVFFc49cMBsYwuLD", "sync": true}, {"__type__": "c4129Li+KJLkZEp0lnX9U5G", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "gridSize": 20, "size": {"__type__": "cc.Size", "width": 108, "height": 13}, "basePoint": {"__type__": "cc.Vec2", "x": 0, "y": 50}, "buildNode": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dAiNAb11IVL1zCwnZIVZH", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]