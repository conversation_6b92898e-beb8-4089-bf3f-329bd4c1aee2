[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "theme_1020_1", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 154}], "_active": true, "_components": [], "_prefab": {"__id__": 159}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}, {"__id__": 26}, {"__id__": 32}, {"__id__": 38}, {"__id__": 44}, {"__id__": 50}, {"__id__": 59}, {"__id__": 72}, {"__id__": 81}, {"__id__": 98}, {"__id__": 115}, {"__id__": 130}, {"__id__": 143}], "_active": true, "_components": [{"__id__": 152}], "_prefab": {"__id__": 153}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "地板老", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 6}], "_active": true, "_components": [], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -255, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 3}, "_prefab": {"__id__": 5}, "_name": "trainItem_1020_1_14", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "cd417f17-4b0a-4bc0-85fe-9138c0c68f52"}, "fileId": "abuPjeNeJIxa1znkimgC8d", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 801, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fVte1E8lF8KxoJadt2wlG", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ef845568BCbqehowNypz/B", "sync": false}, {"__type__": "cc.Node", "_name": "地板", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 10}, {"__id__": 12}, {"__id__": 14}], "_active": true, "_components": [], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-13, -255, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 9}, "_prefab": {"__id__": 11}, "_name": "trainItem_1020_1_7", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 10}, "asset": {"__uuid__": "dfca3e30-4d20-415a-97e1-0448cc4a9bc5"}, "fileId": "edWp5j1+JDXJa4lV7XDbsI", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [13, 800, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58REO8glVPToyE5RqyWwur", "sync": false}, {"__type__": "cc.Node", "_name": "colliders", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [{"__id__": 15}, {"__id__": 18}, {"__id__": 21}], "_active": true, "_components": [], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1062, 95, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": -20}, {"__type__": "cc.Vec2", "x": 120, "y": -20}, {"__type__": "cc.Vec2", "x": 120, "y": -40}, {"__type__": "cc.Vec2", "x": 140, "y": -40}, {"__type__": "cc.Vec2", "x": 140, "y": -60}, {"__type__": "cc.Vec2", "x": 160, "y": -60}, {"__type__": "cc.Vec2", "x": 160, "y": -100}, {"__type__": "cc.Vec2", "x": 180, "y": -100}, {"__type__": "cc.Vec2", "x": 180, "y": -120}, {"__type__": "cc.Vec2", "x": 220, "y": -120}, {"__type__": "cc.Vec2", "x": 220, "y": -140}, {"__type__": "cc.Vec2", "x": 460, "y": -140}, {"__type__": "cc.Vec2", "x": 460, "y": -120}, {"__type__": "cc.Vec2", "x": 400, "y": -120}, {"__type__": "cc.Vec2", "x": 400, "y": -100}, {"__type__": "cc.Vec2", "x": 340, "y": -100}, {"__type__": "cc.Vec2", "x": 340, "y": -80}, {"__type__": "cc.Vec2", "x": 300, "y": -80}, {"__type__": "cc.Vec2", "x": 300, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": 0}, {"__type__": "cc.Vec2", "x": 160, "y": 0}, {"__type__": "cc.Vec2", "x": 160, "y": 20}, {"__type__": "cc.Vec2", "x": 0, "y": 20}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49Lb3ZUKROj6pCUvl4isHo", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-662, -65, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -140, "y": 20}, {"__type__": "cc.Vec2", "x": -140, "y": 0}, {"__type__": "cc.Vec2", "x": -100, "y": 0}, {"__type__": "cc.Vec2", "x": -100, "y": -20}, {"__type__": "cc.Vec2", "x": 0, "y": -20}, {"__type__": "cc.Vec2", "x": 0, "y": -40}, {"__type__": "cc.Vec2", "x": 120, "y": -40}, {"__type__": "cc.Vec2", "x": 120, "y": -60}, {"__type__": "cc.Vec2", "x": 1460, "y": -60}, {"__type__": "cc.Vec2", "x": 1460, "y": -40}, {"__type__": "cc.Vec2", "x": 1520, "y": -40}, {"__type__": "cc.Vec2", "x": 1520, "y": -20}, {"__type__": "cc.Vec2", "x": 1560, "y": -20}, {"__type__": "cc.Vec2", "x": 1560, "y": 0}, {"__type__": "cc.Vec2", "x": 1580, "y": 0}, {"__type__": "cc.Vec2", "x": 1580, "y": 40}, {"__type__": "cc.Vec2", "x": 1600, "y": 40}, {"__type__": "cc.Vec2", "x": 1600, "y": 80}, {"__type__": "cc.Vec2", "x": 1420, "y": 80}, {"__type__": "cc.Vec2", "x": 1420, "y": 40}, {"__type__": "cc.Vec2", "x": 1400, "y": 40}, {"__type__": "cc.Vec2", "x": 1400, "y": 20}, {"__type__": "cc.Vec2", "x": 1380, "y": 20}, {"__type__": "cc.Vec2", "x": 1380, "y": 0}, {"__type__": "cc.Vec2", "x": 1340, "y": 0}, {"__type__": "cc.Vec2", "x": 1340, "y": -20}, {"__type__": "cc.Vec2", "x": 1220, "y": -20}, {"__type__": "cc.Vec2", "x": 1220, "y": -40}, {"__type__": "cc.Vec2", "x": 720, "y": -40}, {"__type__": "cc.Vec2", "x": 720, "y": -20}, {"__type__": "cc.Vec2", "x": 520, "y": -20}, {"__type__": "cc.Vec2", "x": 520, "y": 0}, {"__type__": "cc.Vec2", "x": 180, "y": 0}, {"__type__": "cc.Vec2", "x": 180, "y": 20}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6de9If+bRJibbr+dix3Ro+", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [658, 95, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 20, "y": 0}, {"__type__": "cc.Vec2", "x": 20, "y": -20}, {"__type__": "cc.Vec2", "x": 60, "y": -20}, {"__type__": "cc.Vec2", "x": 60, "y": -40}, {"__type__": "cc.Vec2", "x": 80, "y": -40}, {"__type__": "cc.Vec2", "x": 80, "y": -80}, {"__type__": "cc.Vec2", "x": 280, "y": -80}, {"__type__": "cc.Vec2", "x": 280, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -60}, {"__type__": "cc.Vec2", "x": 260, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -40}, {"__type__": "cc.Vec2", "x": 240, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": -20}, {"__type__": "cc.Vec2", "x": 220, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": 0}, {"__type__": "cc.Vec2", "x": 80, "y": 20}, {"__type__": "cc.Vec2", "x": 0, "y": 20}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "340U0yi1RFYaTqAb5fuXEP", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8RZ5rEdRDhLgcrDbTIok5", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8J9W84tREgK4vTkUsznXI", "sync": false}, {"__type__": "cc.Node", "_name": "墙纸老", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 27}, {"__id__": 29}], "_active": true, "_components": [], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 26}, "_prefab": {"__id__": 28}, "_name": "trainItem_1020_1_13", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "84f87c71-fdf1-4849-973f-53b8e9c0ce7e"}, "fileId": "bavD6V+c1Blrhi7tK0RskW", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 30}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 321, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0buzjwoWNHOaBaJ56Hewqp", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48X4y9hv5Ae7ZHa/ozCP1J", "sync": false}, {"__type__": "cc.Node", "_name": "墙", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 33}, {"__id__": 35}], "_active": true, "_components": [], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 145.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 32}, "_prefab": {"__id__": 34}, "_name": "trainItem_1020_1_5", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "8ef46f05-c6d9-42f1-b9a2-b31a9da1f691"}, "fileId": "c2dR+AST5Fua1/ceWxmtmo", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 334, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52rbM1cUNA4JR3s7dh+uIc", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b93OvTQjFKCKhAmVOTZ+VR", "sync": false}, {"__type__": "cc.Node", "_name": "窗", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 39}, {"__id__": 41}], "_active": true, "_components": [], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 156, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 38}, "_prefab": {"__id__": 40}, "_name": "trainItem_1020_1_6", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 39}, "asset": {"__uuid__": "49c1efc8-9fd0-4e2b-b8a2-8af46a369b7c"}, "fileId": "a7gN4iZVlEzKufuCeCLCpc", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 42}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 260, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0e+KM4CwJB7pv+EpPUIBI1", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84Qr+a5t5HE5Dl5UoNFZ/Y", "sync": false}, {"__type__": "cc.Node", "_name": "盆栽-左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 45}, {"__id__": 47}], "_active": true, "_components": [], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-973, 83, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 46}, "_name": "trainItem_1020_1_11", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 45}, "asset": {"__uuid__": "9ba6158b-84d1-481f-9961-acba6fbcd631"}, "fileId": "eaLdB9W75OmIv0MtMHBbyY", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 48}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80tXBC+QxLFK7IxOsMFoOS", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33OCtrpzVNc7IZsYXe/wqm", "sync": false}, {"__type__": "cc.Node", "_name": "盆栽-右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}], "_active": true, "_components": [], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [498, 48, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 50}, "_prefab": {"__id__": 52}, "_name": "trainItem_1020_1_12", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 51}, "asset": {"__uuid__": "1ff8109c-2037-4fa6-abc9-c0d50009ea7d"}, "fileId": "a64JcdN3lKzL0QDHSYSMsn", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 235, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5dkEo6MVC+5L5ESFl4+jz", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 56}], "_prefab": {"__id__": 57}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -157, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -92, "y": -50}, {"__type__": "cc.Vec2", "x": 86, "y": -50}, {"__type__": "cc.Vec2", "x": 86, "y": -32}, {"__type__": "cc.Vec2", "x": -92, "y": -32}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "61c3S8ELlJ0pxWMYvw8WRG", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59lkPTqZZEjpuUWrere4Cp", "sync": false}, {"__type__": "cc.Node", "_name": "青蛙", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 60}, {"__id__": 62}, {"__id__": 64}, {"__id__": 67}], "_active": true, "_components": [], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [817, 116, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 59}, "_prefab": {"__id__": 61}, "_name": "trainItem_1020_1_3", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 60}, "asset": {"__uuid__": "3c3d009a-51fa-481d-9d82-0bed24f93bc3"}, "fileId": "178Ggr/elMZbE8mwQagUs9", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 63}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cBB3dbQJKwoIRVvWAbilz", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [], "_active": true, "_components": [{"__id__": 65}], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -225, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -92, "y": -50}, {"__type__": "cc.Vec2", "x": 148, "y": -50}, {"__type__": "cc.Vec2", "x": 148, "y": -32}, {"__type__": "cc.Vec2", "x": -92, "y": -32}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6jQqcfD1Og52Ec+vymH1b", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [{"__id__": 68}], "_active": true, "_components": [], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 69}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [110, -290, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22Q4MPHhVHb6jzcMRKA+iU", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94zR2qaX5CuqJH4gwh6kek", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e56Tsg4I5IuofgY4FGzyPc", "sync": false}, {"__type__": "cc.Node", "_name": "茶壶", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 73}, {"__id__": 75}, {"__id__": 77}], "_active": true, "_components": [], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-748, 110, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 72}, "_prefab": {"__id__": 74}, "_name": "trainItem_1020_1_10", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 73}, "asset": {"__uuid__": "72753ff7-ca71-4cbd-8fd5-54728ac6e8e8"}, "fileId": "6dYbKsEA5PRIQdJN1N5y9C", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 76}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdiw95x/JPDJLRK0i/qxw9", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 78}], "_prefab": {"__id__": 79}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -219, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -86, "y": -50}, {"__type__": "cc.Vec2", "x": 73, "y": -50}, {"__type__": "cc.Vec2", "x": 73, "y": -32.4}, {"__type__": "cc.Vec2", "x": -86, "y": -32}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22N+ty+rdJdaqWHZOfYZvl", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "432ORq4xFAaqu5zr64wRUG", "sync": false}, {"__type__": "cc.Node", "_name": "踏板-右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 82}, {"__id__": 84}, {"__id__": 86}], "_active": true, "_components": [], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [811, -232, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 81}, "_prefab": {"__id__": 83}, "_name": "trainItem_1020_1_9", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 82}, "asset": {"__uuid__": "7206c967-55ac-43cc-a571-0fb0d5de428d"}, "fileId": "6fWKGUpDRNBrI6iOapJIpg", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 123, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0f0ZEa0XpL3YfxTDLz7LE4", "sync": false}, {"__type__": "cc.Node", "_name": "colliders", "_objFlags": 0, "_parent": {"__id__": 81}, "_children": [{"__id__": 87}, {"__id__": 90}, {"__id__": 93}], "_active": true, "_components": [], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 88}], "_prefab": {"__id__": 89}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-6, 42, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -160, "y": -10}, {"__type__": "cc.Vec2", "x": 120, "y": -10}, {"__type__": "cc.Vec2", "x": 120, "y": 10}, {"__type__": "cc.Vec2", "x": -160, "y": 10}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8boHk81dMYp2lLfz98/wr", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [34, -38, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -160, "y": -10}, {"__type__": "cc.Vec2", "x": 140, "y": -10}, {"__type__": "cc.Vec2", "x": 140, "y": 30}, {"__type__": "cc.Vec2", "x": -160, "y": 30}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daM6U77jFLkbHO1n8EhrkI", "sync": false}, {"__type__": "cc.Node", "_name": "collider(可走)", "_objFlags": 0, "_parent": {"__id__": 86}, "_children": [], "_active": true, "_components": [{"__id__": 94}], "_prefab": {"__id__": 95}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 93}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 134, "y": -10}, {"__type__": "cc.Vec2", "x": 134, "y": 30}, {"__type__": "cc.Vec2", "x": -146, "y": 30}, {"__type__": "cc.Vec2", "x": -146, "y": -10}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fXEjn8pFMT6kJC9oIRfHb", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3aAJQ5u0VOabMPdsFfB3PH", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "189QCV2S9Lv69/Xm+6nro9", "sync": false}, {"__type__": "cc.Node", "_name": "踏板-左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}], "_active": true, "_components": [], "_prefab": {"__id__": 114}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-776.5, -292, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 98}, "_prefab": {"__id__": 100}, "_name": "trainItem_1020_1_8", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 99}, "asset": {"__uuid__": "6d72b5a4-fe55-4bad-b0b5-671e00638128"}, "fileId": "cby40TMaVA+7tRF882NYmF", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 102}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 183, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7d9a38VSBNI6UHNF1IouHq", "sync": false}, {"__type__": "cc.Node", "_name": "colliders", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [{"__id__": 104}, {"__id__": 107}, {"__id__": 110}], "_active": true, "_components": [], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [{"__id__": 105}], "_prefab": {"__id__": 106}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-129, 22, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -50, "y": -50}, {"__type__": "cc.Vec2", "x": -10, "y": -50}, {"__type__": "cc.Vec2", "x": -10, "y": -32}, {"__type__": "cc.Vec2", "x": 210, "y": 40}, {"__type__": "cc.Vec2", "x": 190, "y": 52}, {"__type__": "cc.Vec2", "x": -50, "y": -23}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7O5kpa9NLTq8J0YkynXtI", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [{"__id__": 108}], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-28.5, -18, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 107}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -50, "y": -50}, {"__type__": "cc.Vec2", "x": -10, "y": -50}, {"__type__": "cc.Vec2", "x": 210, "y": 30}, {"__type__": "cc.Vec2", "x": 190, "y": 56}, {"__type__": "cc.Vec2", "x": -30, "y": -20}, {"__type__": "cc.Vec2", "x": -30, "y": -30}, {"__type__": "cc.Vec2", "x": -50, "y": -30}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d91hFhfwRCi6c5tq0PReqG", "sync": false}, {"__type__": "cc.Node", "_name": "collider(可走)", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [{"__id__": 111}], "_prefab": {"__id__": 112}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-8.5, 2, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -110, "y": -50}, {"__type__": "cc.Vec2", "x": -50, "y": -50}, {"__type__": "cc.Vec2", "x": -50, "y": -30}, {"__type__": "cc.Vec2", "x": 10, "y": -30}, {"__type__": "cc.Vec2", "x": 10, "y": -10}, {"__type__": "cc.Vec2", "x": 70, "y": -10}, {"__type__": "cc.Vec2", "x": 70, "y": 10}, {"__type__": "cc.Vec2", "x": 110, "y": 10}, {"__type__": "cc.Vec2", "x": 110, "y": 50}, {"__type__": "cc.Vec2", "x": 30, "y": 50}, {"__type__": "cc.Vec2", "x": 30, "y": 30}, {"__type__": "cc.Vec2", "x": -30, "y": 30}, {"__type__": "cc.Vec2", "x": -30, "y": 10}, {"__type__": "cc.Vec2", "x": -70, "y": 10}, {"__type__": "cc.Vec2", "x": -70, "y": -10}, {"__type__": "cc.Vec2", "x": -110, "y": -10}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7g3kjyzhFK5WG6hucbhGM", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6jP+2yPJKe5t7uIflOFQM", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43xhTpqzdE+7wcPobeZ7oH", "sync": false}, {"__type__": "cc.Node", "_name": "浴池-左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}, {"__id__": 123}], "_active": true, "_components": [], "_prefab": {"__id__": 129}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-260, -77, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 115}, "_prefab": {"__id__": 117}, "_name": "trainItem_1020_1_1", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 116}, "asset": {"__uuid__": "761aa857-4eb0-4d85-a7b6-faa0a87f79cd"}, "fileId": "33NaIduY1Gmafr+3nRw0o6", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 119}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4YbD7GWhA5J2wC6YAi2yx", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [], "_active": true, "_components": [{"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -112, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -235, "y": -70}, {"__type__": "cc.Vec2", "x": 64, "y": -70}, {"__type__": "cc.Vec2", "x": 64, "y": -50}, {"__type__": "cc.Vec2", "x": 245, "y": -50}, {"__type__": "cc.Vec2", "x": 245, "y": -30}, {"__type__": "cc.Vec2", "x": 325, "y": -30}, {"__type__": "cc.Vec2", "x": 325, "y": -10}, {"__type__": "cc.Vec2", "x": 405, "y": -10}, {"__type__": "cc.Vec2", "x": 425, "y": 50}, {"__type__": "cc.Vec2", "x": -235, "y": 50}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edjy+UlTpPqYv/zDVjQWEA", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [{"__id__": 124}, {"__id__": 126}], "_active": true, "_components": [], "_prefab": {"__id__": 128}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-370, -160, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89NvaVV7JC3pu3lDFdkfqt", "sync": false}, {"__type__": "cc.Node", "_name": "进入点2", "_objFlags": 0, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 127}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-315, -100, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "566SAISptFTrUateivmFwA", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99xdhX/ZNIkZ/+YNVMJzis", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a2qOXa9XxOlqQVYuOZwXBl", "sync": false}, {"__type__": "cc.Node", "_name": "浴池-右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 131}, {"__id__": 133}, {"__id__": 135}, {"__id__": 138}], "_active": true, "_components": [], "_prefab": {"__id__": 142}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [225.5, -194, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 130}, "_prefab": {"__id__": 132}, "_name": "trainItem_1020_1_4", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 131}, "asset": {"__uuid__": "432d8a45-38b8-42f0-8edd-0f7e944bbbbf"}, "fileId": "ecAayh27dOCbt2KU8gcSag", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 130}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 134}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32jfVGu4lIIa5MZorrSeDm", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 130}, "_children": [], "_active": true, "_components": [{"__id__": 136}], "_prefab": {"__id__": 137}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -95, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 135}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -120, "y": -50}, {"__type__": "cc.Vec2", "x": 220, "y": -50}, {"__type__": "cc.Vec2", "x": 240, "y": -30}, {"__type__": "cc.Vec2", "x": 320, "y": -30}, {"__type__": "cc.Vec2", "x": 320, "y": -10}, {"__type__": "cc.Vec2", "x": 360, "y": -10}, {"__type__": "cc.Vec2", "x": 360, "y": 10}, {"__type__": "cc.Vec2", "x": 400, "y": 10}, {"__type__": "cc.Vec2", "x": 400, "y": 150}, {"__type__": "cc.Vec2", "x": -400, "y": 150}, {"__type__": "cc.Vec2", "x": -400, "y": 30}, {"__type__": "cc.Vec2", "x": -342, "y": -12}, {"__type__": "cc.Vec2", "x": -225, "y": -26}, {"__type__": "cc.Vec2", "x": -202, "y": -40}, {"__type__": "cc.Vec2", "x": -140, "y": -40}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80dFvlasNOQo2zrEK6cZpE", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 130}, "_children": [{"__id__": 139}], "_active": true, "_components": [], "_prefab": {"__id__": 141}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 140}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [385, -120, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4b0Rn8ZrtBqpg00RzhIaPc", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "398E2LzQVMC5SiGiLMevTS", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46pvzLGL5ClqZubQfPUWdQ", "sync": false}, {"__type__": "cc.Node", "_name": "浴灯", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 144}, {"__id__": 146}, {"__id__": 148}], "_active": true, "_components": [], "_prefab": {"__id__": 151}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-245, -220.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 143}, "_prefab": {"__id__": 145}, "_name": "trainItem_1020_1_2", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 144}, "asset": {"__uuid__": "cceb3b45-de92-45d3-8dc2-ec2de2164c92"}, "fileId": "11Oo4MvNZCY6UXEl4Nq4LJ", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 143}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 147}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fV9Dre4RDvpn10r88AkyZ", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 143}, "_children": [], "_active": true, "_components": [{"__id__": 149}], "_prefab": {"__id__": 150}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -28.826, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 148}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -70, "y": -30}, {"__type__": "cc.Vec2", "x": 8, "y": -50}, {"__type__": "cc.Vec2", "x": 70, "y": -23}, {"__type__": "cc.Vec2", "x": 70, "y": -10}, {"__type__": "cc.Vec2", "x": -70, "y": -10}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3c9QqLei1JLbT0TDUaOi5U", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4DlucDU5P4pnicms8jTgC", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24vyUqrfBGqKw4Ab6hOHFA", "sync": false}, {"__type__": "cc.Node", "_name": "carriage_grid", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 155}], "_active": true, "_components": [{"__id__": 157}], "_prefab": {"__id__": 158}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 230}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1075, -450, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 154}, "_prefab": {"__id__": 156}, "_name": "map_grid", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 155}, "asset": {"__uuid__": "cbd568b7-c9b6-440c-9afe-1e015ca6575c"}, "fileId": "17I4GwiVFFc49cMBsYwuLD", "sync": true}, {"__type__": "c4129Li+KJLkZEp0lnX9U5G", "_name": "", "_objFlags": 0, "node": {"__id__": 154}, "_enabled": true, "gridSize": 20, "size": {"__type__": "cc.Size", "width": 108, "height": 13}, "basePoint": {"__type__": "cc.Vec2", "x": 0, "y": 50}, "buildNode": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4lpbYn0BJkod/310a/7+c", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]