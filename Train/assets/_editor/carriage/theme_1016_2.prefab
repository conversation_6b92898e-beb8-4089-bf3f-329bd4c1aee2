[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "theme_1016_2", "_objFlags": 512, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 118}, {"__id__": 121}], "_active": true, "_components": [], "_prefab": {"__id__": 126}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2259, "height": 416}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 7}, {"__id__": 11}, {"__id__": 24}, {"__id__": 33}, {"__id__": 46}, {"__id__": 55}, {"__id__": 68}, {"__id__": 92}, {"__id__": 103}, {"__id__": 109}], "_active": true, "_components": [{"__id__": 116}], "_prefab": {"__id__": 117}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "地板", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -274, 0, 0, 0, 0, 1, 0.968, 0.968, 0.968]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 3}, "_prefab": {"__id__": 5}, "_name": "trainItem_1016_2_8", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "be53166d-9fe9-46d5-bdb5-9a8374e451cf"}, "fileId": "97drGbEnFKgaUDW9KNM0WE", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10iKi7D7JFYoK8yiaswEmv", "sync": false}, {"__type__": "cc.Node", "_name": "墙纸", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 8}], "_active": true, "_components": [], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 140, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 7}, "_prefab": {"__id__": 9}, "_name": "trainItem_1016_2_7", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "e92378e8-e56f-4480-b83b-d1ccd31c538f"}, "fileId": "c9S4YBtx9IM7nQ8uSZeVOO", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dttPFocVFdIR4ocxWrlrO", "sync": false}, {"__type__": "cc.Node", "_name": "拉力器", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}, {"__id__": 14}, {"__id__": 17}, {"__id__": 21}], "_active": true, "_components": [], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-812, -35, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 13}, "_name": "trainItem_1016_2_5", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 12}, "asset": {"__uuid__": "77d6343a-de47-46b6-b512-1c832f2b1c8b"}, "fileId": "493hlOmqlBdqPEU2WVuThi", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 16}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -260.8, "y": -150}, {"__type__": "cc.Vec2", "x": 57.7, "y": -149}, {"__type__": "cc.Vec2", "x": 58.3, "y": -111.5}, {"__type__": "cc.Vec2", "x": -261.4, "y": -112}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6foiLc71pNzaoUvE+hkmNK", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 18}], "_active": true, "_components": [], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 19}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32.826, -162.82, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43WD3Tdj1NeKjx9tJxooQY", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1Im+WwCJIpZ/3eoXEshaF", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 119.855, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40V4kx1xZM1L9RwofGOaw5", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "82DruNMPdPr5I3o4VmSa94", "sync": false}, {"__type__": "cc.Node", "_name": "水箱左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 25}, {"__id__": 27}, {"__id__": 30}], "_active": true, "_components": [], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-617, 60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 24}, "_prefab": {"__id__": 26}, "_name": "trainItem_1016_2_4", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 25}, "asset": {"__uuid__": "d9feffdb-87e0-4300-b4a2-9d70cd904541"}, "fileId": "a5S5doWMtM8ra0ZOd+Wg1q", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15.588, -187.052, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -140.9, "y": -60.3}, {"__type__": "cc.Vec2", "x": -80.1, "y": -99.3}, {"__type__": "cc.Vec2", "x": 159.6, "y": -98.1}, {"__type__": "cc.Vec2", "x": 256, "y": -57}, {"__type__": "cc.Vec2", "x": 256, "y": -10}, {"__type__": "cc.Vec2", "x": -140.5, "y": -19}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdsDo1NIdGXIW32ahADprW", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85oDNHsMlHgbdGRVeVWbIk", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7d4yKImUZKnooAIluNKkKH", "sync": false}, {"__type__": "cc.Node", "_name": "跑步机", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 34}, {"__id__": 36}, {"__id__": 39}, {"__id__": 43}], "_active": true, "_components": [], "_prefab": {"__id__": 45}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [779, -102, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 33}, "_prefab": {"__id__": 35}, "_name": "trainItem_1016_2_10", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 34}, "asset": {"__uuid__": "8b5656f1-ed57-4e19-8253-bd62111f2c14"}, "fileId": "e4xh6nnP5GrZkFOAyMw/+X", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 37}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -100.3, "y": -78.3}, {"__type__": "cc.Vec2", "x": 240.1, "y": -77.1}, {"__type__": "cc.Vec2", "x": 238.9, "y": -0.1}, {"__type__": "cc.Vec2", "x": -102.4, "y": 0.6}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67Blh5xmxKQrQJ+b6iLqFb", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 40}], "_active": true, "_components": [], "_prefab": {"__id__": 42}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "上跑步机", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [42.975, -140.195, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e24eq4SA1FBaCljMXCD+Ys", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48NUEzEtVDdp7/OV4BFgom", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 194.043, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6HFcyx8NJvJfkc1OVMklu", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8yapRLiJF1bFMkMLOFBES", "sync": false}, {"__type__": "cc.Node", "_name": "能量舱右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 47}, {"__id__": 49}, {"__id__": 52}], "_active": true, "_components": [], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [492, 27, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 46}, "_prefab": {"__id__": 48}, "_name": "trainItem_1016_2_9", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 47}, "asset": {"__uuid__": "9e07f775-d219-426c-b83f-ace184de9888"}, "fileId": "e65K3wG15DXLtu6uSr6bwa", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-83.134, -194.846, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": 18.9, "y": -59.4}, {"__type__": "cc.Vec2", "x": 200.4, "y": -61.6}, {"__type__": "cc.Vec2", "x": 299, "y": -60.1}, {"__type__": "cc.Vec2", "x": 302, "y": 17.8}, {"__type__": "cc.Vec2", "x": -0.1, "y": 18.1}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f86Qp7kr5LPZL6DpXBpxfI", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 46}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -76.903, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7pyqs+ExKDYHEfCQxm/3i", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "053MUFx4lAsaLIkXCVbc63", "sync": false}, {"__type__": "cc.Node", "_name": "混动舱", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 56}, {"__id__": 58}, {"__id__": 61}, {"__id__": 63}], "_active": true, "_components": [], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [2, 128, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 55}, "_prefab": {"__id__": 57}, "_name": "trainItem_1016_2_1", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 56}, "asset": {"__uuid__": "5d9a17f2-d0b0-48be-a423-b9111fe2c054"}, "fileId": "1bxRw5qHBHeYoEE1MXC8jo", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 59}], "_prefab": {"__id__": 60}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33.773, -260, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -339.9, "y": -72}, {"__type__": "cc.Vec2", "x": -201.7, "y": -75.9}, {"__type__": "cc.Vec2", "x": -160.7, "y": -113.1}, {"__type__": "cc.Vec2", "x": 35.3, "y": -113.7}, {"__type__": "cc.Vec2", "x": 218.9, "y": -113}, {"__type__": "cc.Vec2", "x": 279.7, "y": -75}, {"__type__": "cc.Vec2", "x": 418.8, "y": -72.7}, {"__type__": "cc.Vec2", "x": 420, "y": -15.4}, {"__type__": "cc.Vec2", "x": -341.6, "y": -13.3}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0os0zDbpLkKsHCSohDhoS", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -222.006, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76CURTpllE0pkQ52rlY7tu", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [{"__id__": 64}], "_active": true, "_components": [], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "燃料投入点", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 65}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4.89, -40.825, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07JyJbaDROIIdjfbNe8wJO", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5809ikEDxD8a0508JkQn4s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28CPEx48tDcrcOyjmIvfdz", "sync": false}, {"__type__": "cc.Node", "_name": "椅子", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 69}, {"__id__": 71}, {"__id__": 88}], "_active": true, "_components": [], "_prefab": {"__id__": 91}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [317, -218, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 68}, "_prefab": {"__id__": 70}, "_name": "trainItem_1016_2_3", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 69}, "asset": {"__uuid__": "b3898d48-6011-4df3-aec5-15f80284ba49"}, "fileId": "c3hK673a5GP7pJm4iefIDG", "sync": true}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [{"__id__": 72}, {"__id__": 77}, {"__id__": 82}], "_active": true, "_components": [], "_prefab": {"__id__": 87}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "坐下点", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [{"__id__": 73}], "_active": true, "_components": [], "_prefab": {"__id__": 76}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -102.38, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1015_1", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": false, "_components": [{"__id__": 74}], "_prefab": {"__id__": 75}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 185.63, "height": 325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f043ef9a-6ae6-477f-bda6-e05a20900a76"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51y4S8jiNHR6Ed4kGpVaNU", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4a8LVo5G9F8bsCm1NZS3dk", "sync": false}, {"__type__": "cc.Node", "_name": "进入点左", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [{"__id__": 78}], "_active": true, "_components": [], "_prefab": {"__id__": 81}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-103.247, -102.38, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1015_1", "_objFlags": 0, "_parent": {"__id__": 77}, "_children": [], "_active": false, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 185.63, "height": 325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-108.347, 2.692, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f043ef9a-6ae6-477f-bda6-e05a20900a76"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4uLFmF95BLI9h2Jn+/Pqv", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaxvpJdu1Ph5dbQF3i6lsk", "sync": false}, {"__type__": "cc.Node", "_name": "进入点右", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [{"__id__": 83}], "_active": true, "_components": [], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [112.634, -102.38, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1015_1", "_objFlags": 0, "_parent": {"__id__": 82}, "_children": [], "_active": false, "_components": [{"__id__": 84}], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 185.63, "height": 325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-108.347, 2.692, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f043ef9a-6ae6-477f-bda6-e05a20900a76"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feTK2RSQhK4qq/A6rgyvEl", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43N5LUHPlNGKLluCKJGJUt", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4c6O4mIGBIwauk2JCt3UYg", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 89}], "_prefab": {"__id__": 90}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -92.9, "y": -105.9}, {"__type__": "cc.Vec2", "x": 105.9, "y": -105.7}, {"__type__": "cc.Vec2", "x": 106.5, "y": -47.8}, {"__type__": "cc.Vec2", "x": -93, "y": -46.7}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44SVlSgIRJsJ8CU0jzc8nM", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cftRaKukZDRZKx32nLeIcO", "sync": false}, {"__type__": "cc.Node", "_name": "电脑桌", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 93}, {"__id__": 95}, {"__id__": 98}, {"__id__": 100}], "_active": true, "_components": [], "_prefab": {"__id__": 102}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [399, -296, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 92}, "_prefab": {"__id__": 94}, "_name": "trainItem_1016_2_2", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 93}, "asset": {"__uuid__": "b4853756-48a8-45a8-98a4-8066dd180a35"}, "fileId": "96GJ6iT3dHRLxRaoFlW9wT", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [{"__id__": 96}], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-16.162, 3.463, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -220.1, "y": -91}, {"__type__": "cc.Vec2", "x": 239.8, "y": -90.6}, {"__type__": "cc.Vec2", "x": 238.8, "y": -32.8}, {"__type__": "cc.Vec2", "x": -221, "y": -31.7}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8L3xyyI5OBaTbvIKoAs91", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6VWxoVs9KLrGTw3bnEswm", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 92}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32.34, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1Zk+JMe5EHo3HjnWsy0ge", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01VW/sIbpLG6HlT9V4vfik", "sync": false}, {"__type__": "cc.Node", "_name": "电脑&键盘", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 104}, {"__id__": 106}], "_active": true, "_components": [], "_prefab": {"__id__": 108}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [425, -200, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 103}, "_prefab": {"__id__": 105}, "_name": "trainItem_1016_2_6", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 104}, "asset": {"__uuid__": "02b65ce7-d517-4ced-a9bd-745c99e224e3"}, "fileId": "eaaRSs84lAy7q9MyasiQRv", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -168.473, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aceNEVBFOhriqo95KD/W2", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fBHAMs6ZFwqD8L8AXWI20", "sync": false}, {"__type__": "cc.Node", "_name": "图纸盒", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 110}, {"__id__": 112}], "_active": true, "_components": [], "_prefab": {"__id__": 115}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [140, -283, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 109}, "_prefab": {"__id__": 111}, "_name": "trainItem_1016_2_11", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 110}, "asset": {"__uuid__": "6a3b2fed-ae47-462a-95ec-be40077ccb8a"}, "fileId": "06tMaiunRIY7auBhZTn0Cp", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 113}], "_prefab": {"__id__": 114}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4.806, -24.028, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -78.6, "y": -68}, {"__type__": "cc.Vec2", "x": 60.9, "y": -66}, {"__type__": "cc.Vec2", "x": 61.7, "y": -7.2}, {"__type__": "cc.Vec2", "x": -79.1, "y": -5.9}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06521q4wFMe4n+ty8nuOGO", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dqvo+K5tPiojjrJy9U4kh", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24vyUqrfBGqKw4Ab6hOHFA", "sync": false}, {"__type__": "cc.Node", "_name": "sp_carriage", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 119}], "_prefab": {"__id__": 120}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2421, "height": 1205.************}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1, -60, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 118}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "jingzhi", "_preCacheMode": 1, "_cacheMode": 1, "loop": false, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "jingzhi", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 1, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "fda0bc63-b6ab-461e-8bab-ae0582904738"}, "_N$_defaultCacheMode": 1, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbJ4o6/odKBIXBTjPqyLyH", "sync": false}, {"__type__": "cc.Node", "_name": "carriage_grid", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 122}], "_active": true, "_components": [{"__id__": 124}], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 230}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1075, -450, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 121}, "_prefab": {"__id__": 123}, "_name": "map_grid", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 122}, "asset": {"__uuid__": "cbd568b7-c9b6-440c-9afe-1e015ca6575c"}, "fileId": "17I4GwiVFFc49cMBsYwuLD", "sync": true}, {"__type__": "c4129Li+KJLkZEp0lnX9U5G", "_name": "", "_objFlags": 0, "node": {"__id__": 121}, "_enabled": true, "gridSize": 20, "size": {"__type__": "cc.Size", "width": 108, "height": 13}, "basePoint": {"__type__": "cc.Vec2", "x": 0, "y": 50}, "buildNode": {"__id__": 2}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68XBQqxqxFx5WrmrSRIDP6", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]