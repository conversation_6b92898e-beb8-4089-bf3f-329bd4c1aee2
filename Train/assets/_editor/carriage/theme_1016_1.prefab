[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "theme_1016_1", "_objFlags": 512, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 126}, {"__id__": 129}], "_active": true, "_components": [], "_prefab": {"__id__": 134}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2259, "height": 416}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 7}, {"__id__": 11}, {"__id__": 15}, {"__id__": 19}, {"__id__": 32}, {"__id__": 41}, {"__id__": 54}, {"__id__": 63}, {"__id__": 76}, {"__id__": 100}, {"__id__": 111}, {"__id__": 117}], "_active": true, "_components": [{"__id__": 124}], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "地板老", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [], "_prefab": {"__id__": 6}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -275, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 3}, "_prefab": {"__id__": 5}, "_name": "trainItem_1016_1_13", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 4}, "asset": {"__uuid__": "5100c6a9-a88a-4bd1-b2f9-9e3da5127a0e"}, "fileId": "4fUFuJ+JNFUosDKfgUQnqs", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7e5M29PNlBc4OmTq7GkDXo", "sync": false}, {"__type__": "cc.Node", "_name": "地板", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 8}], "_active": false, "_components": [], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -268.407, 0, 0, 0, 0, 1, 0.968, 0.968, 0.968]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 7}, "_prefab": {"__id__": 9}, "_name": "trainItem_1016_1_8", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "e86a9886-c64e-4bcd-804d-62862c820ccd"}, "fileId": "daHuJqflFGTJrbla+kEcgx", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10iKi7D7JFYoK8yiaswEmv", "sync": false}, {"__type__": "cc.Node", "_name": "墙纸老", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}], "_active": true, "_components": [], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 142.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 11}, "_prefab": {"__id__": 13}, "_name": "trainItem_1016_1_12", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 12}, "asset": {"__uuid__": "c09b2578-bb4d-4cb9-8558-c78fddc18830"}, "fileId": "257I2VYzZDdaTWWGskXdni", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c34J/0/2ZLyoAMNRtYVt6e", "sync": false}, {"__type__": "cc.Node", "_name": "墙纸", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 16}], "_active": false, "_components": [], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.068, 153.407, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 15}, "_prefab": {"__id__": 17}, "_name": "trainItem_1016_1_7", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "b9dfce3f-04cb-47dc-8cb2-0edc41af3f42"}, "fileId": "32pkK9HxhMGqFjTrgLbFAe", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dttPFocVFdIR4ocxWrlrO", "sync": false}, {"__type__": "cc.Node", "_name": "拉力器", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 20}, {"__id__": 22}, {"__id__": 25}, {"__id__": 29}], "_active": true, "_components": [], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-813.151, -28.477, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 19}, "_prefab": {"__id__": 21}, "_name": "trainItem_1016_1_5", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 20}, "asset": {"__uuid__": "a696ec31-26a8-49b6-9be7-e8d21de66fbe"}, "fileId": "88RSjvBJdH6omAG4EJZOaN", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 23}], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -260.8, "y": -150}, {"__type__": "cc.Vec2", "x": 57.7, "y": -149}, {"__type__": "cc.Vec2", "x": 58.3, "y": -111.5}, {"__type__": "cc.Vec2", "x": -261.4, "y": -112}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6foiLc71pNzaoUvE+hkmNK", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [{"__id__": 26}], "_active": true, "_components": [], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "进入点", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 27}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32.826, -162.82, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43WD3Tdj1NeKjx9tJxooQY", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1Im+WwCJIpZ/3eoXEshaF", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 30}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 119.855, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40V4kx1xZM1L9RwofGOaw5", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "82DruNMPdPr5I3o4VmSa94", "sync": false}, {"__type__": "cc.Node", "_name": "水箱左", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 33}, {"__id__": 35}, {"__id__": 38}], "_active": true, "_components": [], "_prefab": {"__id__": 40}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-619.243, 65.769, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 32}, "_prefab": {"__id__": 34}, "_name": "trainItem_1016_1_4", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 33}, "asset": {"__uuid__": "2aab595b-8b1d-4b7d-9005-d60fc96b6a4e"}, "fileId": "e6Hj3shT1Ed4mQ7r4PrbgG", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-15.588, -187.052, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -140.9, "y": -60.3}, {"__type__": "cc.Vec2", "x": -78.8, "y": -79.3}, {"__type__": "cc.Vec2", "x": 161.8, "y": -78.1}, {"__type__": "cc.Vec2", "x": 256, "y": -57}, {"__type__": "cc.Vec2", "x": 256, "y": -10}, {"__type__": "cc.Vec2", "x": -140.5, "y": -19}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdsDo1NIdGXIW32ahADprW", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85oDNHsMlHgbdGRVeVWbIk", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7d4yKImUZKnooAIluNKkKH", "sync": false}, {"__type__": "cc.Node", "_name": "跑步机", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 42}, {"__id__": 44}, {"__id__": 47}, {"__id__": 51}], "_active": true, "_components": [], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [775.75, -89.414, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 41}, "_prefab": {"__id__": 43}, "_name": "trainItem_1016_1_10", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 42}, "asset": {"__uuid__": "c067464c-1c07-410b-9cdb-8e6ffd410415"}, "fileId": "53/v3wYLtFto4svG0LUJwS", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [], "_active": true, "_components": [{"__id__": 45}], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [10.392, -51.959, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -119.7, "y": -58.2}, {"__type__": "cc.Vec2", "x": 237.8, "y": -56.9}, {"__type__": "cc.Vec2", "x": 239.4, "y": 0.2}, {"__type__": "cc.Vec2", "x": -119.6, "y": -0.7}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67Blh5xmxKQrQJ+b6iLqFb", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [{"__id__": 48}], "_active": true, "_components": [], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "上跑步机", "_objFlags": 0, "_parent": {"__id__": 47}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -109.809, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e24eq4SA1FBaCljMXCD+Ys", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48NUEzEtVDdp7/OV4BFgom", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 41}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 52}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 194.043, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6HFcyx8NJvJfkc1OVMklu", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8yapRLiJF1bFMkMLOFBES", "sync": false}, {"__type__": "cc.Node", "_name": "能量舱右", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 55}, {"__id__": 57}, {"__id__": 60}], "_active": true, "_components": [], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [488.487, 36.072, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 54}, "_prefab": {"__id__": 56}, "_name": "trainItem_1016_1_9", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 55}, "asset": {"__uuid__": "5c683f1a-592b-4699-8b47-d639c5e4293b"}, "fileId": "35Y+qlOF5LGYMo1LnMfhj+", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [{"__id__": 58}], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-83.134, -194.846, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -40.8, "y": 16.8}, {"__type__": "cc.Vec2", "x": -39.8, "y": -41.9}, {"__type__": "cc.Vec2", "x": 277.6, "y": -40.8}, {"__type__": "cc.Vec2", "x": 279.6, "y": 17.1}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f86Qp7kr5LPZL6DpXBpxfI", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 54}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -76.903, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7pyqs+ExKDYHEfCQxm/3i", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "053MUFx4lAsaLIkXCVbc63", "sync": false}, {"__type__": "cc.Node", "_name": "混动舱", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 64}, {"__id__": 66}, {"__id__": 69}, {"__id__": 71}], "_active": true, "_components": [], "_prefab": {"__id__": 75}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 100.031, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 63}, "_prefab": {"__id__": 65}, "_name": "trainItem_1016_1_1", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "8600c346-69e0-4277-b14b-2b4919b443ec"}, "fileId": "73HCmKv6VM5bBaDaFqxDc+", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-33.773, -226.021, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -342, "y": -54.6}, {"__type__": "cc.Vec2", "x": -240.2, "y": -93.1}, {"__type__": "cc.Vec2", "x": 279.2, "y": -91.4}, {"__type__": "cc.Vec2", "x": 418.8, "y": -72.7}, {"__type__": "cc.Vec2", "x": 420, "y": -15.4}, {"__type__": "cc.Vec2", "x": -341.6, "y": -13.3}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0os0zDbpLkKsHCSohDhoS", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -222.006, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76CURTpllE0pkQ52rlY7tu", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 63}, "_children": [{"__id__": 72}], "_active": true, "_components": [], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "燃料添加点", "_objFlags": 0, "_parent": {"__id__": 71}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 73}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0SkTcU3hGlZVB8o40E0bH", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4e8kmX9hBDsYqwjqDae6lV", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28CPEx48tDcrcOyjmIvfdz", "sync": false}, {"__type__": "cc.Node", "_name": "椅子", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 77}, {"__id__": 79}, {"__id__": 96}], "_active": true, "_components": [], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [318.873, -211.839, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 76}, "_prefab": {"__id__": 78}, "_name": "trainItem_1016_1_3", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "1e1b0e76-bee4-4aa1-817a-2cd5d4f0fda9"}, "fileId": "01NHlF0NZPk5GRUchd5snc", "sync": true}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [{"__id__": 80}, {"__id__": 85}, {"__id__": 90}], "_active": true, "_components": [], "_prefab": {"__id__": 95}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "坐下点", "_objFlags": 0, "_parent": {"__id__": 79}, "_children": [{"__id__": 81}], "_active": true, "_components": [], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -102.38, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1015_1", "_objFlags": 0, "_parent": {"__id__": 80}, "_children": [], "_active": false, "_components": [{"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 185.63, "height": 325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f043ef9a-6ae6-477f-bda6-e05a20900a76"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51y4S8jiNHR6Ed4kGpVaNU", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4a8LVo5G9F8bsCm1NZS3dk", "sync": false}, {"__type__": "cc.Node", "_name": "进入点左", "_objFlags": 0, "_parent": {"__id__": 79}, "_children": [{"__id__": 86}], "_active": true, "_components": [], "_prefab": {"__id__": 89}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-103.247, -102.38, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1015_1", "_objFlags": 0, "_parent": {"__id__": 85}, "_children": [], "_active": false, "_components": [{"__id__": 87}], "_prefab": {"__id__": 88}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 185.63, "height": 325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-108.347, 2.692, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 86}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f043ef9a-6ae6-477f-bda6-e05a20900a76"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f4uLFmF95BLI9h2Jn+/Pqv", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaxvpJdu1Ph5dbQF3i6lsk", "sync": false}, {"__type__": "cc.Node", "_name": "进入点右", "_objFlags": 0, "_parent": {"__id__": 79}, "_children": [{"__id__": 91}], "_active": true, "_components": [], "_prefab": {"__id__": 94}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [112.634, -102.38, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "character_1015_1", "_objFlags": 0, "_parent": {"__id__": 90}, "_children": [], "_active": false, "_components": [{"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 185.63, "height": 325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-108.347, 2.692, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "_materials": [{"__uuid__": "7afd064b-113f-480e-b793-8817d19f63c3"}], "paused": false, "defaultSkin": "default", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": true, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "f043ef9a-6ae6-477f-bda6-e05a20900a76"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$debugMesh": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feTK2RSQhK4qq/A6rgyvEl", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43N5LUHPlNGKLluCKJGJUt", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4c6O4mIGBIwauk2JCt3UYg", "sync": false}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 76}, "_children": [], "_active": true, "_components": [{"__id__": 97}], "_prefab": {"__id__": 98}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 96}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -92.9, "y": -105.9}, {"__type__": "cc.Vec2", "x": 105.9, "y": -105.7}, {"__type__": "cc.Vec2", "x": 106.5, "y": -47.8}, {"__type__": "cc.Vec2", "x": -93, "y": -46.7}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44SVlSgIRJsJ8CU0jzc8nM", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cftRaKukZDRZKx32nLeIcO", "sync": false}, {"__type__": "cc.Node", "_name": "电脑桌", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 101}, {"__id__": 103}, {"__id__": 106}, {"__id__": 108}], "_active": true, "_components": [], "_prefab": {"__id__": 110}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [401.346, -291.539, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 100}, "_prefab": {"__id__": 102}, "_name": "trainItem_1016_1_2", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 101}, "asset": {"__uuid__": "66cab842-e1e4-4239-9d7a-95df7f18e26f"}, "fileId": "92YMdsdU5JWo2IzgwU0toK", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_active": true, "_components": [{"__id__": 104}], "_prefab": {"__id__": 105}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-16.162, 3.463, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -220.1, "y": -91}, {"__type__": "cc.Vec2", "x": 239.8, "y": -90.6}, {"__type__": "cc.Vec2", "x": 238.8, "y": -32.8}, {"__type__": "cc.Vec2", "x": -221, "y": -31.7}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8L3xyyI5OBaTbvIKoAs91", "sync": false}, {"__type__": "cc.Node", "_name": "usePosList", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6VWxoVs9KLrGTw3bnEswm", "sync": false}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 109}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -32.34, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1Zk+JMe5EHo3HjnWsy0ge", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01VW/sIbpLG6HlT9V4vfik", "sync": false}, {"__type__": "cc.Node", "_name": "电脑&键盘", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 112}, {"__id__": 114}], "_active": true, "_components": [], "_prefab": {"__id__": 116}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [448.789, -180.862, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 111}, "_prefab": {"__id__": 113}, "_name": "trainItem_1016_1_6", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 112}, "asset": {"__uuid__": "f484a964-8e4a-4582-9db1-2ce5f7437fca"}, "fileId": "5cjpKjMqFLqqJ0QGQhBl7c", "sync": true}, {"__type__": "cc.Node", "_name": "zIndex", "_objFlags": 0, "_parent": {"__id__": 111}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 115}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -168.473, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aceNEVBFOhriqo95KD/W2", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fBHAMs6ZFwqD8L8AXWI20", "sync": false}, {"__type__": "cc.Node", "_name": "图纸盒", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 118}, {"__id__": 120}], "_active": true, "_components": [], "_prefab": {"__id__": 123}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [139.658, -289.146, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 117}, "_prefab": {"__id__": 119}, "_name": "trainItem_1016_1_11", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 118}, "asset": {"__uuid__": "380dd139-5a58-43a1-9781-4a6d71df66ca"}, "fileId": "14fFo7L+FOw7QLMnW4c3Rf", "sync": true}, {"__type__": "cc.Node", "_name": "collider", "_objFlags": 0, "_parent": {"__id__": 117}, "_children": [], "_active": true, "_components": [{"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [4.806, -24.028, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PolygonCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "tag": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "points": [{"__type__": "cc.Vec2", "x": -78.2, "y": -46.7}, {"__type__": "cc.Vec2", "x": 80.4, "y": -48}, {"__type__": "cc.Vec2", "x": 80.8, "y": -7.2}, {"__type__": "cc.Vec2", "x": -79.1, "y": -5.9}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06521q4wFMe4n+ty8nuOGO", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dqvo+K5tPiojjrJy9U4kh", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24vyUqrfBGqKw4Ab6hOHFA", "sync": false}, {"__type__": "cc.Node", "_name": "3三号动力室01", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 127}], "_prefab": {"__id__": 128}, "_opacity": 120, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2688, "height": 1242}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1.482, -51.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "d6ff6795-7ba6-4992-800e-6b0e23ebf035"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fuMFGyopNG5xQDUrQkmpc", "sync": false}, {"__type__": "cc.Node", "_name": "carriage_grid", "_objFlags": 512, "_parent": {"__id__": 1}, "_children": [{"__id__": 130}], "_active": true, "_components": [{"__id__": 132}], "_prefab": {"__id__": 133}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2150, "height": 230}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1075, -450, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 129}, "_prefab": {"__id__": 131}, "_name": "map_grid", "_active": true, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 130}, "asset": {"__uuid__": "cbd568b7-c9b6-440c-9afe-1e015ca6575c"}, "fileId": "17I4GwiVFFc49cMBsYwuLD", "sync": true}, {"__type__": "c4129Li+KJLkZEp0lnX9U5G", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "gridSize": 20, "size": {"__type__": "cc.Size", "width": 108, "height": 13}, "basePoint": {"__type__": "cc.Vec2", "x": 0, "y": 50}, "buildNode": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0VV8bz3RAhK38DYXJie8G", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "", "sync": false}]