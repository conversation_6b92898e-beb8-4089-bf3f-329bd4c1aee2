// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const {ccclass, property, menu, executeInEditMode} = cc._decorator;

@ccclass
@executeInEditMode
export default class NodeSync extends cc.Component {

    @property(cc.Node)
    target: cc.Node = null

    @property({tooltip: "同步位置"})
    syncPos: boolean = true

    protected update(dt: number): void {
        if (CC_EDITOR) {
            this.updateSync()
        }
    }

    public updateSync() {
        if (!this.target) return
        if (this.syncPos) {
            let pos = ut.convertToNodeAR(this.target, this.node.parent)
            this.node.setPosition(pos)
        }
    }
}
