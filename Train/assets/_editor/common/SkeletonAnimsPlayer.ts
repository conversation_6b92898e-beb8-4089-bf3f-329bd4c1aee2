const { ccclass, property, menu, executeInEditMode, requireComponent, playOnFocus } = cc._decorator;

const RANGE_LEN = 100
let DefaultAnimsEnum = cc.Enum({ '<None>': 0 });

@ccclass
@executeInEditMode
@requireComponent(sp.Skeleton)
@playOnFocus
@menu("角色动画编辑/骨骼序列动画播放器")
export default class SkeletonAnimsPlayer extends cc.Component {
    @property(sp.Skeleton)
    target: sp.Skeleton = null

    @property
    private _percent: number = 0

    @property({
        range: [0, RANGE_LEN, 1],
        slide: true,
        displayName: "进度",
    })
    get percent() {
        return this._percent
    }
    set percent(val) {
        this._percent = val
        this.updateCurTime()
    }

    @property({type: [DefaultAnimsEnum]})
    animIndexes = []

    @property
    private _mixes = []

    @property({type: [cc.Float]})
    get mixes() {
        return this._mixes
    }
    set mixes(val) {
        this._mixes = val
        this.updateMix()
    }

    @property
    _play: boolean = false

    @property()
    get play() {
        return this._play
    }
    set play(val) {
        this._play = val
        this.paused = !val
    }

    private curTime: number = 0
    private totTime: number = 0

    private paused: boolean = true

    private oldData: sp.SkeletonData = null

    protected onLoad(): void {
        if (!CC_EDITOR) return
        this.replaceUpdateMethod()
    }

    protected onEnable(): void {
        if (!CC_EDITOR) return
        this.updateskeletonData()
        this.updateAnim()
    }

    private getAnims() {
        let animsEnum = this.getAnimsEnum()
        let sk = this.getTarget()
        return this.animIndexes.map((index)=>{
            let name = animsEnum[index]
            if (index == 0) return {dur: 0}
            let dur = sk.getAnimationDuration(name)
            return {name, dur}
        })
    }

    private getTarget() {
        return this.target || this.Component(sp.Skeleton)
    }

    private updateCurTime() {
        let ratio = this.percent / RANGE_LEN
        let tot = this.totTime
        this.curTime = tot * ratio
        this.updateAnim()
    }

    private updateAnim() {
        if (!CC_EDITOR) return
        this.paused = false
        let anims = this.getAnims()
        let curTime = this.curTime % this.totTime
        let sk = this.getTarget()
        for (let anim of anims) {
            let {name, dur} = anim
            if (!name) continue
            sk.setAnimation(0, name, false)
            sk.addTime(cc.misc.clampf(curTime, 0, dur))
            curTime -= dur
            if (curTime <= 0) {
                break
            }
        }
        this.paused = true
    }

    private updateskeletonData() {
        this.curTime = 0
        let anims = this.getAnims()
        let tot = 0
        for (let {dur} of anims) {
            tot += dur
        }
        this.totTime = tot
        this.updateMix()
        cc.setEnumAttr(this, "animIndexes", this.getAnimsEnum())
    }

    private updateMix() {
        let sk = this.getTarget()

        let anims = this.getAnims()
        for (let i = 0; i < this.mixes.length; i++) {
            let from = anims[i]
            if (!from) break
            let to = anims[i + 1]
            if (!from.name || !to.name) continue
            sk.setMix(from.name, to.name, this.mixes[i])
        }
        this.updateAnim()
    }

    private getAnimsEnum() {
        let sk = this.getTarget()
        //@ts-ignore
        return sk.skeletonData.getAnimsEnum()
    }

    update(dt) {
        if (!CC_EDITOR) return
        if (this.play) {
            this.curTime += dt
            this.updateAnim()
        }

        let sk = this.getTarget()
        if (this.oldData != sk.skeletonData) {
            this.oldData = sk.skeletonData
            this.updateskeletonData()
            this.updateAnim()
        }
    }

    replaceUpdateMethod() {
        let self = this
        let sk = this.getTarget()
        let update = function(dt) {
            if (self.paused) return;
            dt *= this.timeScale * sp["timeScale"];

            if (this.isAnimationCached()) {

                // Cache mode and has animation queue.
                if (this._isAniComplete) {
                    if (this._animationQueue.length === 0 && !this._headAniInfo) {
                        let frameCache = this._frameCache;
                        if (frameCache && frameCache.isInvalid()) {
                            frameCache.updateToFrame();
                            let frames = frameCache.frames;
                            this._curFrame = frames[frames.length - 1];
                        }
                        return;
                    }
                    if (!this._headAniInfo) {
                        this._headAniInfo = this._animationQueue.shift();
                    }
                    this._accTime += dt;
                    if (this._accTime > this._headAniInfo.delay) {
                        let aniInfo = this._headAniInfo;
                        this._headAniInfo = null;
                        this.setAnimation(0, aniInfo.animationName, aniInfo.loop);
                    }
                    return;
                }

                this._updateCache(dt);
            } else {
                this._updateRealtime(dt);
            }
        }
        sk["update"] = update.bind(sk)
    }
}
