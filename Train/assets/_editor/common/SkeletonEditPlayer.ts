const { ccclass, property, menu, executeInEditMode, requireComponent, playOnFocus } = cc._decorator;

const RANGE_LEN = 100

@ccclass
@executeInEditMode
@requireComponent(sp.Skeleton)
@playOnFocus
@menu("角色动画编辑/骨骼动画播放器")
export default class SkeletonEditPlayer extends cc.Component {
    @property({readonly: true, displayName: "动画时长"})
    private duration: number = 0

    @property({type: [cc.String], readonly: true, displayName: "事件"})
    private events: string[] = ["e:1"]

    @property
    private _percent: number = 0

    @property({
        range: [0, RANGE_LEN, 1],
        slide: true,
        displayName: "进度",
    })
    get percent() {
        return this._percent
    }
    set percent(val) {
        this._percent = val
        this.updateAnim()
    }

    @property
    _play: boolean = false

    @property()
    get play() {
        return this._play
    }
    set play(val) {
        this._play = val
        this.paused = !val
        this.sk.setAnimation(0, this.sk.animation, this.sk.loop)
    }



    private paused: boolean = false

    private oldAnim: string = null
    private oldData: sp.SkeletonData = null

    private sk: sp.Skeleton = null

    protected onLoad(): void {
        if (!CC_EDITOR) return
        this.sk = this.getComponent(sp.Skeleton)
        let self = this
        let update = function(dt) {
            if (self.paused) return;

            dt *= this.timeScale * sp["timeScale"];

            if (this.isAnimationCached()) {

                // Cache mode and has animation queue.
                if (this._isAniComplete) {
                    if (this._animationQueue.length === 0 && !this._headAniInfo) {
                        let frameCache = this._frameCache;
                        if (frameCache && frameCache.isInvalid()) {
                            frameCache.updateToFrame();
                            let frames = frameCache.frames;
                            this._curFrame = frames[frames.length - 1];
                        }
                        return;
                    }
                    if (!this._headAniInfo) {
                        this._headAniInfo = this._animationQueue.shift();
                    }
                    this._accTime += dt;
                    if (this._accTime > this._headAniInfo.delay) {
                        let aniInfo = this._headAniInfo;
                        this._headAniInfo = null;
                        this.setAnimation(0, aniInfo.animationName, aniInfo.loop);
                    }
                    return;
                }

                this._updateCache(dt);
            } else {
                this._updateRealtime(dt);
            }
        }
        this.sk["update"] = update.bind(this.sk)
        this.paused = true
    }

    protected onEnable(): void {
        if (!CC_EDITOR) return
        this.updateAnim()
    }

    private updateAnim() {
        if (!CC_EDITOR) return
        this.paused = false
        let ratio = this.percent / RANGE_LEN
        let time = this.sk.getAnimationDuration(this.sk.animation)
        this.duration = time
        this.events = this.sk.getEvents(this.sk.animation).map(e => `${e.data.name}:${ut.toFixed(e.time, 2)}`)
        let curTime = time * ratio
        this.sk.setAnimation(0, this.sk.animation, false)
        this.sk.addTime(curTime)
        this.paused = true
    }

    private updateskeletonData() {
        //@ts-ignore
        // cc.setEnumAttr(this, "test1", this.sk.skeletonData.getAnimsEnum())
    }

    update(dt) {
        if (!CC_EDITOR) return

        if (this.oldData != this.sk.skeletonData) {
            this.oldData = this.sk.skeletonData
            this.updateskeletonData()
            this.updateAnim()
        }
        if (this.oldAnim != this.sk.animation) {
            this.oldAnim = this.sk.animation
            this.updateAnim()
        }
    }

}
