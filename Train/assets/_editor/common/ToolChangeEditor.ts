import { HeroAnimation, PlanetMineType } from "../../app/script/common/constant/Enums";
import { resHelper } from "../../app/script/common/helper/ResHelper";

const { ccclass, property, menu, executeInEditMode, requireComponent, playOnFocus } = cc._decorator;

let types = {}
let json = {}
// if (CC_EDITOR) {
//     let json = JSON.parse(fs.readFileSync(Editor.Project.path + "/assets/resources/common/json/Tool.json", 'utf-8'))
//     for (let {id} of json) {
//         types[id] = Number(id)
//     }
// }

@ccclass
@executeInEditMode
@requireComponent(sp.Skeleton)
@menu("角色动画编辑/更换工具")
export default class ToolChangeEditor extends cc.Component {
    @property
    _tool: number = 1001

    @property({ type: cc.Enum(types) })
    get tool() {
        return this._tool
    }
    set tool(val) {
        this.oldTool = this._tool
        this._tool = val
        this.updateView()
    }

    @property
    test: boolean = false

    private sk: sp.Skeleton = null
    private oldTool: number = null

    protected onLoad(): void {
        this.sk = this.Component(sp.Skeleton)
    }

    protected onEnable(): void {
        this.updateView()
    }

    private updateView() {
        this.showTool(false, this.oldTool)
        this.showTool(true, this.tool)
    }

    private async showTool(show: boolean, id: number) {
        let tool = json.find(it => it.id == id)
        if (!tool) return
        resHelper.switchTool(this.sk, tool.type, show && tool.icon, this.uuid)
    }
}
